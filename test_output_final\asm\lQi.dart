// lib: , url: lQi

// class id: 1048722, size: 0x8
class :: {
}

// class id: 4476, size: 0xc, field offset: 0x8
class Sz extends Object {

  static late final Sz qkc; // offset: 0xe8c
  static late final FD _Tpf; // offset: 0xe98
  static late final FD _Spf; // offset: 0xe94

  [closure] Future<void> <anonymous closure>(dynamic, HD, PD) async {
    // ** addr: 0x9563e0, size: 0x88
    // 0x9563e0: EnterFrame
    //     0x9563e0: stp             fp, lr, [SP, #-0x10]!
    //     0x9563e4: mov             fp, SP
    // 0x9563e8: AllocStack(0x20)
    //     0x9563e8: sub             SP, SP, #0x20
    // 0x9563ec: SetupParameters(Sz this /* r1 */, dynamic _ /* r2, fp-0x20 */, dynamic _ /* r3, fp-0x18 */)
    //     0x9563ec: stur            NULL, [fp, #-8]
    //     0x9563f0: movz            x0, #0
    //     0x9563f4: add             x1, fp, w0, sxtw #2
    //     0x9563f8: ldr             x1, [x1, #0x20]
    //     0x9563fc: add             x2, fp, w0, sxtw #2
    //     0x956400: ldr             x2, [x2, #0x18]
    //     0x956404: stur            x2, [fp, #-0x20]
    //     0x956408: add             x3, fp, w0, sxtw #2
    //     0x95640c: ldr             x3, [x3, #0x10]
    //     0x956410: stur            x3, [fp, #-0x18]
    //     0x956414: ldur            w4, [x1, #0x17]
    //     0x956418: add             x4, x4, HEAP, lsl #32
    //     0x95641c: stur            x4, [fp, #-0x10]
    // 0x956420: CheckStackOverflow
    //     0x956420: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x956424: cmp             SP, x16
    //     0x956428: b.ls            #0x956460
    // 0x95642c: InitAsync() -> Future<void?>
    //     0x95642c: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x956430: bl              #0x8c1de0  ; InitAsyncStub
    // 0x956434: r0 = InitLateStaticField(0xe78) // [sQi] fA::_ahf
    //     0x956434: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x956438: ldr             x0, [x0, #0x1cf0]
    //     0x95643c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x956440: cmp             w0, w16
    //     0x956444: b.ne            #0x956450
    //     0x956448: ldr             x2, [PP, #0x6f08]  ; [pp+0x6f08] Field <fA._ahf@549461673>: static late (offset: 0xe78)
    //     0x95644c: bl              #0x94dc14  ; InitLateStaticFieldStub
    // 0x956450: ldur            x1, [fp, #-0x18]
    // 0x956454: ldur            x2, [fp, #-0x20]
    // 0x956458: r0 = call 0x8b91b8
    //     0x956458: bl              #0x8b91b8
    // 0x95645c: r0 = ReturnAsync()
    //     0x95645c: b               #0x8c3a30  ; ReturnAsyncStub
    // 0x956460: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x956460: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x956464: b               #0x95642c
  }
  [closure] Future<void> <anonymous closure>(dynamic, jE<dynamic>, OD) async {
    // ** addr: 0x956468, size: 0x2e8
    // 0x956468: EnterFrame
    //     0x956468: stp             fp, lr, [SP, #-0x10]!
    //     0x95646c: mov             fp, SP
    // 0x956470: AllocStack(0x38)
    //     0x956470: sub             SP, SP, #0x38
    // 0x956474: SetupParameters(Sz this /* r1 */, dynamic _ /* r2, fp-0x20 */, dynamic _ /* r3, fp-0x18 */)
    //     0x956474: stur            NULL, [fp, #-8]
    //     0x956478: movz            x0, #0
    //     0x95647c: add             x1, fp, w0, sxtw #2
    //     0x956480: ldr             x1, [x1, #0x20]
    //     0x956484: add             x2, fp, w0, sxtw #2
    //     0x956488: ldr             x2, [x2, #0x18]
    //     0x95648c: stur            x2, [fp, #-0x20]
    //     0x956490: add             x3, fp, w0, sxtw #2
    //     0x956494: ldr             x3, [x3, #0x10]
    //     0x956498: stur            x3, [fp, #-0x18]
    //     0x95649c: ldur            w4, [x1, #0x17]
    //     0x9564a0: add             x4, x4, HEAP, lsl #32
    //     0x9564a4: stur            x4, [fp, #-0x10]
    // 0x9564a8: CheckStackOverflow
    //     0x9564a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9564ac: cmp             SP, x16
    //     0x9564b0: b.ls            #0x956740
    // 0x9564b4: InitAsync() -> Future<void?>
    //     0x9564b4: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x9564b8: bl              #0x8c1de0  ; InitAsyncStub
    // 0x9564bc: ldur            x3, [fp, #-0x20]
    // 0x9564c0: LoadField: r0 = r3->field_13
    //     0x9564c0: ldur            w0, [x3, #0x13]
    // 0x9564c4: DecompressPointer r0
    //     0x9564c4: add             x0, x0, HEAP, lsl #32
    // 0x9564c8: r16 = Sentinel
    //     0x9564c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9564cc: cmp             w0, w16
    // 0x9564d0: b.eq            #0x956748
    // 0x9564d4: LoadField: r1 = r0->field_4f
    //     0x9564d4: ldur            w1, [x0, #0x4f]
    // 0x9564d8: DecompressPointer r1
    //     0x9564d8: add             x1, x1, HEAP, lsl #32
    // 0x9564dc: r0 = LoadClassIdInstr(r1)
    //     0x9564dc: ldur            x0, [x1, #-1]
    //     0x9564e0: ubfx            x0, x0, #0xc, #0x14
    // 0x9564e4: r2 = "checkLine"
    //     0x9564e4: ldr             x2, [PP, #0x7758]  ; [pp+0x7758] "checkLine"
    // 0x9564e8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9564e8: ldr             x4, [PP, #0x2a8]  ; [pp+0x2a8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9564ec: r0 = GDT[cid_x0 + -0xffe]()
    //     0x9564ec: sub             lr, x0, #0xffe
    //     0x9564f0: ldr             lr, [x21, lr, lsl #3]
    //     0x9564f4: blr             lr
    // 0x9564f8: tbnz            w0, #4, #0x95650c
    // 0x9564fc: ldur            x1, [fp, #-0x18]
    // 0x956500: ldur            x2, [fp, #-0x20]
    // 0x956504: r0 = call 0x8b92f0
    //     0x956504: bl              #0x8b92f0
    // 0x956508: r0 = ReturnAsync()
    //     0x956508: b               #0x8c3a30  ; ReturnAsyncStub
    // 0x95650c: ldur            x2, [fp, #-0x20]
    // 0x956510: LoadField: r0 = r2->field_b
    //     0x956510: ldur            w0, [x2, #0xb]
    // 0x956514: DecompressPointer r0
    //     0x956514: add             x0, x0, HEAP, lsl #32
    // 0x956518: r16 = "data"
    //     0x956518: ldr             x16, [PP, #0x14f0]  ; [pp+0x14f0] "data"
    // 0x95651c: stp             x16, x0, [SP]
    // 0x956520: r4 = 0
    //     0x956520: movz            x4, #0
    // 0x956524: ldr             x0, [SP, #8]
    // 0x956528: r16 = UnlinkedCall_0x2d3c80
    //     0x956528: add             x16, PP, #7, lsl #12  ; [pp+0x7760] UnlinkedCall: 0x2d3c80 - SwitchableCallMissStub
    //     0x95652c: add             x16, x16, #0x760
    // 0x956530: ldp             x5, lr, [x16]
    // 0x956534: blr             lr
    // 0x956538: cmp             w0, NULL
    // 0x95653c: b.eq            #0x956660
    // 0x956540: ldur            x2, [fp, #-0x20]
    // 0x956544: LoadField: r0 = r2->field_b
    //     0x956544: ldur            w0, [x2, #0xb]
    // 0x956548: DecompressPointer r0
    //     0x956548: add             x0, x0, HEAP, lsl #32
    // 0x95654c: r16 = "data"
    //     0x95654c: ldr             x16, [PP, #0x14f0]  ; [pp+0x14f0] "data"
    // 0x956550: stp             x16, x0, [SP]
    // 0x956554: r4 = 0
    //     0x956554: movz            x4, #0
    // 0x956558: ldr             x0, [SP, #8]
    // 0x95655c: r16 = UnlinkedCall_0x2d3c80
    //     0x95655c: add             x16, PP, #7, lsl #12  ; [pp+0x7770] UnlinkedCall: 0x2d3c80 - SwitchableCallMissStub
    //     0x956560: add             x16, x16, #0x770
    // 0x956564: ldp             x5, lr, [x16]
    // 0x956568: blr             lr
    // 0x95656c: r1 = 59
    //     0x95656c: movz            x1, #0x3b
    // 0x956570: branchIfSmi(r0, 0x95657c)
    //     0x956570: tbz             w0, #0, #0x95657c
    // 0x956574: r1 = LoadClassIdInstr(r0)
    //     0x956574: ldur            x1, [x0, #-1]
    //     0x956578: ubfx            x1, x1, #0xc, #0x14
    // 0x95657c: str             x0, [SP]
    // 0x956580: mov             x0, x1
    // 0x956584: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x956584: ldr             x4, [PP, #0x490]  ; [pp+0x490] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x956588: r0 = GDT[cid_x0 + 0x241f]()
    //     0x956588: movz            x17, #0x241f
    //     0x95658c: add             lr, x0, x17
    //     0x956590: ldr             lr, [x21, lr, lsl #3]
    //     0x956594: blr             lr
    // 0x956598: r1 = LoadClassIdInstr(r0)
    //     0x956598: ldur            x1, [x0, #-1]
    //     0x95659c: ubfx            x1, x1, #0xc, #0x14
    // 0x9565a0: mov             x16, x0
    // 0x9565a4: mov             x0, x1
    // 0x9565a8: mov             x1, x16
    // 0x9565ac: r2 = "<!"
    //     0x9565ac: ldr             x2, [PP, #0x7780]  ; [pp+0x7780] "<!"
    // 0x9565b0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9565b0: ldr             x4, [PP, #0x2a8]  ; [pp+0x2a8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9565b4: r0 = GDT[cid_x0 + -0xffe]()
    //     0x9565b4: sub             lr, x0, #0xffe
    //     0x9565b8: ldr             lr, [x21, lr, lsl #3]
    //     0x9565bc: blr             lr
    // 0x9565c0: tbz             w0, #4, #0x956658
    // 0x9565c4: ldur            x2, [fp, #-0x20]
    // 0x9565c8: LoadField: r1 = r2->field_b
    //     0x9565c8: ldur            w1, [x2, #0xb]
    // 0x9565cc: DecompressPointer r1
    //     0x9565cc: add             x1, x1, HEAP, lsl #32
    // 0x9565d0: r0 = __unknown_function__()
    //     0x9565d0: bl              #0x956750  ; [] ::__unknown_function__
    // 0x9565d4: mov             x1, x0
    // 0x9565d8: stur            x1, [fp, #-0x28]
    // 0x9565dc: r0 = Await()
    //     0x9565dc: bl              #0x8c1bb8  ; AwaitStub
    // 0x9565e0: mov             x1, x0
    // 0x9565e4: r0 = call 0x45d1ec
    //     0x9565e4: bl              #0x45d1ec
    // 0x9565e8: mov             x4, x0
    // 0x9565ec: ldur            x3, [fp, #-0x20]
    // 0x9565f0: stur            x4, [fp, #-0x10]
    // 0x9565f4: LoadField: r2 = r3->field_7
    //     0x9565f4: ldur            w2, [x3, #7]
    // 0x9565f8: DecompressPointer r2
    //     0x9565f8: add             x2, x2, HEAP, lsl #32
    // 0x9565fc: mov             x0, x4
    // 0x956600: r1 = Null
    //     0x956600: mov             x1, NULL
    // 0x956604: cmp             w0, NULL
    // 0x956608: b.eq            #0x95662c
    // 0x95660c: cmp             w2, NULL
    // 0x956610: b.eq            #0x95662c
    // 0x956614: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x956614: ldur            w4, [x2, #0x17]
    // 0x956618: DecompressPointer r4
    //     0x956618: add             x4, x4, HEAP, lsl #32
    // 0x95661c: r8 = X0?
    //     0x95661c: ldr             x8, [PP, #0xf0]  ; [pp+0xf0] TypeParameter: X0?
    // 0x956620: LoadField: r9 = r4->field_7
    //     0x956620: ldur            x9, [x4, #7]
    // 0x956624: r3 = Null
    //     0x956624: ldr             x3, [PP, #0x7788]  ; [pp+0x7788] Null
    // 0x956628: blr             x9
    // 0x95662c: ldur            x0, [fp, #-0x10]
    // 0x956630: ldur            x3, [fp, #-0x20]
    // 0x956634: StoreField: r3->field_b = r0
    //     0x956634: stur            w0, [x3, #0xb]
    //     0x956638: tbz             w0, #0, #0x956654
    //     0x95663c: ldurb           w16, [x3, #-1]
    //     0x956640: ldurb           w17, [x0, #-1]
    //     0x956644: and             x16, x17, x16, lsr #2
    //     0x956648: tst             x16, HEAP, lsr #32
    //     0x95664c: b.eq            #0x956654
    //     0x956650: bl              #0x94e188  ; WriteBarrierWrappersStub
    // 0x956654: b               #0x956664
    // 0x956658: ldur            x3, [fp, #-0x20]
    // 0x95665c: b               #0x956664
    // 0x956660: ldur            x3, [fp, #-0x20]
    // 0x956664: r1 = Null
    //     0x956664: mov             x1, NULL
    // 0x956668: r2 = 4
    //     0x956668: movz            x2, #0x4
    // 0x95666c: r0 = AllocateArray()
    //     0x95666c: bl              #0x94fa24  ; AllocateArrayStub
    // 0x956670: r17 = "response: "
    //     0x956670: ldr             x17, [PP, #0x7798]  ; [pp+0x7798] "response: "
    // 0x956674: StoreField: r0->field_f = r17
    //     0x956674: stur            w17, [x0, #0xf]
    // 0x956678: ldur            x2, [fp, #-0x20]
    // 0x95667c: LoadField: r1 = r2->field_b
    //     0x95667c: ldur            w1, [x2, #0xb]
    // 0x956680: DecompressPointer r1
    //     0x956680: add             x1, x1, HEAP, lsl #32
    // 0x956684: StoreField: r0->field_13 = r1
    //     0x956684: stur            w1, [x0, #0x13]
    // 0x956688: str             x0, [SP]
    // 0x95668c: r0 = _interpolate()
    //     0x95668c: bl              #0x2dd54c  ; [dart:core] _StringBase::_interpolate
    // 0x956690: r0 = InitLateStaticField(0xe78) // [sQi] fA::_ahf
    //     0x956690: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x956694: ldr             x0, [x0, #0x1cf0]
    //     0x956698: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x95669c: cmp             w0, w16
    //     0x9566a0: b.ne            #0x9566ac
    //     0x9566a4: ldr             x2, [PP, #0x6f08]  ; [pp+0x6f08] Field <fA._ahf@549461673>: static late (offset: 0xe78)
    //     0x9566a8: bl              #0x94dc14  ; InitLateStaticFieldStub
    // 0x9566ac: ldur            x2, [fp, #-0x20]
    // 0x9566b0: LoadField: r0 = r2->field_b
    //     0x9566b0: ldur            w0, [x2, #0xb]
    // 0x9566b4: DecompressPointer r0
    //     0x9566b4: add             x0, x0, HEAP, lsl #32
    // 0x9566b8: r16 = "msg"
    //     0x9566b8: ldr             x16, [PP, #0x77a0]  ; [pp+0x77a0] "msg"
    // 0x9566bc: stp             x16, x0, [SP]
    // 0x9566c0: r4 = 0
    //     0x9566c0: movz            x4, #0
    // 0x9566c4: ldr             x0, [SP, #8]
    // 0x9566c8: r16 = UnlinkedCall_0x2d3c80
    //     0x9566c8: add             x16, PP, #7, lsl #12  ; [pp+0x77a8] UnlinkedCall: 0x2d3c80 - SwitchableCallMissStub
    //     0x9566cc: add             x16, x16, #0x7a8
    // 0x9566d0: ldp             x5, lr, [x16]
    // 0x9566d4: blr             lr
    // 0x9566d8: r1 = 59
    //     0x9566d8: movz            x1, #0x3b
    // 0x9566dc: branchIfSmi(r0, 0x9566e8)
    //     0x9566dc: tbz             w0, #0, #0x9566e8
    // 0x9566e0: r1 = LoadClassIdInstr(r0)
    //     0x9566e0: ldur            x1, [x0, #-1]
    //     0x9566e4: ubfx            x1, x1, #0xc, #0x14
    // 0x9566e8: r16 = "token无效"
    //     0x9566e8: ldr             x16, [PP, #0x77b8]  ; [pp+0x77b8] "token无效"
    // 0x9566ec: stp             x16, x0, [SP]
    // 0x9566f0: mov             x0, x1
    // 0x9566f4: mov             lr, x0
    // 0x9566f8: ldr             lr, [x21, lr, lsl #3]
    // 0x9566fc: blr             lr
    // 0x956700: tbnz            w0, #4, #0x956730
    // 0x956704: r1 = Function '<anonymous closure>':.
    //     0x956704: ldr             x1, [PP, #0x77c0]  ; [pp+0x77c0] AnonymousClosure: (0x8b959c), in [lQi] Sz::<anonymous closure> (0x956468)
    // 0x956708: r2 = Null
    //     0x956708: mov             x2, NULL
    // 0x95670c: r0 = AllocateClosure()
    //     0x95670c: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x956710: r1 = Function '<anonymous closure>':.
    //     0x956710: ldr             x1, [PP, #0x77c8]  ; [pp+0x77c8] AnonymousClosure: (0x956884), in [lQi] Sz::<anonymous closure> (0x956468)
    // 0x956714: r2 = Null
    //     0x956714: mov             x2, NULL
    // 0x956718: stur            x0, [fp, #-0x10]
    // 0x95671c: r0 = AllocateClosure()
    //     0x95671c: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x956720: str             x0, [SP]
    // 0x956724: ldur            x1, [fp, #-0x10]
    // 0x956728: r4 = const [0, 0x2, 0x1, 0x1, WTc, 0x1, null]
    //     0x956728: ldr             x4, [PP, #0x77d0]  ; [pp+0x77d0] List(7) [0, 0x2, 0x1, 0x1, "WTc", 0x1, Null]
    // 0x95672c: r0 = call 0x438c0c
    //     0x95672c: bl              #0x438c0c
    // 0x956730: ldur            x1, [fp, #-0x18]
    // 0x956734: ldur            x2, [fp, #-0x20]
    // 0x956738: r0 = call 0x8b92f0
    //     0x956738: bl              #0x8b92f0
    // 0x95673c: r0 = ReturnAsync()
    //     0x95673c: b               #0x8c3a30  ; ReturnAsyncStub
    // 0x956740: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x956740: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x956744: b               #0x9564b4
    // 0x956748: r9 = lIf
    //     0x956748: ldr             x9, [PP, #0x77d8]  ; [pp+0x77d8] Field <jE.lIf>: late (offset: 0x14)
    // 0x95674c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95674c: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x956884, size: 0xcc
    // 0x956884: EnterFrame
    //     0x956884: stp             fp, lr, [SP, #-0x10]!
    //     0x956888: mov             fp, SP
    // 0x95688c: AllocStack(0x38)
    //     0x95688c: sub             SP, SP, #0x38
    // 0x956890: SetupParameters(Sz this /* r1 */)
    //     0x956890: stur            NULL, [fp, #-8]
    //     0x956894: movz            x0, #0
    //     0x956898: add             x1, fp, w0, sxtw #2
    //     0x95689c: ldr             x1, [x1, #0x10]
    //     0x9568a0: ldur            w2, [x1, #0x17]
    //     0x9568a4: add             x2, x2, HEAP, lsl #32
    //     0x9568a8: stur            x2, [fp, #-0x10]
    // 0x9568ac: CheckStackOverflow
    //     0x9568ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9568b0: cmp             SP, x16
    //     0x9568b4: b.ls            #0x956940
    // 0x9568b8: InitAsync() -> Future<void?>
    //     0x9568b8: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x9568bc: bl              #0x8c1de0  ; InitAsyncStub
    // 0x9568c0: r0 = InitLateStaticField(0xcc8) // [aQi] Bz::qkc
    //     0x9568c0: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9568c4: ldr             x0, [x0, #0x1990]
    //     0x9568c8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9568cc: cmp             w0, w16
    //     0x9568d0: b.ne            #0x9568dc
    //     0x9568d4: ldr             x2, [PP, #0x6b28]  ; [pp+0x6b28] Field <Bz.qkc>: static late final (offset: 0xcc8)
    //     0x9568d8: bl              #0x94dbac  ; InitLateFinalStaticFieldStub
    // 0x9568dc: mov             x1, x0
    // 0x9568e0: stur            x0, [fp, #-0x18]
    // 0x9568e4: r0 = __unknown_function__()
    //     0x9568e4: bl              #0x8f91f4  ; [] ::__unknown_function__
    // 0x9568e8: mov             x1, x0
    // 0x9568ec: stur            x1, [fp, #-0x20]
    // 0x9568f0: r0 = Await()
    //     0x9568f0: bl              #0x8c1bb8  ; AwaitStub
    // 0x9568f4: ldur            x0, [fp, #-0x18]
    // 0x9568f8: LoadField: r1 = r0->field_2b
    //     0x9568f8: ldur            w1, [x0, #0x2b]
    // 0x9568fc: DecompressPointer r1
    //     0x9568fc: add             x1, x1, HEAP, lsl #32
    // 0x956900: r16 = Sentinel
    //     0x956900: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x956904: cmp             w1, w16
    // 0x956908: b.eq            #0x956948
    // 0x95690c: r0 = __unknown_function__()
    //     0x95690c: bl              #0x8f6f0c  ; [] ::__unknown_function__
    // 0x956910: r1 = Function '<anonymous closure>':.
    //     0x956910: ldr             x1, [PP, #0x77e0]  ; [pp+0x77e0] AnonymousClosure: (0x8b9360), in [lQi] Sz::<anonymous closure> (0x956468)
    // 0x956914: r2 = Null
    //     0x956914: mov             x2, NULL
    // 0x956918: stur            x0, [fp, #-0x10]
    // 0x95691c: r0 = AllocateClosure()
    //     0x95691c: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x956920: r16 = <Null?>
    //     0x956920: ldr             x16, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    // 0x956924: ldur            lr, [fp, #-0x10]
    // 0x956928: stp             lr, x16, [SP, #8]
    // 0x95692c: str             x0, [SP]
    // 0x956930: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x956930: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x956934: r0 = call 0x7e60e0
    //     0x956934: bl              #0x7e60e0
    // 0x956938: r0 = Null
    //     0x956938: mov             x0, NULL
    // 0x95693c: r0 = ReturnAsyncNotFuture()
    //     0x95693c: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x956940: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x956940: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x956944: b               #0x9568b8
    // 0x956948: r9 = xSb
    //     0x956948: ldr             x9, [PP, #0x77e8]  ; [pp+0x77e8] Field <Bz.xSb>: late (offset: 0x2c)
    // 0x95694c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x95694c: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic, hE, ND) async {
    // ** addr: 0x956950, size: 0x1dc
    // 0x956950: EnterFrame
    //     0x956950: stp             fp, lr, [SP, #-0x10]!
    //     0x956954: mov             fp, SP
    // 0x956958: AllocStack(0x48)
    //     0x956958: sub             SP, SP, #0x48
    // 0x95695c: SetupParameters(Sz this /* r1 */, dynamic _ /* r2, fp-0x20 */, dynamic _ /* r3, fp-0x18 */)
    //     0x95695c: stur            NULL, [fp, #-8]
    //     0x956960: movz            x0, #0
    //     0x956964: add             x1, fp, w0, sxtw #2
    //     0x956968: ldr             x1, [x1, #0x20]
    //     0x95696c: add             x2, fp, w0, sxtw #2
    //     0x956970: ldr             x2, [x2, #0x18]
    //     0x956974: stur            x2, [fp, #-0x20]
    //     0x956978: add             x3, fp, w0, sxtw #2
    //     0x95697c: ldr             x3, [x3, #0x10]
    //     0x956980: stur            x3, [fp, #-0x18]
    //     0x956984: ldur            w4, [x1, #0x17]
    //     0x956988: add             x4, x4, HEAP, lsl #32
    //     0x95698c: stur            x4, [fp, #-0x10]
    // 0x956990: CheckStackOverflow
    //     0x956990: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x956994: cmp             SP, x16
    //     0x956998: b.ls            #0x956b24
    // 0x95699c: InitAsync() -> Future<void?>
    //     0x95699c: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x9569a0: bl              #0x8c1de0  ; InitAsyncStub
    // 0x9569a4: ldr             x16, [THR, #0x88]  ; THR::empty_array
    // 0x9569a8: stp             x16, NULL, [SP]
    // 0x9569ac: r0 = Map._fromLiteral()
    //     0x9569ac: bl              #0x2dc7b0  ; [dart:core] Map::Map._fromLiteral
    // 0x9569b0: stur            x0, [fp, #-0x28]
    // 0x9569b4: r0 = InitLateStaticField(0xcc8) // [aQi] Bz::qkc
    //     0x9569b4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9569b8: ldr             x0, [x0, #0x1990]
    //     0x9569bc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9569c0: cmp             w0, w16
    //     0x9569c4: b.ne            #0x9569d0
    //     0x9569c8: ldr             x2, [PP, #0x6b28]  ; [pp+0x6b28] Field <Bz.qkc>: static late final (offset: 0xcc8)
    //     0x9569cc: bl              #0x94dbac  ; InitLateFinalStaticFieldStub
    // 0x9569d0: mov             x1, x0
    // 0x9569d4: stur            x0, [fp, #-0x30]
    // 0x9569d8: r0 = __unknown_function__()
    //     0x9569d8: bl              #0x930fe4  ; [] ::__unknown_function__
    // 0x9569dc: mov             x1, x0
    // 0x9569e0: stur            x1, [fp, #-0x38]
    // 0x9569e4: r0 = Await()
    //     0x9569e4: bl              #0x8c1bb8  ; AwaitStub
    // 0x9569e8: ldur            x1, [fp, #-0x30]
    // 0x9569ec: stur            x0, [fp, #-0x30]
    // 0x9569f0: LoadField: r0 = r1->field_13
    //     0x9569f0: ldur            w0, [x1, #0x13]
    // 0x9569f4: DecompressPointer r0
    //     0x9569f4: add             x0, x0, HEAP, lsl #32
    // 0x9569f8: r16 = Sentinel
    //     0x9569f8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9569fc: cmp             w0, w16
    // 0x956a00: b.ne            #0x956a10
    // 0x956a04: r2 = Fcf
    //     0x956a04: add             x2, PP, #0xd, lsl #12  ; [pp+0xd2a0] Field <Bz.Fcf>: late (offset: 0x14)
    //     0x956a08: ldr             x2, [x2, #0x2a0]
    // 0x956a0c: r0 = InitLateInstanceField()
    //     0x956a0c: bl              #0x94db08  ; InitLateInstanceFieldStub
    // 0x956a10: ldur            x1, [fp, #-0x28]
    // 0x956a14: mov             x2, x0
    // 0x956a18: r0 = call 0x826dd8
    //     0x956a18: bl              #0x826dd8
    // 0x956a1c: r1 = Null
    //     0x956a1c: mov             x1, NULL
    // 0x956a20: r2 = 4
    //     0x956a20: movz            x2, #0x4
    // 0x956a24: r0 = AllocateArray()
    //     0x956a24: bl              #0x94fa24  ; AllocateArrayStub
    // 0x956a28: r17 = "token"
    //     0x956a28: ldr             x17, [PP, #0x7f78]  ; [pp+0x7f78] "token"
    // 0x956a2c: StoreField: r0->field_f = r17
    //     0x956a2c: stur            w17, [x0, #0xf]
    // 0x956a30: ldur            x1, [fp, #-0x30]
    // 0x956a34: StoreField: r0->field_13 = r1
    //     0x956a34: stur            w1, [x0, #0x13]
    // 0x956a38: stp             x0, NULL, [SP]
    // 0x956a3c: r0 = Map._fromLiteral()
    //     0x956a3c: bl              #0x2dc7b0  ; [dart:core] Map::Map._fromLiteral
    // 0x956a40: ldur            x1, [fp, #-0x28]
    // 0x956a44: mov             x2, x0
    // 0x956a48: r0 = call 0x826dd8
    //     0x956a48: bl              #0x826dd8
    // 0x956a4c: ldur            x3, [fp, #-0x20]
    // 0x956a50: LoadField: r4 = r3->field_4b
    //     0x956a50: ldur            w4, [x3, #0x4b]
    // 0x956a54: DecompressPointer r4
    //     0x956a54: add             x4, x4, HEAP, lsl #32
    // 0x956a58: stur            x4, [fp, #-0x30]
    // 0x956a5c: cmp             w4, NULL
    // 0x956a60: b.eq            #0x956a8c
    // 0x956a64: mov             x0, x4
    // 0x956a68: r2 = Null
    //     0x956a68: mov             x2, NULL
    // 0x956a6c: r1 = Null
    //     0x956a6c: mov             x1, NULL
    // 0x956a70: r8 = Map
    //     0x956a70: ldr             x8, [PP, #0xcb8]  ; [pp+0xcb8] Type: Map
    // 0x956a74: r3 = Null
    //     0x956a74: add             x3, PP, #0xd, lsl #12  ; [pp+0xd2a8] Null
    //     0x956a78: ldr             x3, [x3, #0x2a8]
    // 0x956a7c: r0 = Map()
    //     0x956a7c: bl              #0x95a8fc  ; IsType_Map_Stub
    // 0x956a80: ldur            x1, [fp, #-0x28]
    // 0x956a84: ldur            x2, [fp, #-0x30]
    // 0x956a88: r0 = call 0x826dd8
    //     0x956a88: bl              #0x826dd8
    // 0x956a8c: ldur            x0, [fp, #-0x20]
    // 0x956a90: ldur            x3, [fp, #-0x28]
    // 0x956a94: r1 = Null
    //     0x956a94: mov             x1, NULL
    // 0x956a98: r2 = 4
    //     0x956a98: movz            x2, #0x4
    // 0x956a9c: r0 = AllocateArray()
    //     0x956a9c: bl              #0x94fa24  ; AllocateArrayStub
    // 0x956aa0: r17 = "params: "
    //     0x956aa0: add             x17, PP, #0xd, lsl #12  ; [pp+0xd2b8] "params: "
    //     0x956aa4: ldr             x17, [x17, #0x2b8]
    // 0x956aa8: StoreField: r0->field_f = r17
    //     0x956aa8: stur            w17, [x0, #0xf]
    // 0x956aac: ldur            x1, [fp, #-0x28]
    // 0x956ab0: StoreField: r0->field_13 = r1
    //     0x956ab0: stur            w1, [x0, #0x13]
    // 0x956ab4: str             x0, [SP]
    // 0x956ab8: r0 = _interpolate()
    //     0x956ab8: bl              #0x2dd54c  ; [dart:core] _StringBase::_interpolate
    // 0x956abc: r0 = InitLateStaticField(0xe78) // [sQi] fA::_ahf
    //     0x956abc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x956ac0: ldr             x0, [x0, #0x1cf0]
    //     0x956ac4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x956ac8: cmp             w0, w16
    //     0x956acc: b.ne            #0x956ad8
    //     0x956ad0: ldr             x2, [PP, #0x6f08]  ; [pp+0x6f08] Field <fA._ahf@549461673>: static late (offset: 0xe78)
    //     0x956ad4: bl              #0x94dc14  ; InitLateStaticFieldStub
    // 0x956ad8: ldur            x1, [fp, #-0x28]
    // 0x956adc: r0 = call 0x487c0c
    //     0x956adc: bl              #0x487c0c
    // 0x956ae0: mov             x1, x0
    // 0x956ae4: r0 = __unknown_function__()
    //     0x956ae4: bl              #0x956b2c  ; [] ::__unknown_function__
    // 0x956ae8: mov             x1, x0
    // 0x956aec: stur            x1, [fp, #-0x28]
    // 0x956af0: r0 = Await()
    //     0x956af0: bl              #0x8c1bb8  ; AwaitStub
    // 0x956af4: ldur            x2, [fp, #-0x20]
    // 0x956af8: StoreField: r2->field_4b = r0
    //     0x956af8: stur            w0, [x2, #0x4b]
    //     0x956afc: tbz             w0, #0, #0x956b18
    //     0x956b00: ldurb           w16, [x2, #-1]
    //     0x956b04: ldurb           w17, [x0, #-1]
    //     0x956b08: and             x16, x17, x16, lsr #2
    //     0x956b0c: tst             x16, HEAP, lsr #32
    //     0x956b10: b.eq            #0x956b18
    //     0x956b14: bl              #0x94e168  ; WriteBarrierWrappersStub
    // 0x956b18: ldur            x1, [fp, #-0x18]
    // 0x956b1c: r0 = call 0x8b9698
    //     0x956b1c: bl              #0x8b9698
    // 0x956b20: r0 = ReturnAsync()
    //     0x956b20: b               #0x8c3a30  ; ReturnAsyncStub
    // 0x956b24: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x956b24: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x956b28: b               #0x95699c
  }
  [closure] bool <anonymous closure>(dynamic, int?) {
    // ** addr: 0x42a10c, size: -0x1
  }
  [closure] jla <anonymous closure>(dynamic, ((dynamic) => void)?) {
    // ** addr: 0x8b959c, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Zy<cz>?) {
    // ** addr: 0x8b9360, size: -0x1
  }
}
