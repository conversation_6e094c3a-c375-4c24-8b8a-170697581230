// lib: , url: DQi

// class id: 1048740, size: 0x8
class :: {
}

// class id: 4463, size: 0x18, field offset: 0x8
abstract class uA<X0, X1, X2> extends Object
    implements Map<X0, X1> {

  int length(uA<X0, X1, X2>) {
    // ** addr: 0x8fe10c, size: 0x5c
    // 0x8fe10c: EnterFrame
    //     0x8fe10c: stp             fp, lr, [SP, #-0x10]!
    //     0x8fe110: mov             fp, SP
    // 0x8fe114: ldr             x1, [fp, #0x10]
    // 0x8fe118: LoadField: r2 = r1->field_13
    //     0x8fe118: ldur            w2, [x1, #0x13]
    // 0x8fe11c: DecompressPointer r2
    //     0x8fe11c: add             x2, x2, HEAP, lsl #32
    // 0x8fe120: LoadField: r1 = r2->field_13
    //     0x8fe120: ldur            w1, [x2, #0x13]
    // 0x8fe124: DecompressPointer r1
    //     0x8fe124: add             x1, x1, HEAP, lsl #32
    // 0x8fe128: r3 = LoadInt32Instr(r1)
    //     0x8fe128: sbfx            x3, x1, #1, #0x1f
    // 0x8fe12c: asr             x1, x3, #1
    // 0x8fe130: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x8fe130: ldur            w3, [x2, #0x17]
    // 0x8fe134: DecompressPointer r3
    //     0x8fe134: add             x3, x3, HEAP, lsl #32
    // 0x8fe138: r2 = LoadInt32Instr(r3)
    //     0x8fe138: sbfx            x2, x3, #1, #0x1f
    // 0x8fe13c: sub             x3, x1, x2
    // 0x8fe140: lsl             x0, x3, #1
    // 0x8fe144: LeaveFrame
    //     0x8fe144: mov             SP, fp
    //     0x8fe148: ldp             fp, lr, [SP], #0x10
    // 0x8fe14c: ret
    //     0x8fe14c: ret             
  }
  void Ij(uA<X0, X1, X2>, (dynamic, X1, X2) => void) {
    // ** addr: 0x8cbc38, size: 0x78
    // 0x8cbc38: EnterFrame
    //     0x8cbc38: stp             fp, lr, [SP, #-0x10]!
    //     0x8cbc3c: mov             fp, SP
    // 0x8cbc40: CheckStackOverflow
    //     0x8cbc40: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cbc44: cmp             SP, x16
    //     0x8cbc48: b.ls            #0x8cbc90
    // 0x8cbc4c: ldr             x3, [fp, #0x18]
    // 0x8cbc50: LoadField: r2 = r3->field_7
    //     0x8cbc50: ldur            w2, [x3, #7]
    // 0x8cbc54: DecompressPointer r2
    //     0x8cbc54: add             x2, x2, HEAP, lsl #32
    // 0x8cbc58: ldr             x0, [fp, #0x10]
    // 0x8cbc5c: r1 = Null
    //     0x8cbc5c: mov             x1, NULL
    // 0x8cbc60: r8 = (dynamic this, X1, X2) => void?
    //     0x8cbc60: add             x8, PP, #0x21, lsl #12  ; [pp+0x21c88] FunctionType: (dynamic this, X1, X2) => void?
    //     0x8cbc64: ldr             x8, [x8, #0xc88]
    // 0x8cbc68: LoadField: r9 = r8->field_7
    //     0x8cbc68: ldur            x9, [x8, #7]
    // 0x8cbc6c: r3 = Null
    //     0x8cbc6c: add             x3, PP, #0x21, lsl #12  ; [pp+0x21c90] Null
    //     0x8cbc70: ldr             x3, [x3, #0xc90]
    // 0x8cbc74: blr             x9
    // 0x8cbc78: ldr             x1, [fp, #0x18]
    // 0x8cbc7c: ldr             x2, [fp, #0x10]
    // 0x8cbc80: r0 = call 0x78f880
    //     0x8cbc80: bl              #0x78f880
    // 0x8cbc84: LeaveFrame
    //     0x8cbc84: mov             SP, fp
    //     0x8cbc88: ldp             fp, lr, [SP], #0x10
    // 0x8cbc8c: ret
    //     0x8cbc8c: ret             
    // 0x8cbc90: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cbc90: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cbc94: b               #0x8cbc4c
  }
  Map<Y0, Y1> ok<Y0, Y1>(uA<X0, X1, X2>, (dynamic, X1, X2) => Ra<Y0, Y1>) {
    // ** addr: 0x8cbc98, size: 0xa0
    // 0x8cbc98: EnterFrame
    //     0x8cbc98: stp             fp, lr, [SP, #-0x10]!
    //     0x8cbc9c: mov             fp, SP
    // 0x8cbca0: AllocStack(0x20)
    //     0x8cbca0: sub             SP, SP, #0x20
    // 0x8cbca4: SetupParameters()
    //     0x8cbca4: ldur            w0, [x4, #0xf]
    //     0x8cbca8: add             x0, x0, HEAP, lsl #32
    //     0x8cbcac: cbnz            w0, #0x8cbcb8
    //     0x8cbcb0: mov             x4, NULL
    //     0x8cbcb4: b               #0x8cbccc
    //     0x8cbcb8: ldur            w0, [x4, #0x17]
    //     0x8cbcbc: add             x0, x0, HEAP, lsl #32
    //     0x8cbcc0: add             x1, fp, w0, sxtw #2
    //     0x8cbcc4: ldr             x1, [x1, #0x10]
    //     0x8cbcc8: mov             x4, x1
    //     0x8cbccc: ldr             x3, [fp, #0x18]
    //     0x8cbcd0: stur            x4, [fp, #-8]
    // 0x8cbcd4: CheckStackOverflow
    //     0x8cbcd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cbcd8: cmp             SP, x16
    //     0x8cbcdc: b.ls            #0x8cbd30
    // 0x8cbce0: LoadField: r2 = r3->field_7
    //     0x8cbce0: ldur            w2, [x3, #7]
    // 0x8cbce4: DecompressPointer r2
    //     0x8cbce4: add             x2, x2, HEAP, lsl #32
    // 0x8cbce8: ldr             x0, [fp, #0x10]
    // 0x8cbcec: mov             x1, x4
    // 0x8cbcf0: r8 = (dynamic this, X1, X2) => Ra<Y0, Y1>
    //     0x8cbcf0: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ee90] FunctionType: (dynamic this, X1, X2) => Ra<Y0, Y1>
    //     0x8cbcf4: ldr             x8, [x8, #0xe90]
    // 0x8cbcf8: LoadField: r9 = r8->field_7
    //     0x8cbcf8: ldur            x9, [x8, #7]
    // 0x8cbcfc: r3 = Null
    //     0x8cbcfc: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ee98] Null
    //     0x8cbd00: ldr             x3, [x3, #0xe98]
    // 0x8cbd04: blr             x9
    // 0x8cbd08: ldur            x16, [fp, #-8]
    // 0x8cbd0c: ldr             lr, [fp, #0x18]
    // 0x8cbd10: stp             lr, x16, [SP, #8]
    // 0x8cbd14: ldr             x16, [fp, #0x10]
    // 0x8cbd18: str             x16, [SP]
    // 0x8cbd1c: r4 = const [0x2, 0x2, 0x2, 0x2, null]
    //     0x8cbd1c: ldr             x4, [PP, #0x1d80]  ; [pp+0x1d80] List(5) [0x2, 0x2, 0x2, 0x2, Null]
    // 0x8cbd20: r0 = call 0x3232cc
    //     0x8cbd20: bl              #0x3232cc
    // 0x8cbd24: LeaveFrame
    //     0x8cbd24: mov             SP, fp
    //     0x8cbd28: ldp             fp, lr, [SP], #0x10
    // 0x8cbd2c: ret
    //     0x8cbd2c: ret             
    // 0x8cbd30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cbd30: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cbd34: b               #0x8cbce0
  }
  bool dyn:get:dk(uA<X0, X1, X2>) {
    // ** addr: 0x8cbd50, size: 0x68
    // 0x8cbd50: EnterFrame
    //     0x8cbd50: stp             fp, lr, [SP, #-0x10]!
    //     0x8cbd54: mov             fp, SP
    // 0x8cbd58: ldr             x1, [fp, #0x10]
    // 0x8cbd5c: LoadField: r2 = r1->field_13
    //     0x8cbd5c: ldur            w2, [x1, #0x13]
    // 0x8cbd60: DecompressPointer r2
    //     0x8cbd60: add             x2, x2, HEAP, lsl #32
    // 0x8cbd64: LoadField: r1 = r2->field_13
    //     0x8cbd64: ldur            w1, [x2, #0x13]
    // 0x8cbd68: DecompressPointer r1
    //     0x8cbd68: add             x1, x1, HEAP, lsl #32
    // 0x8cbd6c: r3 = LoadInt32Instr(r1)
    //     0x8cbd6c: sbfx            x3, x1, #1, #0x1f
    // 0x8cbd70: asr             x1, x3, #1
    // 0x8cbd74: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x8cbd74: ldur            w3, [x2, #0x17]
    // 0x8cbd78: DecompressPointer r3
    //     0x8cbd78: add             x3, x3, HEAP, lsl #32
    // 0x8cbd7c: r2 = LoadInt32Instr(r3)
    //     0x8cbd7c: sbfx            x2, x3, #1, #0x1f
    // 0x8cbd80: sub             x3, x1, x2
    // 0x8cbd84: cbnz            x3, #0x8cbd90
    // 0x8cbd88: r0 = false
    //     0x8cbd88: add             x0, NULL, #0x30  ; false
    // 0x8cbd8c: b               #0x8cbd94
    // 0x8cbd90: r0 = true
    //     0x8cbd90: add             x0, NULL, #0x20  ; true
    // 0x8cbd94: LeaveFrame
    //     0x8cbd94: mov             SP, fp
    //     0x8cbd98: ldp             fp, lr, [SP], #0x10
    // 0x8cbd9c: ret
    //     0x8cbd9c: ret             
  }
  X2? [](uA<X0, X1, X2>, Object?) {
    // ** addr: 0x8cbdb8, size: 0x4c
    // 0x8cbdb8: EnterFrame
    //     0x8cbdb8: stp             fp, lr, [SP, #-0x10]!
    //     0x8cbdbc: mov             fp, SP
    // 0x8cbdc0: CheckStackOverflow
    //     0x8cbdc0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cbdc4: cmp             SP, x16
    //     0x8cbdc8: b.ls            #0x8cbde4
    // 0x8cbdcc: ldr             x1, [fp, #0x18]
    // 0x8cbdd0: ldr             x2, [fp, #0x10]
    // 0x8cbdd4: r0 = call 0x7a4544
    //     0x8cbdd4: bl              #0x7a4544
    // 0x8cbdd8: LeaveFrame
    //     0x8cbdd8: mov             SP, fp
    //     0x8cbddc: ldp             fp, lr, [SP], #0x10
    // 0x8cbde0: ret
    //     0x8cbde0: ret             
    // 0x8cbde4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cbde4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cbde8: b               #0x8cbdcc
  }
  void []=(uA<X0, X1, X2>, X1, X2) {
    // ** addr: 0x8cbe04, size: 0x54
    // 0x8cbe04: EnterFrame
    //     0x8cbe04: stp             fp, lr, [SP, #-0x10]!
    //     0x8cbe08: mov             fp, SP
    // 0x8cbe0c: CheckStackOverflow
    //     0x8cbe0c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cbe10: cmp             SP, x16
    //     0x8cbe14: b.ls            #0x8cbe38
    // 0x8cbe18: ldr             x1, [fp, #0x20]
    // 0x8cbe1c: ldr             x2, [fp, #0x18]
    // 0x8cbe20: ldr             x3, [fp, #0x10]
    // 0x8cbe24: r0 = call 0x7ad5f8
    //     0x8cbe24: bl              #0x7ad5f8
    // 0x8cbe28: r0 = Null
    //     0x8cbe28: mov             x0, NULL
    // 0x8cbe2c: LeaveFrame
    //     0x8cbe2c: mov             SP, fp
    //     0x8cbe30: ldp             fp, lr, [SP], #0x10
    // 0x8cbe34: ret
    //     0x8cbe34: ret             
    // 0x8cbe38: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cbe38: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cbe3c: b               #0x8cbe18
  }
  bool dyn:get:isEmpty(uA<X0, X1, X2>) {
    // ** addr: 0x8cbe58, size: 0x68
    // 0x8cbe58: EnterFrame
    //     0x8cbe58: stp             fp, lr, [SP, #-0x10]!
    //     0x8cbe5c: mov             fp, SP
    // 0x8cbe60: ldr             x1, [fp, #0x10]
    // 0x8cbe64: LoadField: r2 = r1->field_13
    //     0x8cbe64: ldur            w2, [x1, #0x13]
    // 0x8cbe68: DecompressPointer r2
    //     0x8cbe68: add             x2, x2, HEAP, lsl #32
    // 0x8cbe6c: LoadField: r1 = r2->field_13
    //     0x8cbe6c: ldur            w1, [x2, #0x13]
    // 0x8cbe70: DecompressPointer r1
    //     0x8cbe70: add             x1, x1, HEAP, lsl #32
    // 0x8cbe74: r3 = LoadInt32Instr(r1)
    //     0x8cbe74: sbfx            x3, x1, #1, #0x1f
    // 0x8cbe78: asr             x1, x3, #1
    // 0x8cbe7c: ArrayLoad: r3 = r2[0]  ; List_4
    //     0x8cbe7c: ldur            w3, [x2, #0x17]
    // 0x8cbe80: DecompressPointer r3
    //     0x8cbe80: add             x3, x3, HEAP, lsl #32
    // 0x8cbe84: r2 = LoadInt32Instr(r3)
    //     0x8cbe84: sbfx            x2, x3, #1, #0x1f
    // 0x8cbe88: sub             x3, x1, x2
    // 0x8cbe8c: cbz             x3, #0x8cbe98
    // 0x8cbe90: r0 = false
    //     0x8cbe90: add             x0, NULL, #0x30  ; false
    // 0x8cbe94: b               #0x8cbe9c
    // 0x8cbe98: r0 = true
    //     0x8cbe98: add             x0, NULL, #0x20  ; true
    // 0x8cbe9c: LeaveFrame
    //     0x8cbe9c: mov             SP, fp
    //     0x8cbea0: ldp             fp, lr, [SP], #0x10
    // 0x8cbea4: ret
    //     0x8cbea4: ret             
  }
  [closure] void <anonymous closure>(dynamic, X1, X2) {
    // ** addr: 0x7e1754, size: -0x1
  }
  [closure] Ra<X1, X2> <anonymous closure>(dynamic) {
    // ** addr: 0x7453c0, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, X0, Ra<X1, X2>) {
    // ** addr: 0x32338c, size: -0x1
  }
  [closure] Ra<X1, X2> <anonymous closure>(dynamic, Ra<X0, Ra<X1, X2>>) {
    // ** addr: 0x746004, size: -0x1
  }
  [closure] Ra<X0, Ra<X1, X2>> <anonymous closure>(dynamic, Ra<X1, X2>) {
    // ** addr: 0x3234fc, size: -0x1
  }
}
