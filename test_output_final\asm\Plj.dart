// lib: , url: Plj

// class id: 1049806, size: 0x8
class :: {
}

// class id: 3278, size: 0x1c, field offset: 0x1c
abstract class xZa<X0 bound It> extends sZa<X0 bound It> {

  [closure] void Sif(dynamic, pK) {
    // ** addr: 0x51d8e0, size: -0x1
  }
  [closure] void IEc(dynamic, qK) {
    // ** addr: 0x51d850, size: -0x1
  }
  [closure] void JEc(dynamic, lK) {
    // ** addr: 0x51bc10, size: -0x1
  }
  [closure] void Tif(dynamic) {
    // ** addr: 0x51b9a8, size: -0x1
  }
}
