// lib: , url: Baj

// class id: 1049229, size: 0x8
class :: {

  static late final Set<Oga> _cge; // offset: 0xa38
  static late final Set<Oga> _dge; // offset: 0xa3c
  static late final Set<Oga> _ege; // offset: 0xa40
  static late final Set<Oga> _fge; // offset: 0xa44
}

// class id: 1648, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class nva extends Object {
}

// class id: 1691, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class isa extends Object {
}

// class id: 2754, size: 0x24, field offset: 0x8
//   transformed mixin,
abstract class _jva extends _DF
     with Pu {
}

// class id: 2755, size: 0x30, field offset: 0x24
class kva extends _jva {

  [closure] static void <anonymous closure>(dynamic, isa, eja) {
    // ** addr: 0x5fad7c, size: -0x1
  }
  [closure] static List<_iva> <anonymous closure>(dynamic) {
    // ** addr: 0x5faebc, size: -0x1
  }
}

// class id: 2756, size: 0x10, field offset: 0x8
//   const constructor, 
class _iva extends _DF {
}

// class id: 2757, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _gva extends _DF
     with hsa {
}

// class id: 2758, size: 0x24, field offset: 0x8
//   const constructor, 
class hva extends _gva
    implements isa {

  Oga field_8;
  bool field_c;
  bool field_10;
  bool field_14;
  bool field_18;
  eva field_1c;
  bool field_20;
}

// class id: 3104, size: 0x1c, field offset: 0x14
class _qva extends Mt<dynamic> {

  [closure] void _Zfe(dynamic) {
    // ** addr: 0x644b34, size: -0x1
  }
}

// class id: 3105, size: 0x18, field offset: 0x14
class _mva extends Mt<dynamic> {

  [closure] Zma _Xfe(dynamic, ena, Rga) {
    // ** addr: 0x5f99f4, size: -0x1
  }
}

// class id: 3499, size: 0x14, field offset: 0x10
//   const constructor, 
class _rva extends VG {
}

// class id: 3888, size: 0x10, field offset: 0xc
//   const constructor, 
class pva extends It {
}

// class id: 3889, size: 0x18, field offset: 0xc
//   const constructor, 
class lva extends It {
}

// class id: 4597, size: 0x28, field offset: 0x24
class ova extends _Ou {
}

// class id: 5451, size: 0x14, field offset: 0x14
enum eva extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
