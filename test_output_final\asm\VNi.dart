// lib: , url: VNi

// class id: 1048610, size: 0x8
class :: {
}

// class id: 4605, size: 0x8, field offset: 0x8
abstract class yu extends Object {

  static late final nu yZe; // offset: 0xca8
  static late final Map<String, List<(dynamic) => void>> xZe; // offset: 0xca4

  [closure] static Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x8e808c, size: 0xd4
    // 0x8e808c: EnterFrame
    //     0x8e808c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e8090: mov             fp, SP
    // 0x8e8094: AllocStack(0x20)
    //     0x8e8094: sub             SP, SP, #0x20
    // 0x8e8098: SetupParameters(dynamic _ /* r1 */)
    //     0x8e8098: stur            NULL, [fp, #-8]
    //     0x8e809c: movz            x0, #0
    //     0x8e80a0: add             x1, fp, w0, sxtw #2
    //     0x8e80a4: ldr             x1, [x1, #0x10]
    //     0x8e80a8: ldur            w2, [x1, #0x17]
    //     0x8e80ac: add             x2, x2, HEAP, lsl #32
    //     0x8e80b0: stur            x2, [fp, #-0x10]
    // 0x8e80b4: CheckStackOverflow
    //     0x8e80b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e80b8: cmp             SP, x16
    //     0x8e80bc: b.ls            #0x8e8158
    // 0x8e80c0: InitAsync() -> Future<void?>
    //     0x8e80c0: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x8e80c4: bl              #0x8c1de0  ; InitAsyncStub
    // 0x8e80c8: ldur            x1, [fp, #-0x10]
    // 0x8e80cc: LoadField: r0 = r1->field_1b
    //     0x8e80cc: ldur            w0, [x1, #0x1b]
    // 0x8e80d0: DecompressPointer r0
    //     0x8e80d0: add             x0, x0, HEAP, lsl #32
    // 0x8e80d4: cmp             w0, NULL
    // 0x8e80d8: b.ne            #0x8e80e4
    // 0x8e80dc: r2 = Null
    //     0x8e80dc: mov             x2, NULL
    // 0x8e80e0: b               #0x8e80fc
    // 0x8e80e4: str             x0, [SP]
    // 0x8e80e8: ClosureCall
    //     0x8e80e8: ldr             x4, [PP, #0x490]  ; [pp+0x490] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x8e80ec: ldur            x2, [x0, #0x1f]
    //     0x8e80f0: blr             x2
    // 0x8e80f4: mov             x2, x0
    // 0x8e80f8: ldur            x1, [fp, #-0x10]
    // 0x8e80fc: mov             x0, x2
    // 0x8e8100: stur            x2, [fp, #-0x18]
    // 0x8e8104: r0 = Await()
    //     0x8e8104: bl              #0x8c1bb8  ; AwaitStub
    // 0x8e8108: ldur            x0, [fp, #-0x10]
    // 0x8e810c: LoadField: r1 = r0->field_33
    //     0x8e810c: ldur            w1, [x0, #0x33]
    // 0x8e8110: DecompressPointer r1
    //     0x8e8110: add             x1, x1, HEAP, lsl #32
    // 0x8e8114: r16 = Sentinel
    //     0x8e8114: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8e8118: cmp             w1, w16
    // 0x8e811c: b.ne            #0x8e8130
    // 0x8e8120: r16 = "bZe"
    //     0x8e8120: add             x16, PP, #0xa, lsl #12  ; [pp+0xad68] "bZe"
    //     0x8e8124: ldr             x16, [x16, #0xd68]
    // 0x8e8128: str             x16, [SP]
    // 0x8e812c: r0 = _throwLocalNotInitialized()
    //     0x8e812c: bl              #0x301b2c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x8e8130: ldur            x0, [fp, #-0x10]
    // 0x8e8134: LoadField: r1 = r0->field_33
    //     0x8e8134: ldur            w1, [x0, #0x33]
    // 0x8e8138: DecompressPointer r1
    //     0x8e8138: add             x1, x1, HEAP, lsl #32
    // 0x8e813c: str             x1, [SP]
    // 0x8e8140: mov             x0, x1
    // 0x8e8144: ClosureCall
    //     0x8e8144: ldr             x4, [PP, #0x490]  ; [pp+0x490] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x8e8148: ldur            x2, [x0, #0x1f]
    //     0x8e814c: blr             x2
    // 0x8e8150: r0 = Null
    //     0x8e8150: mov             x0, NULL
    // 0x8e8154: r0 = ReturnAsyncNotFuture()
    //     0x8e8154: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x8e8158: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e8158: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e815c: b               #0x8e80c0
  }
  [closure] static Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x8e816c, size: 0x80
    // 0x8e816c: EnterFrame
    //     0x8e816c: stp             fp, lr, [SP, #-0x10]!
    //     0x8e8170: mov             fp, SP
    // 0x8e8174: AllocStack(0x18)
    //     0x8e8174: sub             SP, SP, #0x18
    // 0x8e8178: SetupParameters(dynamic _ /* r1 */)
    //     0x8e8178: stur            NULL, [fp, #-8]
    //     0x8e817c: movz            x0, #0
    //     0x8e8180: add             x1, fp, w0, sxtw #2
    //     0x8e8184: ldr             x1, [x1, #0x10]
    //     0x8e8188: ldur            w2, [x1, #0x17]
    //     0x8e818c: add             x2, x2, HEAP, lsl #32
    //     0x8e8190: stur            x2, [fp, #-0x10]
    // 0x8e8194: CheckStackOverflow
    //     0x8e8194: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e8198: cmp             SP, x16
    //     0x8e819c: b.ls            #0x8e81e4
    // 0x8e81a0: InitAsync() -> Future<void?>
    //     0x8e81a0: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x8e81a4: bl              #0x8c1de0  ; InitAsyncStub
    // 0x8e81a8: ldur            x0, [fp, #-0x10]
    // 0x8e81ac: LoadField: r1 = r0->field_1b
    //     0x8e81ac: ldur            w1, [x0, #0x1b]
    // 0x8e81b0: DecompressPointer r1
    //     0x8e81b0: add             x1, x1, HEAP, lsl #32
    // 0x8e81b4: cmp             w1, NULL
    // 0x8e81b8: b.ne            #0x8e81c4
    // 0x8e81bc: r1 = Null
    //     0x8e81bc: mov             x1, NULL
    // 0x8e81c0: b               #0x8e81d0
    // 0x8e81c4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x8e81c4: ldr             x4, [PP, #0x5a8]  ; [pp+0x5a8] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x8e81c8: r0 = call 0x4336f8
    //     0x8e81c8: bl              #0x4336f8
    // 0x8e81cc: mov             x1, x0
    // 0x8e81d0: mov             x0, x1
    // 0x8e81d4: stur            x1, [fp, #-0x18]
    // 0x8e81d8: r0 = Await()
    //     0x8e81d8: bl              #0x8c1bb8  ; AwaitStub
    // 0x8e81dc: r0 = Null
    //     0x8e81dc: mov             x0, NULL
    // 0x8e81e0: r0 = ReturnAsyncNotFuture()
    //     0x8e81e0: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x8e81e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e81e4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e81e8: b               #0x8e81a0
  }
  [closure] static Ku <anonymous closure>(dynamic, (dynamic) => void, pI) {
    // ** addr: 0x433560, size: -0x1
  }
  [closure] static pI <anonymous closure>(dynamic, (dynamic) => void) {
    // ** addr: 0x43349c, size: -0x1
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0x433698, size: -0x1
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0x433640, size: -0x1
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0x433230, size: -0x1
  }
  [closure] static bool <anonymous closure>(dynamic) {
    // ** addr: 0x432f8c, size: -0x1
  }
  [closure] static Nt <anonymous closure>(dynamic, (dynamic) => void) {
    // ** addr: 0x42c9e0, size: -0x1
  }
  [closure] static pI <anonymous closure>(dynamic, aoa) {
    // ** addr: 0x42cc18, size: -0x1
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0x42cabc, size: -0x1
  }
  [closure] static void <anonymous closure>(dynamic, aL) {
    // ** addr: 0x432f3c, size: -0x1
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0x42c640, size: -0x1
  }
  [closure] static pI <anonymous closure>(dynamic, yF, (dynamic) => void, pI) {
    // ** addr: 0x4387dc, size: -0x1
  }
  [closure] static Ju <anonymous closure>(dynamic, (dynamic) => void) {
    // ** addr: 0x4da3f4, size: -0x1
  }
  [closure] static pI <anonymous closure>(dynamic, yF, (dynamic) => void, pI) {
    // ** addr: 0x4339cc, size: -0x1
  }
}
