// lib: , url: Pfj

// class id: 1049491, size: 0x8
class :: {

  static late final Int32List _oJg; // offset: 0x1364
  static late final Uint32List _pJg; // offset: 0x1368
  static late final Uint8List _hJg; // offset: 0x1348
  static late final Int8List _iJg; // offset: 0x134c
  static late final Uint32List _lJg; // offset: 0x1358
  static late final Int32List _mJg; // offset: 0x135c
  static late final Uint16List _jJg; // offset: 0x1350
  static late final Int16List _kJg; // offset: 0x1354
  static late final Uint64List _qJg; // offset: 0x136c
  static late final Float64List _rJg; // offset: 0x1370
  static late final Float32List _nJg; // offset: 0x1360
}
