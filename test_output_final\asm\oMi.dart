// lib: Sqj, url: oMi

// class id: 1049674, size: 0x8
class :: {
}

// class id: 759, size: 0xc, field offset: 0xc
class kVa extends OTa {

  static late final vWa iog; // offset: 0x1278

  [closure] static kVa <anonymous closure>(dynamic) {
    // ** addr: 0x4700a4, size: -0x1
  }
  [closure] static kVa _Psg(dynamic, String, LTa, KTa, za, za, List<int>?) {
    // ** addr: 0x4701a4, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x47004c, size: -0x1
  }
}
