// lib: , url: ROi

// class id: 1048653, size: 0x8
class :: {
}

// class id: 3390, size: 0x4c, field offset: 0x30
class _Jw extends Wu<dynamic> {

  dynamic dyn:get:data(_Jw) {
    // ** addr: 0x8f0a60, size: 0x28
    // 0x8f0a60: ldr             x1, [SP]
    // 0x8f0a64: LoadField: r0 = r1->field_2f
    //     0x8f0a64: ldur            w0, [x1, #0x2f]
    // 0x8f0a68: DecompressPointer r0
    //     0x8f0a68: add             x0, x0, HEAP, lsl #32
    // 0x8f0a6c: ret
    //     0x8f0a6c: ret             
  }
  [closure] Null <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x6bb060, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x6bb458, size: -0x1
  }
  [closure] qoa <anonymous closure>(dynamic, Map<String, dynamic>) {
    // ** addr: 0x4acd10, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x4ac4a4, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x4acc34, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x4ad0d4, size: -0x1
  }
  [closure] fla <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x4ab0b8, size: -0x1
  }
  [closure] qoa <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x4abd44, size: -0x1
  }
  [closure] qoa <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x4ab6b0, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x4aba78, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x4ac2b0, size: -0x1
  }
}

// class id: 4122, size: 0x10, field offset: 0x10
class Iw extends Tu {
}
