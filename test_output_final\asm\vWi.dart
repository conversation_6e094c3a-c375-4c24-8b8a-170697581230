// lib: , url: VWi

// class id: 1049035, size: 0x8
class :: {
}

// class id: 2264, size: 0xc, field offset: 0x8
class M<PERSON> extends Object {
}

// class id: 2265, size: 0x14, field offset: 0x8
//   const constructor, 
class J<PERSON> extends Object {
}

// class id: 2266, size: 0x18, field offset: 0x8
class IZ extends Object {
}

// class id: 2837, size: 0x10, field offset: 0x8
class LZ extends _DF {
}

// class id: 2838, size: 0x8, field offset: 0x8
//   const constructor, 
class KZ extends _DF {
}

// class id: 2839, size: 0x34, field offset: 0x8
abstract class FZ extends _DF {

  [closure] void zvc(dynamic, JZ) {
    // ** addr: 0x53200c, size: -0x1
  }
  [closure] void ZPe(dynamic, KZ) {
    // ** addr: 0x79b710, size: -0x1
  }
}

// class id: 2840, size: 0x64, field offset: 0x34
class NZ extends FZ {

  late Ea _qQe; // offset: 0x4c

  [closure] void zvc(dynamic, JZ) {
    // ** addr: 0x532048, size: -0x1
  }
  [closure] void _xQe(dynamic, Ea) {
    // ** addr: 0x79babc, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x79bfc4, size: -0x1
  }
  [closure] void _wQe(dynamic, Qe) {
    // ** addr: 0x79b994, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Object, ua) {
    // ** addr: 0x79b910, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Object, ua) {
    // ** addr: 0x79b68c, size: -0x1
  }
}
