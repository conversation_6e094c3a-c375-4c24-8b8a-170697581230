// lib: Rqj, url: vKi

// class id: 1049673, size: 0x8
class :: {
}

// class id: 760, size: 0xc, field offset: 0xc
class iVa extends OTa {

  static late final vWa iog; // offset: 0x1274

  [closure] static iVa <anonymous closure>(dynamic) {
    // ** addr: 0x47021c, size: -0x1
  }
  [closure] static iVa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x470334, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x4701c4, size: -0x1
  }
}
