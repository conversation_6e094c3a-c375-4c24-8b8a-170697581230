// lib: , url: Doj

// class id: 1049911, size: 0x8
class :: {
}

// class id: 175, size: 0x8, field offset: 0x8
//   const constructor, 
class ifb extends Object {
}

// class id: 5363, size: 0x14, field offset: 0x14
enum khb extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5365, size: 0x14, field offset: 0x14
enum Vjb extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5366, size: 0x14, field offset: 0x14
enum Ujb extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
