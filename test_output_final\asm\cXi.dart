// lib: , url: CXi

// class id: 1049073, size: 0x8
class :: {
}

// class id: 2109, size: 0x6c, field offset: 0x5c
class Rda extends _ada {

  [closure] double CBc(dynamic, double) {
    // ** addr: 0x3495c8, size: -0x1
  }
  [closure] double BBc(dynamic, double) {
    // ** addr: 0x34f3fc, size: -0x1
  }
  [closure] double ABc(dynamic, double) {
    // ** addr: 0x352cb4, size: -0x1
  }
  [closure] double zBc(dynamic, double) {
    // ** addr: 0x3554f4, size: -0x1
  }
  [closure] void _Gve(dynamic, sca, er) {
    // ** addr: 0x388704, size: -0x1
  }
}
