// lib: , url: QOi

// class id: 1048652, size: 0x8
class :: {
}

// class id: 3318, size: 0x18, field offset: 0x14
class _Hw extends Mt<dynamic> {

  [closure] qoa <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x58c57c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x58ca70, size: -0x1
  }
}

// class id: 4059, size: 0xc, field offset: 0xc
class Gw extends It {
}
