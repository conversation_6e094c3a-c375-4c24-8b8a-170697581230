// lib: , url: Ebj

// class id: 1049287, size: 0x8
class :: {
}

// class id: 1246, size: 0x8, field offset: 0x8
abstract class mGa extends Object {

  [closure] static double pn(dynamic, num, kGa) {
    // ** addr: 0x605428, size: -0x1
  }
}

// class id: 1247, size: 0x8, field offset: 0x8
abstract class lGa extends Object {

  [closure] static bool Uk(dynamic, Lpa, Lpa) {
    // ** addr: 0x6790e8, size: -0x1
  }
}

// class id: 3087, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class _oGa extends Mt<dynamic>
     with Ft {
}

// class id: 3088, size: 0x28, field offset: 0x14
class _pGa extends _oGa {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x65e870, size: -0x1
  }
  [closure] void _Vgf(dynamic, nI) {
    // ** addr: 0x662080, size: -0x1
  }
}

// class id: 3877, size: 0x38, field offset: 0xc
//   const constructor, 
class nGa extends It {
}
