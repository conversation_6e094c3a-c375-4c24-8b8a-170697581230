// lib: , url: TZi

// class id: 1049198, size: 0x8
class :: {
}

// class id: 1689, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class jsa extends Object {
}

// class id: 1690, size: 0x10, field offset: 0x8
class ksa extends jsa {

  [closure] Future<void> _kae(dynamic, MethodCall) {
    // ** addr: 0x6120e4, size: -0x1
  }
}

// class id: 1692, size: 0x8, field offset: 0x8
abstract class hsa extends Object
    implements isa {
}

// class id: 2759, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class lsa extends _DF {
}
