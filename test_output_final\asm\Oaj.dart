// lib: Fqj, url: oaj

// class id: 1049661, size: 0x8
class :: {
}

// class id: 772, size: 0xc, field offset: 0xc
class KUa extends OTa {

  static late final vWa iog; // offset: 0x1244

  [closure] static KUa <anonymous closure>(dynamic) {
    // ** addr: 0x471494, size: -0x1
  }
  [closure] static KUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x4715ac, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x47143c, size: -0x1
  }
}
