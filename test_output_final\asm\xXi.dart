// lib: , url: XXi

// class id: 1049100, size: 0x8
class :: {
}

// class id: 1906, size: 0x10, field offset: 0x8
//   const constructor, 
class vga extends Object {
}

// class id: 1907, size: 0x10, field offset: 0x8
class _uga extends Object
    implements tga {

  [closure] vga <anonymous closure>(dynamic, Map<Object?, Object?>) {
    // ** addr: 0x8086c0, size: -0x1
  }
  [closure] static _uga _uga.ZCe(dynamic, ByteData) {
    // ** addr: 0x807c1c, size: -0x1
  }
}

// class id: 1908, size: 0x8, field offset: 0x8
abstract class tga extends Object {
}
