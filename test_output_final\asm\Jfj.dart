// lib: yqj, url: jfj

// class id: 1049654, size: 0x8
class :: {
}

// class id: 779, size: 0xc, field offset: 0xc
class wUa extends OTa {

  static late final vWa iog; // offset: 0x1228

  [closure] static wUa <anonymous closure>(dynamic) {
    // ** addr: 0x471f70, size: -0x1
  }
  [closure] static wUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>?) {
    // ** addr: 0x472074, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x471f18, size: -0x1
  }
}
