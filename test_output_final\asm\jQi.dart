// lib: , url: JQi

// class id: 1048746, size: 0x8
class :: {
}

// class id: 4451, size: 0x24, field offset: 0x8
class HA<X0> extends Object
    implements GA<X0> {

  bool dyn:get:dk(HA<X0>) {
    // ** addr: 0x919f28, size: 0x34
    // 0x919f28: ldr             x1, [SP]
    // 0x919f2c: LoadField: r2 = r1->field_13
    //     0x919f2c: ldur            x2, [x1, #0x13]
    // 0x919f30: cbnz            x2, #0x919f3c
    // 0x919f34: r0 = false
    //     0x919f34: add             x0, NULL, #0x30  ; false
    // 0x919f38: b               #0x919f40
    // 0x919f3c: r0 = true
    //     0x919f3c: add             x0, NULL, #0x20  ; true
    // 0x919f40: ret
    //     0x919f40: ret             
  }
  X0 dyn:get:Kj(HA<X0>) {
    // ** addr: 0x919f5c, size: 0x48
    // 0x919f5c: EnterFrame
    //     0x919f5c: stp             fp, lr, [SP, #-0x10]!
    //     0x919f60: mov             fp, SP
    // 0x919f64: CheckStackOverflow
    //     0x919f64: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x919f68: cmp             SP, x16
    //     0x919f6c: b.ls            #0x919f84
    // 0x919f70: ldr             x1, [fp, #0x10]
    // 0x919f74: r0 = call 0x5354dc
    //     0x919f74: bl              #0x5354dc
    // 0x919f78: LeaveFrame
    //     0x919f78: mov             SP, fp
    //     0x919f7c: ldp             fp, lr, [SP], #0x10
    // 0x919f80: ret
    //     0x919f80: ret             
    // 0x919f84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x919f84: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x919f88: b               #0x919f70
  }
}

// class id: 4452, size: 0xc, field offset: 0x8
abstract class GA<X0> extends Object {
}
