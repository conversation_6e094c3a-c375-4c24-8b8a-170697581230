// lib: , url: UOi

// class id: 1048656, size: 0x8
class :: {
}

// class id: 3387, size: 0x34, field offset: 0x30
class _Pw extends Wu<dynamic> {

  [closure] fla <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x4b96f8, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x4ba2d0, size: -0x1
  }
}

// class id: 4119, size: 0x10, field offset: 0x10
class Ow extends Tu {
}
