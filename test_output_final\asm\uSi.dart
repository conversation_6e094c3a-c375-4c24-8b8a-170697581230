// lib: , url: USi

// class id: 1049095, size: 0x8
class :: {

  static late final int _zNe; // offset: 0xbe0

  [closure] static List<Xfa> <anonymous closure>(dynamic, _bga) {
    // ** addr: 0x620f78, size: -0x1
  }
}

// class id: 1920, size: 0xa0, field offset: 0x8
class ega extends Object {

  [closure] void hNe(dynamic, Eca) {
    // ** addr: 0x81cb24, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, xs, (dynamic, Object?) => void) {
    // ** addr: 0x81c478, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, Object?) {
    // ** addr: 0x390fd0, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, Object?) {
    // ** addr: 0x391208, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, Object?) {
    // ** addr: 0x391c4c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, Object?) {
    // ** addr: 0x391e84, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, Object?) {
    // ** addr: 0x3924f0, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, Object?) {
    // ** addr: 0x3926b8, size: -0x1
  }
}

// class id: 1921, size: 0x18, field offset: 0x8
class _cga extends Object
    implements Ca<X0> {

  int rn(_cga, _cga) {
    // ** addr: 0x8cb55c, size: 0x64
    // 0x8cb55c: EnterFrame
    //     0x8cb55c: stp             fp, lr, [SP, #-0x10]!
    //     0x8cb560: mov             fp, SP
    // 0x8cb564: CheckStackOverflow
    //     0x8cb564: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cb568: cmp             SP, x16
    //     0x8cb56c: b.ls            #0x8cb5a0
    // 0x8cb570: ldr             x1, [fp, #0x18]
    // 0x8cb574: ldr             x2, [fp, #0x10]
    // 0x8cb578: r0 = call 0x322efc
    //     0x8cb578: bl              #0x322efc
    // 0x8cb57c: mov             x2, x0
    // 0x8cb580: r0 = BoxInt64Instr(r2)
    //     0x8cb580: sbfiz           x0, x2, #1, #0x1f
    //     0x8cb584: cmp             x2, x0, asr #1
    //     0x8cb588: b.eq            #0x8cb594
    //     0x8cb58c: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8cb590: stur            x2, [x0, #7]
    // 0x8cb594: LeaveFrame
    //     0x8cb594: mov             SP, fp
    //     0x8cb598: ldp             fp, lr, [SP], #0x10
    // 0x8cb59c: ret
    //     0x8cb59c: ret             
    // 0x8cb5a0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cb5a0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cb5a4: b               #0x8cb570
  }
}

// class id: 1922, size: 0x18, field offset: 0x8
class _bga extends Object
    implements Ca<X0> {

  int rn(_bga, _bga) {
    // ** addr: 0x8cb4d8, size: 0x64
    // 0x8cb4d8: EnterFrame
    //     0x8cb4d8: stp             fp, lr, [SP, #-0x10]!
    //     0x8cb4dc: mov             fp, SP
    // 0x8cb4e0: CheckStackOverflow
    //     0x8cb4e0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cb4e4: cmp             SP, x16
    //     0x8cb4e8: b.ls            #0x8cb51c
    // 0x8cb4ec: ldr             x1, [fp, #0x18]
    // 0x8cb4f0: ldr             x2, [fp, #0x10]
    // 0x8cb4f4: r0 = call 0x322db0
    //     0x8cb4f4: bl              #0x322db0
    // 0x8cb4f8: mov             x2, x0
    // 0x8cb4fc: r0 = BoxInt64Instr(r2)
    //     0x8cb4fc: sbfiz           x0, x2, #1, #0x1f
    //     0x8cb500: cmp             x2, x0, asr #1
    //     0x8cb504: b.eq            #0x8cb510
    //     0x8cb508: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8cb50c: stur            x2, [x0, #7]
    // 0x8cb510: LeaveFrame
    //     0x8cb510: mov             SP, fp
    //     0x8cb514: ldp             fp, lr, [SP], #0x10
    // 0x8cb518: ret
    //     0x8cb518: ret             
    // 0x8cb51c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cb51c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cb520: b               #0x8cb4ec
  }
  [closure] List<Xfa> <anonymous closure>(dynamic, _bga) {
    // ** addr: 0x621604, size: -0x1
  }
  [closure] int <anonymous closure>(dynamic, Xfa, Xfa) {
    // ** addr: 0x621f44, size: -0x1
  }
  [closure] void snh(dynamic, int) {
    // ** addr: 0x621da4, size: -0x1
  }
  [closure] Xfa <anonymous closure>(dynamic, int) {
    // ** addr: 0x621d30, size: -0x1
  }
}

// class id: 1923, size: 0x18, field offset: 0x8
class _aga extends Object
    implements Ca<X0> {

  int rn(_aga, _aga) {
    // ** addr: 0x8cb454, size: 0x64
    // 0x8cb454: EnterFrame
    //     0x8cb454: stp             fp, lr, [SP, #-0x10]!
    //     0x8cb458: mov             fp, SP
    // 0x8cb45c: CheckStackOverflow
    //     0x8cb45c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cb460: cmp             SP, x16
    //     0x8cb464: b.ls            #0x8cb498
    // 0x8cb468: ldr             x1, [fp, #0x18]
    // 0x8cb46c: ldr             x2, [fp, #0x10]
    // 0x8cb470: r0 = call 0x322c64
    //     0x8cb470: bl              #0x322c64
    // 0x8cb474: mov             x2, x0
    // 0x8cb478: r0 = BoxInt64Instr(r2)
    //     0x8cb478: sbfiz           x0, x2, #1, #0x1f
    //     0x8cb47c: cmp             x2, x0, asr #1
    //     0x8cb480: b.eq            #0x8cb48c
    //     0x8cb484: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8cb488: stur            x2, [x0, #7]
    // 0x8cb48c: LeaveFrame
    //     0x8cb48c: mov             SP, fp
    //     0x8cb490: ldp             fp, lr, [SP], #0x10
    // 0x8cb494: ret
    //     0x8cb494: ret             
    // 0x8cb498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cb498: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cb49c: b               #0x8cb468
  }
}

// class id: 1924, size: 0x10, field offset: 0x8
class Tfa extends Object {

  Tfa +(Tfa, Tfa) {
    // ** addr: 0x8ce730, size: 0x84
    // 0x8ce730: EnterFrame
    //     0x8ce730: stp             fp, lr, [SP, #-0x10]!
    //     0x8ce734: mov             fp, SP
    // 0x8ce738: CheckStackOverflow
    //     0x8ce738: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ce73c: cmp             SP, x16
    //     0x8ce740: b.ls            #0x8ce794
    // 0x8ce744: ldr             x0, [fp, #0x10]
    // 0x8ce748: r2 = Null
    //     0x8ce748: mov             x2, NULL
    // 0x8ce74c: r1 = Null
    //     0x8ce74c: mov             x1, NULL
    // 0x8ce750: r4 = 59
    //     0x8ce750: movz            x4, #0x3b
    // 0x8ce754: branchIfSmi(r0, 0x8ce760)
    //     0x8ce754: tbz             w0, #0, #0x8ce760
    // 0x8ce758: r4 = LoadClassIdInstr(r0)
    //     0x8ce758: ldur            x4, [x0, #-1]
    //     0x8ce75c: ubfx            x4, x4, #0xc, #0x14
    // 0x8ce760: cmp             x4, #0x784
    // 0x8ce764: b.eq            #0x8ce77c
    // 0x8ce768: r8 = Tfa
    //     0x8ce768: add             x8, PP, #0xe, lsl #12  ; [pp+0xeab0] Type: Tfa
    //     0x8ce76c: ldr             x8, [x8, #0xab0]
    // 0x8ce770: r3 = Null
    //     0x8ce770: add             x3, PP, #0x13, lsl #12  ; [pp+0x13e58] Null
    //     0x8ce774: ldr             x3, [x3, #0xe58]
    // 0x8ce778: r0 = Tfa()
    //     0x8ce778: bl              #0x8ce7c4  ; IsType_Tfa_Stub
    // 0x8ce77c: ldr             x1, [fp, #0x18]
    // 0x8ce780: ldr             x2, [fp, #0x10]
    // 0x8ce784: r0 = call 0x32ef6c
    //     0x8ce784: bl              #0x32ef6c
    // 0x8ce788: LeaveFrame
    //     0x8ce788: mov             SP, fp
    //     0x8ce78c: ldp             fp, lr, [SP], #0x10
    // 0x8ce790: ret
    //     0x8ce790: ret             
    // 0x8ce794: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ce794: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ce798: b               #0x8ce744
  }
}

// class id: 1925, size: 0x14, field offset: 0x8
//   const constructor, 
class Sfa extends Object {

  static late final Map<int, Sfa> _DGd; // offset: 0xbc4
  static late final Map<Sfa, int> _tLe; // offset: 0xbc8
}

// class id: 1926, size: 0x10, field offset: 0x8
class Rfa extends Object {
}

// class id: 1927, size: 0x10, field offset: 0x8
class Qfa extends Object {
}

// class id: 1964, size: 0xc, field offset: 0x8
//   const constructor, 
class Eca extends Object {

  _OneByteString field_8;
}

// class id: 1985, size: 0xd0, field offset: 0x8
class Xfa extends _Qba {

  static late final Int32List _fMe; // offset: 0xbd4
  static late final Float64List _hMe; // offset: 0xbdc
  static late final Int32List _gMe; // offset: 0xbd8
  static late final ega _eMe; // offset: 0xbd0

  [closure] bool <anonymous closure>(dynamic, Xfa) {
    // ** addr: 0x629fa8, size: -0x1
  }
  [closure] void _sMe(dynamic, Xfa) {
    // ** addr: 0x35de80, size: -0x1
  }
  [closure] void _qMe(dynamic, Xfa) {
    // ** addr: 0x35e50c, size: -0x1
  }
}

// class id: 2830, size: 0xc, field offset: 0x8
//   const constructor, 
abstract class gga extends _DF
    implements Ca<X0> {

  int rn(gga, gga) {
    // ** addr: 0x8cb140, size: 0x64
    // 0x8cb140: EnterFrame
    //     0x8cb140: stp             fp, lr, [SP, #-0x10]!
    //     0x8cb144: mov             fp, SP
    // 0x8cb148: CheckStackOverflow
    //     0x8cb148: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cb14c: cmp             SP, x16
    //     0x8cb150: b.ls            #0x8cb184
    // 0x8cb154: ldr             x1, [fp, #0x18]
    // 0x8cb158: ldr             x2, [fp, #0x10]
    // 0x8cb15c: r0 = call 0x31efc8
    //     0x8cb15c: bl              #0x31efc8
    // 0x8cb160: mov             x2, x0
    // 0x8cb164: r0 = BoxInt64Instr(r2)
    //     0x8cb164: sbfiz           x0, x2, #1, #0x1f
    //     0x8cb168: cmp             x2, x0, asr #1
    //     0x8cb16c: b.eq            #0x8cb178
    //     0x8cb170: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8cb174: stur            x2, [x0, #7]
    // 0x8cb178: LeaveFrame
    //     0x8cb178: mov             SP, fp
    //     0x8cb17c: ldp             fp, lr, [SP], #0x10
    // 0x8cb180: ret
    //     0x8cb180: ret             
    // 0x8cb184: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cb184: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cb188: b               #0x8cb154
  }
}

// class id: 2831, size: 0x14, field offset: 0xc
//   const constructor, 
class hga extends gga {

  _Mint field_c;
}

// class id: 2832, size: 0x7c, field offset: 0x8
class Vfa extends _DF {
}

// class id: 3415, size: 0x100, field offset: 0x8
//   const constructor, 
class Zfa extends zJ {
}

// class id: 3416, size: 0x10, field offset: 0x8
//   const constructor, 
class Yfa extends zJ {

  bool dyn:get:dk(Yfa) {
    // ** addr: 0x8e8920, size: 0x3c
    // 0x8e8920: ldr             x1, [SP]
    // 0x8e8924: LoadField: r2 = r1->field_7
    //     0x8e8924: ldur            w2, [x1, #7]
    // 0x8e8928: DecompressPointer r2
    //     0x8e8928: add             x2, x2, HEAP, lsl #32
    // 0x8e892c: cmp             w2, NULL
    // 0x8e8930: b.eq            #0x8e893c
    // 0x8e8934: r0 = true
    //     0x8e8934: add             x0, NULL, #0x20  ; true
    // 0x8e8938: b               #0x8e8940
    // 0x8e893c: r0 = false
    //     0x8e893c: add             x0, NULL, #0x30  ; false
    // 0x8e8940: ret
    //     0x8e8940: ret             
  }
}

// class id: 4533, size: 0x34, field offset: 0x24
class dga extends Pu {

  [closure] bool <anonymous closure>(dynamic, Xfa) {
    // ** addr: 0x62acc8, size: -0x1
  }
  [closure] int <anonymous closure>(dynamic, Xfa, Xfa) {
    // ** addr: 0x62ac8c, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, Xfa) {
    // ** addr: 0x61e150, size: -0x1
  }
}
