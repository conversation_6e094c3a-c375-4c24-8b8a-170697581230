// lib: , url: UNi

// class id: 1048609, size: 0x8
class :: {
}

// class id: 4606, size: 0x38, field offset: 0x8
class xu extends Object {
}

// class id: 4607, size: 0x3c, field offset: 0x8
class wu extends Object {
}

// class id: 4609, size: 0x44, field offset: 0x8
class uu extends Object {
}

// class id: 4610, size: 0x3c, field offset: 0x8
class tu extends Object {
}

// class id: 4611, size: 0x44, field offset: 0x8
class su extends Object {
}

// class id: 4616, size: 0x1c, field offset: 0x8
class nu extends Object {
}
