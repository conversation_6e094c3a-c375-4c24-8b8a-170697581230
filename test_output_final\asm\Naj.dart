// lib: Eqj, url: naj

// class id: 1049660, size: 0x8
class :: {
}

// class id: 773, size: 0xc, field offset: 0xc
class IUa extends OTa {

  static late final vWa iog; // offset: 0x1240

  [closure] static IUa <anonymous closure>(dynamic) {
    // ** addr: 0x471624, size: -0x1
  }
  [closure] static IUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x47173c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x4715cc, size: -0x1
  }
}
