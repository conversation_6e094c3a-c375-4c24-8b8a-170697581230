// lib: , url: TPi

// class id: 1048705, size: 0x8
class :: {
}

// class id: 3351, size: 0x50, field offset: 0x30
class _hz extends Wu<dynamic> {

  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x506bf8, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x506bc0, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x50669c, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Da?) {
    // ** addr: 0x506afc, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x505e5c, size: -0x1
  }
  [closure] Tla <anonymous closure>(dynamic, aoa, int) {
    // ** addr: 0x506194, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x506148, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x505e08, size: -0x1
  }
  [closure] pI <anonymous closure>(dynamic, aoa, int) {
    // ** addr: 0x506558, size: -0x1
  }
}

// class id: 4087, size: 0x10, field offset: 0x10
class gz extends Tu {
}
