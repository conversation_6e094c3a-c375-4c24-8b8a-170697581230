// lib: , url: ZZi

// class id: 1049205, size: 0x8
class :: {
}

// class id: 1677, size: 0xc, field offset: 0x8
//   const constructor, 
abstract class cta<X0> extends Object {
}

// class id: 1679, size: 0x10, field offset: 0x8
abstract class _Xsa<X0> extends Object {
}

// class id: 1680, size: 0x14, field offset: 0x10
abstract class Ysa extends _Xsa<dynamic> {

  late final Jd<bta> _Ivc; // offset: 0x10
}

// class id: 1681, size: 0x14, field offset: 0x14
abstract class bta extends Ysa {
}

// class id: 1682, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class _Zsa extends Ysa
     with Ft {
}

// class id: 1683, size: 0x14, field offset: 0x14
class ata extends _Zsa {
}

// class id: 1685, size: 0x14, field offset: 0x8
//   const constructor, 
class Qsa extends Object {
}

// class id: 3117, size: 0x24, field offset: 0x14
//   transformed mixin,
abstract class _Usa<C1X0> extends Mt<C1X0>
     with sP<X0 bound It> {

  [closure] void _cOc(dynamic, Bqa<Object?>) {
    // ** addr: 0x53cbec, size: -0x1
  }
  [closure] void MHb(dynamic) {
    // ** addr: 0x65e074, size: -0x1
  }
}

// class id: 3118, size: 0x38, field offset: 0x24
class _Vsa<C1X0> extends _Usa<C1X0> {

  [closure] Future<void> <anonymous closure>(dynamic, C1X0) async {
    // ** addr: 0x9012f0, size: 0xf0
    // 0x9012f0: EnterFrame
    //     0x9012f0: stp             fp, lr, [SP, #-0x10]!
    //     0x9012f4: mov             fp, SP
    // 0x9012f8: AllocStack(0x28)
    //     0x9012f8: sub             SP, SP, #0x28
    // 0x9012fc: SetupParameters(_Vsa<C1X0> this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x9012fc: stur            NULL, [fp, #-8]
    //     0x901300: movz            x0, #0
    //     0x901304: add             x1, fp, w0, sxtw #2
    //     0x901308: ldr             x1, [x1, #0x18]
    //     0x90130c: add             x2, fp, w0, sxtw #2
    //     0x901310: ldr             x2, [x2, #0x10]
    //     0x901314: stur            x2, [fp, #-0x18]
    //     0x901318: ldur            w3, [x1, #0x17]
    //     0x90131c: add             x3, x3, HEAP, lsl #32
    //     0x901320: stur            x3, [fp, #-0x10]
    // 0x901324: CheckStackOverflow
    //     0x901324: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x901328: cmp             SP, x16
    //     0x90132c: b.ls            #0x9013d4
    // 0x901330: InitAsync() -> Future<void?>
    //     0x901330: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x901334: bl              #0x8c1de0  ; InitAsyncStub
    // 0x901338: ldur            x1, [fp, #-0x10]
    // 0x90133c: LoadField: r0 = r1->field_f
    //     0x90133c: ldur            w0, [x1, #0xf]
    // 0x901340: DecompressPointer r0
    //     0x901340: add             x0, x0, HEAP, lsl #32
    // 0x901344: LoadField: r2 = r0->field_23
    //     0x901344: ldur            w2, [x0, #0x23]
    // 0x901348: DecompressPointer r2
    //     0x901348: add             x2, x2, HEAP, lsl #32
    // 0x90134c: LoadField: r0 = r1->field_13
    //     0x90134c: ldur            w0, [x1, #0x13]
    // 0x901350: DecompressPointer r0
    //     0x901350: add             x0, x0, HEAP, lsl #32
    // 0x901354: cmp             w2, w0
    // 0x901358: b.eq            #0x901364
    // 0x90135c: r0 = Null
    //     0x90135c: mov             x0, NULL
    // 0x901360: r0 = ReturnAsyncNotFuture()
    //     0x901360: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x901364: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x901364: ldur            w0, [x1, #0x17]
    // 0x901368: DecompressPointer r0
    //     0x901368: add             x0, x0, HEAP, lsl #32
    // 0x90136c: str             x0, [SP]
    // 0x901370: ClosureCall
    //     0x901370: ldr             x4, [PP, #0x490]  ; [pp+0x490] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x901374: ldur            x2, [x0, #0x1f]
    //     0x901378: blr             x2
    // 0x90137c: cmp             w0, NULL
    // 0x901380: b.eq            #0x9013dc
    // 0x901384: ldur            x16, [fp, #-0x18]
    // 0x901388: stp             x16, x0, [SP]
    // 0x90138c: ClosureCall
    //     0x90138c: ldr             x4, [PP, #0x170]  ; [pp+0x170] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x901390: ldur            x2, [x0, #0x1f]
    //     0x901394: blr             x2
    // 0x901398: mov             x1, x0
    // 0x90139c: stur            x1, [fp, #-0x18]
    // 0x9013a0: r0 = Await()
    //     0x9013a0: bl              #0x8c1bb8  ; AwaitStub
    // 0x9013a4: ldur            x0, [fp, #-0x10]
    // 0x9013a8: LoadField: r1 = r0->field_f
    //     0x9013a8: ldur            w1, [x0, #0xf]
    // 0x9013ac: DecompressPointer r1
    //     0x9013ac: add             x1, x1, HEAP, lsl #32
    // 0x9013b0: LoadField: r2 = r1->field_23
    //     0x9013b0: ldur            w2, [x1, #0x23]
    // 0x9013b4: DecompressPointer r2
    //     0x9013b4: add             x2, x2, HEAP, lsl #32
    // 0x9013b8: LoadField: r3 = r0->field_13
    //     0x9013b8: ldur            w3, [x0, #0x13]
    // 0x9013bc: DecompressPointer r3
    //     0x9013bc: add             x3, x3, HEAP, lsl #32
    // 0x9013c0: cmp             w2, w3
    // 0x9013c4: b.ne            #0x9013cc
    // 0x9013c8: r0 = call 0x53c6d0
    //     0x9013c8: bl              #0x53c6d0
    // 0x9013cc: r0 = Null
    //     0x9013cc: mov             x0, NULL
    // 0x9013d0: r0 = ReturnAsyncNotFuture()
    //     0x9013d0: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x9013d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9013d4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9013d8: b               #0x901330
    // 0x9013dc: r0 = NullErrorSharedWithoutFPURegs()
    //     0x9013dc: bl              #0x950284  ; NullErrorSharedWithoutFPURegsStub
  }
  [closure] void _ibe(dynamic, Ea) {
    // ** addr: 0x53bd74, size: -0x1
  }
  [closure] void _rbe(dynamic) {
    // ** addr: 0x53d310, size: -0x1
  }
  [closure] Future<bool> _sbe(dynamic) {
    // ** addr: 0x53cea4, size: -0x1
  }
  [closure] void _ube(dynamic) {
    // ** addr: 0x53ce10, size: -0x1
  }
  [closure] RJ<bool> <anonymous closure>(dynamic, bool) {
    // ** addr: 0x53d27c, size: -0x1
  }
  [closure] (dynamic, C1X0) => Future<void> <anonymous closure>(dynamic) {
    // ** addr: 0x53c730, size: -0x1
  }
  [closure] (dynamic, C1X0) => Future<void> <anonymous closure>(dynamic) {
    // ** addr: 0x65e36c, size: -0x1
  }
  [closure] (dynamic, C1X0) => Future<void> <anonymous closure>(dynamic) {
    // ** addr: 0x65e228, size: -0x1
  }
  [closure] (dynamic, C1X0) => Future<void> <anonymous closure>(dynamic) {
    // ** addr: 0x65e0e4, size: -0x1
  }
}

// class id: 3505, size: 0x24, field offset: 0x10
//   const constructor, 
class _Wsa extends VG {
}

// class id: 3897, size: 0x24, field offset: 0xc
//   const constructor, 
class Ssa<X0> extends It {
}

// class id: 4157, size: 0xc, field offset: 0x8
abstract class dta<X0> extends mF {

  [closure] Future<void> Tae(dynamic, Object?) {
    // ** addr: 0x65e170, size: -0x1
  }
  [closure] Future<void> Uae(dynamic, Object?) {
    // ** addr: 0x65e2b4, size: -0x1
  }
}

// class id: 4161, size: 0xc, field offset: 0xc
abstract class ita<X0> extends dta<X0> {
}

// class id: 4192, size: 0xc, field offset: 0xc
abstract class eta extends nF<dynamic> {
}

// class id: 4193, size: 0xc, field offset: 0xc
//   transformed mixin,
abstract class _fta extends eta
     with Ft {
}

// class id: 4194, size: 0x28, field offset: 0xc
//   transformed mixin,
abstract class _gta extends _fta
     with Pu {
}

// class id: 4571, size: 0x38, field offset: 0x38
class _jta extends vP<dynamic> {
}

// class id: 5458, size: 0x14, field offset: 0x14
enum Tsa extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
