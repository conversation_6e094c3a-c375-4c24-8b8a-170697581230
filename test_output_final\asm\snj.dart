// lib: nrj, url: snj

// class id: 1049852, size: 0x8
class :: {

  [closure] static Stream<Uint8List> <anonymous closure>(dynamic, _Feb) {
    // ** addr: 0x904658, size: 0xcd8
    // 0x904658: EnterFrame
    //     0x904658: stp             fp, lr, [SP, #-0x10]!
    //     0x90465c: mov             fp, SP
    // 0x904660: AllocStack(0x1a0)
    //     0x904660: sub             SP, SP, #0x1a0
    // 0x904664: SetupParameters(dynamic _ /* r2, fp-0xc0 */, dynamic _ /* r3, fp-0xb8 */)
    //     0x904664: stur            NULL, [fp, #-8]
    //     0x904668: movz            x1, #0
    //     0x90466c: add             x2, fp, w1, sxtw #2
    //     0x904670: ldr             x2, [x2, #0x18]
    //     0x904674: stur            x2, [fp, #-0xc0]
    //     0x904678: add             x3, fp, w1, sxtw #2
    //     0x90467c: ldr             x3, [x3, #0x10]
    //     0x904680: stur            x3, [fp, #-0xb8]
    //     0x904684: ldur            w4, [x2, #0x17]
    //     0x904688: add             x4, x4, HEAP, lsl #32
    //     0x90468c: stur            x4, [fp, #-0xb0]
    // 0x904690: CheckStackOverflow
    //     0x904690: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x904694: cmp             SP, x16
    //     0x904698: b.ls            #0x9052fc
    // 0x90469c: r0 = <Uint8List>
    //     0x90469c: ldr             x0, [PP, #0x14b0]  ; [pp+0x14b0] TypeArguments: <Uint8List>
    // 0x9046a0: r0 = InitSyncStar()
    //     0x9046a0: bl              #0x904610  ; InitSyncStarStub
    // 0x9046a4: r0 = Null
    //     0x9046a4: mov             x0, NULL
    // 0x9046a8: r0 = YieldAsyncStar()
    //     0x9046a8: bl              #0x904488  ; YieldAsyncStarStub
    // 0x9046ac: ldur            x0, [fp, #-0xb0]
    // 0x9046b0: LoadField: r1 = r0->field_f
    //     0x9046b0: ldur            w1, [x0, #0xf]
    // 0x9046b4: DecompressPointer r1
    //     0x9046b4: add             x1, x1, HEAP, lsl #32
    // 0x9046b8: LoadField: r2 = r1->field_13
    //     0x9046b8: ldur            w2, [x1, #0x13]
    // 0x9046bc: DecompressPointer r2
    //     0x9046bc: add             x2, x2, HEAP, lsl #32
    // 0x9046c0: cmp             w2, #0x20
    // 0x9046c4: b.ne            #0x9046f8
    // 0x9046c8: r0 = InitLateStaticField(0x145c) // [pnj] ::web
    //     0x9046c8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9046cc: ldr             x0, [x0, #0x28b8]
    //     0x9046d0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9046d4: cmp             w0, w16
    //     0x9046d8: b.ne            #0x9046e8
    //     0x9046dc: add             x2, PP, #0x14, lsl #12  ; [pp+0x14c78] Field <::.web>: static late final (offset: 0x145c)
    //     0x9046e0: ldr             x2, [x2, #0xc78]
    //     0x9046e4: bl              #0x94dbac  ; InitLateFinalStaticFieldStub
    // 0x9046e8: mov             x1, x0
    // 0x9046ec: r0 = call 0x560f28
    //     0x9046ec: bl              #0x560f28
    // 0x9046f0: mov             x3, x0
    // 0x9046f4: b               #0x904724
    // 0x9046f8: r0 = InitLateStaticField(0x145c) // [pnj] ::web
    //     0x9046f8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9046fc: ldr             x0, [x0, #0x28b8]
    //     0x904700: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x904704: cmp             w0, w16
    //     0x904708: b.ne            #0x904718
    //     0x90470c: add             x2, PP, #0x14, lsl #12  ; [pp+0x14c78] Field <::.web>: static late final (offset: 0x145c)
    //     0x904710: ldr             x2, [x2, #0xc78]
    //     0x904714: bl              #0x94dbac  ; InitLateFinalStaticFieldStub
    // 0x904718: mov             x1, x0
    // 0x90471c: r0 = call 0x560df4
    //     0x90471c: bl              #0x560df4
    // 0x904720: mov             x3, x0
    // 0x904724: ldur            x0, [fp, #-0xb0]
    // 0x904728: stur            x3, [fp, #-0xc8]
    // 0x90472c: r4 = LoadStaticField(0x145c)
    //     0x90472c: ldr             x4, [THR, #0x68]  ; THR::field_table_values
    //     0x904730: ldr             x4, [x4, #0x28b8]
    // 0x904734: mov             x1, x4
    // 0x904738: mov             x2, x3
    // 0x90473c: stur            x4, [fp, #-0xc0]
    // 0x904740: r0 = call 0x560bc0
    //     0x904740: bl              #0x560bc0
    // 0x904744: mov             x3, x0
    // 0x904748: ldur            x0, [fp, #-0xb0]
    // 0x90474c: r17 = -368
    //     0x90474c: movn            x17, #0x16f
    // 0x904750: str             x3, [fp, x17]
    // 0x904754: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x904754: ldur            w4, [x0, #0x17]
    // 0x904758: DecompressPointer r4
    //     0x904758: add             x4, x4, HEAP, lsl #32
    // 0x90475c: r17 = -360
    //     0x90475c: movn            x17, #0x167
    // 0x904760: str             x4, [fp, x17]
    // 0x904764: LoadField: r1 = r4->field_b
    //     0x904764: ldur            w1, [x4, #0xb]
    // 0x904768: DecompressPointer r1
    //     0x904768: add             x1, x1, HEAP, lsl #32
    // 0x90476c: r2 = LoadInt32Instr(r1)
    //     0x90476c: sbfx            x2, x1, #1, #0x1f
    // 0x904770: cmp             x2, x3
    // 0x904774: b.ne            #0x9051e0
    // 0x904778: ldur            x1, [fp, #-0xb8]
    // 0x90477c: r0 = call 0x560638
    //     0x90477c: bl              #0x560638
    // 0x904780: stur            x0, [fp, #-0xd0]
    // 0x904784: r0 = InitLateStaticField(0x530) // [dart:ffi] ::zrb
    //     0x904784: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x904788: ldr             x0, [x0, #0xa60]
    //     0x90478c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x904790: cmp             w0, w16
    //     0x904794: b.ne            #0x9047a4
    //     0x904798: add             x2, PP, #0x14, lsl #12  ; [pp+0x14c80] Field <::.zrb>: static late final (offset: 0x530)
    //     0x90479c: ldr             x2, [x2, #0xc80]
    //     0x9047a0: bl              #0x94dbac  ; InitLateFinalStaticFieldStub
    // 0x9047a4: mov             x1, x0
    // 0x9047a8: ldur            x0, [fp, #-0xb0]
    // 0x9047ac: stur            x1, [fp, #-0xd8]
    // 0x9047b0: LoadField: r2 = r0->field_f
    //     0x9047b0: ldur            w2, [x0, #0xf]
    // 0x9047b4: DecompressPointer r2
    //     0x9047b4: add             x2, x2, HEAP, lsl #32
    // 0x9047b8: r16 = <Uint8>
    //     0x9047b8: add             x16, PP, #0x14, lsl #12  ; [pp+0x14c88] TypeArguments: <Uint8>
    //     0x9047bc: ldr             x16, [x16, #0xc88]
    // 0x9047c0: ldur            lr, [fp, #-0xb8]
    // 0x9047c4: stp             lr, x16, [SP, #8]
    // 0x9047c8: str             x2, [SP]
    // 0x9047cc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9047cc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9047d0: r0 = call 0x56024c
    //     0x9047d0: bl              #0x56024c
    // 0x9047d4: mov             x1, x0
    // 0x9047d8: ldur            x0, [fp, #-0xb0]
    // 0x9047dc: stur            x1, [fp, #-0xe0]
    // 0x9047e0: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x9047e0: ldur            w2, [x0, #0x17]
    // 0x9047e4: DecompressPointer r2
    //     0x9047e4: add             x2, x2, HEAP, lsl #32
    // 0x9047e8: r16 = <Uint8>
    //     0x9047e8: add             x16, PP, #0x14, lsl #12  ; [pp+0x14c88] TypeArguments: <Uint8>
    //     0x9047ec: ldr             x16, [x16, #0xc88]
    // 0x9047f0: ldur            lr, [fp, #-0xb8]
    // 0x9047f4: stp             lr, x16, [SP, #8]
    // 0x9047f8: str             x2, [SP]
    // 0x9047fc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9047fc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x904800: r0 = call 0x56024c
    //     0x904800: bl              #0x56024c
    // 0x904804: ldur            x1, [fp, #-0xc0]
    // 0x904808: ldur            x2, [fp, #-0xd0]
    // 0x90480c: ldur            x3, [fp, #-0xc8]
    // 0x904810: ldur            x5, [fp, #-0xd8]
    // 0x904814: ldur            x6, [fp, #-0xe0]
    // 0x904818: mov             x7, x0
    // 0x90481c: r0 = call 0x55ffa4
    //     0x90481c: bl              #0x55ffa4
    // 0x904820: mov             x1, x0
    // 0x904824: r0 = call 0x55ff4c
    //     0x904824: bl              #0x55ff4c
    // 0x904828: r16 = <Uint8>
    //     0x904828: add             x16, PP, #0x14, lsl #12  ; [pp+0x14c88] TypeArguments: <Uint8>
    //     0x90482c: ldr             x16, [x16, #0xc88]
    // 0x904830: ldur            lr, [fp, #-0xb8]
    // 0x904834: stp             lr, x16, [SP, #8]
    // 0x904838: r3 = 4096
    //     0x904838: movz            x3, #0x1000
    // 0x90483c: str             x3, [SP]
    // 0x904840: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x904840: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x904844: r0 = call 0x55fd58
    //     0x904844: bl              #0x55fd58
    // 0x904848: stur            x0, [fp, #-0xc8]
    // 0x90484c: r16 = <Pointer<Uint8>>
    //     0x90484c: add             x16, PP, #0x14, lsl #12  ; [pp+0x14c90] TypeArguments: <Pointer<Uint8>>
    //     0x904850: ldr             x16, [x16, #0xc90]
    // 0x904854: stp             x0, x16, [SP, #8]
    // 0x904858: r16 = "Pointer<Uint8>"
    //     0x904858: add             x16, PP, #0x14, lsl #12  ; [pp+0x14c98] "Pointer<Uint8>"
    //     0x90485c: ldr             x16, [x16, #0xc98]
    // 0x904860: str             x16, [SP]
    // 0x904864: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x904864: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x904868: r0 = call 0x2ed194
    //     0x904868: bl              #0x2ed194
    // 0x90486c: r16 = <int>
    //     0x90486c: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] TypeArguments: <int>
    // 0x904870: r30 = 8192
    //     0x904870: movz            lr, #0x2000
    // 0x904874: stp             lr, x16, [SP, #8]
    // 0x904878: r16 = "length"
    //     0x904878: add             x16, PP, #0x14, lsl #12  ; [pp+0x14ca0] "length"
    //     0x90487c: ldr             x16, [x16, #0xca0]
    // 0x904880: str             x16, [SP]
    // 0x904884: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x904884: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x904888: r0 = call 0x2ed194
    //     0x904888: bl              #0x2ed194
    // 0x90488c: r0 = _ExternalUint8Array()
    //     0x90488c: bl              #0x905554  ; Allocate_ExternalUint8ArrayStub -> _ExternalUint8Array (size=-0x8)
    // 0x904890: mov             x1, x0
    // 0x904894: r0 = 8192
    //     0x904894: movz            x0, #0x2000
    // 0x904898: stur            x1, [fp, #-0xd8]
    // 0x90489c: StoreField: r1->field_13 = r0
    //     0x90489c: stur            w0, [x1, #0x13]
    // 0x9048a0: ldur            x0, [fp, #-0xc8]
    // 0x9048a4: LoadField: r2 = r0->field_7
    //     0x9048a4: ldur            x2, [x0, #7]
    // 0x9048a8: StoreField: r1->field_7 = r2
    //     0x9048a8: stur            x2, [x1, #7]
    // 0x9048ac: r16 = <Uint8>
    //     0x9048ac: add             x16, PP, #0x14, lsl #12  ; [pp+0x14c88] TypeArguments: <Uint8>
    //     0x9048b0: ldr             x16, [x16, #0xc88]
    // 0x9048b4: ldur            lr, [fp, #-0xb8]
    // 0x9048b8: stp             lr, x16, [SP, #8]
    // 0x9048bc: r3 = 4112
    //     0x9048bc: movz            x3, #0x1010
    // 0x9048c0: str             x3, [SP]
    // 0x9048c4: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9048c4: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9048c8: r0 = call 0x55fd58
    //     0x9048c8: bl              #0x55fd58
    // 0x9048cc: stur            x0, [fp, #-0xe0]
    // 0x9048d0: r16 = <Pointer<Uint8>>
    //     0x9048d0: add             x16, PP, #0x14, lsl #12  ; [pp+0x14c90] TypeArguments: <Pointer<Uint8>>
    //     0x9048d4: ldr             x16, [x16, #0xc90]
    // 0x9048d8: stp             x0, x16, [SP, #8]
    // 0x9048dc: r16 = "Pointer<Uint8>"
    //     0x9048dc: add             x16, PP, #0x14, lsl #12  ; [pp+0x14c98] "Pointer<Uint8>"
    //     0x9048e0: ldr             x16, [x16, #0xc98]
    // 0x9048e4: str             x16, [SP]
    // 0x9048e8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x9048e8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x9048ec: r0 = call 0x2ed194
    //     0x9048ec: bl              #0x2ed194
    // 0x9048f0: r16 = <int>
    //     0x9048f0: ldr             x16, [PP, #0x9d8]  ; [pp+0x9d8] TypeArguments: <int>
    // 0x9048f4: r30 = 8224
    //     0x9048f4: movz            lr, #0x2020
    // 0x9048f8: stp             lr, x16, [SP, #8]
    // 0x9048fc: r16 = "length"
    //     0x9048fc: add             x16, PP, #0x14, lsl #12  ; [pp+0x14ca0] "length"
    //     0x904900: ldr             x16, [x16, #0xca0]
    // 0x904904: str             x16, [SP]
    // 0x904908: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x904908: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x90490c: r0 = call 0x2ed194
    //     0x90490c: bl              #0x2ed194
    // 0x904910: r0 = _ExternalUint8Array()
    //     0x904910: bl              #0x905554  ; Allocate_ExternalUint8ArrayStub -> _ExternalUint8Array (size=-0x8)
    // 0x904914: mov             x1, x0
    // 0x904918: r0 = 8224
    //     0x904918: movz            x0, #0x2020
    // 0x90491c: stur            x1, [fp, #-0xe8]
    // 0x904920: StoreField: r1->field_13 = r0
    //     0x904920: stur            w0, [x1, #0x13]
    // 0x904924: ldur            x0, [fp, #-0xe0]
    // 0x904928: LoadField: r2 = r0->field_7
    //     0x904928: ldur            x2, [x0, #7]
    // 0x90492c: StoreField: r1->field_7 = r2
    //     0x90492c: stur            x2, [x1, #7]
    // 0x904930: r16 = <hg>
    //     0x904930: add             x16, PP, #0x14, lsl #12  ; [pp+0x14ca8] TypeArguments: <hg>
    //     0x904934: ldr             x16, [x16, #0xca8]
    // 0x904938: ldur            lr, [fp, #-0xb8]
    // 0x90493c: stp             lr, x16, [SP, #8]
    // 0x904940: r2 = 4
    //     0x904940: movz            x2, #0x4
    // 0x904944: str             x2, [SP]
    // 0x904948: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x904948: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x90494c: r0 = call 0x55fd58
    //     0x90494c: bl              #0x55fd58
    // 0x904950: mov             x2, x0
    // 0x904954: ldur            x0, [fp, #-0xb0]
    // 0x904958: stur            x2, [fp, #-0xf0]
    // 0x90495c: LoadField: r3 = r0->field_13
    //     0x90495c: ldur            w3, [x0, #0x13]
    // 0x904960: DecompressPointer r3
    //     0x904960: add             x3, x3, HEAP, lsl #32
    // 0x904964: stur            x3, [fp, #-0xb8]
    // 0x904968: r1 = <List<int>>
    //     0x904968: add             x1, PP, #8, lsl #12  ; [pp+0x8d38] TypeArguments: <List<int>>
    //     0x90496c: ldr             x1, [x1, #0xd38]
    // 0x904970: r0 = _StreamIterator()
    //     0x904970: bl              #0x90405c  ; Allocate_StreamIteratorStub -> _StreamIterator<X0> (size=0x18)
    // 0x904974: mov             x1, x0
    // 0x904978: r0 = false
    //     0x904978: add             x0, NULL, #0x30  ; false
    // 0x90497c: StoreField: r1->field_13 = r0
    //     0x90497c: stur            w0, [x1, #0x13]
    // 0x904980: ldur            x2, [fp, #-0xb8]
    // 0x904984: StoreField: r1->field_f = r2
    //     0x904984: stur            w2, [x1, #0xf]
    // 0x904988: mov             x2, THR
    // 0x90498c: stur            x2, [fp, #-0xf8]
    // 0x904990: mov             x9, x1
    // 0x904994: ldur            x8, [fp, #-0xd0]
    // 0x904998: ldur            x7, [fp, #-0xc8]
    // 0x90499c: ldur            x6, [fp, #-0xd8]
    // 0x9049a0: ldur            x5, [fp, #-0xe0]
    // 0x9049a4: ldur            x4, [fp, #-0xe8]
    // 0x9049a8: ldur            x3, [fp, #-0xf0]
    // 0x9049ac: stur            x9, [fp, #-0xb8]
    // 0x9049b0: stur            x8, [fp, #-0xc8]
    // 0x9049b4: stur            x7, [fp, #-0xd0]
    // 0x9049b8: stur            x6, [fp, #-0xd8]
    // 0x9049bc: stur            x5, [fp, #-0xe0]
    // 0x9049c0: stur            x4, [fp, #-0xe8]
    // 0x9049c4: stur            x3, [fp, #-0xf0]
    // 0x9049c8: CheckStackOverflow
    //     0x9049c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9049cc: cmp             SP, x16
    //     0x9049d0: b.ls            #0x905304
    // 0x9049d4: LoadField: r10 = r9->field_b
    //     0x9049d4: ldur            w10, [x9, #0xb]
    // 0x9049d8: DecompressPointer r10
    //     0x9049d8: add             x10, x10, HEAP, lsl #32
    // 0x9049dc: stur            x10, [fp, #-0xb0]
    // 0x9049e0: cmp             w10, NULL
    // 0x9049e4: b.eq            #0x904a7c
    // 0x9049e8: LoadField: r1 = r9->field_13
    //     0x9049e8: ldur            w1, [x9, #0x13]
    // 0x9049ec: DecompressPointer r1
    //     0x9049ec: add             x1, x1, HEAP, lsl #32
    // 0x9049f0: tbnz            w1, #4, #0x90526c
    // 0x9049f4: r1 = <bool>
    //     0x9049f4: ldr             x1, [PP, #0x31a0]  ; [pp+0x31a0] TypeArguments: <bool>
    // 0x9049f8: r0 = _Future()
    //     0x9049f8: bl              #0x8c0e10  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x9049fc: r1 = 0
    //     0x9049fc: movz            x1, #0
    // 0x904a00: stur            x0, [fp, #-0x100]
    // 0x904a04: StoreField: r0->field_b = r1
    //     0x904a04: stur            x1, [x0, #0xb]
    // 0x904a08: r0 = InitLateStaticField(0x590) // [dart:async] s::_current
    //     0x904a08: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x904a0c: ldr             x0, [x0, #0xb20]
    //     0x904a10: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x904a14: cmp             w0, w16
    //     0x904a18: b.ne            #0x904a24
    //     0x904a1c: ldr             x2, [PP, #0x158]  ; [pp+0x158] Field <s._current@4048458>: static late (offset: 0x590)
    //     0x904a20: bl              #0x94dc14  ; InitLateStaticFieldStub
    // 0x904a24: ldur            x2, [fp, #-0x100]
    // 0x904a28: StoreField: r2->field_13 = r0
    //     0x904a28: stur            w0, [x2, #0x13]
    // 0x904a2c: mov             x0, x2
    // 0x904a30: ldur            x3, [fp, #-0xb8]
    // 0x904a34: StoreField: r3->field_f = r0
    //     0x904a34: stur            w0, [x3, #0xf]
    //     0x904a38: ldurb           w16, [x3, #-1]
    //     0x904a3c: ldurb           w17, [x0, #-1]
    //     0x904a40: and             x16, x17, x16, lsr #2
    //     0x904a44: tst             x16, HEAP, lsr #32
    //     0x904a48: b.eq            #0x904a50
    //     0x904a4c: bl              #0x94e188  ; WriteBarrierWrappersStub
    // 0x904a50: r4 = false
    //     0x904a50: add             x4, NULL, #0x30  ; false
    // 0x904a54: StoreField: r3->field_13 = r4
    //     0x904a54: stur            w4, [x3, #0x13]
    // 0x904a58: ldur            x5, [fp, #-0xb0]
    // 0x904a5c: r0 = LoadClassIdInstr(r5)
    //     0x904a5c: ldur            x0, [x5, #-1]
    //     0x904a60: ubfx            x0, x0, #0xc, #0x14
    // 0x904a64: mov             x1, x5
    // 0x904a68: r0 = GDT[cid_x0 + -0x6aa]()
    //     0x904a68: sub             lr, x0, #0x6aa
    //     0x904a6c: ldr             lr, [x21, lr, lsl #3]
    //     0x904a70: blr             lr
    // 0x904a74: ldur            x1, [fp, #-0x100]
    // 0x904a78: b               #0x904a88
    // 0x904a7c: ldur            x1, [fp, #-0xb8]
    // 0x904a80: r0 = call 0x55e304
    //     0x904a80: bl              #0x55e304
    // 0x904a84: mov             x1, x0
    // 0x904a88: mov             x0, x1
    // 0x904a8c: stur            x1, [fp, #-0x100]
    // 0x904a90: r0 = Await()
    //     0x904a90: bl              #0x8c1bb8  ; AwaitStub
    // 0x904a94: mov             x1, x0
    // 0x904a98: stur            x1, [fp, #-0x100]
    // 0x904a9c: tbnz            w0, #5, #0x904aa4
    // 0x904aa0: r0 = AssertBoolean()
    //     0x904aa0: bl              #0x94dcc0  ; AssertBooleanStub
    // 0x904aa4: ldur            x0, [fp, #-0x100]
    // 0x904aa8: tbnz            w0, #4, #0x905114
    // 0x904aac: ldur            x3, [fp, #-0xb8]
    // 0x904ab0: LoadField: r0 = r3->field_13
    //     0x904ab0: ldur            w0, [x3, #0x13]
    // 0x904ab4: DecompressPointer r0
    //     0x904ab4: add             x0, x0, HEAP, lsl #32
    // 0x904ab8: tbnz            w0, #4, #0x904b14
    // 0x904abc: LoadField: r4 = r3->field_f
    //     0x904abc: ldur            w4, [x3, #0xf]
    // 0x904ac0: DecompressPointer r4
    //     0x904ac0: add             x4, x4, HEAP, lsl #32
    // 0x904ac4: r17 = -264
    //     0x904ac4: movn            x17, #0x107
    // 0x904ac8: str             x4, [fp, x17]
    // 0x904acc: LoadField: r5 = r3->field_7
    //     0x904acc: ldur            w5, [x3, #7]
    // 0x904ad0: DecompressPointer r5
    //     0x904ad0: add             x5, x5, HEAP, lsl #32
    // 0x904ad4: mov             x0, x4
    // 0x904ad8: mov             x2, x5
    // 0x904adc: stur            x5, [fp, #-0x100]
    // 0x904ae0: r1 = Null
    //     0x904ae0: mov             x1, NULL
    // 0x904ae4: cmp             w2, NULL
    // 0x904ae8: b.eq            #0x904b08
    // 0x904aec: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x904aec: ldur            w4, [x2, #0x17]
    // 0x904af0: DecompressPointer r4
    //     0x904af0: add             x4, x4, HEAP, lsl #32
    // 0x904af4: r8 = X0
    //     0x904af4: ldr             x8, [PP, #0x190]  ; [pp+0x190] TypeParameter: X0
    // 0x904af8: LoadField: r9 = r4->field_7
    //     0x904af8: ldur            x9, [x4, #7]
    // 0x904afc: r3 = Null
    //     0x904afc: add             x3, PP, #0x14, lsl #12  ; [pp+0x14cb0] Null
    //     0x904b00: ldr             x3, [x3, #0xcb0]
    // 0x904b04: blr             x9
    // 0x904b08: r17 = -264
    //     0x904b08: movn            x17, #0x107
    // 0x904b0c: ldr             x0, [fp, x17]
    // 0x904b10: b               #0x904b54
    // 0x904b14: LoadField: r4 = r3->field_7
    //     0x904b14: ldur            w4, [x3, #7]
    // 0x904b18: DecompressPointer r4
    //     0x904b18: add             x4, x4, HEAP, lsl #32
    // 0x904b1c: mov             x2, x4
    // 0x904b20: stur            x4, [fp, #-0x100]
    // 0x904b24: r0 = Null
    //     0x904b24: mov             x0, NULL
    // 0x904b28: r1 = Null
    //     0x904b28: mov             x1, NULL
    // 0x904b2c: cmp             w2, NULL
    // 0x904b30: b.eq            #0x904b50
    // 0x904b34: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x904b34: ldur            w4, [x2, #0x17]
    // 0x904b38: DecompressPointer r4
    //     0x904b38: add             x4, x4, HEAP, lsl #32
    // 0x904b3c: r8 = X0
    //     0x904b3c: ldr             x8, [PP, #0x190]  ; [pp+0x190] TypeParameter: X0
    // 0x904b40: LoadField: r9 = r4->field_7
    //     0x904b40: ldur            x9, [x4, #7]
    // 0x904b44: r3 = Null
    //     0x904b44: add             x3, PP, #0x14, lsl #12  ; [pp+0x14cc0] Null
    //     0x904b48: ldr             x3, [x3, #0xcc0]
    // 0x904b4c: blr             x9
    // 0x904b50: r0 = Null
    //     0x904b50: mov             x0, NULL
    // 0x904b54: mov             x9, x0
    // 0x904b58: ldur            x8, [fp, #-0xb8]
    // 0x904b5c: ldur            x7, [fp, #-0xc8]
    // 0x904b60: ldur            x6, [fp, #-0xd0]
    // 0x904b64: ldur            x5, [fp, #-0xd8]
    // 0x904b68: ldur            x4, [fp, #-0xe0]
    // 0x904b6c: ldur            x3, [fp, #-0xe8]
    // 0x904b70: ldur            x2, [fp, #-0xf0]
    // 0x904b74: r10 = 0
    //     0x904b74: movz            x10, #0
    // 0x904b78: ldur            x1, [fp, #-0xf8]
    // 0x904b7c: r17 = -272
    //     0x904b7c: movn            x17, #0x10f
    // 0x904b80: str             x10, [fp, x17]
    // 0x904b84: stur            x9, [fp, #-0xd0]
    // 0x904b88: stur            x8, [fp, #-0xd8]
    // 0x904b8c: stur            x7, [fp, #-0x100]
    // 0x904b90: r17 = -264
    //     0x904b90: movn            x17, #0x107
    // 0x904b94: str             x6, [fp, x17]
    // 0x904b98: r17 = -280
    //     0x904b98: movn            x17, #0x117
    // 0x904b9c: str             x5, [fp, x17]
    // 0x904ba0: r17 = -288
    //     0x904ba0: movn            x17, #0x11f
    // 0x904ba4: str             x4, [fp, x17]
    // 0x904ba8: r17 = -296
    //     0x904ba8: movn            x17, #0x127
    // 0x904bac: str             x3, [fp, x17]
    // 0x904bb0: r17 = -304
    //     0x904bb0: movn            x17, #0x12f
    // 0x904bb4: str             x2, [fp, x17]
    // 0x904bb8: CheckStackOverflow
    //     0x904bb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x904bbc: cmp             SP, x16
    //     0x904bc0: b.ls            #0x90530c
    // 0x904bc4: r0 = LoadClassIdInstr(r9)
    //     0x904bc4: ldur            x0, [x9, #-1]
    //     0x904bc8: ubfx            x0, x0, #0xc, #0x14
    // 0x904bcc: str             x9, [SP]
    // 0x904bd0: r0 = GDT[cid_x0 + 0x9e28]()
    //     0x904bd0: movz            x17, #0x9e28
    //     0x904bd4: add             lr, x0, x17
    //     0x904bd8: ldr             lr, [x21, lr, lsl #3]
    //     0x904bdc: blr             lr
    // 0x904be0: r1 = LoadInt32Instr(r0)
    //     0x904be0: sbfx            x1, x0, #1, #0x1f
    //     0x904be4: tbz             w0, #0, #0x904bec
    //     0x904be8: ldur            x1, [x0, #7]
    // 0x904bec: r17 = -272
    //     0x904bec: movn            x17, #0x10f
    // 0x904bf0: ldr             x2, [fp, x17]
    // 0x904bf4: cmp             x2, x1
    // 0x904bf8: b.ge            #0x9050d4
    // 0x904bfc: ldur            x1, [fp, #-0xd0]
    // 0x904c00: r0 = LoadClassIdInstr(r1)
    //     0x904c00: ldur            x0, [x1, #-1]
    //     0x904c04: ubfx            x0, x0, #0xc, #0x14
    // 0x904c08: str             x1, [SP]
    // 0x904c0c: r0 = GDT[cid_x0 + 0x9e28]()
    //     0x904c0c: movz            x17, #0x9e28
    //     0x904c10: add             lr, x0, x17
    //     0x904c14: ldr             lr, [x21, lr, lsl #3]
    //     0x904c18: blr             lr
    // 0x904c1c: r1 = LoadInt32Instr(r0)
    //     0x904c1c: sbfx            x1, x0, #1, #0x1f
    //     0x904c20: tbz             w0, #0, #0x904c28
    //     0x904c24: ldur            x1, [x0, #7]
    // 0x904c28: r17 = -272
    //     0x904c28: movn            x17, #0x10f
    // 0x904c2c: ldr             x3, [fp, x17]
    // 0x904c30: sub             x0, x1, x3
    // 0x904c34: cmp             x0, #1, lsl #12
    // 0x904c38: b.le            #0x904c44
    // 0x904c3c: r5 = 4096
    //     0x904c3c: movz            x5, #0x1000
    // 0x904c40: b               #0x904c58
    // 0x904c44: cmp             x0, #1, lsl #12
    // 0x904c48: b.ge            #0x904c54
    // 0x904c4c: mov             x5, x0
    // 0x904c50: b               #0x904c58
    // 0x904c54: mov             x5, x0
    // 0x904c58: ldur            x4, [fp, #-0xd0]
    // 0x904c5c: r17 = -312
    //     0x904c5c: movn            x17, #0x137
    // 0x904c60: str             x5, [fp, x17]
    // 0x904c64: r0 = LoadClassIdInstr(r4)
    //     0x904c64: ldur            x0, [x4, #-1]
    //     0x904c68: ubfx            x0, x0, #0xc, #0x14
    // 0x904c6c: mov             x1, x4
    // 0x904c70: mov             x2, x3
    // 0x904c74: r0 = GDT[cid_x0 + 0xb0c4]()
    //     0x904c74: movz            x17, #0xb0c4
    //     0x904c78: add             lr, x0, x17
    //     0x904c7c: ldr             lr, [x21, lr, lsl #3]
    //     0x904c80: blr             lr
    // 0x904c84: mov             x1, x0
    // 0x904c88: r17 = -312
    //     0x904c88: movn            x17, #0x137
    // 0x904c8c: ldr             x2, [fp, x17]
    // 0x904c90: r0 = call 0x55fc10
    //     0x904c90: bl              #0x55fc10
    // 0x904c94: r17 = -320
    //     0x904c94: movn            x17, #0x13f
    // 0x904c98: str             x0, [fp, x17]
    // 0x904c9c: str             x0, [SP]
    // 0x904ca0: r0 = length()
    //     0x904ca0: bl              #0x8fd738  ; [dart:_internal] k::length
    // 0x904ca4: r17 = -336
    //     0x904ca4: movn            x17, #0x14f
    // 0x904ca8: str             x0, [fp, x17]
    // 0x904cac: r4 = LoadInt32Instr(r0)
    //     0x904cac: sbfx            x4, x0, #1, #0x1f
    //     0x904cb0: tbz             w0, #0, #0x904cb8
    //     0x904cb4: ldur            x4, [x0, #7]
    // 0x904cb8: r17 = -328
    //     0x904cb8: movn            x17, #0x147
    // 0x904cbc: str             x4, [fp, x17]
    // 0x904cc0: tbnz            x4, #0x3f, #0x904ccc
    // 0x904cc4: cmp             x4, #1, lsl #12
    // 0x904cc8: b.le            #0x904ce0
    // 0x904ccc: mov             x2, x0
    // 0x904cd0: r1 = 0
    //     0x904cd0: movz            x1, #0
    // 0x904cd4: r3 = 4096
    //     0x904cd4: movz            x3, #0x1000
    // 0x904cd8: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x904cd8: ldr             x4, [PP, #0x308]  ; [pp+0x308] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x904cdc: r0 = call 0x2d6cfc
    //     0x904cdc: bl              #0x2d6cfc
    // 0x904ce0: r17 = -304
    //     0x904ce0: movn            x17, #0x12f
    // 0x904ce4: ldr             x4, [fp, x17]
    // 0x904ce8: r17 = -312
    //     0x904ce8: movn            x17, #0x137
    // 0x904cec: ldr             x0, [fp, x17]
    // 0x904cf0: r17 = -280
    //     0x904cf0: movn            x17, #0x117
    // 0x904cf4: ldr             x1, [fp, x17]
    // 0x904cf8: r17 = -328
    //     0x904cf8: movn            x17, #0x147
    // 0x904cfc: ldr             x3, [fp, x17]
    // 0x904d00: r17 = -320
    //     0x904d00: movn            x17, #0x13f
    // 0x904d04: ldr             x5, [fp, x17]
    // 0x904d08: r2 = 0
    //     0x904d08: movz            x2, #0
    // 0x904d0c: r6 = 0
    //     0x904d0c: movz            x6, #0
    // 0x904d10: r0 = call 0x6dfb3c
    //     0x904d10: bl              #0x6dfb3c
    // 0x904d14: ldur            x1, [fp, #-0xc0]
    // 0x904d18: LoadField: r0 = r1->field_4f
    //     0x904d18: ldur            w0, [x1, #0x4f]
    // 0x904d1c: DecompressPointer r0
    //     0x904d1c: add             x0, x0, HEAP, lsl #32
    // 0x904d20: r16 = Sentinel
    //     0x904d20: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x904d24: cmp             w0, w16
    // 0x904d28: b.ne            #0x904d38
    // 0x904d2c: r2 = _MVg
    //     0x904d2c: add             x2, PP, #0x14, lsl #12  ; [pp+0x14cd0] Field <Geb._MVg@1317239889>: late final (offset: 0x50)
    //     0x904d30: ldr             x2, [x2, #0xcd0]
    // 0x904d34: r0 = InitLateFinalInstanceField()
    //     0x904d34: bl              #0x94da68  ; InitLateFinalInstanceFieldStub
    // 0x904d38: mov             x3, x0
    // 0x904d3c: r17 = -312
    //     0x904d3c: movn            x17, #0x137
    // 0x904d40: ldr             x2, [fp, x17]
    // 0x904d44: r17 = -320
    //     0x904d44: movn            x17, #0x13f
    // 0x904d48: str             x3, [fp, x17]
    // 0x904d4c: r0 = BoxInt64Instr(r2)
    //     0x904d4c: sbfiz           x0, x2, #1, #0x1f
    //     0x904d50: cmp             x2, x0, asr #1
    //     0x904d54: b.eq            #0x904d60
    //     0x904d58: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x904d5c: stur            x2, [x0, #7]
    // 0x904d60: ldur            x16, [fp, #-0x100]
    // 0x904d64: stp             x16, x3, [SP, #0x20]
    // 0x904d68: r17 = -288
    //     0x904d68: movn            x17, #0x11f
    // 0x904d6c: ldr             x16, [fp, x17]
    // 0x904d70: r17 = -304
    //     0x904d70: movn            x17, #0x12f
    // 0x904d74: ldr             lr, [fp, x17]
    // 0x904d78: stp             lr, x16, [SP, #0x10]
    // 0x904d7c: r17 = -264
    //     0x904d7c: movn            x17, #0x107
    // 0x904d80: ldr             x16, [fp, x17]
    // 0x904d84: stp             x0, x16, [SP]
    // 0x904d88: mov             x0, x3
    // 0x904d8c: ClosureCall
    //     0x904d8c: ldr             x4, [PP, #0x1b70]  ; [pp+0x1b70] List(5) [0, 0x6, 0x6, 0x6, Null]
    //     0x904d90: ldur            x2, [x0, #0x1f]
    //     0x904d94: blr             x2
    // 0x904d98: r17 = -320
    //     0x904d98: movn            x17, #0x13f
    // 0x904d9c: str             x0, [fp, x17]
    // 0x904da0: cmp             w0, #2
    // 0x904da4: r16 = true
    //     0x904da4: add             x16, NULL, #0x20  ; true
    // 0x904da8: r17 = false
    //     0x904da8: add             x17, NULL, #0x30  ; false
    // 0x904dac: csel            x1, x16, x17, eq
    // 0x904db0: str             NULL, [SP]
    // 0x904db4: r2 = Null
    //     0x904db4: mov             x2, NULL
    // 0x904db8: r4 = const [0, 0x3, 0x1, 0x2, Mob, 0x2, null]
    //     0x904db8: add             x4, PP, #0x14, lsl #12  ; [pp+0x14cd8] List(7) [0, 0x3, 0x1, 0x2, "Mob", 0x2, Null]
    //     0x904dbc: ldr             x4, [x4, #0xcd8]
    // 0x904dc0: r0 = call 0x55efac
    //     0x904dc0: bl              #0x55efac
    // 0x904dc4: r17 = -304
    //     0x904dc4: movn            x17, #0x12f
    // 0x904dc8: ldr             x0, [fp, x17]
    // 0x904dcc: LoadField: r1 = r0->field_7
    //     0x904dcc: ldur            x1, [x0, #7]
    // 0x904dd0: ldrsw           x2, [x1]
    // 0x904dd4: mov             x1, x2
    // 0x904dd8: sxtw            x1, w1
    // 0x904ddc: cmp             x1, #0
    // 0x904de0: b.le            #0x905084
    // 0x904de4: r4 = 0
    //     0x904de4: movz            x4, #0
    // 0x904de8: add             x1, fp, w4, sxtw #2
    // 0x904dec: LoadField: r1 = r1->field_fffffff8
    //     0x904dec: ldur            x1, [x1, #-8]
    // 0x904df0: ArrayLoad: r5 = r1[0]  ; List_4
    //     0x904df0: ldur            w5, [x1, #0x17]
    // 0x904df4: DecompressPointer r5
    //     0x904df4: add             x5, x5, HEAP, lsl #32
    // 0x904df8: r17 = -336
    //     0x904df8: movn            x17, #0x14f
    // 0x904dfc: str             x5, [fp, x17]
    // 0x904e00: sbfiz           x6, x2, #1, #0x1f
    // 0x904e04: cmp             w2, w6, asr #1
    // 0x904e08: b.eq            #0x904e38
    // 0x904e0c: r6 = inline_Allocate_Mint()
    //     0x904e0c: ldp             x6, x1, [THR, #0x50]  ; THR::top
    //     0x904e10: add             x6, x6, #0x10
    //     0x904e14: cmp             x1, x6
    //     0x904e18: b.ls            #0x905314
    //     0x904e1c: str             x6, [THR, #0x50]  ; THR::top
    //     0x904e20: sub             x6, x6, #0xf
    //     0x904e24: movz            x1, #0xc15c
    //     0x904e28: movk            x1, #0x3, lsl #16
    //     0x904e2c: stur            x1, [x6, #-1]
    // 0x904e30: sxtw            x1, w2
    // 0x904e34: StoreField: r6->field_7 = r1
    //     0x904e34: stur            x1, [x6, #7]
    // 0x904e38: mov             x2, x6
    // 0x904e3c: r17 = -320
    //     0x904e3c: movn            x17, #0x13f
    // 0x904e40: str             x6, [fp, x17]
    // 0x904e44: r1 = 0
    //     0x904e44: movz            x1, #0
    // 0x904e48: r3 = 4112
    //     0x904e48: movz            x3, #0x1010
    // 0x904e4c: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x904e4c: ldr             x4, [PP, #0x308]  ; [pp+0x308] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x904e50: r0 = call 0x2d6cfc
    //     0x904e50: bl              #0x2d6cfc
    // 0x904e54: mov             x2, x0
    // 0x904e58: r17 = -328
    //     0x904e58: movn            x17, #0x147
    // 0x904e5c: str             x2, [fp, x17]
    // 0x904e60: r0 = BoxInt64Instr(r2)
    //     0x904e60: sbfiz           x0, x2, #1, #0x1f
    //     0x904e64: cmp             x2, x0, asr #1
    //     0x904e68: b.eq            #0x904e74
    //     0x904e6c: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x904e70: stur            x2, [x0, #7]
    // 0x904e74: mov             x4, x0
    // 0x904e78: r17 = -344
    //     0x904e78: movn            x17, #0x157
    // 0x904e7c: str             x0, [fp, x17]
    // 0x904e80: r0 = AllocateUint8Array()
    //     0x904e80: bl              #0x94f700  ; AllocateUint8ArrayStub
    // 0x904e84: mov             x4, x0
    // 0x904e88: r17 = -328
    //     0x904e88: movn            x17, #0x147
    // 0x904e8c: ldr             x0, [fp, x17]
    // 0x904e90: r17 = -352
    //     0x904e90: movn            x17, #0x15f
    // 0x904e94: str             x4, [fp, x17]
    // 0x904e98: tbnz            x0, #0x3f, #0x904ea4
    // 0x904e9c: cmp             x0, x0
    // 0x904ea0: b.le            #0x904ebc
    // 0x904ea4: r17 = -344
    //     0x904ea4: movn            x17, #0x157
    // 0x904ea8: ldr             x2, [fp, x17]
    // 0x904eac: mov             x3, x0
    // 0x904eb0: r1 = 0
    //     0x904eb0: movz            x1, #0
    // 0x904eb4: r4 = const [0, 0x3, 0, 0x3, null]
    //     0x904eb4: ldr             x4, [PP, #0x308]  ; [pp+0x308] List(5) [0, 0x3, 0, 0x3, Null]
    // 0x904eb8: r0 = call 0x2d6cfc
    //     0x904eb8: bl              #0x2d6cfc
    // 0x904ebc: r17 = -328
    //     0x904ebc: movn            x17, #0x147
    // 0x904ec0: ldr             x2, [fp, x17]
    // 0x904ec4: r17 = 4112
    //     0x904ec4: movz            x17, #0x1010
    // 0x904ec8: cmp             x2, x17
    // 0x904ecc: b.gt            #0x905294
    // 0x904ed0: cbnz            x2, #0x904eec
    // 0x904ed4: r17 = -296
    //     0x904ed4: movn            x17, #0x127
    // 0x904ed8: ldr             x23, [fp, x17]
    // 0x904edc: r17 = -352
    //     0x904edc: movn            x17, #0x15f
    // 0x904ee0: ldr             x20, [fp, x17]
    // 0x904ee4: ldur            x24, [fp, #-0xf8]
    // 0x904ee8: b               #0x905024
    // 0x904eec: r17 = -344
    //     0x904eec: movn            x17, #0x157
    // 0x904ef0: ldr             x0, [fp, x17]
    // 0x904ef4: cmp             w0, #0x800
    // 0x904ef8: b.ge            #0x904fdc
    // 0x904efc: r17 = -296
    //     0x904efc: movn            x17, #0x127
    // 0x904f00: ldr             x23, [fp, x17]
    // 0x904f04: r17 = -352
    //     0x904f04: movn            x17, #0x15f
    // 0x904f08: ldr             x20, [fp, x17]
    // 0x904f0c: LoadField: r1 = r23->field_7
    //     0x904f0c: ldur            x1, [x23, #7]
    // 0x904f10: mov             x3, x0
    // 0x904f14: mov             x2, x1
    // 0x904f18: add             x0, x20, #0x17
    // 0x904f1c: cbz             x3, #0x904fd4
    // 0x904f20: cmp             x0, x2
    // 0x904f24: b.ls            #0x904f8c
    // 0x904f28: sxtw            x3, w3
    // 0x904f2c: add             x16, x2, x3, asr #1
    // 0x904f30: cmp             x0, x16
    // 0x904f34: b.hs            #0x904f8c
    // 0x904f38: mov             x2, x16
    // 0x904f3c: add             x0, x0, x3, asr #1
    // 0x904f40: tbz             w3, #4, #0x904f4c
    // 0x904f44: ldr             x16, [x2, #-8]!
    // 0x904f48: str             x16, [x0, #-8]!
    // 0x904f4c: tbz             w3, #3, #0x904f58
    // 0x904f50: ldr             w16, [x2, #-4]!
    // 0x904f54: str             w16, [x0, #-4]!
    // 0x904f58: tbz             w3, #2, #0x904f64
    // 0x904f5c: ldrh            w16, [x2, #-2]!
    // 0x904f60: strh            w16, [x0, #-2]!
    // 0x904f64: tbz             w3, #1, #0x904f70
    // 0x904f68: ldrb            w16, [x2, #-1]!
    // 0x904f6c: strb            w16, [x0, #-1]!
    // 0x904f70: ands            w3, w3, #0xffffffe1
    // 0x904f74: b.eq            #0x904fd4
    // 0x904f78: ldp             x16, x17, [x2, #-0x10]!
    // 0x904f7c: stp             x16, x17, [x0, #-0x10]!
    // 0x904f80: subs            w3, w3, #0x20
    // 0x904f84: b.ne            #0x904f78
    // 0x904f88: b               #0x904fd4
    // 0x904f8c: tbz             w3, #4, #0x904f98
    // 0x904f90: ldr             x16, [x2], #8
    // 0x904f94: str             x16, [x0], #8
    // 0x904f98: tbz             w3, #3, #0x904fa4
    // 0x904f9c: ldr             w16, [x2], #4
    // 0x904fa0: str             w16, [x0], #4
    // 0x904fa4: tbz             w3, #2, #0x904fb0
    // 0x904fa8: ldrh            w16, [x2], #2
    // 0x904fac: strh            w16, [x0], #2
    // 0x904fb0: tbz             w3, #1, #0x904fbc
    // 0x904fb4: ldrb            w16, [x2], #1
    // 0x904fb8: strb            w16, [x0], #1
    // 0x904fbc: ands            w3, w3, #0xffffffe1
    // 0x904fc0: b.eq            #0x904fd4
    // 0x904fc4: ldp             x16, x17, [x2], #0x10
    // 0x904fc8: stp             x16, x17, [x0], #0x10
    // 0x904fcc: subs            w3, w3, #0x20
    // 0x904fd0: b.ne            #0x904fc4
    // 0x904fd4: ldur            x24, [fp, #-0xf8]
    // 0x904fd8: b               #0x905024
    // 0x904fdc: r17 = -296
    //     0x904fdc: movn            x17, #0x127
    // 0x904fe0: ldr             x23, [fp, x17]
    // 0x904fe4: r17 = -352
    //     0x904fe4: movn            x17, #0x15f
    // 0x904fe8: ldr             x20, [fp, x17]
    // 0x904fec: ldur            x24, [fp, #-0xf8]
    // 0x904ff0: LoadField: r0 = r20->field_7
    //     0x904ff0: ldur            x0, [x20, #7]
    // 0x904ff4: LoadField: r1 = r23->field_7
    //     0x904ff4: ldur            x1, [x23, #7]
    // 0x904ff8: CallRuntime_MemoryMove(void*, const void*, size_t) -> void*
    //     0x904ff8: ldr             x9, [x24, #0x5f0]
    //     0x904ffc: mov             x17, fp
    //     0x905000: str             fp, [SP, #-8]!
    //     0x905004: mov             fp, SP
    //     0x905008: and             SP, SP, #0xfffffffffffffff0
    //     0x90500c: mov             x19, sp
    //     0x905010: mov             sp, SP
    //     0x905014: blr             x9
    //     0x905018: mov             sp, x19
    //     0x90501c: mov             SP, fp
    //     0x905020: ldr             fp, [SP], #8
    // 0x905024: r17 = -336
    //     0x905024: movn            x17, #0x14f
    // 0x905028: ldr             x16, [fp, x17]
    // 0x90502c: stp             x20, x16, [SP]
    // 0x905030: r0 = add()
    //     0x905030: bl              #0x55eea0  ; [dart:async] _AsyncStarStreamController::add
    // 0x905034: tbz             w0, #4, #0x905054
    // 0x905038: r0 = Null
    //     0x905038: mov             x0, NULL
    // 0x90503c: r0 = YieldAsyncStar()
    //     0x90503c: bl              #0x904488  ; YieldAsyncStarStub
    // 0x905040: r16 = true
    //     0x905040: add             x16, NULL, #0x20  ; true
    // 0x905044: cmp             w0, w16
    // 0x905048: b.eq            #0x905054
    // 0x90504c: ldur            x1, [fp, #-0xd8]
    // 0x905050: b               #0x905088
    // 0x905054: ldur            x1, [fp, #-0xd8]
    // 0x905058: LoadField: r0 = r1->field_b
    //     0x905058: ldur            w0, [x1, #0xb]
    // 0x90505c: DecompressPointer r0
    //     0x90505c: add             x0, x0, HEAP, lsl #32
    // 0x905060: cmp             w0, NULL
    // 0x905064: b.eq            #0x90507c
    // 0x905068: r0 = call 0x55e11c
    //     0x905068: bl              #0x55e11c
    // 0x90506c: mov             x1, x0
    // 0x905070: r17 = -344
    //     0x905070: movn            x17, #0x157
    // 0x905074: str             x1, [fp, x17]
    // 0x905078: r0 = Await()
    //     0x905078: bl              #0x8c1bb8  ; AwaitStub
    // 0x90507c: r0 = Null
    //     0x90507c: mov             x0, NULL
    // 0x905080: r0 = ReturnAsyncStar()
    //     0x905080: b               #0x9041a0  ; ReturnAsyncStarStub
    // 0x905084: ldur            x1, [fp, #-0xd8]
    // 0x905088: r17 = -272
    //     0x905088: movn            x17, #0x10f
    // 0x90508c: ldr             x2, [fp, x17]
    // 0x905090: r17 = -312
    //     0x905090: movn            x17, #0x137
    // 0x905094: ldr             x0, [fp, x17]
    // 0x905098: add             x10, x2, x0
    // 0x90509c: ldur            x9, [fp, #-0xd0]
    // 0x9050a0: mov             x8, x1
    // 0x9050a4: ldur            x7, [fp, #-0x100]
    // 0x9050a8: r17 = -264
    //     0x9050a8: movn            x17, #0x107
    // 0x9050ac: ldr             x6, [fp, x17]
    // 0x9050b0: r17 = -280
    //     0x9050b0: movn            x17, #0x117
    // 0x9050b4: ldr             x5, [fp, x17]
    // 0x9050b8: r17 = -288
    //     0x9050b8: movn            x17, #0x11f
    // 0x9050bc: ldr             x4, [fp, x17]
    // 0x9050c0: r17 = -296
    //     0x9050c0: movn            x17, #0x127
    // 0x9050c4: ldr             x3, [fp, x17]
    // 0x9050c8: r17 = -304
    //     0x9050c8: movn            x17, #0x12f
    // 0x9050cc: ldr             x2, [fp, x17]
    // 0x9050d0: b               #0x904b78
    // 0x9050d4: ldur            x1, [fp, #-0xd8]
    // 0x9050d8: mov             x9, x1
    // 0x9050dc: ldur            x8, [fp, #-0x100]
    // 0x9050e0: r17 = -264
    //     0x9050e0: movn            x17, #0x107
    // 0x9050e4: ldr             x7, [fp, x17]
    // 0x9050e8: r17 = -280
    //     0x9050e8: movn            x17, #0x117
    // 0x9050ec: ldr             x6, [fp, x17]
    // 0x9050f0: r17 = -288
    //     0x9050f0: movn            x17, #0x11f
    // 0x9050f4: ldr             x5, [fp, x17]
    // 0x9050f8: r17 = -296
    //     0x9050f8: movn            x17, #0x127
    // 0x9050fc: ldr             x4, [fp, x17]
    // 0x905100: r17 = -304
    //     0x905100: movn            x17, #0x12f
    // 0x905104: ldr             x3, [fp, x17]
    // 0x905108: ldur            x2, [fp, #-0xf8]
    // 0x90510c: r0 = false
    //     0x90510c: add             x0, NULL, #0x30  ; false
    // 0x905110: b               #0x9049ac
    // 0x905114: ldur            x1, [fp, #-0xb8]
    // 0x905118: LoadField: r0 = r1->field_b
    //     0x905118: ldur            w0, [x1, #0xb]
    // 0x90511c: DecompressPointer r0
    //     0x90511c: add             x0, x0, HEAP, lsl #32
    // 0x905120: cmp             w0, NULL
    // 0x905124: b.eq            #0x905138
    // 0x905128: r0 = call 0x55e11c
    //     0x905128: bl              #0x55e11c
    // 0x90512c: mov             x1, x0
    // 0x905130: stur            x1, [fp, #-0xd0]
    // 0x905134: r0 = Await()
    //     0x905134: bl              #0x8c1bb8  ; AwaitStub
    // 0x905138: ldur            x1, [fp, #-0xc0]
    // 0x90513c: ldur            x2, [fp, #-0xc8]
    // 0x905140: ldur            x3, [fp, #-0xe0]
    // 0x905144: ldur            x5, [fp, #-0xf0]
    // 0x905148: r0 = call 0x55ec40
    //     0x905148: bl              #0x55ec40
    // 0x90514c: mov             x1, x0
    // 0x905150: r0 = call 0x55ff4c
    //     0x905150: bl              #0x55ff4c
    // 0x905154: ldur            x16, [fp, #-0xf0]
    // 0x905158: stp             xzr, x16, [SP]
    // 0x90515c: r0 = _loadInt32()
    //     0x90515c: bl              #0x55ebb4  ; [dart:ffi] ::_loadInt32
    // 0x905160: r1 = LoadInt32Instr(r0)
    //     0x905160: sbfx            x1, x0, #1, #0x1f
    //     0x905164: tbz             w0, #0, #0x90516c
    //     0x905168: ldur            x1, [x0, #7]
    // 0x90516c: cmp             x1, #0
    // 0x905170: b.le            #0x9051d8
    // 0x905174: r0 = 0
    //     0x905174: movz            x0, #0
    // 0x905178: add             x1, fp, w0, sxtw #2
    // 0x90517c: LoadField: r1 = r1->field_fffffff8
    //     0x90517c: ldur            x1, [x1, #-8]
    // 0x905180: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x905180: ldur            w0, [x1, #0x17]
    // 0x905184: DecompressPointer r0
    //     0x905184: add             x0, x0, HEAP, lsl #32
    // 0x905188: stur            x0, [fp, #-0xc0]
    // 0x90518c: ldur            x16, [fp, #-0xf0]
    // 0x905190: stp             xzr, x16, [SP]
    // 0x905194: r0 = _loadInt32()
    //     0x905194: bl              #0x55ebb4  ; [dart:ffi] ::_loadInt32
    // 0x905198: str             x0, [SP]
    // 0x90519c: ldur            x1, [fp, #-0xe8]
    // 0x9051a0: r2 = 0
    //     0x9051a0: movz            x2, #0
    // 0x9051a4: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x9051a4: ldr             x4, [PP, #0x2b0]  ; [pp+0x2b0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x9051a8: r0 = call 0x3386f4
    //     0x9051a8: bl              #0x3386f4
    // 0x9051ac: ldur            x16, [fp, #-0xc0]
    // 0x9051b0: stp             x0, x16, [SP]
    // 0x9051b4: r0 = add()
    //     0x9051b4: bl              #0x55eea0  ; [dart:async] _AsyncStarStreamController::add
    // 0x9051b8: tbz             w0, #4, #0x9051d0
    // 0x9051bc: r0 = Null
    //     0x9051bc: mov             x0, NULL
    // 0x9051c0: r0 = YieldAsyncStar()
    //     0x9051c0: bl              #0x904488  ; YieldAsyncStarStub
    // 0x9051c4: r16 = true
    //     0x9051c4: add             x16, NULL, #0x20  ; true
    // 0x9051c8: cmp             w0, w16
    // 0x9051cc: b.ne            #0x9051d8
    // 0x9051d0: r0 = Null
    //     0x9051d0: mov             x0, NULL
    // 0x9051d4: r0 = ReturnAsyncStar()
    //     0x9051d4: b               #0x9041a0  ; ReturnAsyncStarStub
    // 0x9051d8: r0 = Null
    //     0x9051d8: mov             x0, NULL
    // 0x9051dc: r0 = ReturnAsyncStar()
    //     0x9051dc: b               #0x9041a0  ; ReturnAsyncStarStub
    // 0x9051e0: r1 = Null
    //     0x9051e0: mov             x1, NULL
    // 0x9051e4: r2 = 6
    //     0x9051e4: movz            x2, #0x6
    // 0x9051e8: r0 = AllocateArray()
    //     0x9051e8: bl              #0x94fa24  ; AllocateArrayStub
    // 0x9051ec: mov             x2, x0
    // 0x9051f0: r17 = "must be "
    //     0x9051f0: add             x17, PP, #0x14, lsl #12  ; [pp+0x14ce0] "must be "
    //     0x9051f4: ldr             x17, [x17, #0xce0]
    // 0x9051f8: StoreField: r2->field_f = r17
    //     0x9051f8: stur            w17, [x2, #0xf]
    // 0x9051fc: r17 = -368
    //     0x9051fc: movn            x17, #0x16f
    // 0x905200: ldr             x3, [fp, x17]
    // 0x905204: r0 = BoxInt64Instr(r3)
    //     0x905204: sbfiz           x0, x3, #1, #0x1f
    //     0x905208: cmp             x3, x0, asr #1
    //     0x90520c: b.eq            #0x905218
    //     0x905210: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x905214: stur            x3, [x0, #7]
    // 0x905218: StoreField: r2->field_13 = r0
    //     0x905218: stur            w0, [x2, #0x13]
    // 0x90521c: r17 = " bytes"
    //     0x90521c: add             x17, PP, #0x14, lsl #12  ; [pp+0x14ce8] " bytes"
    //     0x905220: ldr             x17, [x17, #0xce8]
    // 0x905224: ArrayStore: r2[0] = r17  ; List_4
    //     0x905224: stur            w17, [x2, #0x17]
    // 0x905228: str             x2, [SP]
    // 0x90522c: r0 = _interpolate()
    //     0x90522c: bl              #0x2dd54c  ; [dart:core] _StringBase::_interpolate
    // 0x905230: stur            x0, [fp, #-0xc0]
    // 0x905234: r0 = ArgumentError()
    //     0x905234: bl              #0x8bab10  ; AllocateArgumentErrorStub -> ArgumentError (size=0x1c)
    // 0x905238: mov             x1, x0
    // 0x90523c: r0 = "iv"
    //     0x90523c: ldr             x0, [PP, #0x7fb0]  ; [pp+0x7fb0] "iv"
    // 0x905240: StoreField: r1->field_13 = r0
    //     0x905240: stur            w0, [x1, #0x13]
    // 0x905244: ldur            x0, [fp, #-0xc0]
    // 0x905248: ArrayStore: r1[0] = r0  ; List_4
    //     0x905248: stur            w0, [x1, #0x17]
    // 0x90524c: r17 = -360
    //     0x90524c: movn            x17, #0x167
    // 0x905250: ldr             x0, [fp, x17]
    // 0x905254: StoreField: r1->field_f = r0
    //     0x905254: stur            w0, [x1, #0xf]
    // 0x905258: r0 = true
    //     0x905258: add             x0, NULL, #0x20  ; true
    // 0x90525c: StoreField: r1->field_b = r0
    //     0x90525c: stur            w0, [x1, #0xb]
    // 0x905260: mov             x0, x1
    // 0x905264: r0 = Throw()
    //     0x905264: bl              #0x94dd08  ; ThrowStub
    // 0x905268: brk             #0
    // 0x90526c: mov             x1, x9
    // 0x905270: r0 = StateError()
    //     0x905270: bl              #0x8baac8  ; AllocateStateErrorStub -> StateError (size=0x10)
    // 0x905274: mov             x1, x0
    // 0x905278: r0 = "Already waiting for next."
    //     0x905278: add             x0, PP, #0x14, lsl #12  ; [pp+0x14be0] "Already waiting for next."
    //     0x90527c: ldr             x0, [x0, #0xbe0]
    // 0x905280: stur            x1, [fp, #-0xc0]
    // 0x905284: StoreField: r1->field_b = r0
    //     0x905284: stur            w0, [x1, #0xb]
    // 0x905288: mov             x0, x1
    // 0x90528c: r0 = Throw()
    //     0x90528c: bl              #0x94dd08  ; ThrowStub
    // 0x905290: brk             #0
    // 0x905294: ldur            x1, [fp, #-0xd8]
    // 0x905298: r17 = -352
    //     0x905298: movn            x17, #0x15f
    // 0x90529c: ldr             x20, [fp, x17]
    // 0x9052a0: r0 = call 0x2d6c4c
    //     0x9052a0: bl              #0x2d6c4c
    // 0x9052a4: mov             x1, x0
    // 0x9052a8: stur            x1, [fp, #-0xb0]
    // 0x9052ac: r0 = Throw()
    //     0x9052ac: bl              #0x94dd08  ; ThrowStub
    // 0x9052b0: brk             #0
    // 0x9052b4: sub             SP, fp, #0x1a0
    // 0x9052b8: mov             x2, x0
    // 0x9052bc: stur            x0, [fp, #-0xb0]
    // 0x9052c0: mov             x0, x1
    // 0x9052c4: stur            x1, [fp, #-0xb8]
    // 0x9052c8: ldur            x1, [fp, #-0x90]
    // 0x9052cc: LoadField: r3 = r1->field_b
    //     0x9052cc: ldur            w3, [x1, #0xb]
    // 0x9052d0: DecompressPointer r3
    //     0x9052d0: add             x3, x3, HEAP, lsl #32
    // 0x9052d4: cmp             w3, NULL
    // 0x9052d8: b.eq            #0x9052ec
    // 0x9052dc: r0 = call 0x55e11c
    //     0x9052dc: bl              #0x55e11c
    // 0x9052e0: mov             x1, x0
    // 0x9052e4: stur            x1, [fp, #-0xc0]
    // 0x9052e8: r0 = Await()
    //     0x9052e8: bl              #0x8c1bb8  ; AwaitStub
    // 0x9052ec: ldur            x0, [fp, #-0xb0]
    // 0x9052f0: ldur            x1, [fp, #-0xb8]
    // 0x9052f4: r0 = ReThrow()
    //     0x9052f4: bl              #0x94dce4  ; ReThrowStub
    // 0x9052f8: brk             #0
    // 0x9052fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9052fc: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x905300: b               #0x90469c
    // 0x905304: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x905304: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x905308: b               #0x9049d4
    // 0x90530c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90530c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x905310: b               #0x904bc4
    // 0x905314: stp             x4, x5, [SP, #-0x10]!
    // 0x905318: stp             x0, x2, [SP, #-0x10]!
    // 0x90531c: r0 = AllocateMint()
    //     0x90531c: bl              #0x94f9d0  ; AllocateMintStub
    // 0x905320: mov             x6, x0
    // 0x905324: ldp             x0, x2, [SP], #0x10
    // 0x905328: ldp             x4, x5, [SP], #0x10
    // 0x90532c: b               #0x904e30
  }
  [closure] static bool <anonymous closure>(dynamic, int) {
    // ** addr: 0x55fbf8, size: -0x1
  }
}

// class id: 403, size: 0xc, field offset: 0x8
class _Feb extends Object
    implements fg {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x55ff00, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x5609c0, size: -0x1
  }
}

// class id: 404, size: 0xc, field offset: 0x8
class _Eeb extends Object {
}

// class id: 405, size: 0x8, field offset: 0x8
//   const constructor, 
class _Deb extends Object
    implements fg {
}

// class id: 407, size: 0xc, field offset: 0x8
class _Beb extends Object
    implements Ceb {
}

// class id: 5745, size: 0x10, field offset: 0xc
class _zeb extends Error
    implements Aeb {
}
