// lib: Poj, url: Ulj

// class id: 1049811, size: 0x8
class :: {
}

// class id: 625, size: 0x18, field offset: 0x8
abstract class F<PERSON>a extends Object {
}

// class id: 626, size: 0x1c, field offset: 0x18
class _G<PERSON><PERSON> extends FZa {

  String +(_GZa, dynamic) {
    // ** addr: 0x90cf9c, size: 0x4c
    // 0x90cf9c: EnterFrame
    //     0x90cf9c: stp             fp, lr, [SP, #-0x10]!
    //     0x90cfa0: mov             fp, SP
    // 0x90cfa4: CheckStackOverflow
    //     0x90cfa4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90cfa8: cmp             SP, x16
    //     0x90cfac: b.ls            #0x90cfc8
    // 0x90cfb0: ldr             x1, [fp, #0x18]
    // 0x90cfb4: ldr             x2, [fp, #0x10]
    // 0x90cfb8: r0 = call 0x598a1c
    //     0x90cfb8: bl              #0x598a1c
    // 0x90cfbc: LeaveFrame
    //     0x90cfbc: mov             SP, fp
    //     0x90cfc0: ldp             fp, lr, [SP], #0x10
    // 0x90cfc4: ret
    //     0x90cfc4: ret             
    // 0x90cfc8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90cfc8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90cfcc: b               #0x90cfb0
  }
}

// class id: 627, size: 0x1c, field offset: 0x8
class DZa extends Object {
}

// class id: 5401, size: 0x14, field offset: 0x14
enum EZa extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
