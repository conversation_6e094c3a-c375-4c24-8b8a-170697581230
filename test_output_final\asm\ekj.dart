// lib: , url: Ekj

// class id: 1049741, size: 0x8
class :: {
}

// class id: 2029, size: 0x58, field offset: 0x58
class OXa extends Bea {
}

// class id: 2030, size: 0x68, field offset: 0x58
class <PERSON>Xa extends Bea {
}

// class id: 2031, size: 0x80, field offset: 0x58
class KXa extends Bea {

  late aoa xSb; // offset: 0x5c
}

// class id: 3606, size: 0x10, field offset: 0x10
//   const constructor, 
class NXa extends Fz {
}

// class id: 3607, size: 0x28, field offset: 0x10
class LXa extends Fz {
}

// class id: 3608, size: 0x28, field offset: 0x10
//   const constructor, 
class JXa extends Fz {
}
