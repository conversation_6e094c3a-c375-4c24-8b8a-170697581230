// lib: Dqj, url: ofj

// class id: 1049659, size: 0x8
class :: {
}

// class id: 774, size: 0xc, field offset: 0xc
class GUa extends OTa {

  static late final vWa iog; // offset: 0x123c

  [closure] static GUa <anonymous closure>(dynamic) {
    // ** addr: 0x4717b4, size: -0x1
  }
  [closure] static GUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x4718cc, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x47175c, size: -0x1
  }
}
