// lib: , url: Taj

// class id: 1049250, size: 0x8
class :: {

  [closure] static r <anonymous closure>(dynamic, Y0) {
    // ** addr: 0x645200, size: -0x1
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0x6452ec, size: -0x1
  }
}

// class id: 1639, size: 0x18, field offset: 0x8
class _Dxa<X0> extends Object {
}

// class id: 1640, size: 0x10, field offset: 0x8
//   const constructor, 
class Bxa extends Object {

  bool field_8;
  bool field_c;
}

// class id: 3095, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class _zxa<C1X0> extends Mt<C1X0>
     with dja {
}

// class id: 3096, size: 0x2c, field offset: 0x14
class Axa<C1X0> extends _zxa<C1X0> {

  late final (dynamic, C1X0) => r _Gke; // offset: 0x18

  [closure] void _Kke(dynamic, Lwa) {
    // ** addr: 0x5fcf54, size: -0x1
  }
  [closure] void _Lke(dynamic, Jwa) {
    // ** addr: 0x5fcf18, size: -0x1
  }
  [closure] void _Nke(dynamic) {
    // ** addr: 0x53ea3c, size: -0x1
  }
  [closure] void _Oke(dynamic) {
    // ** addr: 0x53e1e4, size: -0x1
  }
  [closure] void Cke(dynamic) {
    // ** addr: 0x6456e4, size: -0x1
  }
  [closure] void Dke(dynamic) {
    // ** addr: 0x6456ac, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, C1X0) {
    // ** addr: 0x645388, size: -0x1
  }
}

// class id: 3883, size: 0x2c, field offset: 0xc
//   const constructor, 
class yxa<X0> extends It {
}

// class id: 4585, size: 0x34, field offset: 0x2c
class Cxa extends iJ<dynamic> {
}
