// lib: , url: ngj

// class id: 1049513, size: 0x8
class :: {
}

// class id: 938, size: 0x10, field offset: 0x8
abstract class _bPa extends Object {
}

// class id: 939, size: 0x14, field offset: 0x10
class _ePa extends _bPa {
}

// class id: 940, size: 0x14, field offset: 0x10
class _dPa extends _bPa {

  static late final RegExp _nMg; // offset: 0x13a8
}

// class id: 941, size: 0x10, field offset: 0x10
class _cPa extends _bPa {
}

// class id: 942, size: 0x2c, field offset: 0x8
class TOa extends Object {

  static late final List<RegExp> _BLg; // offset: 0x139c
  static late final Map<String, bool> _CLg; // offset: 0x13a0
  static late final Map<String, RegExp> _ELg; // offset: 0x13a4

  [closure] Da <anonymous closure>(dynamic, int, int, int, int, int, int, int, bool) {
    // ** addr: 0x76caac, size: -0x1
  }
  [closure] static bool iMg(dynamic, String?) {
    // ** addr: 0x76ca7c, size: -0x1
  }
  [closure] static _dPa <anonymous closure>(dynamic, String, TOa) {
    // ** addr: 0x3c728c, size: -0x1
  }
  [closure] static _ePa <anonymous closure>(dynamic, String, TOa) {
    // ** addr: 0x3c7240, size: -0x1
  }
  [closure] static _cPa <anonymous closure>(dynamic, String, TOa) {
    // ** addr: 0x3c71f4, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, _bPa) {
    // ** addr: 0x3c797c, size: -0x1
  }
  [closure] RegExp _hMg(dynamic) {
    // ** addr: 0x893c88, size: -0x1
  }
  [closure] int <anonymous closure>(dynamic, int) {
    // ** addr: 0x893eec, size: -0x1
  }
}
