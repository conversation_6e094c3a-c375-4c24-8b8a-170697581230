// lib: , url: Tkj

// class id: 1049756, size: 0x8
class :: {
}

// class id: 670, size: 0x34, field offset: 0x8
class wYa<X0> extends Object
    implements ja<X0> {

  late int _state; // offset: 0x2c
  late (dynamic) => bool _Dtg; // offset: 0x24

  [closure] bool _Ftg(dynamic) {
    // ** addr: 0x3f69cc, size: -0x1
  }
  [closure] bool _Gtg(dynamic) {
    // ** addr: 0x3f6720, size: -0x1
  }
}

// class id: 671, size: 0x10, field offset: 0x8
abstract class _uYa<X0> extends Object {
}

// class id: 672, size: 0x24, field offset: 0x10
class xYa<X0> extends _uYa<X0> {
}

// class id: 5668, size: 0x10, field offset: 0xc
abstract class sYa<X0> extends Iterable<X0>
    implements Set<X0> {

  bool dyn:get:dk(sYa<X0>) {
    // ** addr: 0x8daea8, size: 0x48
    // 0x8daea8: EnterFrame
    //     0x8daea8: stp             fp, lr, [SP, #-0x10]!
    //     0x8daeac: mov             fp, SP
    // 0x8daeb0: CheckStackOverflow
    //     0x8daeb0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8daeb4: cmp             SP, x16
    //     0x8daeb8: b.ls            #0x8daed0
    // 0x8daebc: ldr             x1, [fp, #0x10]
    // 0x8daec0: r0 = call 0x3eac40
    //     0x8daec0: bl              #0x3eac40
    // 0x8daec4: LeaveFrame
    //     0x8daec4: mov             SP, fp
    //     0x8daec8: ldp             fp, lr, [SP], #0x10
    // 0x8daecc: ret
    //     0x8daecc: ret             
    // 0x8daed0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8daed0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8daed4: b               #0x8daebc
  }
  bool dyn:get:isEmpty(sYa<X0>) {
    // ** addr: 0x8daef0, size: 0x48
    // 0x8daef0: EnterFrame
    //     0x8daef0: stp             fp, lr, [SP, #-0x10]!
    //     0x8daef4: mov             fp, SP
    // 0x8daef8: CheckStackOverflow
    //     0x8daef8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8daefc: cmp             SP, x16
    //     0x8daf00: b.ls            #0x8daf18
    // 0x8daf04: ldr             x1, [fp, #0x10]
    // 0x8daf08: r0 = call 0x3e7014
    //     0x8daf08: bl              #0x3e7014
    // 0x8daf0c: LeaveFrame
    //     0x8daf0c: mov             SP, fp
    //     0x8daf10: ldp             fp, lr, [SP], #0x10
    // 0x8daf14: ret
    //     0x8daf14: ret             
    // 0x8daf18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8daf18: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8daf1c: b               #0x8daf04
  }
}

// class id: 5669, size: 0x24, field offset: 0x10
class vYa<X0> extends sYa<X0> {

  int length(vYa<X0>) {
    // ** addr: 0x8fdde4, size: 0x48
    // 0x8fdde4: EnterFrame
    //     0x8fdde4: stp             fp, lr, [SP, #-0x10]!
    //     0x8fdde8: mov             fp, SP
    // 0x8fddec: ldr             x2, [fp, #0x10]
    // 0x8fddf0: LoadField: r3 = r2->field_f
    //     0x8fddf0: ldur            x3, [x2, #0xf]
    // 0x8fddf4: r0 = BoxInt64Instr(r3)
    //     0x8fddf4: sbfiz           x0, x3, #1, #0x1f
    //     0x8fddf8: cmp             x3, x0, asr #1
    //     0x8fddfc: b.eq            #0x8fde08
    //     0x8fde00: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8fde04: stur            x3, [x0, #7]
    // 0x8fde08: LeaveFrame
    //     0x8fde08: mov             SP, fp
    //     0x8fde0c: ldp             fp, lr, [SP], #0x10
    // 0x8fde10: ret
    //     0x8fde10: ret             
  }
  X0 dyn:get:Kj(vYa<X0>) {
    // ** addr: 0x8dae60, size: 0x48
    // 0x8dae60: EnterFrame
    //     0x8dae60: stp             fp, lr, [SP, #-0x10]!
    //     0x8dae64: mov             fp, SP
    // 0x8dae68: CheckStackOverflow
    //     0x8dae68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8dae6c: cmp             SP, x16
    //     0x8dae70: b.ls            #0x8dae88
    // 0x8dae74: ldr             x1, [fp, #0x10]
    // 0x8dae78: r0 = call 0x3e1be8
    //     0x8dae78: bl              #0x3e1be8
    // 0x8dae7c: LeaveFrame
    //     0x8dae7c: mov             SP, fp
    //     0x8dae80: ldp             fp, lr, [SP], #0x10
    // 0x8dae84: ret
    //     0x8dae84: ret             
    // 0x8dae88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8dae88: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8dae8c: b               #0x8dae74
  }
  [closure] bool ek(dynamic, Object?) {
    // ** addr: 0x3d713c, size: -0x1
  }
}
