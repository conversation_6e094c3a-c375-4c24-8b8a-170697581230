// lib: arj, url: Ubj

// class id: 1049311, size: 0x8
class :: {
}

// class id: 3064, size: 0x30, field offset: 0x14
class _pIa extends Mt<dynamic> {

  late nIa _NJc; // offset: 0x28

  [closure] void wCg(dynamic) {
    // ** addr: 0x5548c0, size: -0x1
  }
  [closure] pI _tCg(dynamic, aoa, int) {
    // ** addr: 0x607630, size: -0x1
  }
  [closure] void _yUf(dynamic, int) {
    // ** addr: 0x60752c, size: -0x1
  }
}

// class id: 3859, size: 0x50, field offset: 0xc
class oIa extends It {
}

// class id: 4546, size: 0x64, field offset: 0x54
class nIa extends Xra {
}
