// lib: xqj, url: ifj

// class id: 1049653, size: 0x8
class :: {
}

// class id: 780, size: 0xc, field offset: 0xc
class uUa extends OTa {

  static late final vWa iog; // offset: 0x1224

  [closure] static uUa <anonymous closure>(dynamic) {
    // ** addr: 0x4720ec, size: -0x1
  }
  [closure] static uUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>?) {
    // ** addr: 0x4721f0, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x472094, size: -0x1
  }
}
