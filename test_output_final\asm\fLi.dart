// lib: Gpj, url: fLi

// class id: 1049678, size: 0x8
class :: {
}

// class id: 749, size: 0x10, field offset: 0x8
abstract class uVa extends Object
    implements LTa {
}

// class id: 751, size: 0x18, field offset: 0x8
abstract class tVa extends Object
    implements KTa {

  tVa? *(tVa, za?) {
    // ** addr: 0x8ed2f0, size: 0x60
    // 0x8ed2f0: EnterFrame
    //     0x8ed2f0: stp             fp, lr, [SP, #-0x10]!
    //     0x8ed2f4: mov             fp, SP
    // 0x8ed2f8: ldr             x0, [fp, #0x10]
    // 0x8ed2fc: r2 = Null
    //     0x8ed2fc: mov             x2, NULL
    // 0x8ed300: r1 = Null
    //     0x8ed300: mov             x1, NULL
    // 0x8ed304: r4 = LoadClassIdInstr(r0)
    //     0x8ed304: ldur            x4, [x0, #-1]
    //     0x8ed308: ubfx            x4, x4, #0xc, #0x14
    // 0x8ed30c: r17 = 5658
    //     0x8ed30c: movz            x17, #0x161a
    // 0x8ed310: cmp             x4, x17
    // 0x8ed314: b.eq            #0x8ed32c
    // 0x8ed318: r8 = za?
    //     0x8ed318: add             x8, PP, #0x13, lsl #12  ; [pp+0x139f0] Type: za?
    //     0x8ed31c: ldr             x8, [x8, #0x9f0]
    // 0x8ed320: r3 = Null
    //     0x8ed320: add             x3, PP, #0x13, lsl #12  ; [pp+0x139f8] Null
    //     0x8ed324: ldr             x3, [x3, #0x9f8]
    // 0x8ed328: r0 = DefaultNullableTypeTest()
    //     0x8ed328: bl              #0x94d980  ; DefaultNullableTypeTestStub
    // 0x8ed32c: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x8ed32c: ldr             x0, [PP, #0x828]  ; [pp+0x828] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x8ed330: r0 = Throw()
    //     0x8ed330: bl              #0x94dd08  ; ThrowStub
    // 0x8ed334: brk             #0
  }
}

// class id: 753, size: 0x8, field offset: 0x8
abstract class sVa extends Object
    implements JTa {
}

// class id: 755, size: 0xc, field offset: 0x8
abstract class OTa extends Object
    implements ITa {
}
