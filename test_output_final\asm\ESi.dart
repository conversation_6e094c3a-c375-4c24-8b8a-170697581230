// lib: , url: ESi

// class id: 1048831, size: 0x8
class :: {
}

// class id: 4711, size: 0x10, field offset: 0x10
//   const constructor, transformed mixin,
abstract class _FG extends mr
     with GG {
}

// class id: 4712, size: 0x3c, field offset: 0x10
//   const constructor, 
class HG extends _FG {

  _Mint field_8;
  mr field_10;
  mr field_1c;
  mr field_20;
  mr field_24;
  mr field_28;
  mr field_2c;
  mr field_30;
  mr field_34;
  mr field_38;
  _OneByteString field_14;
}
