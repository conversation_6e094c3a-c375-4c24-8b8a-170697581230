// lib: Mpj, url: Ejj

// class id: 1049692, size: 0x8
class :: {
}

// class id: 733, size: 0x14, field offset: 0x8
class UVa extends QVa {

  static late final vWa iog; // offset: 0x118c
  late int _csg; // offset: 0xc

  [closure] static (dynamic) => UVa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x461f84, size: -0x1
  }
  [closure] static UVa <anonymous closure>(dynamic) {
    // ** addr: 0x462020, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x461f20, size: -0x1
  }
}
