// lib: , url: GYi

// class id: 1049137, size: 0x8
class :: {
}

// class id: 1800, size: 0x8, field offset: 0x8
abstract class dja extends Object {
}

// class id: 1801, size: 0x10, field offset: 0x8
class cja extends Object {

  static late final cja _Alc; // offset: 0xbb4

  [closure] Future<dynamic> _TJe(dynamic, MethodCall) {
    // ** addr: 0x53e63c, size: -0x1
  }
}

// class id: 5485, size: 0x14, field offset: 0x14
enum bja extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
