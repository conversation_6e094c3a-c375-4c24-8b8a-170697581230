// lib: ipj, url: Gij

// class id: 1049613, size: 0x8
class :: {
}

// class id: 868, size: 0x14, field offset: 0x14
class LSa extends HRa {

  static late final vWa iog; // offset: 0x10ec

  [closure] static (dynamic) => LSa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x4839bc, size: -0x1
  }
  [closure] static LSa <anonymous closure>(dynamic) {
    // ** addr: 0x483a10, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x483958, size: -0x1
  }
}
