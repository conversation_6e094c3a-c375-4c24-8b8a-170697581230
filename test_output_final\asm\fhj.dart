// lib: , url: Fhj

// class id: 1049580, size: 0x8
class :: {
}

// class id: 878, size: 0x18, field offset: 0x8
class eRa extends Object
    implements cRa<X0 bound dRa> {

  late Pb<dRa> _XPg; // offset: 0x10
  late dRa VPg; // offset: 0x14

  [closure] void _YPg(dynamic) {
    // ** addr: 0x649430, size: -0x1
  }
}

// class id: 879, size: 0x1c, field offset: 0x8
//   const constructor, 
class dRa extends Object {
}

// class id: 880, size: 0xc, field offset: 0x8
abstract class cRa<X0 bound dRa> extends Object {
}
