// lib: Opj, url: Fjj

// class id: 1049693, size: 0x8
class :: {
}

// class id: 732, size: 0xc, field offset: 0x8
class WVa extends QVa {

  static late final vWa iog; // offset: 0x1194

  [closure] static (dynamic) => WVa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x461748, size: -0x1
  }
  [closure] static WVa <anonymous closure>(dynamic) {
    // ** addr: 0x46179c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x4616e4, size: -0x1
  }
}
