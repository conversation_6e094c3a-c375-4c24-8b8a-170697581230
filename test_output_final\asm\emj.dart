// lib: Ioj, url: emj

// class id: 1049820, size: 0x8
class :: {
}

// class id: 447, size: 0x8, field offset: 0x8
abstract class _Zcb extends Object
    implements ce, Rab {
}

// class id: 448, size: 0x8, field offset: 0x8
abstract class Ucb extends Object
    implements Scb {
}

// class id: 449, size: 0x8, field offset: 0x8
abstract class Tcb extends Object
    implements Scb {
}

// class id: 450, size: 0x8, field offset: 0x8
abstract class Scb extends Object
    implements nI {
}

// class id: 451, size: 0x8, field offset: 0x8
abstract class ecb extends Object
    implements Scb {
}

// class id: 452, size: 0x8, field offset: 0x8
abstract class dcb extends Object
    implements Scb {
}

// class id: 453, size: 0x8, field offset: 0x8
abstract class ccb extends Object
    implements Scb {
}

// class id: 454, size: 0x8, field offset: 0x8
abstract class bcb extends Object
    implements Scb {
}

// class id: 455, size: 0x8, field offset: 0x8
abstract class acb extends Object
    implements Scb {
}

// class id: 456, size: 0x8, field offset: 0x8
abstract class Zbb extends Object
    implements Scb {
}

// class id: 457, size: 0x8, field offset: 0x8
abstract class Ybb extends Object
    implements Scb {
}

// class id: 458, size: 0x8, field offset: 0x8
abstract class Xbb extends Object
    implements Tcb {
}

// class id: 459, size: 0x8, field offset: 0x8
abstract class Wbb extends Object
    implements Tcb {
}

// class id: 460, size: 0x8, field offset: 0x8
abstract class Vbb extends Object
    implements Tcb {
}

// class id: 461, size: 0x8, field offset: 0x8
abstract class Ubb extends Object
    implements Tcb {
}

// class id: 462, size: 0x8, field offset: 0x8
abstract class Tbb extends Object
    implements Tcb {
}

// class id: 463, size: 0x8, field offset: 0x8
abstract class Sbb extends Object
    implements Tcb {
}

// class id: 464, size: 0x8, field offset: 0x8
abstract class Rbb extends Object
    implements Tcb {
}

// class id: 465, size: 0x8, field offset: 0x8
abstract class Qbb extends Object
    implements Ucb {
}

// class id: 466, size: 0x8, field offset: 0x8
abstract class Pbb extends Object
    implements Ucb {
}

// class id: 467, size: 0x8, field offset: 0x8
abstract class Obb extends Object
    implements Ucb {
}

// class id: 468, size: 0x8, field offset: 0x8
abstract class Nbb extends Object
    implements Ucb {
}

// class id: 469, size: 0x8, field offset: 0x8
abstract class Mbb extends Object
    implements Ucb {
}

// class id: 470, size: 0x8, field offset: 0x8
abstract class Lbb extends Object
    implements Ucb {
}

// class id: 471, size: 0x8, field offset: 0x8
abstract class Kbb extends Object
    implements Scb {
}

// class id: 472, size: 0x8, field offset: 0x8
abstract class _Ibb extends Object
    implements Xab {
}

// class id: 473, size: 0x8, field offset: 0x8
abstract class Dbb extends Object {
}

// class id: 475, size: 0x8, field offset: 0x8
abstract class _nbb extends Object
    implements Xab {
}

// class id: 476, size: 0x8, field offset: 0x8
abstract class _lbb extends Object
    implements Xab {
}

// class id: 477, size: 0x8, field offset: 0x8
abstract class dbb extends Object
    implements _kab {
}

// class id: 478, size: 0x8, field offset: 0x8
abstract class _bbb extends Object
    implements Xab {
}

// class id: 479, size: 0x8, field offset: 0x8
abstract class _Yab extends Object
    implements dbb, _kab {
}

// class id: 480, size: 0x18, field offset: 0x8
class _Tab extends Object {
}

// class id: 481, size: 0x8, field offset: 0x8
abstract class Sab extends Object {
}

// class id: 482, size: 0x8, field offset: 0x8
abstract class Rab extends Object {
}

// class id: 483, size: 0x8, field offset: 0x8
abstract class Qab extends Object {
}

// class id: 484, size: 0x8, field offset: 0x8
abstract class _Oab extends Object
    implements Qab {
}

// class id: 485, size: 0x10, field offset: 0x8
class Lab extends Object
    implements Ja {
}

// class id: 486, size: 0x8, field offset: 0x8
abstract class _Kab extends Object
    implements Jab {
}

// class id: 487, size: 0x8, field offset: 0x8
abstract class Jab extends Object {
}

// class id: 488, size: 0x8, field offset: 0x8
abstract class _Iab extends Object
    implements ce, _Gab {
}

// class id: 489, size: 0x8, field offset: 0x8
abstract class _Gab extends Object
    implements ce, Sab {
}

// class id: 490, size: 0x8, field offset: 0x8
abstract class Dab extends Object {
}

// class id: 491, size: 0x8, field offset: 0x8
abstract class Cab extends Dab {
}

// class id: 492, size: 0x14, field offset: 0x8
class _Eab extends Cab {

  int dyn:get:length(_Eab) {
    // ** addr: 0x92f258, size: 0x60
    // 0x92f258: EnterFrame
    //     0x92f258: stp             fp, lr, [SP, #-0x10]!
    //     0x92f25c: mov             fp, SP
    // 0x92f260: CheckStackOverflow
    //     0x92f260: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92f264: cmp             SP, x16
    //     0x92f268: b.ls            #0x92f298
    // 0x92f26c: ldr             x1, [fp, #0x10]
    // 0x92f270: r0 = call 0x6b77dc
    //     0x92f270: bl              #0x6b77dc
    // 0x92f274: mov             x2, x0
    // 0x92f278: r0 = BoxInt64Instr(r2)
    //     0x92f278: sbfiz           x0, x2, #1, #0x1f
    //     0x92f27c: cmp             x2, x0, asr #1
    //     0x92f280: b.eq            #0x92f28c
    //     0x92f284: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x92f288: stur            x2, [x0, #7]
    // 0x92f28c: LeaveFrame
    //     0x92f28c: mov             SP, fp
    //     0x92f290: ldp             fp, lr, [SP], #0x10
    // 0x92f294: ret
    //     0x92f294: ret             
    // 0x92f298: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92f298: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92f29c: b               #0x92f26c
  }
  [closure] void <anonymous closure>(dynamic, String, String) {
    // ** addr: 0x7171d4, size: -0x1
  }
}

// class id: 495, size: 0x8, field offset: 0x8
abstract class wab extends Object
    implements XZa {
}

// class id: 504, size: 0x8, field offset: 0x8
abstract class _kab extends Object {
}

// class id: 505, size: 0x8, field offset: 0x8
abstract class jab extends Object {
}

// class id: 506, size: 0x8, field offset: 0x8
//   transformed mixin,
abstract class _iab extends jab
     with _kab {
}

// class id: 507, size: 0xc, field offset: 0x8
class lab extends _iab {
}

// class id: 511, size: 0x8, field offset: 0x8
abstract class bab extends Object
    implements XZa {
}

// class id: 518, size: 0x8, field offset: 0x8
abstract class YZa extends Object {
}

// class id: 519, size: 0x8, field offset: 0x8
abstract class XZa extends Object {
}

// class id: 520, size: 0x18, field offset: 0x8
abstract class ce extends XZa {
}

// class id: 521, size: 0x18, field offset: 0x18
class bdb extends ce {
}

// class id: 522, size: 0x18, field offset: 0x18
//   transformed mixin,
abstract class _Xcb extends ce
     with _Oab {
}

// class id: 523, size: 0x18, field offset: 0x18
//   transformed mixin,
abstract class _Ycb extends _Xcb
     with _Zcb {
}

// class id: 524, size: 0x1c, field offset: 0x18
abstract class adb extends _Ycb
    implements Qab, Rab {

  int? dyn:get:length(adb) {
    // ** addr: 0x92ec9c, size: 0x60
    // 0x92ec9c: EnterFrame
    //     0x92ec9c: stp             fp, lr, [SP, #-0x10]!
    //     0x92eca0: mov             fp, SP
    // 0x92eca4: CheckStackOverflow
    //     0x92eca4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92eca8: cmp             SP, x16
    //     0x92ecac: b.ls            #0x92ecdc
    // 0x92ecb0: ldr             x1, [fp, #0x10]
    // 0x92ecb4: r0 = call 0x6b5388
    //     0x92ecb4: bl              #0x6b5388
    // 0x92ecb8: mov             x2, x0
    // 0x92ecbc: r0 = BoxInt64Instr(r2)
    //     0x92ecbc: sbfiz           x0, x2, #1, #0x1f
    //     0x92ecc0: cmp             x2, x0, asr #1
    //     0x92ecc4: b.eq            #0x92ecd0
    //     0x92ecc8: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x92eccc: stur            x2, [x0, #7]
    // 0x92ecd0: LeaveFrame
    //     0x92ecd0: mov             SP, fp
    //     0x92ecd4: ldp             fp, lr, [SP], #0x10
    // 0x92ecd8: ret
    //     0x92ecd8: ret             
    // 0x92ecdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92ecdc: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92ece0: b               #0x92ecb0
  }
}

// class id: 525, size: 0x1c, field offset: 0x1c
class qwa extends adb {

  int dyn:get:length(qwa) {
    // ** addr: 0x92ed08, size: 0x60
    // 0x92ed08: EnterFrame
    //     0x92ed08: stp             fp, lr, [SP, #-0x10]!
    //     0x92ed0c: mov             fp, SP
    // 0x92ed10: CheckStackOverflow
    //     0x92ed10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92ed14: cmp             SP, x16
    //     0x92ed18: b.ls            #0x92ed48
    // 0x92ed1c: ldr             x1, [fp, #0x10]
    // 0x92ed20: r0 = call 0x6b5388
    //     0x92ed20: bl              #0x6b5388
    // 0x92ed24: mov             x2, x0
    // 0x92ed28: r0 = BoxInt64Instr(r2)
    //     0x92ed28: sbfiz           x0, x2, #1, #0x1f
    //     0x92ed2c: cmp             x2, x0, asr #1
    //     0x92ed30: b.eq            #0x92ed3c
    //     0x92ed34: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x92ed38: stur            x2, [x0, #7]
    // 0x92ed3c: LeaveFrame
    //     0x92ed3c: mov             SP, fp
    //     0x92ed40: ldp             fp, lr, [SP], #0x10
    // 0x92ed44: ret
    //     0x92ed44: ret             
    // 0x92ed48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92ed48: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92ed4c: b               #0x92ed1c
  }
}

// class id: 526, size: 0x1c, field offset: 0x1c
class rKa extends adb {
}

// class id: 527, size: 0x18, field offset: 0x18
//   transformed mixin,
abstract class _Mab extends ce
     with bab {
}

// class id: 528, size: 0x18, field offset: 0x18
//   transformed mixin,
abstract class _Nab extends _Mab
     with _Oab {
}

// class id: 529, size: 0x20, field offset: 0x18
//   transformed mixin,
abstract class _Pab extends _Nab
     with _Gab {
}

// class id: 530, size: 0x34, field offset: 0x20
abstract class nI extends _Pab
    implements Qab, Rab, Sab {

  static late final RegExp _Inf; // offset: 0xe84
  late final PZa Mnf; // offset: 0x30
}

// class id: 532, size: 0x34, field offset: 0x34
abstract class Xab extends nI
    implements Dbb {
}

// class id: 533, size: 0x34, field offset: 0x34
class Qcb extends Xab {
}

// class id: 534, size: 0x34, field offset: 0x34
class Pcb extends Xab {
}

// class id: 535, size: 0x34, field offset: 0x34
class Ocb extends Xab {
}

// class id: 536, size: 0x34, field offset: 0x34
class Ncb extends Xab {
}

// class id: 537, size: 0x34, field offset: 0x34
class Mcb extends Xab {
}

// class id: 538, size: 0x34, field offset: 0x34
class Kcb extends Xab {
}

// class id: 539, size: 0x34, field offset: 0x34
class Jcb extends Xab {
}

// class id: 540, size: 0x34, field offset: 0x34
class Icb extends Xab {
}

// class id: 541, size: 0x34, field offset: 0x34
class Hcb extends Xab {
}

// class id: 542, size: 0x34, field offset: 0x34
class Gcb extends Xab {
}

// class id: 543, size: 0x34, field offset: 0x34
class Rya extends Xab {
}

// class id: 544, size: 0x34, field offset: 0x34
class Fcb extends Xab {
}

// class id: 545, size: 0x34, field offset: 0x34
class Ecb extends Xab {
}

// class id: 546, size: 0x34, field offset: 0x34
class Dcb extends Xab {
}

// class id: 547, size: 0x34, field offset: 0x34
class Ccb extends Xab {
}

// class id: 548, size: 0x34, field offset: 0x34
class Bcb extends Xab {
}

// class id: 549, size: 0x34, field offset: 0x34
class Acb extends Xab {
}

// class id: 550, size: 0x34, field offset: 0x34
class ycb extends Xab {
}

// class id: 551, size: 0x34, field offset: 0x34
class xcb extends Xab {
}

// class id: 552, size: 0x34, field offset: 0x34
class wcb extends Xab {
}

// class id: 553, size: 0x34, field offset: 0x34
class vcb extends Xab {
}

// class id: 554, size: 0x34, field offset: 0x34
class ucb extends Xab {
}

// class id: 555, size: 0x34, field offset: 0x34
class tcb extends Xab {
}

// class id: 556, size: 0x34, field offset: 0x34
class rcb extends Xab {
}

// class id: 557, size: 0x34, field offset: 0x34
class qcb extends Xab {
}

// class id: 558, size: 0x34, field offset: 0x34
class ocb extends Xab {
}

// class id: 559, size: 0x34, field offset: 0x34
class ncb extends Xab {
}

// class id: 560, size: 0x34, field offset: 0x34
class mcb extends Xab {
}

// class id: 561, size: 0x34, field offset: 0x34
class lcb extends Xab {
}

// class id: 562, size: 0x34, field offset: 0x34
//   transformed mixin,
abstract class _icb extends Xab
     with _bbb {
}

// class id: 563, size: 0x34, field offset: 0x34
//   transformed mixin,
abstract class _jcb extends _icb
     with _lbb {
}

// class id: 564, size: 0x34, field offset: 0x34
class kcb extends _jcb {
}

// class id: 565, size: 0x34, field offset: 0x34
class hcb extends Xab {
}

// class id: 566, size: 0x34, field offset: 0x34
class Gbb extends Xab
    implements YZa {
}

// class id: 567, size: 0x34, field offset: 0x34
class Fbb extends Xab {
}

// class id: 568, size: 0x34, field offset: 0x34
class Ebb extends Xab {
}

// class id: 569, size: 0x34, field offset: 0x34
class Cbb extends Xab {
}

// class id: 570, size: 0x34, field offset: 0x34
class Bbb extends Xab {
}

// class id: 571, size: 0x34, field offset: 0x34
class Abb extends Xab {
}

// class id: 572, size: 0x34, field offset: 0x34
class zbb extends Xab {

  int? dyn:get:length(zbb) {
    // ** addr: 0x92efec, size: 0x60
    // 0x92efec: EnterFrame
    //     0x92efec: stp             fp, lr, [SP, #-0x10]!
    //     0x92eff0: mov             fp, SP
    // 0x92eff4: CheckStackOverflow
    //     0x92eff4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92eff8: cmp             SP, x16
    //     0x92effc: b.ls            #0x92f02c
    // 0x92f000: ldr             x1, [fp, #0x10]
    // 0x92f004: r0 = call 0x6b6b4c
    //     0x92f004: bl              #0x6b6b4c
    // 0x92f008: mov             x2, x0
    // 0x92f00c: r0 = BoxInt64Instr(r2)
    //     0x92f00c: sbfiz           x0, x2, #1, #0x1f
    //     0x92f010: cmp             x2, x0, asr #1
    //     0x92f014: b.eq            #0x92f020
    //     0x92f018: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x92f01c: stur            x2, [x0, #7]
    // 0x92f020: LeaveFrame
    //     0x92f020: mov             SP, fp
    //     0x92f024: ldp             fp, lr, [SP], #0x10
    // 0x92f028: ret
    //     0x92f028: ret             
    // 0x92f02c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92f02c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92f030: b               #0x92f000
  }
  [closure] bool <anonymous closure>(dynamic, Jbb) {
    // ** addr: 0x6b6c44, size: -0x1
  }
}

// class id: 573, size: 0x34, field offset: 0x34
//   transformed mixin,
abstract class _xbb extends Xab
     with _nbb {
}

// class id: 574, size: 0x34, field offset: 0x34
class pcb extends _xbb {
}

// class id: 575, size: 0x34, field offset: 0x34
class gcb extends _xbb {
}

// class id: 576, size: 0x34, field offset: 0x34
class fcb extends _xbb {
}

// class id: 577, size: 0x34, field offset: 0x34
class ybb extends _xbb {
}

// class id: 578, size: 0x34, field offset: 0x34
class wbb extends Xab {
}

// class id: 579, size: 0x34, field offset: 0x34
class ubb extends Xab {
}

// class id: 580, size: 0x34, field offset: 0x34
class tbb extends Xab {
}

// class id: 581, size: 0x34, field offset: 0x34
class sbb extends Xab {
}

// class id: 582, size: 0x34, field offset: 0x34
class rbb extends Xab {
}

// class id: 583, size: 0x34, field offset: 0x34
class qbb extends Xab {
}

// class id: 584, size: 0x34, field offset: 0x34
class pbb extends Xab
    implements YZa {
}

// class id: 585, size: 0x34, field offset: 0x34
//   transformed mixin,
abstract class _kbb extends Xab
     with _lbb {
}

// class id: 586, size: 0x34, field offset: 0x34
//   transformed mixin,
abstract class _mbb extends _kbb
     with _nbb {
}

// class id: 587, size: 0x34, field offset: 0x34
class scb extends _mbb {
}

// class id: 588, size: 0x34, field offset: 0x34
//   transformed mixin,
abstract class _Hbb extends _mbb
     with _Ibb {
}

// class id: 589, size: 0x34, field offset: 0x34
class Lcb extends _Hbb {
}

// class id: 590, size: 0x34, field offset: 0x34
class zcb extends _Hbb {

  int? dyn:get:length(zcb) {
    // ** addr: 0x92ee58, size: 0x60
    // 0x92ee58: EnterFrame
    //     0x92ee58: stp             fp, lr, [SP, #-0x10]!
    //     0x92ee5c: mov             fp, SP
    // 0x92ee60: CheckStackOverflow
    //     0x92ee60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92ee64: cmp             SP, x16
    //     0x92ee68: b.ls            #0x92ee98
    // 0x92ee6c: ldr             x1, [fp, #0x10]
    // 0x92ee70: r0 = call 0x6b6894
    //     0x92ee70: bl              #0x6b6894
    // 0x92ee74: mov             x2, x0
    // 0x92ee78: r0 = BoxInt64Instr(r2)
    //     0x92ee78: sbfiz           x0, x2, #1, #0x1f
    //     0x92ee7c: cmp             x2, x0, asr #1
    //     0x92ee80: b.eq            #0x92ee8c
    //     0x92ee84: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x92ee88: stur            x2, [x0, #7]
    // 0x92ee8c: LeaveFrame
    //     0x92ee8c: mov             SP, fp
    //     0x92ee90: ldp             fp, lr, [SP], #0x10
    // 0x92ee94: ret
    //     0x92ee94: ret             
    // 0x92ee98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92ee98: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92ee9c: b               #0x92ee6c
  }
}

// class id: 591, size: 0x34, field offset: 0x34
class Jbb extends _Hbb
    implements Kbb, Lbb, Mbb, Nbb, Obb, Pbb, Qbb, Rbb, Sbb, Tbb, Ubb, Vbb, Wbb, Xbb, Ybb, Zbb, acb, bcb, ccb, dcb, ecb {
}

// class id: 592, size: 0x34, field offset: 0x34
class obb extends _mbb {
}

// class id: 593, size: 0x34, field offset: 0x34
class jbb extends Xab {
}

// class id: 594, size: 0x34, field offset: 0x34
class ibb extends Xab
    implements yab {
}

// class id: 595, size: 0x34, field offset: 0x34
class hbb extends Xab {
}

// class id: 596, size: 0x34, field offset: 0x34
abstract class gbb extends Xab {
}

// class id: 597, size: 0x34, field offset: 0x34
class Rcb extends gbb
    implements YZa {
}

// class id: 598, size: 0x34, field offset: 0x34
class fbb extends gbb {
}

// class id: 599, size: 0x34, field offset: 0x34
//   transformed mixin,
abstract class _Wab extends Xab
     with _Yab {
}

// class id: 600, size: 0x34, field offset: 0x34
//   transformed mixin,
abstract class _Zab extends _Wab
     with _kab {
}

// class id: 601, size: 0x34, field offset: 0x34
//   transformed mixin,
abstract class _abb extends _Zab
     with _bbb {
}

// class id: 602, size: 0x34, field offset: 0x34
class ebb extends _abb
    implements dbb {
}

// class id: 603, size: 0x34, field offset: 0x34
class cbb extends _abb
    implements dbb {
}

// class id: 604, size: 0x20, field offset: 0x18
//   transformed mixin,
abstract class _Fab extends ce
     with _Gab {
}

// class id: 605, size: 0x20, field offset: 0x20
//   transformed mixin,
abstract class _Hab extends _Fab
     with _Iab {
}

// class id: 606, size: 0x20, field offset: 0x20
class oKa extends _Hab {
}

// class id: 607, size: 0x24, field offset: 0x20
abstract class nKa extends _Hab {

  [closure] void <anonymous closure>(dynamic, nI) {
    // ** addr: 0x6b46fc, size: -0x1
  }
}

// class id: 608, size: 0x24, field offset: 0x24
class cdb extends nKa {
}

// class id: 609, size: 0x24, field offset: 0x24
//   transformed mixin,
abstract class _Vcb extends nKa
     with _Kab {
}

// class id: 610, size: 0x24, field offset: 0x24
class Wcb extends _Vcb
    implements Jab {
}

// class id: 612, size: 0x8, field offset: 0x8
abstract class yab extends XZa {
}

// class id: 613, size: 0x8, field offset: 0x8
//   transformed mixin,
abstract class _vab extends XZa
     with wab {
}

// class id: 614, size: 0x18, field offset: 0x8
class xab extends _vab
    implements yab {

  late lab Swb; // offset: 0x14
  late final nKa Cof; // offset: 0x10
}

// class id: 5727, size: 0x10, field offset: 0xc
class _Uab extends B<dynamic> {

  int length(_Uab) {
    // ** addr: 0x8fd50c, size: 0xa4
    // 0x8fd50c: EnterFrame
    //     0x8fd50c: stp             fp, lr, [SP, #-0x10]!
    //     0x8fd510: mov             fp, SP
    // 0x8fd514: ldr             x2, [fp, #0x10]
    // 0x8fd518: LoadField: r3 = r2->field_b
    //     0x8fd518: ldur            w3, [x2, #0xb]
    // 0x8fd51c: DecompressPointer r3
    //     0x8fd51c: add             x3, x3, HEAP, lsl #32
    // 0x8fd520: ArrayLoad: r2 = r3[0]  ; List_4
    //     0x8fd520: ldur            w2, [x3, #0x17]
    // 0x8fd524: DecompressPointer r2
    //     0x8fd524: add             x2, x2, HEAP, lsl #32
    // 0x8fd528: mov             x3, x2
    // 0x8fd52c: r2 = 0
    //     0x8fd52c: movz            x2, #0
    // 0x8fd530: CheckStackOverflow
    //     0x8fd530: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fd534: cmp             SP, x16
    //     0x8fd538: b.ls            #0x8fd590
    // 0x8fd53c: cmp             w3, NULL
    // 0x8fd540: b.eq            #0x8fd570
    // 0x8fd544: r4 = LoadClassIdInstr(r3)
    //     0x8fd544: ldur            x4, [x3, #-1]
    //     0x8fd548: ubfx            x4, x4, #0xc, #0x14
    // 0x8fd54c: sub             x16, x4, #0x215
    // 0x8fd550: cmp             x16, #0x46
    // 0x8fd554: b.hi            #0x8fd560
    // 0x8fd558: add             x4, x2, #1
    // 0x8fd55c: mov             x2, x4
    // 0x8fd560: LoadField: r0 = r3->field_f
    //     0x8fd560: ldur            w0, [x3, #0xf]
    // 0x8fd564: DecompressPointer r0
    //     0x8fd564: add             x0, x0, HEAP, lsl #32
    // 0x8fd568: mov             x3, x0
    // 0x8fd56c: b               #0x8fd530
    // 0x8fd570: r0 = BoxInt64Instr(r2)
    //     0x8fd570: sbfiz           x0, x2, #1, #0x1f
    //     0x8fd574: cmp             x2, x0, asr #1
    //     0x8fd578: b.eq            #0x8fd584
    //     0x8fd57c: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8fd580: stur            x2, [x0, #7]
    // 0x8fd584: LeaveFrame
    //     0x8fd584: mov             SP, fp
    //     0x8fd588: ldp             fp, lr, [SP], #0x10
    // 0x8fd58c: ret
    //     0x8fd58c: ret             
    // 0x8fd590: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fd590: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fd594: b               #0x8fd53c
  }
  void []=(_Uab, int, nI) {
    // ** addr: 0x8fd5b0, size: 0x94
    // 0x8fd5b0: EnterFrame
    //     0x8fd5b0: stp             fp, lr, [SP, #-0x10]!
    //     0x8fd5b4: mov             fp, SP
    // 0x8fd5b8: AllocStack(0x18)
    //     0x8fd5b8: sub             SP, SP, #0x18
    // 0x8fd5bc: CheckStackOverflow
    //     0x8fd5bc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fd5c0: cmp             SP, x16
    //     0x8fd5c4: b.ls            #0x8fd624
    // 0x8fd5c8: ldr             x0, [fp, #0x18]
    // 0x8fd5cc: r2 = Null
    //     0x8fd5cc: mov             x2, NULL
    // 0x8fd5d0: r1 = Null
    //     0x8fd5d0: mov             x1, NULL
    // 0x8fd5d4: branchIfSmi(r0, 0x8fd5fc)
    //     0x8fd5d4: tbz             w0, #0, #0x8fd5fc
    // 0x8fd5d8: r4 = LoadClassIdInstr(r0)
    //     0x8fd5d8: ldur            x4, [x0, #-1]
    //     0x8fd5dc: ubfx            x4, x4, #0xc, #0x14
    // 0x8fd5e0: sub             x4, x4, #0x3b
    // 0x8fd5e4: cmp             x4, #1
    // 0x8fd5e8: b.ls            #0x8fd5fc
    // 0x8fd5ec: r8 = int
    //     0x8fd5ec: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8fd5f0: r3 = Null
    //     0x8fd5f0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ef28] Null
    //     0x8fd5f4: ldr             x3, [x3, #0xf28]
    // 0x8fd5f8: r0 = int()
    //     0x8fd5f8: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8fd5fc: ldr             x16, [fp, #0x20]
    // 0x8fd600: ldr             lr, [fp, #0x18]
    // 0x8fd604: stp             lr, x16, [SP, #8]
    // 0x8fd608: ldr             x16, [fp, #0x10]
    // 0x8fd60c: str             x16, [SP]
    // 0x8fd610: r0 = call 0x308bd4
    //     0x8fd610: bl              #0x308bd4
    // 0x8fd614: r0 = Null
    //     0x8fd614: mov             x0, NULL
    // 0x8fd618: LeaveFrame
    //     0x8fd618: mov             SP, fp
    //     0x8fd61c: ldp             fp, lr, [SP], #0x10
    // 0x8fd620: ret
    //     0x8fd620: ret             
    // 0x8fd624: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fd624: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fd628: b               #0x8fd5c8
  }
  nI [](_Uab, int) {
    // ** addr: 0x8fd644, size: 0x88
    // 0x8fd644: EnterFrame
    //     0x8fd644: stp             fp, lr, [SP, #-0x10]!
    //     0x8fd648: mov             fp, SP
    // 0x8fd64c: AllocStack(0x10)
    //     0x8fd64c: sub             SP, SP, #0x10
    // 0x8fd650: CheckStackOverflow
    //     0x8fd650: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fd654: cmp             SP, x16
    //     0x8fd658: b.ls            #0x8fd6ac
    // 0x8fd65c: ldr             x0, [fp, #0x10]
    // 0x8fd660: r2 = Null
    //     0x8fd660: mov             x2, NULL
    // 0x8fd664: r1 = Null
    //     0x8fd664: mov             x1, NULL
    // 0x8fd668: branchIfSmi(r0, 0x8fd690)
    //     0x8fd668: tbz             w0, #0, #0x8fd690
    // 0x8fd66c: r4 = LoadClassIdInstr(r0)
    //     0x8fd66c: ldur            x4, [x0, #-1]
    //     0x8fd670: ubfx            x4, x4, #0xc, #0x14
    // 0x8fd674: sub             x4, x4, #0x3b
    // 0x8fd678: cmp             x4, #1
    // 0x8fd67c: b.ls            #0x8fd690
    // 0x8fd680: r8 = int
    //     0x8fd680: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8fd684: r3 = Null
    //     0x8fd684: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ef50] Null
    //     0x8fd688: ldr             x3, [x3, #0xf50]
    // 0x8fd68c: r0 = int()
    //     0x8fd68c: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8fd690: ldr             x16, [fp, #0x18]
    // 0x8fd694: ldr             lr, [fp, #0x10]
    // 0x8fd698: stp             lr, x16, [SP]
    // 0x8fd69c: r0 = call 0x30af24
    //     0x8fd69c: bl              #0x30af24
    // 0x8fd6a0: LeaveFrame
    //     0x8fd6a0: mov             SP, fp
    //     0x8fd6a4: ldp             fp, lr, [SP], #0x10
    // 0x8fd6a8: ret
    //     0x8fd6a8: ret             
    // 0x8fd6ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fd6ac: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fd6b0: b               #0x8fd65c
  }
}

// class id: 5775, size: 0x14, field offset: 0xc
class _Vab extends ja<dynamic> {
}
