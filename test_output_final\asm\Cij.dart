// lib: gpj, url: Cij

// class id: 1049609, size: 0x8
class :: {
}

// class id: 827, size: 0x54, field offset: 0x30
class DSa extends vSa {

  static late final vWa iog; // offset: 0x10e4
  late Uint8List _pYc; // offset: 0x40
  late Uint8List _vpg; // offset: 0x30
  late int _zpg; // offset: 0x44
  late Uint8List _ypg; // offset: 0x3c
  late Uint8List _xpg; // offset: 0x38
  late Uint8List _wpg; // offset: 0x34

  [closure] static (dynamic) => DSa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x48377c, size: -0x1
  }
  [closure] static DSa <anonymous closure>(dynamic) {
    // ** addr: 0x4837d0, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x483718, size: -0x1
  }
}
