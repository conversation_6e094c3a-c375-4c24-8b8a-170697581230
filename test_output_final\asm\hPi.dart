// lib: , url: HPi

// class id: 1048693, size: 0x8
class :: {
}

// class id: 3353, size: 0x30, field offset: 0x30
//   transformed mixin,
abstract class _Oy extends Wu<dynamic>
     with ov<X0 bound It> {

  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x8fc354, size: 0x2ec
    // 0x8fc354: EnterFrame
    //     0x8fc354: stp             fp, lr, [SP, #-0x10]!
    //     0x8fc358: mov             fp, SP
    // 0x8fc35c: AllocStack(0x38)
    //     0x8fc35c: sub             SP, SP, #0x38
    // 0x8fc360: SetupParameters(_Oy this /* r1 */)
    //     0x8fc360: stur            NULL, [fp, #-8]
    //     0x8fc364: movz            x0, #0
    //     0x8fc368: add             x1, fp, w0, sxtw #2
    //     0x8fc36c: ldr             x1, [x1, #0x10]
    //     0x8fc370: ldur            w2, [x1, #0x17]
    //     0x8fc374: add             x2, x2, HEAP, lsl #32
    //     0x8fc378: stur            x2, [fp, #-0x10]
    // 0x8fc37c: CheckStackOverflow
    //     0x8fc37c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fc380: cmp             SP, x16
    //     0x8fc384: b.ls            #0x8fc630
    // 0x8fc388: InitAsync() -> Future<void?>
    //     0x8fc388: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x8fc38c: bl              #0x8c1de0  ; InitAsyncStub
    // 0x8fc390: r1 = "zzqqzf"
    //     0x8fc390: add             x1, PP, #0x17, lsl #12  ; [pp+0x170b0] "zzqqzf"
    //     0x8fc394: ldr             x1, [x1, #0xb0]
    // 0x8fc398: r0 = call 0x3f8834
    //     0x8fc398: bl              #0x3f8834
    // 0x8fc39c: mov             x1, x0
    // 0x8fc3a0: r0 = call 0x4d9e48
    //     0x8fc3a0: bl              #0x4d9e48
    // 0x8fc3a4: ldur            x2, [fp, #-0x10]
    // 0x8fc3a8: LoadField: r3 = r2->field_b
    //     0x8fc3a8: ldur            w3, [x2, #0xb]
    // 0x8fc3ac: DecompressPointer r3
    //     0x8fc3ac: add             x3, x3, HEAP, lsl #32
    // 0x8fc3b0: stur            x3, [fp, #-0x18]
    // 0x8fc3b4: LoadField: r4 = r3->field_1b
    //     0x8fc3b4: ldur            w4, [x3, #0x1b]
    // 0x8fc3b8: DecompressPointer r4
    //     0x8fc3b8: add             x4, x4, HEAP, lsl #32
    // 0x8fc3bc: ArrayLoad: r0 = r3[0]  ; List_4
    //     0x8fc3bc: ldur            w0, [x3, #0x17]
    // 0x8fc3c0: DecompressPointer r0
    //     0x8fc3c0: add             x0, x0, HEAP, lsl #32
    // 0x8fc3c4: LoadField: r1 = r4->field_b
    //     0x8fc3c4: ldur            w1, [x4, #0xb]
    // 0x8fc3c8: DecompressPointer r1
    //     0x8fc3c8: add             x1, x1, HEAP, lsl #32
    // 0x8fc3cc: r5 = LoadInt32Instr(r0)
    //     0x8fc3cc: sbfx            x5, x0, #1, #0x1f
    //     0x8fc3d0: tbz             w0, #0, #0x8fc3d8
    //     0x8fc3d4: ldur            x5, [x0, #7]
    // 0x8fc3d8: r0 = LoadInt32Instr(r1)
    //     0x8fc3d8: sbfx            x0, x1, #1, #0x1f
    // 0x8fc3dc: mov             x1, x5
    // 0x8fc3e0: cmp             x1, x0
    // 0x8fc3e4: b.hs            #0x8fc638
    // 0x8fc3e8: LoadField: r0 = r4->field_f
    //     0x8fc3e8: ldur            w0, [x4, #0xf]
    // 0x8fc3ec: DecompressPointer r0
    //     0x8fc3ec: add             x0, x0, HEAP, lsl #32
    // 0x8fc3f0: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x8fc3f0: add             x16, x0, x5, lsl #2
    //     0x8fc3f4: ldur            w1, [x16, #0xf]
    // 0x8fc3f8: DecompressPointer r1
    //     0x8fc3f8: add             x1, x1, HEAP, lsl #32
    // 0x8fc3fc: r16 = "channel"
    //     0x8fc3fc: ldr             x16, [PP, #0x7a88]  ; [pp+0x7a88] "channel"
    // 0x8fc400: stp             x16, x1, [SP]
    // 0x8fc404: r4 = 0
    //     0x8fc404: movz            x4, #0
    // 0x8fc408: ldr             x0, [SP, #8]
    // 0x8fc40c: r5 = UnlinkedCall_0x2d3c80
    //     0x8fc40c: add             x16, PP, #0x17, lsl #12  ; [pp+0x170b8] UnlinkedCall: 0x2d3c80 - SwitchableCallMissStub
    //     0x8fc410: ldp             x5, lr, [x16, #0xb8]
    // 0x8fc414: blr             lr
    // 0x8fc418: r1 = 59
    //     0x8fc418: movz            x1, #0x3b
    // 0x8fc41c: branchIfSmi(r0, 0x8fc428)
    //     0x8fc41c: tbz             w0, #0, #0x8fc428
    // 0x8fc420: r1 = LoadClassIdInstr(r0)
    //     0x8fc420: ldur            x1, [x0, #-1]
    //     0x8fc424: ubfx            x1, x1, #0xc, #0x14
    // 0x8fc428: r16 = "money"
    //     0x8fc428: ldr             x16, [PP, #0x7b68]  ; [pp+0x7b68] "money"
    // 0x8fc42c: stp             x16, x0, [SP]
    // 0x8fc430: mov             x0, x1
    // 0x8fc434: mov             lr, x0
    // 0x8fc438: ldr             lr, [x21, lr, lsl #3]
    // 0x8fc43c: blr             lr
    // 0x8fc440: tbnz            w0, #4, #0x8fc4e4
    // 0x8fc444: ldur            x2, [fp, #-0x18]
    // 0x8fc448: LoadField: r1 = r2->field_f
    //     0x8fc448: ldur            w1, [x2, #0xf]
    // 0x8fc44c: DecompressPointer r1
    //     0x8fc44c: add             x1, x1, HEAP, lsl #32
    // 0x8fc450: r0 = LoadClassIdInstr(r1)
    //     0x8fc450: ldur            x0, [x1, #-1]
    //     0x8fc454: ubfx            x0, x0, #0xc, #0x14
    // 0x8fc458: r2 = "id"
    //     0x8fc458: ldr             x2, [PP, #0x6f48]  ; [pp+0x6f48] "id"
    // 0x8fc45c: r0 = GDT[cid_x0 + 0x5f1]()
    //     0x8fc45c: add             lr, x0, #0x5f1
    //     0x8fc460: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc464: blr             lr
    // 0x8fc468: mov             x3, x0
    // 0x8fc46c: r2 = Null
    //     0x8fc46c: mov             x2, NULL
    // 0x8fc470: r1 = Null
    //     0x8fc470: mov             x1, NULL
    // 0x8fc474: stur            x3, [fp, #-0x20]
    // 0x8fc478: branchIfSmi(r0, 0x8fc4a0)
    //     0x8fc478: tbz             w0, #0, #0x8fc4a0
    // 0x8fc47c: r4 = LoadClassIdInstr(r0)
    //     0x8fc47c: ldur            x4, [x0, #-1]
    //     0x8fc480: ubfx            x4, x4, #0xc, #0x14
    // 0x8fc484: sub             x4, x4, #0x3b
    // 0x8fc488: cmp             x4, #1
    // 0x8fc48c: b.ls            #0x8fc4a0
    // 0x8fc490: r8 = int
    //     0x8fc490: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8fc494: r3 = Null
    //     0x8fc494: add             x3, PP, #0x17, lsl #12  ; [pp+0x170c8] Null
    //     0x8fc498: ldr             x3, [x3, #0xc8]
    // 0x8fc49c: r0 = int()
    //     0x8fc49c: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8fc4a0: ldur            x0, [fp, #-0x20]
    // 0x8fc4a4: r1 = LoadInt32Instr(r0)
    //     0x8fc4a4: sbfx            x1, x0, #1, #0x1f
    //     0x8fc4a8: tbz             w0, #0, #0x8fc4b0
    //     0x8fc4ac: ldur            x1, [x0, #7]
    // 0x8fc4b0: r0 = __unknown_function__()
    //     0x8fc4b0: bl              #0x8f6d98  ; [] ::__unknown_function__
    // 0x8fc4b4: ldur            x2, [fp, #-0x10]
    // 0x8fc4b8: r1 = Function '<anonymous closure>':.
    //     0x8fc4b8: add             x1, PP, #0x17, lsl #12  ; [pp+0x170d8] AnonymousClosure: (0x4da77c), in [HPi] _Oy::<anonymous closure> (0x500fe4)
    //     0x8fc4bc: ldr             x1, [x1, #0xd8]
    // 0x8fc4c0: stur            x0, [fp, #-0x10]
    // 0x8fc4c4: r0 = AllocateClosure()
    //     0x8fc4c4: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x8fc4c8: r16 = <Null?>
    //     0x8fc4c8: ldr             x16, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    // 0x8fc4cc: ldur            lr, [fp, #-0x10]
    // 0x8fc4d0: stp             lr, x16, [SP, #8]
    // 0x8fc4d4: str             x0, [SP]
    // 0x8fc4d8: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8fc4d8: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8fc4dc: r0 = call 0x7e60e0
    //     0x8fc4dc: bl              #0x7e60e0
    // 0x8fc4e0: b               #0x8fc628
    // 0x8fc4e4: ldur            x2, [fp, #-0x18]
    // 0x8fc4e8: LoadField: r3 = r2->field_1b
    //     0x8fc4e8: ldur            w3, [x2, #0x1b]
    // 0x8fc4ec: DecompressPointer r3
    //     0x8fc4ec: add             x3, x3, HEAP, lsl #32
    // 0x8fc4f0: ArrayLoad: r0 = r2[0]  ; List_4
    //     0x8fc4f0: ldur            w0, [x2, #0x17]
    // 0x8fc4f4: DecompressPointer r0
    //     0x8fc4f4: add             x0, x0, HEAP, lsl #32
    // 0x8fc4f8: LoadField: r1 = r3->field_b
    //     0x8fc4f8: ldur            w1, [x3, #0xb]
    // 0x8fc4fc: DecompressPointer r1
    //     0x8fc4fc: add             x1, x1, HEAP, lsl #32
    // 0x8fc500: r4 = LoadInt32Instr(r0)
    //     0x8fc500: sbfx            x4, x0, #1, #0x1f
    //     0x8fc504: tbz             w0, #0, #0x8fc50c
    //     0x8fc508: ldur            x4, [x0, #7]
    // 0x8fc50c: r0 = LoadInt32Instr(r1)
    //     0x8fc50c: sbfx            x0, x1, #1, #0x1f
    // 0x8fc510: mov             x1, x4
    // 0x8fc514: cmp             x1, x0
    // 0x8fc518: b.hs            #0x8fc63c
    // 0x8fc51c: LoadField: r0 = r3->field_f
    //     0x8fc51c: ldur            w0, [x3, #0xf]
    // 0x8fc520: DecompressPointer r0
    //     0x8fc520: add             x0, x0, HEAP, lsl #32
    // 0x8fc524: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x8fc524: add             x16, x0, x4, lsl #2
    //     0x8fc528: ldur            w1, [x16, #0xf]
    // 0x8fc52c: DecompressPointer r1
    //     0x8fc52c: add             x1, x1, HEAP, lsl #32
    // 0x8fc530: r16 = "channel"
    //     0x8fc530: ldr             x16, [PP, #0x7a88]  ; [pp+0x7a88] "channel"
    // 0x8fc534: stp             x16, x1, [SP]
    // 0x8fc538: r4 = 0
    //     0x8fc538: movz            x4, #0
    // 0x8fc53c: ldr             x0, [SP, #8]
    // 0x8fc540: r5 = UnlinkedCall_0x2d3c80
    //     0x8fc540: add             x16, PP, #0x17, lsl #12  ; [pp+0x170e0] UnlinkedCall: 0x2d3c80 - SwitchableCallMissStub
    //     0x8fc544: ldp             x5, lr, [x16, #0xe0]
    // 0x8fc548: blr             lr
    // 0x8fc54c: mov             x3, x0
    // 0x8fc550: r2 = Null
    //     0x8fc550: mov             x2, NULL
    // 0x8fc554: r1 = Null
    //     0x8fc554: mov             x1, NULL
    // 0x8fc558: stur            x3, [fp, #-0x10]
    // 0x8fc55c: r4 = 59
    //     0x8fc55c: movz            x4, #0x3b
    // 0x8fc560: branchIfSmi(r0, 0x8fc56c)
    //     0x8fc560: tbz             w0, #0, #0x8fc56c
    // 0x8fc564: r4 = LoadClassIdInstr(r0)
    //     0x8fc564: ldur            x4, [x0, #-1]
    //     0x8fc568: ubfx            x4, x4, #0xc, #0x14
    // 0x8fc56c: sub             x4, x4, #0x5d
    // 0x8fc570: cmp             x4, #1
    // 0x8fc574: b.ls            #0x8fc588
    // 0x8fc578: r8 = String
    //     0x8fc578: ldr             x8, [PP, #0x328]  ; [pp+0x328] Type: String
    // 0x8fc57c: r3 = Null
    //     0x8fc57c: add             x3, PP, #0x17, lsl #12  ; [pp+0x170f0] Null
    //     0x8fc580: ldr             x3, [x3, #0xf0]
    // 0x8fc584: r0 = String()
    //     0x8fc584: bl              #0x958918  ; IsType_String_Stub
    // 0x8fc588: ldur            x0, [fp, #-0x18]
    // 0x8fc58c: LoadField: r1 = r0->field_f
    //     0x8fc58c: ldur            w1, [x0, #0xf]
    // 0x8fc590: DecompressPointer r1
    //     0x8fc590: add             x1, x1, HEAP, lsl #32
    // 0x8fc594: r0 = LoadClassIdInstr(r1)
    //     0x8fc594: ldur            x0, [x1, #-1]
    //     0x8fc598: ubfx            x0, x0, #0xc, #0x14
    // 0x8fc59c: r2 = "id"
    //     0x8fc59c: ldr             x2, [PP, #0x6f48]  ; [pp+0x6f48] "id"
    // 0x8fc5a0: r0 = GDT[cid_x0 + 0x5f1]()
    //     0x8fc5a0: add             lr, x0, #0x5f1
    //     0x8fc5a4: ldr             lr, [x21, lr, lsl #3]
    //     0x8fc5a8: blr             lr
    // 0x8fc5ac: mov             x3, x0
    // 0x8fc5b0: r2 = Null
    //     0x8fc5b0: mov             x2, NULL
    // 0x8fc5b4: r1 = Null
    //     0x8fc5b4: mov             x1, NULL
    // 0x8fc5b8: stur            x3, [fp, #-0x18]
    // 0x8fc5bc: branchIfSmi(r0, 0x8fc5e4)
    //     0x8fc5bc: tbz             w0, #0, #0x8fc5e4
    // 0x8fc5c0: r4 = LoadClassIdInstr(r0)
    //     0x8fc5c0: ldur            x4, [x0, #-1]
    //     0x8fc5c4: ubfx            x4, x4, #0xc, #0x14
    // 0x8fc5c8: sub             x4, x4, #0x3b
    // 0x8fc5cc: cmp             x4, #1
    // 0x8fc5d0: b.ls            #0x8fc5e4
    // 0x8fc5d4: r8 = int
    //     0x8fc5d4: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8fc5d8: r3 = Null
    //     0x8fc5d8: add             x3, PP, #0x17, lsl #12  ; [pp+0x17100] Null
    //     0x8fc5dc: ldr             x3, [x3, #0x100]
    // 0x8fc5e0: r0 = int()
    //     0x8fc5e0: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8fc5e4: ldur            x0, [fp, #-0x18]
    // 0x8fc5e8: r2 = LoadInt32Instr(r0)
    //     0x8fc5e8: sbfx            x2, x0, #1, #0x1f
    //     0x8fc5ec: tbz             w0, #0, #0x8fc5f4
    //     0x8fc5f0: ldur            x2, [x0, #7]
    // 0x8fc5f4: ldur            x1, [fp, #-0x10]
    // 0x8fc5f8: r0 = __unknown_function__()
    //     0x8fc5f8: bl              #0x8f6bf0  ; [] ::__unknown_function__
    // 0x8fc5fc: r1 = Function '<anonymous closure>':.
    //     0x8fc5fc: add             x1, PP, #0x17, lsl #12  ; [pp+0x17110] AnonymousClosure: (0x501ba4), in [HPi] _Oy::<anonymous closure> (0x500fe4)
    //     0x8fc600: ldr             x1, [x1, #0x110]
    // 0x8fc604: r2 = Null
    //     0x8fc604: mov             x2, NULL
    // 0x8fc608: stur            x0, [fp, #-0x10]
    // 0x8fc60c: r0 = AllocateClosure()
    //     0x8fc60c: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x8fc610: r16 = <Null?>
    //     0x8fc610: ldr             x16, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    // 0x8fc614: ldur            lr, [fp, #-0x10]
    // 0x8fc618: stp             lr, x16, [SP, #8]
    // 0x8fc61c: str             x0, [SP]
    // 0x8fc620: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8fc620: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8fc624: r0 = call 0x7e60e0
    //     0x8fc624: bl              #0x7e60e0
    // 0x8fc628: r0 = Null
    //     0x8fc628: mov             x0, NULL
    // 0x8fc62c: r0 = ReturnAsyncNotFuture()
    //     0x8fc62c: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x8fc630: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fc630: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fc634: b               #0x8fc388
    // 0x8fc638: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8fc638: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8fc63c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8fc63c: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] yla <anonymous closure>(dynamic, aoa) {
    // ** addr: 0x500fe4, size: -0x1
  }
  [closure] Tla <anonymous closure>(dynamic, aoa, (dynamic, (dynamic) => void) => void) {
    // ** addr: 0x501024, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x4a1d54, size: -0x1
  }
  [closure] qoa <anonymous closure>(dynamic, int) {
    // ** addr: 0x501d2c, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x4da77c, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x501ba4, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x502358, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x4dafc4, size: -0x1
  }
}

// class id: 3354, size: 0x48, field offset: 0x30
class _Py extends _Oy {

  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x504238, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x45a714, size: -0x1
  }
  [closure] ela <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x502ec8, size: -0x1
  }
  [closure] Cka <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x50296c, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x502450, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x500b4c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x4d8d34, size: -0x1
  }
  [closure] jla <anonymous closure>(dynamic, ((dynamic) => void)?) {
    // ** addr: 0x4db13c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x5023cc, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x502510, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x5028f8, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x5040e0, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x6bf1b0, size: -0x1
  }
}

// class id: 4089, size: 0x10, field offset: 0x10
class Ny extends Tu {
}
