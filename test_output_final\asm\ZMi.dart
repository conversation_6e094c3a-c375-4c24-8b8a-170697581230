// lib: Nqj, url: ZMi

// class id: 1049669, size: 0x8
class :: {
}

// class id: 764, size: 0xc, field offset: 0xc
class aVa extends OTa {

  static late final vWa iog; // offset: 0x1264

  [closure] static aVa <anonymous closure>(dynamic) {
    // ** addr: 0x47082c, size: -0x1
  }
  [closure] static aVa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x470944, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x4707d4, size: -0x1
  }
}
