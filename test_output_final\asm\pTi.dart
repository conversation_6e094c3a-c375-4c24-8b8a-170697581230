// lib: , url: PTi

// class id: 1048893, size: 0x8
class :: {
}

// class id: 3257, size: 0x18, field offset: 0x14
class _FN extends Mt<dynamic> {

  late Coa _Qzc; // offset: 0x14

  [closure] Zma <anonymous closure>(dynamic, ena, Rga) {
    // ** addr: 0x5be734, size: -0x1
  }
  [closure] pI _Wzc(dynamic, aoa, pI?) {
    // ** addr: 0x5be3ac, size: -0x1
  }
  [closure] pI <anonymous closure>(dynamic, aoa) {
    // ** addr: 0x5be688, size: -0x1
  }
}

// class id: 4000, size: 0x90, field offset: 0xc
//   const constructor, 
class CN extends It {

  [closure] static UN <anonymous closure>(dynamic, fr?, fr?) {
    // ** addr: 0x63d770, size: -0x1
  }
}

// class id: 5578, size: 0x14, field offset: 0x14
enum BN extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
