// lib: , url: HUi

// class id: 1049092, size: 0x8
class :: {
}

// class id: 1932, size: 0x10, field offset: 0x8
//   const constructor, 
class Hfa extends Object {

  _Mint field_8;

  Hfa -(Hfa, int) {
    // ** addr: 0x9326a0, size: 0x80
    // 0x9326a0: EnterFrame
    //     0x9326a0: stp             fp, lr, [SP, #-0x10]!
    //     0x9326a4: mov             fp, SP
    // 0x9326a8: CheckStackOverflow
    //     0x9326a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9326ac: cmp             SP, x16
    //     0x9326b0: b.ls            #0x932700
    // 0x9326b4: ldr             x0, [fp, #0x10]
    // 0x9326b8: r2 = Null
    //     0x9326b8: mov             x2, NULL
    // 0x9326bc: r1 = Null
    //     0x9326bc: mov             x1, NULL
    // 0x9326c0: branchIfSmi(r0, 0x9326e8)
    //     0x9326c0: tbz             w0, #0, #0x9326e8
    // 0x9326c4: r4 = LoadClassIdInstr(r0)
    //     0x9326c4: ldur            x4, [x0, #-1]
    //     0x9326c8: ubfx            x4, x4, #0xc, #0x14
    // 0x9326cc: sub             x4, x4, #0x3b
    // 0x9326d0: cmp             x4, #1
    // 0x9326d4: b.ls            #0x9326e8
    // 0x9326d8: r8 = int
    //     0x9326d8: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x9326dc: r3 = Null
    //     0x9326dc: add             x3, PP, #0x45, lsl #12  ; [pp+0x45000] Null
    //     0x9326e0: ldr             x3, [x3]
    // 0x9326e4: r0 = int()
    //     0x9326e4: bl              #0x9595b0  ; IsType_int_Stub
    // 0x9326e8: ldr             x1, [fp, #0x18]
    // 0x9326ec: ldr             x2, [fp, #0x10]
    // 0x9326f0: r0 = call 0x6c3d88
    //     0x9326f0: bl              #0x6c3d88
    // 0x9326f4: LeaveFrame
    //     0x9326f4: mov             SP, fp
    //     0x9326f8: ldp             fp, lr, [SP], #0x10
    // 0x9326fc: ret
    //     0x9326fc: ret             
    // 0x932700: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x932700: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x932704: b               #0x9326b4
  }
  Hfa +(Hfa, int) {
    // ** addr: 0x93272c, size: 0x80
    // 0x93272c: EnterFrame
    //     0x93272c: stp             fp, lr, [SP, #-0x10]!
    //     0x932730: mov             fp, SP
    // 0x932734: CheckStackOverflow
    //     0x932734: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x932738: cmp             SP, x16
    //     0x93273c: b.ls            #0x93278c
    // 0x932740: ldr             x0, [fp, #0x10]
    // 0x932744: r2 = Null
    //     0x932744: mov             x2, NULL
    // 0x932748: r1 = Null
    //     0x932748: mov             x1, NULL
    // 0x93274c: branchIfSmi(r0, 0x932774)
    //     0x93274c: tbz             w0, #0, #0x932774
    // 0x932750: r4 = LoadClassIdInstr(r0)
    //     0x932750: ldur            x4, [x0, #-1]
    //     0x932754: ubfx            x4, x4, #0xc, #0x14
    // 0x932758: sub             x4, x4, #0x3b
    // 0x93275c: cmp             x4, #1
    // 0x932760: b.ls            #0x932774
    // 0x932764: r8 = int
    //     0x932764: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x932768: r3 = Null
    //     0x932768: add             x3, PP, #0x45, lsl #12  ; [pp+0x45010] Null
    //     0x93276c: ldr             x3, [x3, #0x10]
    // 0x932770: r0 = int()
    //     0x932770: bl              #0x9595b0  ; IsType_int_Stub
    // 0x932774: ldr             x1, [fp, #0x18]
    // 0x932778: ldr             x2, [fp, #0x10]
    // 0x93277c: r0 = call 0x6c3de4
    //     0x93277c: bl              #0x6c3de4
    // 0x932780: LeaveFrame
    //     0x932780: mov             SP, fp
    //     0x932784: ldp             fp, lr, [SP], #0x10
    // 0x932788: ret
    //     0x932788: ret             
    // 0x93278c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93278c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x932790: b               #0x932740
  }
}
