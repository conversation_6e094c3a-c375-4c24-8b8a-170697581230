// lib: , url: FOi

// class id: 1048641, size: 0x8
class :: {
}

// class id: 3323, size: 0x30, field offset: 0x14
class _gw extends Mt<dynamic> {

  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x58626c, size: -0x1
  }
  [closure] pI <anonymous closure>(dynamic, aoa, int) {
    // ** addr: 0x585ff4, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x585fac, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x585bf0, size: -0x1
  }
  [closure] Cka <anonymous closure>(dynamic, String) {
    // ** addr: 0x585b44, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x585b0c, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x585ce0, size: -0x1
  }
}

// class id: 4064, size: 0x18, field offset: 0xc
class fw extends It {
}
