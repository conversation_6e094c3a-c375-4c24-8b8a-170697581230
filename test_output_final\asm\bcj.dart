// lib: , url: bcj

// class id: 1049318, size: 0x8
class :: {
}

// class id: 1216, size: 0x24, field offset: 0x8
class wIa extends Object {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x5f8c8c, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, cqa<dynamic>, dynamic) {
    // ** addr: 0x5f8c10, size: -0x1
  }
  [closure] xH<void> MCg(dynamic, {required AJ key, required String? name, required Object? Tob, required String jNc, required pI Yyc}) {
    // ** addr: 0x5f821c, size: -0x1
  }
}
