// lib: , url: PPi

// class id: 1048701, size: 0x8
class :: {
}

// class id: 4502, size: 0x1c, field offset: 0x8
class Zy<X0> extends Object {

  late final X0? data; // offset: 0x18

  const X0? dyn:get:data(Zy<X0>) {
    // ** addr: 0x8ddfc0, size: 0x4c
    // 0x8ddfc0: EnterFrame
    //     0x8ddfc0: stp             fp, lr, [SP, #-0x10]!
    //     0x8ddfc4: mov             fp, SP
    // 0x8ddfc8: ldr             x1, [fp, #0x10]
    // 0x8ddfcc: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x8ddfcc: ldur            w0, [x1, #0x17]
    // 0x8ddfd0: DecompressPointer r0
    //     0x8ddfd0: add             x0, x0, HEAP, lsl #32
    // 0x8ddfd4: r16 = Sentinel
    //     0x8ddfd4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8ddfd8: cmp             w0, w16
    // 0x8ddfdc: b.eq            #0x8ddfec
    // 0x8ddfe0: LeaveFrame
    //     0x8ddfe0: mov             SP, fp
    //     0x8ddfe4: ldp             fp, lr, [SP], #0x10
    // 0x8ddfe8: ret
    //     0x8ddfe8: ret             
    // 0x8ddfec: r9 = data
    //     0x8ddfec: ldr             x9, [PP, #0x78c8]  ; [pp+0x78c8] Field <Zy.data>: late final (offset: 0x18)
    // 0x8ddff0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8ddff0: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
