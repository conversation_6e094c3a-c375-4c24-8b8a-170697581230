// lib: Bqj, url: mfj

// class id: 1049657, size: 0x8
class :: {
}

// class id: 776, size: 0xc, field offset: 0xc
class CUa extends OTa {

  static late final vWa iog; // offset: 0x1234

  [closure] static CUa <anonymous closure>(dynamic) {
    // ** addr: 0x471ad4, size: -0x1
  }
  [closure] static CUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x471bec, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x471a7c, size: -0x1
  }
}
