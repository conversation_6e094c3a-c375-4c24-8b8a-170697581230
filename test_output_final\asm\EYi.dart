// lib: , url: EYi

// class id: 1049135, size: 0x8
class :: {
}

// class id: 1802, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _<PERSON>ia extends Object
     with <PERSON><PERSON> {
}

// class id: 1803, size: 0x8, field offset: 0x8
class _aja extends _Zia {

  static late final _aja qkc; // offset: 0xb9c
}

// class id: 1804, size: 0x8, field offset: 0x8
abstract class Yia extends Object {
}

// class id: 1805, size: 0x28, field offset: 0x8
class Xia extends Object {

  static late final Xia _Alc; // offset: 0xb90
  late yga _KIe; // offset: 0x18
  late MethodChannel _IIe; // offset: 0x10

  [closure] Future<dynamic> _QIe(dynamic, MethodCall) {
    // ** addr: 0x36c928, size: -0x1
  }
  [closure] double <anonymous closure>(dynamic, num) {
    // ** addr: 0x385770, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, String) {
    // ** addr: 0x384f04, size: -0x1
  }
  [closure] List<dynamic> <anonymous closure>(dynamic, String) {
    // ** addr: 0x384b7c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x37bf54, size: -0x1
  }
}

// class id: 1806, size: 0x28, field offset: 0x8
class zga extends Object {
}

// class id: 1807, size: 0x8, field offset: 0x8
abstract class Wia extends Object
    implements xga {
}

// class id: 1808, size: 0x18, field offset: 0x8
//   const constructor, 
class Via extends Object {
}

// class id: 1809, size: 0x8, field offset: 0x8
abstract class Uia extends Object {
}

// class id: 1810, size: 0x8, field offset: 0x8
abstract class xga extends Object {
}

// class id: 1811, size: 0x8, field offset: 0x8
abstract class Tia extends Object {
}

// class id: 1812, size: 0x14, field offset: 0x8
//   const constructor, 
class wga extends Object {

  _OneByteString field_8;
  zia field_c;
  Ys field_10;

  [closure] int QOh(dynamic, int) {
    // ** addr: 0x444210, size: -0x1
  }
}

// class id: 1813, size: 0x14, field offset: 0x8
class Ria extends Object {
}

// class id: 1814, size: 0x4c, field offset: 0x8
//   const constructor, 
class yga extends Object {

  Map<String, dynamic> WNb(yga) {
    // ** addr: 0x8d4d94, size: 0x48
    // 0x8d4d94: EnterFrame
    //     0x8d4d94: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4d98: mov             fp, SP
    // 0x8d4d9c: CheckStackOverflow
    //     0x8d4d9c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4da0: cmp             SP, x16
    //     0x8d4da4: b.ls            #0x8d4dbc
    // 0x8d4da8: ldr             x1, [fp, #0x10]
    // 0x8d4dac: r0 = call 0x383620
    //     0x8d4dac: bl              #0x383620
    // 0x8d4db0: LeaveFrame
    //     0x8d4db0: mov             SP, fp
    //     0x8d4db4: ldp             fp, lr, [SP], #0x10
    // 0x8d4db8: ret
    //     0x8d4db8: ret             
    // 0x8d4dbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4dbc: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4dc0: b               #0x8d4da8
  }
}

// class id: 1815, size: 0x18, field offset: 0x8
//   const constructor, 
class Nia extends Object {

  _Mint field_8;
  bool field_10;
  bool field_14;

  Map<String, dynamic> WNb(Nia) {
    // ** addr: 0x8d4d4c, size: 0x48
    // 0x8d4d4c: EnterFrame
    //     0x8d4d4c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d4d50: mov             fp, SP
    // 0x8d4d54: CheckStackOverflow
    //     0x8d4d54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d4d58: cmp             SP, x16
    //     0x8d4d5c: b.ls            #0x8d4d74
    // 0x8d4d60: ldr             x1, [fp, #0x10]
    // 0x8d4d64: r0 = call 0x3834e4
    //     0x8d4d64: bl              #0x3834e4
    // 0x8d4d68: LeaveFrame
    //     0x8d4d68: mov             SP, fp
    //     0x8d4d6c: ldp             fp, lr, [SP], #0x10
    // 0x8d4d70: ret
    //     0x8d4d70: ret             
    // 0x8d4d74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d4d74: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d4d78: b               #0x8d4d60
  }
}

// class id: 5486, size: 0x14, field offset: 0x14
enum Sia extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5487, size: 0x14, field offset: 0x14
enum Qia extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5488, size: 0x14, field offset: 0x14
enum Pia extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5489, size: 0x14, field offset: 0x14
enum Oia extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5490, size: 0x14, field offset: 0x14
enum Mia extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5491, size: 0x14, field offset: 0x14
enum Lia extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
