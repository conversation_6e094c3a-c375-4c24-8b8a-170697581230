// lib: , url: GOi

// class id: 1048642, size: 0x8
class :: {
}

// class id: 3398, size: 0x48, field offset: 0x30
class _iw extends Wu<dynamic> {

  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x489a58, size: -0x1
  }
  [closure] pI <anonymous closure>(dynamic, aoa, int) {
    // ** addr: 0x4899c4, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x48997c, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x489680, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x489774, size: -0x1
  }
}

// class id: 4130, size: 0x18, field offset: 0x10
class hw extends Tu {
}
