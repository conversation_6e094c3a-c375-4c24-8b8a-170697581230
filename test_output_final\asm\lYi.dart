// lib: , url: lYi

// class id: 1049118, size: 0x8
class :: {
}

// class id: 1834, size: 0x10, field offset: 0x8
//   const constructor, 
class _Vha extends Object {

  Qha field_8;
  Pha field_c;
}

// class id: 1835, size: 0x10, field offset: 0x8
class Fga extends Object {

  static late final Fga qkc; // offset: 0xb54
  static late final Map<_Vha, Set<Pga>> _pGe; // offset: 0xb58
  static late final Map<Pga, Oga> _rGe; // offset: 0xb60
  static late final Map<Pga, Oga> _qGe; // offset: 0xb5c
}

// class id: 2812, size: 0x10, field offset: 0x8
//   const constructor, 
abstract class Sha extends _DF {

  [closure] static Rha Goh(dynamic) {
    // ** addr: 0x61a8e4, size: -0x1
  }
}

// class id: 2813, size: 0x10, field offset: 0x10
//   const constructor, 
class Uha extends Sha {
}

// class id: 2814, size: 0x10, field offset: 0x10
//   const constructor, 
class Tha extends Sha {
}

// class id: 2815, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class Rha extends _DF {
}

// class id: 5497, size: 0x14, field offset: 0x14
enum Qha extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5498, size: 0x14, field offset: 0x14
enum Pha extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
