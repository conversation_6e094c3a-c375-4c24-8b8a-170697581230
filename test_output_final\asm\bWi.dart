// lib: , url: BWi

// class id: 1049018, size: 0x8
class :: {
}

// class id: 2294, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class SY extends Object {
}

// class id: 2295, size: 0x28, field offset: 0x8
//   const constructor, 
class _TY extends SY {
}

// class id: 2296, size: 0x18, field offset: 0x8
//   const constructor, 
class lu extends SY {

  gr field_8;
  gr field_c;
  gr field_10;
  gr field_14;

  lu +(lu, lu) {
    // ** addr: 0x8dc114, size: 0x88
    // 0x8dc114: EnterFrame
    //     0x8dc114: stp             fp, lr, [SP, #-0x10]!
    //     0x8dc118: mov             fp, SP
    // 0x8dc11c: CheckStackOverflow
    //     0x8dc11c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8dc120: cmp             SP, x16
    //     0x8dc124: b.ls            #0x8dc17c
    // 0x8dc128: ldr             x0, [fp, #0x10]
    // 0x8dc12c: r2 = Null
    //     0x8dc12c: mov             x2, NULL
    // 0x8dc130: r1 = Null
    //     0x8dc130: mov             x1, NULL
    // 0x8dc134: r4 = 59
    //     0x8dc134: movz            x4, #0x3b
    // 0x8dc138: branchIfSmi(r0, 0x8dc144)
    //     0x8dc138: tbz             w0, #0, #0x8dc144
    // 0x8dc13c: r4 = LoadClassIdInstr(r0)
    //     0x8dc13c: ldur            x4, [x0, #-1]
    //     0x8dc140: ubfx            x4, x4, #0xc, #0x14
    // 0x8dc144: sub             x4, x4, #0x8f8
    // 0x8dc148: cmp             x4, #2
    // 0x8dc14c: b.ls            #0x8dc164
    // 0x8dc150: r8 = lu
    //     0x8dc150: add             x8, PP, #0x13, lsl #12  ; [pp+0x13de0] Type: lu
    //     0x8dc154: ldr             x8, [x8, #0xde0]
    // 0x8dc158: r3 = Null
    //     0x8dc158: add             x3, PP, #0x13, lsl #12  ; [pp+0x13de8] Null
    //     0x8dc15c: ldr             x3, [x3, #0xde8]
    // 0x8dc160: r0 = DefaultTypeTest()
    //     0x8dc160: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8dc164: ldr             x1, [fp, #0x18]
    // 0x8dc168: ldr             x2, [fp, #0x10]
    // 0x8dc16c: r0 = call 0x3f9a68
    //     0x8dc16c: bl              #0x3f9a68
    // 0x8dc170: LeaveFrame
    //     0x8dc170: mov             SP, fp
    //     0x8dc174: ldp             fp, lr, [SP], #0x10
    // 0x8dc178: ret
    //     0x8dc178: ret             
    // 0x8dc17c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8dc17c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8dc180: b               #0x8dc128
  }
  lu -(lu, lu) {
    // ** addr: 0x8dc19c, size: 0x88
    // 0x8dc19c: EnterFrame
    //     0x8dc19c: stp             fp, lr, [SP, #-0x10]!
    //     0x8dc1a0: mov             fp, SP
    // 0x8dc1a4: CheckStackOverflow
    //     0x8dc1a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8dc1a8: cmp             SP, x16
    //     0x8dc1ac: b.ls            #0x8dc204
    // 0x8dc1b0: ldr             x0, [fp, #0x10]
    // 0x8dc1b4: r2 = Null
    //     0x8dc1b4: mov             x2, NULL
    // 0x8dc1b8: r1 = Null
    //     0x8dc1b8: mov             x1, NULL
    // 0x8dc1bc: r4 = 59
    //     0x8dc1bc: movz            x4, #0x3b
    // 0x8dc1c0: branchIfSmi(r0, 0x8dc1cc)
    //     0x8dc1c0: tbz             w0, #0, #0x8dc1cc
    // 0x8dc1c4: r4 = LoadClassIdInstr(r0)
    //     0x8dc1c4: ldur            x4, [x0, #-1]
    //     0x8dc1c8: ubfx            x4, x4, #0xc, #0x14
    // 0x8dc1cc: sub             x4, x4, #0x8f8
    // 0x8dc1d0: cmp             x4, #2
    // 0x8dc1d4: b.ls            #0x8dc1ec
    // 0x8dc1d8: r8 = lu
    //     0x8dc1d8: add             x8, PP, #0x13, lsl #12  ; [pp+0x13de0] Type: lu
    //     0x8dc1dc: ldr             x8, [x8, #0xde0]
    // 0x8dc1e0: r3 = Null
    //     0x8dc1e0: add             x3, PP, #0x13, lsl #12  ; [pp+0x13df8] Null
    //     0x8dc1e4: ldr             x3, [x3, #0xdf8]
    // 0x8dc1e8: r0 = DefaultTypeTest()
    //     0x8dc1e8: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8dc1ec: ldr             x1, [fp, #0x18]
    // 0x8dc1f0: ldr             x2, [fp, #0x10]
    // 0x8dc1f4: r0 = call 0x3f9b4c
    //     0x8dc1f4: bl              #0x3f9b4c
    // 0x8dc1f8: LeaveFrame
    //     0x8dc1f8: mov             SP, fp
    //     0x8dc1fc: ldp             fp, lr, [SP], #0x10
    // 0x8dc200: ret
    //     0x8dc200: ret             
    // 0x8dc204: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8dc204: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8dc208: b               #0x8dc1b0
  }
  lu *(lu, double) {
    // ** addr: 0x8dc224, size: 0x50
    // 0x8dc224: EnterFrame
    //     0x8dc224: stp             fp, lr, [SP, #-0x10]!
    //     0x8dc228: mov             fp, SP
    // 0x8dc22c: CheckStackOverflow
    //     0x8dc22c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8dc230: cmp             SP, x16
    //     0x8dc234: b.ls            #0x8dc254
    // 0x8dc238: ldr             x0, [fp, #0x10]
    // 0x8dc23c: LoadField: d0 = r0->field_7
    //     0x8dc23c: ldur            d0, [x0, #7]
    // 0x8dc240: ldr             x1, [fp, #0x18]
    // 0x8dc244: r0 = call 0x812dc4
    //     0x8dc244: bl              #0x812dc4
    // 0x8dc248: LeaveFrame
    //     0x8dc248: mov             SP, fp
    //     0x8dc24c: ldp             fp, lr, [SP], #0x10
    // 0x8dc250: ret
    //     0x8dc250: ret             
    // 0x8dc254: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8dc254: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8dc258: b               #0x8dc238
  }
}
