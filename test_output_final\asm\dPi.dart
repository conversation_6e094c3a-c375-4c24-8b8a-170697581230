// lib: , url: DPi

// class id: 1048689, size: 0x8
class :: {
}

// class id: 3358, size: 0x48, field offset: 0x30
class _Gy extends Wu<dynamic> {

  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x4f69d0, size: -0x1
  }
  [closure] Tla <anonymous closure>(dynamic, aoa, int) {
    // ** addr: 0x4f6200, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x4f61b8, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x4f5ee4, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x4f5fb0, size: -0x1
  }
}

// class id: 4093, size: 0x10, field offset: 0x10
class Fy extends Tu {
}
