// lib: Cqj, url: nfj

// class id: 1049658, size: 0x8
class :: {
}

// class id: 775, size: 0xc, field offset: 0xc
class EUa extends OTa {

  static late final vWa iog; // offset: 0x1238

  [closure] static EUa <anonymous closure>(dynamic) {
    // ** addr: 0x471944, size: -0x1
  }
  [closure] static EUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x471a5c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x4718ec, size: -0x1
  }
}
