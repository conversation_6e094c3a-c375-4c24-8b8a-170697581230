// lib: , url: jgj

// class id: 1049511, size: 0x8
class :: {
}

// class id: 943, size: 0x6c, field offset: 0x8
class aPa extends Object {

  [closure] Da fBi(dynamic, int) {
    // ** addr: 0x3c62a8, size: -0x1
  }
  [closure] void yOg(dynamic, int) {
    // ** addr: 0x894230, size: -0x1
  }
  [closure] void zOg(dynamic, int) {
    // ** addr: 0x894208, size: -0x1
  }
  [closure] void BOg(dynamic, int) {
    // ** addr: 0x8941e0, size: -0x1
  }
  [closure] void COg(dynamic, int) {
    // ** addr: 0x8941b8, size: -0x1
  }
  [closure] void EOg(dynamic, int) {
    // ** addr: 0x894190, size: -0x1
  }
  [closure] void DOg(dynamic, int) {
    // ** addr: 0x894168, size: -0x1
  }
  [closure] void uOg(dynamic, int) {
    // ** addr: 0x892b80, size: -0x1
  }
  [closure] void xOg(dynamic, int) {
    // ** addr: 0x8931a8, size: -0x1
  }
}
