// lib: , url: GZi

// class id: 1049189, size: 0x8
class :: {
}

// class id: 1781, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class hka extends Object {
}

// class id: 1782, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class bra extends hka {
}

// class id: 3439, size: 0x3c, field offset: 0x3c
//   transformed mixin,
abstract class _Zqa extends joa
     with coa {
}

// class id: 3440, size: 0x40, field offset: 0x3c
class _ara<X0 bound hka> extends _Zqa {
}

// class id: 3475, size: 0x18, field offset: 0x10
//   const constructor, 
class Yqa<X0 bound hka> extends Xna {
}
