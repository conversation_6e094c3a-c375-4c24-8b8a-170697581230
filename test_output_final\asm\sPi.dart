// lib: , url: SPi

// class id: 1048704, size: 0x8
class :: {
}

// class id: 4496, size: 0x44, field offset: 0x8
class fz extends Object {

  Map<String, dynamic> WNb(fz) {
    // ** addr: 0x9033ac, size: 0x48
    // 0x9033ac: EnterFrame
    //     0x9033ac: stp             fp, lr, [SP, #-0x10]!
    //     0x9033b0: mov             fp, SP
    // 0x9033b4: CheckStackOverflow
    //     0x9033b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9033b8: cmp             SP, x16
    //     0x9033bc: b.ls            #0x9033d4
    // 0x9033c0: ldr             x1, [fp, #0x10]
    // 0x9033c4: r0 = call 0x55a14c
    //     0x9033c4: bl              #0x55a14c
    // 0x9033c8: LeaveFrame
    //     0x9033c8: mov             SP, fp
    //     0x9033cc: ldp             fp, lr, [SP], #0x10
    // 0x9033d0: ret
    //     0x9033d0: ret             
    // 0x9033d4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9033d4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9033d8: b               #0x9033c0
  }
}
