// lib: , url: Rkj

// class id: 1049754, size: 0x8
class :: {
}

// class id: 676, size: 0x10, field offset: 0x8
//   const constructor, 
class nYa extends Object {

  lYa field_8;
  mr field_c;
}

// class id: 677, size: 0x10, field offset: 0x8
//   const constructor, 
class mYa extends Object {

  kYa field_8;
  mr field_c;
}

// class id: 5405, size: 0x14, field offset: 0x14
enum lYa extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5406, size: 0x14, field offset: 0x14
enum kYa extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5407, size: 0x14, field offset: 0x14
enum jYa extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5408, size: 0x14, field offset: 0x14
enum iYa extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
