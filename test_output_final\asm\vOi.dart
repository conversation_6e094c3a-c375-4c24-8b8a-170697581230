// lib: , url: VOi

// class id: 1048657, size: 0x8
class :: {
}

// class id: 3317, size: 0x38, field offset: 0x14
class _Rw extends Mt<dynamic> {

  [closure] Null <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x58efb4, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x58ef10, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x58eec0, size: -0x1
  }
  [closure] pI <anonymous closure>(dynamic, aoa, int) {
    // ** addr: 0x58d6d8, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x58d68c, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x58d32c, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x58d380, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, Lz) {
    // ** addr: 0x638344, size: -0x1
  }
  [closure] dynamic <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x638298, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x638220, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x638518, size: -0x1
  }
}

// class id: 4058, size: 0x14, field offset: 0xc
class Qw extends It {
}
