// lib: , url: Fnj

// class id: 1049861, size: 0x8
class :: {
}

// class id: 322, size: 0x10, field offset: 0x8
class pgb extends Object
    implements Ofb {
}

// class id: 323, size: 0x10, field offset: 0x8
class ogb extends Object
    implements Nfb {
}

// class id: 324, size: 0x10, field offset: 0x8
class mgb extends Object
    implements Lfb {
}

// class id: 325, size: 0x10, field offset: 0x8
class kgb extends Object
    implements Qfb {
}

// class id: 326, size: 0xc, field offset: 0x8
class Xfb extends Object
    implements wfb {
}

// class id: 327, size: 0xc, field offset: 0x8
class Vfb extends Object
    implements tfb {
}

// class id: 328, size: 0x38, field offset: 0x8
class Tfb extends Object {

  static late Tfb qkc; // offset: 0x1470
  late final tfb FXg; // offset: 0x8
  late final egb GXg; // offset: 0xc
  late final cgb HXg; // offset: 0x10
  late final ggb IXg; // offset: 0x14
  late final agb JXg; // offset: 0x18
  late final igb KXg; // offset: 0x1c
  late final kgb LXg; // offset: 0x20
  late final Xfb MXg; // offset: 0x24
  late final mgb NXg; // offset: 0x28
  late final ogb OXg; // offset: 0x2c
  late final pgb PXg; // offset: 0x30
  late final rgb QXg; // offset: 0x34
}

// class id: 330, size: 0xc, field offset: 0x8
class rgb extends Sfb {
}

// class id: 332, size: 0x10, field offset: 0xc
class qgb extends Rfb {
}

// class id: 335, size: 0x14, field offset: 0xc
class jgb extends Pfb {
}

// class id: 339, size: 0x14, field offset: 0xc
class ngb extends Mfb {
}

// class id: 342, size: 0x14, field offset: 0xc
class lgb extends Kfb {
}

// class id: 344, size: 0x10, field offset: 0x8
class igb extends Jfb {
}

// class id: 346, size: 0x10, field offset: 0xc
class hgb extends Ifb {
}

// class id: 348, size: 0xc, field offset: 0x8
class ggb extends Hfb {
}

// class id: 350, size: 0x10, field offset: 0xc
class fgb extends Ffb {
}

// class id: 352, size: 0xc, field offset: 0x8
class egb extends Efb {
}

// class id: 354, size: 0x10, field offset: 0xc
class dgb extends Dfb {
}

// class id: 356, size: 0xc, field offset: 0x8
class cgb extends Cfb {
}

// class id: 358, size: 0x10, field offset: 0xc
class bgb extends Afb {
}

// class id: 360, size: 0xc, field offset: 0x8
class agb extends zfb {
}

// class id: 362, size: 0x10, field offset: 0xc
class Zfb extends yfb {
}

// class id: 364, size: 0x10, field offset: 0xc
class Yfb extends xfb {
}

// class id: 367, size: 0x10, field offset: 0xc
class Wfb extends vfb {
}

// class id: 370, size: 0x14, field offset: 0xc
class Ufb extends sfb {
}
