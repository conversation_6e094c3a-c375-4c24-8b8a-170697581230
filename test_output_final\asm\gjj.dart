// lib: Qpj, url: Gjj

// class id: 1049694, size: 0x8
class :: {
}

// class id: 730, size: 0x14, field offset: 0x8
class YVa extends Object
    implements VRa {

  static late final vWa iog; // offset: 0x119c

  [closure] static (dynamic) => YVa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x461440, size: -0x1
  }
  [closure] static YVa <anonymous closure>(dynamic) {
    // ** addr: 0x461494, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x4613a8, size: -0x1
  }
}
