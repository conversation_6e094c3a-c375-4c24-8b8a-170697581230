// lib: Wpj, url: Mjj

// class id: 1049700, size: 0x8
class :: {
}

// class id: 722, size: 0x8, field offset: 0x8
class mWa extends Object
    implements dSa {

  static late final vWa iog; // offset: 0x11b4

  [closure] static (dynamic) => mWa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x460844, size: -0x1
  }
  [closure] static mWa <anonymous closure>(dynamic) {
    // ** addr: 0x460918, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x4607ac, size: -0x1
  }
}
