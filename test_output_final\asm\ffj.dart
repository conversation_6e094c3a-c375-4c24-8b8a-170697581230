// lib: , url: Ffj

// class id: 1049481, size: 0x8
class :: {
}

// class id: 965, size: 0xc, field offset: 0x8
class iOa extends Object {

  hOa [](iOa, int) {
    // ** addr: 0x94bfa0, size: 0xa4
    // 0x94bfa0: EnterFrame
    //     0x94bfa0: stp             fp, lr, [SP, #-0x10]!
    //     0x94bfa4: mov             fp, SP
    // 0x94bfa8: ldr             x0, [fp, #0x10]
    // 0x94bfac: r2 = Null
    //     0x94bfac: mov             x2, NULL
    // 0x94bfb0: r1 = Null
    //     0x94bfb0: mov             x1, NULL
    // 0x94bfb4: branchIfSmi(r0, 0x94bfdc)
    //     0x94bfb4: tbz             w0, #0, #0x94bfdc
    // 0x94bfb8: r4 = LoadClassIdInstr(r0)
    //     0x94bfb8: ldur            x4, [x0, #-1]
    //     0x94bfbc: ubfx            x4, x4, #0xc, #0x14
    // 0x94bfc0: sub             x4, x4, #0x3b
    // 0x94bfc4: cmp             x4, #1
    // 0x94bfc8: b.ls            #0x94bfdc
    // 0x94bfcc: r8 = int
    //     0x94bfcc: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x94bfd0: r3 = Null
    //     0x94bfd0: add             x3, PP, #0x29, lsl #12  ; [pp+0x297d0] Null
    //     0x94bfd4: ldr             x3, [x3, #0x7d0]
    // 0x94bfd8: r0 = int()
    //     0x94bfd8: bl              #0x9595b0  ; IsType_int_Stub
    // 0x94bfdc: ldr             x2, [fp, #0x18]
    // 0x94bfe0: LoadField: r3 = r2->field_7
    //     0x94bfe0: ldur            w3, [x2, #7]
    // 0x94bfe4: DecompressPointer r3
    //     0x94bfe4: add             x3, x3, HEAP, lsl #32
    // 0x94bfe8: LoadField: r2 = r3->field_b
    //     0x94bfe8: ldur            w2, [x3, #0xb]
    // 0x94bfec: DecompressPointer r2
    //     0x94bfec: add             x2, x2, HEAP, lsl #32
    // 0x94bff0: ldr             x4, [fp, #0x10]
    // 0x94bff4: r5 = LoadInt32Instr(r4)
    //     0x94bff4: sbfx            x5, x4, #1, #0x1f
    //     0x94bff8: tbz             w4, #0, #0x94c000
    //     0x94bffc: ldur            x5, [x4, #7]
    // 0x94c000: r0 = LoadInt32Instr(r2)
    //     0x94c000: sbfx            x0, x2, #1, #0x1f
    // 0x94c004: mov             x1, x5
    // 0x94c008: cmp             x1, x0
    // 0x94c00c: b.hs            #0x94c028
    // 0x94c010: ArrayLoad: r0 = r3[r5]  ; Unknown_4
    //     0x94c010: add             x16, x3, x5, lsl #2
    //     0x94c014: ldur            w0, [x16, #0xf]
    // 0x94c018: DecompressPointer r0
    //     0x94c018: add             x0, x0, HEAP, lsl #32
    // 0x94c01c: LeaveFrame
    //     0x94c01c: mov             SP, fp
    //     0x94c020: ldp             fp, lr, [SP], #0x10
    // 0x94c024: ret
    //     0x94c024: ret             
    // 0x94c028: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x94c028: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
  }
}

// class id: 966, size: 0x28, field offset: 0x8
class hOa extends Object {

  late Int32List lDb; // offset: 0x14
}
