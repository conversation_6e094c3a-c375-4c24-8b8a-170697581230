// lib: , url: ITi

// class id: 1048886, size: 0x8
class :: {
}

// class id: 2488, size: 0x8, field offset: 0x8
class EM extends Object {
}

// class id: 2489, size: 0x10, field offset: 0x8
class DM extends Object {
}

// class id: 2587, size: 0x58, field offset: 0x48
abstract class WM extends kM {
}

// class id: 2589, size: 0x84, field offset: 0x58
class XM extends WM {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x803b34, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x803ad0, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x808f18, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x808eac, size: -0x1
  }
}
