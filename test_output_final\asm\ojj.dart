// lib: Xpj, url: Ojj

// class id: 1049702, size: 0x8
class :: {
}

// class id: 720, size: 0x8, field offset: 0x8
class qWa extends Object
    implements dSa {

  static late final vWa iog; // offset: 0x11b8
  static late final Map<String, String> _zsg; // offset: 0x11bc

  [closure] static (dynamic) => qWa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x46009c, size: -0x1
  }
  [closure] static qWa <anonymous closure>(dynamic) {
    // ** addr: 0x460228, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x460038, size: -0x1
  }
}
