// lib: Kqj, url: CNi

// class id: 1049666, size: 0x8
class :: {
}

// class id: 767, size: 0xc, field offset: 0xc
class UUa extends OTa {

  static late final vWa iog; // offset: 0x1258

  [closure] static UUa <anonymous closure>(dynamic) {
    // ** addr: 0x470cc4, size: -0x1
  }
  [closure] static UUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x470ddc, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x470c6c, size: -0x1
  }
}
