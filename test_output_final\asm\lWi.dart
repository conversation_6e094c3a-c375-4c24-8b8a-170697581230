// lib: , url: LWi

// class id: 1049028, size: 0x8
class :: {
}

// class id: 2285, size: 0x18, field offset: 0x8
class _pZ extends Object
    implements mZ {
}

// class id: 2286, size: 0x18, field offset: 0x8
//   const constructor, 
class _oZ extends Object
    implements lZ {
}

// class id: 2288, size: 0x8, field offset: 0x8
abstract class mZ extends Object {
}

// class id: 2289, size: 0x38, field offset: 0x8
//   const constructor, 
class lZ extends Object {
}

// class id: 5533, size: 0x14, field offset: 0x14
enum kZ extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
