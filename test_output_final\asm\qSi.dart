// lib: , url: QSi

// class id: 1048842, size: 0x8
class :: {

  static late final MG bVe; // offset: 0xc40
}

// class id: 2704, size: 0x8, field offset: 0x8
abstract class NH extends MG {
}

// class id: 2705, size: 0x8, field offset: 0x8
//   transformed mixin,
abstract class _MH extends NH
     with KG {
}

// class id: 2706, size: 0x8, field offset: 0x8
class OH extends _MH {
}

// class id: 4222, size: 0x10, field offset: 0xc
//   const constructor, 
class _LH extends fF {
}
