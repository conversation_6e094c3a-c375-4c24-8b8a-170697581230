// lib: Qoj, url: Dgj

// class id: 1049528, size: 0x8
class :: {
}

// class id: 3062, size: 0x24, field offset: 0x14
class _zPa extends Mt<dynamic> {

  late final bX _jJc; // offset: 0x14
  late r _bSc; // offset: 0x18

  [closure] void <anonymous closure>(dynamic, Ea) {
    // ** addr: 0x648c88, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, r) {
    // ** addr: 0x648e30, size: -0x1
  }
}

// class id: 3857, size: 0x18, field offset: 0xc
//   const constructor, 
class yPa extends It {
}
