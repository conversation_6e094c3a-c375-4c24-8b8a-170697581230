// lib: , url: package:flutter/src/services/message_codec.dart

// class id: 1049110, size: 0x8
class :: {
}

// class id: 1886, size: 0xc, field offset: 0x8
class dha extends Object
    implements Ja {
}

// class id: 1887, size: 0x18, field offset: 0x8
class cha extends Object
    implements Ja {
}

// class id: 1888, size: 0x8, field offset: 0x8
abstract class bha extends Object {
}

// class id: 1889, size: 0x10, field offset: 0x8
//   const constructor, 
class MethodCall extends Object {
}

// class id: 1890, size: 0xc, field offset: 0x8
abstract class aha<X0> extends Object {
}
