// lib: , url: VSi

// class id: 1049096, size: 0x8
class :: {
}

// class id: 1914, size: 0xc, field offset: 0x8
//   const constructor, 
abstract class Pfa extends Object {
}

// class id: 1915, size: 0xc, field offset: 0xc
//   const constructor, 
class nga extends Pfa {

  _OneByteString field_8;
}

// class id: 1916, size: 0xc, field offset: 0xc
//   const constructor, 
class mga extends Pfa {

  _OneByteString field_8;
}

// class id: 1917, size: 0xc, field offset: 0xc
//   const constructor, 
class lga extends Pfa {

  _OneByteString field_8;
}

// class id: 1918, size: 0x10, field offset: 0xc
//   const constructor, 
class kga extends Pfa {
}

// class id: 1919, size: 0x18, field offset: 0xc
//   const constructor, 
class jga extends Pfa {
}

// class id: 5503, size: 0x14, field offset: 0x14
enum iga extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
