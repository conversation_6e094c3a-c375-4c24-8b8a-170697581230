// lib: , url: Enj

// class id: 1049860, size: 0x8
class :: {
}

// class id: 329, size: 0x8, field offset: 0x8
abstract class Sfb extends Object {

  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x923b18, size: 0x128
    // 0x923b18: EnterFrame
    //     0x923b18: stp             fp, lr, [SP, #-0x10]!
    //     0x923b1c: mov             fp, SP
    // 0x923b20: AllocStack(0x28)
    //     0x923b20: sub             SP, SP, #0x28
    // 0x923b24: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x923b24: stur            NULL, [fp, #-8]
    //     0x923b28: movz            x0, #0
    //     0x923b2c: add             x1, fp, w0, sxtw #2
    //     0x923b30: ldr             x1, [x1, #0x18]
    //     0x923b34: add             x2, fp, w0, sxtw #2
    //     0x923b38: ldr             x2, [x2, #0x10]
    //     0x923b3c: stur            x2, [fp, #-0x18]
    //     0x923b40: ldur            w3, [x1, #0x17]
    //     0x923b44: add             x3, x3, HEAP, lsl #32
    //     0x923b48: stur            x3, [fp, #-0x10]
    // 0x923b4c: CheckStackOverflow
    //     0x923b4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x923b50: cmp             SP, x16
    //     0x923b54: b.ls            #0x923c30
    // 0x923b58: InitAsync() -> Future<Null?>
    //     0x923b58: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x923b5c: bl              #0x8c1de0  ; InitAsyncStub
    // 0x923b60: ldur            x0, [fp, #-0x18]
    // 0x923b64: r2 = Null
    //     0x923b64: mov             x2, NULL
    // 0x923b68: r1 = Null
    //     0x923b68: mov             x1, NULL
    // 0x923b6c: r4 = 59
    //     0x923b6c: movz            x4, #0x3b
    // 0x923b70: branchIfSmi(r0, 0x923b7c)
    //     0x923b70: tbz             w0, #0, #0x923b7c
    // 0x923b74: r4 = LoadClassIdInstr(r0)
    //     0x923b74: ldur            x4, [x0, #-1]
    //     0x923b78: ubfx            x4, x4, #0xc, #0x14
    // 0x923b7c: sub             x4, x4, #0x59
    // 0x923b80: cmp             x4, #2
    // 0x923b84: b.ls            #0x923b98
    // 0x923b88: r8 = List<Object?>?
    //     0x923b88: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x923b8c: r3 = Null
    //     0x923b8c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31278] Null
    //     0x923b90: ldr             x3, [x3, #0x278]
    // 0x923b94: r0 = List<Object?>?()
    //     0x923b94: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x923b98: ldur            x0, [fp, #-0x18]
    // 0x923b9c: cmp             w0, NULL
    // 0x923ba0: b.eq            #0x923c38
    // 0x923ba4: r1 = LoadClassIdInstr(r0)
    //     0x923ba4: ldur            x1, [x0, #-1]
    //     0x923ba8: ubfx            x1, x1, #0xc, #0x14
    // 0x923bac: stp             xzr, x0, [SP]
    // 0x923bb0: mov             x0, x1
    // 0x923bb4: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x923bb4: movz            x17, #0x32a
    //     0x923bb8: movk            x17, #0x1, lsl #16
    //     0x923bbc: add             lr, x0, x17
    //     0x923bc0: ldr             lr, [x21, lr, lsl #3]
    //     0x923bc4: blr             lr
    // 0x923bc8: mov             x3, x0
    // 0x923bcc: r2 = Null
    //     0x923bcc: mov             x2, NULL
    // 0x923bd0: r1 = Null
    //     0x923bd0: mov             x1, NULL
    // 0x923bd4: stur            x3, [fp, #-0x18]
    // 0x923bd8: branchIfSmi(r0, 0x923c00)
    //     0x923bd8: tbz             w0, #0, #0x923c00
    // 0x923bdc: r4 = LoadClassIdInstr(r0)
    //     0x923bdc: ldur            x4, [x0, #-1]
    //     0x923be0: ubfx            x4, x4, #0xc, #0x14
    // 0x923be4: sub             x4, x4, #0x3b
    // 0x923be8: cmp             x4, #1
    // 0x923bec: b.ls            #0x923c00
    // 0x923bf0: r8 = int?
    //     0x923bf0: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x923bf4: r3 = Null
    //     0x923bf4: add             x3, PP, #0x31, lsl #12  ; [pp+0x31288] Null
    //     0x923bf8: ldr             x3, [x3, #0x288]
    // 0x923bfc: r0 = int?()
    //     0x923bfc: bl              #0x959574  ; IsType_int?_Stub
    // 0x923c00: ldur            x0, [fp, #-0x10]
    // 0x923c04: LoadField: r1 = r0->field_f
    //     0x923c04: ldur            w1, [x0, #0xf]
    // 0x923c08: DecompressPointer r1
    //     0x923c08: add             x1, x1, HEAP, lsl #32
    // 0x923c0c: ldur            x0, [fp, #-0x18]
    // 0x923c10: cmp             w0, NULL
    // 0x923c14: b.eq            #0x923c3c
    // 0x923c18: r2 = LoadInt32Instr(r0)
    //     0x923c18: sbfx            x2, x0, #1, #0x1f
    //     0x923c1c: tbz             w0, #0, #0x923c24
    //     0x923c20: ldur            x2, [x0, #7]
    // 0x923c24: r0 = call 0x64d650
    //     0x923c24: bl              #0x64d650
    // 0x923c28: r0 = Null
    //     0x923c28: mov             x0, NULL
    // 0x923c2c: r0 = ReturnAsyncNotFuture()
    //     0x923c2c: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x923c30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x923c30: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x923c34: b               #0x923b58
    // 0x923c38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x923c38: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x923c3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x923c3c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 331, size: 0xc, field offset: 0x8
abstract class Rfb extends Object {
}

// class id: 333, size: 0x8, field offset: 0x8
abstract class Qfb extends Object {

  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x92450c, size: 0x128
    // 0x92450c: EnterFrame
    //     0x92450c: stp             fp, lr, [SP, #-0x10]!
    //     0x924510: mov             fp, SP
    // 0x924514: AllocStack(0x28)
    //     0x924514: sub             SP, SP, #0x28
    // 0x924518: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x924518: stur            NULL, [fp, #-8]
    //     0x92451c: movz            x0, #0
    //     0x924520: add             x1, fp, w0, sxtw #2
    //     0x924524: ldr             x1, [x1, #0x18]
    //     0x924528: add             x2, fp, w0, sxtw #2
    //     0x92452c: ldr             x2, [x2, #0x10]
    //     0x924530: stur            x2, [fp, #-0x18]
    //     0x924534: ldur            w3, [x1, #0x17]
    //     0x924538: add             x3, x3, HEAP, lsl #32
    //     0x92453c: stur            x3, [fp, #-0x10]
    // 0x924540: CheckStackOverflow
    //     0x924540: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x924544: cmp             SP, x16
    //     0x924548: b.ls            #0x924624
    // 0x92454c: InitAsync() -> Future<Null?>
    //     0x92454c: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x924550: bl              #0x8c1de0  ; InitAsyncStub
    // 0x924554: ldur            x0, [fp, #-0x18]
    // 0x924558: r2 = Null
    //     0x924558: mov             x2, NULL
    // 0x92455c: r1 = Null
    //     0x92455c: mov             x1, NULL
    // 0x924560: r4 = 59
    //     0x924560: movz            x4, #0x3b
    // 0x924564: branchIfSmi(r0, 0x924570)
    //     0x924564: tbz             w0, #0, #0x924570
    // 0x924568: r4 = LoadClassIdInstr(r0)
    //     0x924568: ldur            x4, [x0, #-1]
    //     0x92456c: ubfx            x4, x4, #0xc, #0x14
    // 0x924570: sub             x4, x4, #0x59
    // 0x924574: cmp             x4, #2
    // 0x924578: b.ls            #0x92458c
    // 0x92457c: r8 = List<Object?>?
    //     0x92457c: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x924580: r3 = Null
    //     0x924580: add             x3, PP, #0x31, lsl #12  ; [pp+0x31440] Null
    //     0x924584: ldr             x3, [x3, #0x440]
    // 0x924588: r0 = List<Object?>?()
    //     0x924588: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x92458c: ldur            x0, [fp, #-0x18]
    // 0x924590: cmp             w0, NULL
    // 0x924594: b.eq            #0x92462c
    // 0x924598: r1 = LoadClassIdInstr(r0)
    //     0x924598: ldur            x1, [x0, #-1]
    //     0x92459c: ubfx            x1, x1, #0xc, #0x14
    // 0x9245a0: stp             xzr, x0, [SP]
    // 0x9245a4: mov             x0, x1
    // 0x9245a8: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9245a8: movz            x17, #0x32a
    //     0x9245ac: movk            x17, #0x1, lsl #16
    //     0x9245b0: add             lr, x0, x17
    //     0x9245b4: ldr             lr, [x21, lr, lsl #3]
    //     0x9245b8: blr             lr
    // 0x9245bc: mov             x3, x0
    // 0x9245c0: r2 = Null
    //     0x9245c0: mov             x2, NULL
    // 0x9245c4: r1 = Null
    //     0x9245c4: mov             x1, NULL
    // 0x9245c8: stur            x3, [fp, #-0x18]
    // 0x9245cc: branchIfSmi(r0, 0x9245f4)
    //     0x9245cc: tbz             w0, #0, #0x9245f4
    // 0x9245d0: r4 = LoadClassIdInstr(r0)
    //     0x9245d0: ldur            x4, [x0, #-1]
    //     0x9245d4: ubfx            x4, x4, #0xc, #0x14
    // 0x9245d8: sub             x4, x4, #0x3b
    // 0x9245dc: cmp             x4, #1
    // 0x9245e0: b.ls            #0x9245f4
    // 0x9245e4: r8 = int?
    //     0x9245e4: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9245e8: r3 = Null
    //     0x9245e8: add             x3, PP, #0x31, lsl #12  ; [pp+0x31450] Null
    //     0x9245ec: ldr             x3, [x3, #0x450]
    // 0x9245f0: r0 = int?()
    //     0x9245f0: bl              #0x959574  ; IsType_int?_Stub
    // 0x9245f4: ldur            x0, [fp, #-0x10]
    // 0x9245f8: LoadField: r1 = r0->field_f
    //     0x9245f8: ldur            w1, [x0, #0xf]
    // 0x9245fc: DecompressPointer r1
    //     0x9245fc: add             x1, x1, HEAP, lsl #32
    // 0x924600: ldur            x0, [fp, #-0x18]
    // 0x924604: cmp             w0, NULL
    // 0x924608: b.eq            #0x924630
    // 0x92460c: r2 = LoadInt32Instr(r0)
    //     0x92460c: sbfx            x2, x0, #1, #0x1f
    //     0x924610: tbz             w0, #0, #0x924618
    //     0x924614: ldur            x2, [x0, #7]
    // 0x924618: r0 = call 0x64e274
    //     0x924618: bl              #0x64e274
    // 0x92461c: r0 = Null
    //     0x92461c: mov             x0, NULL
    // 0x924620: r0 = ReturnAsyncNotFuture()
    //     0x924620: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x924624: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x924624: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x924628: b               #0x92454c
    // 0x92462c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92462c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x924630: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924630: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 334, size: 0xc, field offset: 0x8
abstract class Pfb extends Object {
}

// class id: 336, size: 0x8, field offset: 0x8
abstract class Ofb extends Object {

  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x923c4c, size: 0x128
    // 0x923c4c: EnterFrame
    //     0x923c4c: stp             fp, lr, [SP, #-0x10]!
    //     0x923c50: mov             fp, SP
    // 0x923c54: AllocStack(0x28)
    //     0x923c54: sub             SP, SP, #0x28
    // 0x923c58: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x923c58: stur            NULL, [fp, #-8]
    //     0x923c5c: movz            x0, #0
    //     0x923c60: add             x1, fp, w0, sxtw #2
    //     0x923c64: ldr             x1, [x1, #0x18]
    //     0x923c68: add             x2, fp, w0, sxtw #2
    //     0x923c6c: ldr             x2, [x2, #0x10]
    //     0x923c70: stur            x2, [fp, #-0x18]
    //     0x923c74: ldur            w3, [x1, #0x17]
    //     0x923c78: add             x3, x3, HEAP, lsl #32
    //     0x923c7c: stur            x3, [fp, #-0x10]
    // 0x923c80: CheckStackOverflow
    //     0x923c80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x923c84: cmp             SP, x16
    //     0x923c88: b.ls            #0x923d64
    // 0x923c8c: InitAsync() -> Future<Null?>
    //     0x923c8c: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x923c90: bl              #0x8c1de0  ; InitAsyncStub
    // 0x923c94: ldur            x0, [fp, #-0x18]
    // 0x923c98: r2 = Null
    //     0x923c98: mov             x2, NULL
    // 0x923c9c: r1 = Null
    //     0x923c9c: mov             x1, NULL
    // 0x923ca0: r4 = 59
    //     0x923ca0: movz            x4, #0x3b
    // 0x923ca4: branchIfSmi(r0, 0x923cb0)
    //     0x923ca4: tbz             w0, #0, #0x923cb0
    // 0x923ca8: r4 = LoadClassIdInstr(r0)
    //     0x923ca8: ldur            x4, [x0, #-1]
    //     0x923cac: ubfx            x4, x4, #0xc, #0x14
    // 0x923cb0: sub             x4, x4, #0x59
    // 0x923cb4: cmp             x4, #2
    // 0x923cb8: b.ls            #0x923ccc
    // 0x923cbc: r8 = List<Object?>?
    //     0x923cbc: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x923cc0: r3 = Null
    //     0x923cc0: add             x3, PP, #0x31, lsl #12  ; [pp+0x312a8] Null
    //     0x923cc4: ldr             x3, [x3, #0x2a8]
    // 0x923cc8: r0 = List<Object?>?()
    //     0x923cc8: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x923ccc: ldur            x0, [fp, #-0x18]
    // 0x923cd0: cmp             w0, NULL
    // 0x923cd4: b.eq            #0x923d6c
    // 0x923cd8: r1 = LoadClassIdInstr(r0)
    //     0x923cd8: ldur            x1, [x0, #-1]
    //     0x923cdc: ubfx            x1, x1, #0xc, #0x14
    // 0x923ce0: stp             xzr, x0, [SP]
    // 0x923ce4: mov             x0, x1
    // 0x923ce8: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x923ce8: movz            x17, #0x32a
    //     0x923cec: movk            x17, #0x1, lsl #16
    //     0x923cf0: add             lr, x0, x17
    //     0x923cf4: ldr             lr, [x21, lr, lsl #3]
    //     0x923cf8: blr             lr
    // 0x923cfc: mov             x3, x0
    // 0x923d00: r2 = Null
    //     0x923d00: mov             x2, NULL
    // 0x923d04: r1 = Null
    //     0x923d04: mov             x1, NULL
    // 0x923d08: stur            x3, [fp, #-0x18]
    // 0x923d0c: branchIfSmi(r0, 0x923d34)
    //     0x923d0c: tbz             w0, #0, #0x923d34
    // 0x923d10: r4 = LoadClassIdInstr(r0)
    //     0x923d10: ldur            x4, [x0, #-1]
    //     0x923d14: ubfx            x4, x4, #0xc, #0x14
    // 0x923d18: sub             x4, x4, #0x3b
    // 0x923d1c: cmp             x4, #1
    // 0x923d20: b.ls            #0x923d34
    // 0x923d24: r8 = int?
    //     0x923d24: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x923d28: r3 = Null
    //     0x923d28: add             x3, PP, #0x31, lsl #12  ; [pp+0x312b8] Null
    //     0x923d2c: ldr             x3, [x3, #0x2b8]
    // 0x923d30: r0 = int?()
    //     0x923d30: bl              #0x959574  ; IsType_int?_Stub
    // 0x923d34: ldur            x0, [fp, #-0x10]
    // 0x923d38: LoadField: r1 = r0->field_f
    //     0x923d38: ldur            w1, [x0, #0xf]
    // 0x923d3c: DecompressPointer r1
    //     0x923d3c: add             x1, x1, HEAP, lsl #32
    // 0x923d40: ldur            x0, [fp, #-0x18]
    // 0x923d44: cmp             w0, NULL
    // 0x923d48: b.eq            #0x923d70
    // 0x923d4c: r2 = LoadInt32Instr(r0)
    //     0x923d4c: sbfx            x2, x0, #1, #0x1f
    //     0x923d50: tbz             w0, #0, #0x923d58
    //     0x923d54: ldur            x2, [x0, #7]
    // 0x923d58: r0 = call 0x64d778
    //     0x923d58: bl              #0x64d778
    // 0x923d5c: r0 = Null
    //     0x923d5c: mov             x0, NULL
    // 0x923d60: r0 = ReturnAsyncNotFuture()
    //     0x923d60: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x923d64: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x923d64: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x923d68: b               #0x923c8c
    // 0x923d6c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x923d6c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x923d70: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x923d70: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 337, size: 0x8, field offset: 0x8
abstract class Nfb extends Object {

  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x923d80, size: 0x128
    // 0x923d80: EnterFrame
    //     0x923d80: stp             fp, lr, [SP, #-0x10]!
    //     0x923d84: mov             fp, SP
    // 0x923d88: AllocStack(0x28)
    //     0x923d88: sub             SP, SP, #0x28
    // 0x923d8c: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x923d8c: stur            NULL, [fp, #-8]
    //     0x923d90: movz            x0, #0
    //     0x923d94: add             x1, fp, w0, sxtw #2
    //     0x923d98: ldr             x1, [x1, #0x18]
    //     0x923d9c: add             x2, fp, w0, sxtw #2
    //     0x923da0: ldr             x2, [x2, #0x10]
    //     0x923da4: stur            x2, [fp, #-0x18]
    //     0x923da8: ldur            w3, [x1, #0x17]
    //     0x923dac: add             x3, x3, HEAP, lsl #32
    //     0x923db0: stur            x3, [fp, #-0x10]
    // 0x923db4: CheckStackOverflow
    //     0x923db4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x923db8: cmp             SP, x16
    //     0x923dbc: b.ls            #0x923e98
    // 0x923dc0: InitAsync() -> Future<Null?>
    //     0x923dc0: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x923dc4: bl              #0x8c1de0  ; InitAsyncStub
    // 0x923dc8: ldur            x0, [fp, #-0x18]
    // 0x923dcc: r2 = Null
    //     0x923dcc: mov             x2, NULL
    // 0x923dd0: r1 = Null
    //     0x923dd0: mov             x1, NULL
    // 0x923dd4: r4 = 59
    //     0x923dd4: movz            x4, #0x3b
    // 0x923dd8: branchIfSmi(r0, 0x923de4)
    //     0x923dd8: tbz             w0, #0, #0x923de4
    // 0x923ddc: r4 = LoadClassIdInstr(r0)
    //     0x923ddc: ldur            x4, [x0, #-1]
    //     0x923de0: ubfx            x4, x4, #0xc, #0x14
    // 0x923de4: sub             x4, x4, #0x59
    // 0x923de8: cmp             x4, #2
    // 0x923dec: b.ls            #0x923e00
    // 0x923df0: r8 = List<Object?>?
    //     0x923df0: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x923df4: r3 = Null
    //     0x923df4: add             x3, PP, #0x31, lsl #12  ; [pp+0x312d8] Null
    //     0x923df8: ldr             x3, [x3, #0x2d8]
    // 0x923dfc: r0 = List<Object?>?()
    //     0x923dfc: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x923e00: ldur            x0, [fp, #-0x18]
    // 0x923e04: cmp             w0, NULL
    // 0x923e08: b.eq            #0x923ea0
    // 0x923e0c: r1 = LoadClassIdInstr(r0)
    //     0x923e0c: ldur            x1, [x0, #-1]
    //     0x923e10: ubfx            x1, x1, #0xc, #0x14
    // 0x923e14: stp             xzr, x0, [SP]
    // 0x923e18: mov             x0, x1
    // 0x923e1c: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x923e1c: movz            x17, #0x32a
    //     0x923e20: movk            x17, #0x1, lsl #16
    //     0x923e24: add             lr, x0, x17
    //     0x923e28: ldr             lr, [x21, lr, lsl #3]
    //     0x923e2c: blr             lr
    // 0x923e30: mov             x3, x0
    // 0x923e34: r2 = Null
    //     0x923e34: mov             x2, NULL
    // 0x923e38: r1 = Null
    //     0x923e38: mov             x1, NULL
    // 0x923e3c: stur            x3, [fp, #-0x18]
    // 0x923e40: branchIfSmi(r0, 0x923e68)
    //     0x923e40: tbz             w0, #0, #0x923e68
    // 0x923e44: r4 = LoadClassIdInstr(r0)
    //     0x923e44: ldur            x4, [x0, #-1]
    //     0x923e48: ubfx            x4, x4, #0xc, #0x14
    // 0x923e4c: sub             x4, x4, #0x3b
    // 0x923e50: cmp             x4, #1
    // 0x923e54: b.ls            #0x923e68
    // 0x923e58: r8 = int?
    //     0x923e58: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x923e5c: r3 = Null
    //     0x923e5c: add             x3, PP, #0x31, lsl #12  ; [pp+0x312e8] Null
    //     0x923e60: ldr             x3, [x3, #0x2e8]
    // 0x923e64: r0 = int?()
    //     0x923e64: bl              #0x959574  ; IsType_int?_Stub
    // 0x923e68: ldur            x0, [fp, #-0x10]
    // 0x923e6c: LoadField: r1 = r0->field_f
    //     0x923e6c: ldur            w1, [x0, #0xf]
    // 0x923e70: DecompressPointer r1
    //     0x923e70: add             x1, x1, HEAP, lsl #32
    // 0x923e74: ldur            x0, [fp, #-0x18]
    // 0x923e78: cmp             w0, NULL
    // 0x923e7c: b.eq            #0x923ea4
    // 0x923e80: r2 = LoadInt32Instr(r0)
    //     0x923e80: sbfx            x2, x0, #1, #0x1f
    //     0x923e84: tbz             w0, #0, #0x923e8c
    //     0x923e88: ldur            x2, [x0, #7]
    // 0x923e8c: r0 = call 0x64d878
    //     0x923e8c: bl              #0x64d878
    // 0x923e90: r0 = Null
    //     0x923e90: mov             x0, NULL
    // 0x923e94: r0 = ReturnAsyncNotFuture()
    //     0x923e94: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x923e98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x923e98: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x923e9c: b               #0x923dc0
    // 0x923ea0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x923ea0: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x923ea4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x923ea4: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 338, size: 0xc, field offset: 0x8
abstract class Mfb extends Object {
}

// class id: 340, size: 0x8, field offset: 0x8
abstract class Lfb extends Object {

  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x923ec0, size: 0x1e4
    // 0x923ec0: EnterFrame
    //     0x923ec0: stp             fp, lr, [SP, #-0x10]!
    //     0x923ec4: mov             fp, SP
    // 0x923ec8: AllocStack(0x30)
    //     0x923ec8: sub             SP, SP, #0x30
    // 0x923ecc: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x923ecc: stur            NULL, [fp, #-8]
    //     0x923ed0: movz            x0, #0
    //     0x923ed4: add             x1, fp, w0, sxtw #2
    //     0x923ed8: ldr             x1, [x1, #0x18]
    //     0x923edc: add             x2, fp, w0, sxtw #2
    //     0x923ee0: ldr             x2, [x2, #0x10]
    //     0x923ee4: stur            x2, [fp, #-0x18]
    //     0x923ee8: ldur            w3, [x1, #0x17]
    //     0x923eec: add             x3, x3, HEAP, lsl #32
    //     0x923ef0: stur            x3, [fp, #-0x10]
    // 0x923ef4: CheckStackOverflow
    //     0x923ef4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x923ef8: cmp             SP, x16
    //     0x923efc: b.ls            #0x924090
    // 0x923f00: InitAsync() -> Future<Null?>
    //     0x923f00: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x923f04: bl              #0x8c1de0  ; InitAsyncStub
    // 0x923f08: ldur            x0, [fp, #-0x18]
    // 0x923f0c: r2 = Null
    //     0x923f0c: mov             x2, NULL
    // 0x923f10: r1 = Null
    //     0x923f10: mov             x1, NULL
    // 0x923f14: r4 = 59
    //     0x923f14: movz            x4, #0x3b
    // 0x923f18: branchIfSmi(r0, 0x923f24)
    //     0x923f18: tbz             w0, #0, #0x923f24
    // 0x923f1c: r4 = LoadClassIdInstr(r0)
    //     0x923f1c: ldur            x4, [x0, #-1]
    //     0x923f20: ubfx            x4, x4, #0xc, #0x14
    // 0x923f24: sub             x4, x4, #0x59
    // 0x923f28: cmp             x4, #2
    // 0x923f2c: b.ls            #0x923f40
    // 0x923f30: r8 = List<Object?>?
    //     0x923f30: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x923f34: r3 = Null
    //     0x923f34: add             x3, PP, #0x31, lsl #12  ; [pp+0x31308] Null
    //     0x923f38: ldr             x3, [x3, #0x308]
    // 0x923f3c: r0 = List<Object?>?()
    //     0x923f3c: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x923f40: ldur            x1, [fp, #-0x18]
    // 0x923f44: cmp             w1, NULL
    // 0x923f48: b.eq            #0x924098
    // 0x923f4c: r0 = LoadClassIdInstr(r1)
    //     0x923f4c: ldur            x0, [x1, #-1]
    //     0x923f50: ubfx            x0, x0, #0xc, #0x14
    // 0x923f54: stp             xzr, x1, [SP]
    // 0x923f58: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x923f58: movz            x17, #0x32a
    //     0x923f5c: movk            x17, #0x1, lsl #16
    //     0x923f60: add             lr, x0, x17
    //     0x923f64: ldr             lr, [x21, lr, lsl #3]
    //     0x923f68: blr             lr
    // 0x923f6c: mov             x3, x0
    // 0x923f70: r2 = Null
    //     0x923f70: mov             x2, NULL
    // 0x923f74: r1 = Null
    //     0x923f74: mov             x1, NULL
    // 0x923f78: stur            x3, [fp, #-0x20]
    // 0x923f7c: branchIfSmi(r0, 0x923fa4)
    //     0x923f7c: tbz             w0, #0, #0x923fa4
    // 0x923f80: r4 = LoadClassIdInstr(r0)
    //     0x923f80: ldur            x4, [x0, #-1]
    //     0x923f84: ubfx            x4, x4, #0xc, #0x14
    // 0x923f88: sub             x4, x4, #0x3b
    // 0x923f8c: cmp             x4, #1
    // 0x923f90: b.ls            #0x923fa4
    // 0x923f94: r8 = int?
    //     0x923f94: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x923f98: r3 = Null
    //     0x923f98: add             x3, PP, #0x31, lsl #12  ; [pp+0x31318] Null
    //     0x923f9c: ldr             x3, [x3, #0x318]
    // 0x923fa0: r0 = int?()
    //     0x923fa0: bl              #0x959574  ; IsType_int?_Stub
    // 0x923fa4: ldur            x0, [fp, #-0x18]
    // 0x923fa8: r1 = LoadClassIdInstr(r0)
    //     0x923fa8: ldur            x1, [x0, #-1]
    //     0x923fac: ubfx            x1, x1, #0xc, #0x14
    // 0x923fb0: r16 = 2
    //     0x923fb0: movz            x16, #0x2
    // 0x923fb4: stp             x16, x0, [SP]
    // 0x923fb8: mov             x0, x1
    // 0x923fbc: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x923fbc: movz            x17, #0x32a
    //     0x923fc0: movk            x17, #0x1, lsl #16
    //     0x923fc4: add             lr, x0, x17
    //     0x923fc8: ldr             lr, [x21, lr, lsl #3]
    //     0x923fcc: blr             lr
    // 0x923fd0: mov             x3, x0
    // 0x923fd4: r2 = Null
    //     0x923fd4: mov             x2, NULL
    // 0x923fd8: r1 = Null
    //     0x923fd8: mov             x1, NULL
    // 0x923fdc: stur            x3, [fp, #-0x18]
    // 0x923fe0: r4 = 59
    //     0x923fe0: movz            x4, #0x3b
    // 0x923fe4: branchIfSmi(r0, 0x923ff0)
    //     0x923fe4: tbz             w0, #0, #0x923ff0
    // 0x923fe8: r4 = LoadClassIdInstr(r0)
    //     0x923fe8: ldur            x4, [x0, #-1]
    //     0x923fec: ubfx            x4, x4, #0xc, #0x14
    // 0x923ff0: sub             x4, x4, #0x59
    // 0x923ff4: cmp             x4, #2
    // 0x923ff8: b.ls            #0x92400c
    // 0x923ffc: r8 = List<Object?>?
    //     0x923ffc: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x924000: r3 = Null
    //     0x924000: add             x3, PP, #0x31, lsl #12  ; [pp+0x31328] Null
    //     0x924004: ldr             x3, [x3, #0x328]
    // 0x924008: r0 = List<Object?>?()
    //     0x924008: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x92400c: ldur            x0, [fp, #-0x18]
    // 0x924010: cmp             w0, NULL
    // 0x924014: b.ne            #0x924020
    // 0x924018: r3 = Null
    //     0x924018: mov             x3, NULL
    // 0x92401c: b               #0x92404c
    // 0x924020: r1 = LoadClassIdInstr(r0)
    //     0x924020: ldur            x1, [x0, #-1]
    //     0x924024: ubfx            x1, x1, #0xc, #0x14
    // 0x924028: r16 = <String?>
    //     0x924028: ldr             x16, [PP, #0x2718]  ; [pp+0x2718] TypeArguments: <String?>
    // 0x92402c: stp             x0, x16, [SP]
    // 0x924030: mov             x0, x1
    // 0x924034: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x924034: ldr             x4, [PP, #0x9e0]  ; [pp+0x9e0] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x924038: r0 = GDT[cid_x0 + 0xb268]()
    //     0x924038: movz            x17, #0xb268
    //     0x92403c: add             lr, x0, x17
    //     0x924040: ldr             lr, [x21, lr, lsl #3]
    //     0x924044: blr             lr
    // 0x924048: mov             x3, x0
    // 0x92404c: ldur            x1, [fp, #-0x10]
    // 0x924050: ldur            x0, [fp, #-0x20]
    // 0x924054: LoadField: r2 = r1->field_f
    //     0x924054: ldur            w2, [x1, #0xf]
    // 0x924058: DecompressPointer r2
    //     0x924058: add             x2, x2, HEAP, lsl #32
    // 0x92405c: cmp             w0, NULL
    // 0x924060: b.eq            #0x92409c
    // 0x924064: cmp             w3, NULL
    // 0x924068: b.eq            #0x9240a0
    // 0x92406c: r1 = LoadInt32Instr(r0)
    //     0x92406c: sbfx            x1, x0, #1, #0x1f
    //     0x924070: tbz             w0, #0, #0x924078
    //     0x924074: ldur            x1, [x0, #7]
    // 0x924078: mov             x16, x1
    // 0x92407c: mov             x1, x2
    // 0x924080: mov             x2, x16
    // 0x924084: r0 = call 0x64d9e8
    //     0x924084: bl              #0x64d9e8
    // 0x924088: r0 = Null
    //     0x924088: mov             x0, NULL
    // 0x92408c: r0 = ReturnAsyncNotFuture()
    //     0x92408c: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x924090: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x924090: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x924094: b               #0x923f00
    // 0x924098: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924098: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92409c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92409c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9240a0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9240a0: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 341, size: 0xc, field offset: 0x8
abstract class Kfb extends Object {
}

// class id: 343, size: 0x8, field offset: 0x8
abstract class Jfb extends Object {

  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x92464c, size: 0x3c0
    // 0x92464c: EnterFrame
    //     0x92464c: stp             fp, lr, [SP, #-0x10]!
    //     0x924650: mov             fp, SP
    // 0x924654: AllocStack(0x48)
    //     0x924654: sub             SP, SP, #0x48
    // 0x924658: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x924658: stur            NULL, [fp, #-8]
    //     0x92465c: movz            x0, #0
    //     0x924660: add             x1, fp, w0, sxtw #2
    //     0x924664: ldr             x1, [x1, #0x18]
    //     0x924668: add             x2, fp, w0, sxtw #2
    //     0x92466c: ldr             x2, [x2, #0x10]
    //     0x924670: stur            x2, [fp, #-0x18]
    //     0x924674: ldur            w3, [x1, #0x17]
    //     0x924678: add             x3, x3, HEAP, lsl #32
    //     0x92467c: stur            x3, [fp, #-0x10]
    // 0x924680: CheckStackOverflow
    //     0x924680: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x924684: cmp             SP, x16
    //     0x924688: b.ls            #0x9249e8
    // 0x92468c: InitAsync() -> Future<Null?>
    //     0x92468c: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x924690: bl              #0x8c1de0  ; InitAsyncStub
    // 0x924694: ldur            x0, [fp, #-0x18]
    // 0x924698: r2 = Null
    //     0x924698: mov             x2, NULL
    // 0x92469c: r1 = Null
    //     0x92469c: mov             x1, NULL
    // 0x9246a0: r4 = 59
    //     0x9246a0: movz            x4, #0x3b
    // 0x9246a4: branchIfSmi(r0, 0x9246b0)
    //     0x9246a4: tbz             w0, #0, #0x9246b0
    // 0x9246a8: r4 = LoadClassIdInstr(r0)
    //     0x9246a8: ldur            x4, [x0, #-1]
    //     0x9246ac: ubfx            x4, x4, #0xc, #0x14
    // 0x9246b0: sub             x4, x4, #0x59
    // 0x9246b4: cmp             x4, #2
    // 0x9246b8: b.ls            #0x9246cc
    // 0x9246bc: r8 = List<Object?>?
    //     0x9246bc: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x9246c0: r3 = Null
    //     0x9246c0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31470] Null
    //     0x9246c4: ldr             x3, [x3, #0x470]
    // 0x9246c8: r0 = List<Object?>?()
    //     0x9246c8: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x9246cc: ldur            x1, [fp, #-0x18]
    // 0x9246d0: cmp             w1, NULL
    // 0x9246d4: b.eq            #0x9249f0
    // 0x9246d8: r0 = LoadClassIdInstr(r1)
    //     0x9246d8: ldur            x0, [x1, #-1]
    //     0x9246dc: ubfx            x0, x0, #0xc, #0x14
    // 0x9246e0: stp             xzr, x1, [SP]
    // 0x9246e4: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9246e4: movz            x17, #0x32a
    //     0x9246e8: movk            x17, #0x1, lsl #16
    //     0x9246ec: add             lr, x0, x17
    //     0x9246f0: ldr             lr, [x21, lr, lsl #3]
    //     0x9246f4: blr             lr
    // 0x9246f8: mov             x3, x0
    // 0x9246fc: r2 = Null
    //     0x9246fc: mov             x2, NULL
    // 0x924700: r1 = Null
    //     0x924700: mov             x1, NULL
    // 0x924704: stur            x3, [fp, #-0x20]
    // 0x924708: branchIfSmi(r0, 0x924730)
    //     0x924708: tbz             w0, #0, #0x924730
    // 0x92470c: r4 = LoadClassIdInstr(r0)
    //     0x92470c: ldur            x4, [x0, #-1]
    //     0x924710: ubfx            x4, x4, #0xc, #0x14
    // 0x924714: sub             x4, x4, #0x3b
    // 0x924718: cmp             x4, #1
    // 0x92471c: b.ls            #0x924730
    // 0x924720: r8 = int?
    //     0x924720: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x924724: r3 = Null
    //     0x924724: add             x3, PP, #0x31, lsl #12  ; [pp+0x31480] Null
    //     0x924728: ldr             x3, [x3, #0x480]
    // 0x92472c: r0 = int?()
    //     0x92472c: bl              #0x959574  ; IsType_int?_Stub
    // 0x924730: ldur            x1, [fp, #-0x18]
    // 0x924734: r0 = LoadClassIdInstr(r1)
    //     0x924734: ldur            x0, [x1, #-1]
    //     0x924738: ubfx            x0, x0, #0xc, #0x14
    // 0x92473c: r16 = 2
    //     0x92473c: movz            x16, #0x2
    // 0x924740: stp             x16, x1, [SP]
    // 0x924744: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924744: movz            x17, #0x32a
    //     0x924748: movk            x17, #0x1, lsl #16
    //     0x92474c: add             lr, x0, x17
    //     0x924750: ldr             lr, [x21, lr, lsl #3]
    //     0x924754: blr             lr
    // 0x924758: mov             x3, x0
    // 0x92475c: r2 = Null
    //     0x92475c: mov             x2, NULL
    // 0x924760: r1 = Null
    //     0x924760: mov             x1, NULL
    // 0x924764: stur            x3, [fp, #-0x28]
    // 0x924768: r4 = 59
    //     0x924768: movz            x4, #0x3b
    // 0x92476c: branchIfSmi(r0, 0x924778)
    //     0x92476c: tbz             w0, #0, #0x924778
    // 0x924770: r4 = LoadClassIdInstr(r0)
    //     0x924770: ldur            x4, [x0, #-1]
    //     0x924774: ubfx            x4, x4, #0xc, #0x14
    // 0x924778: cmp             x4, #0x3e
    // 0x92477c: b.eq            #0x924790
    // 0x924780: r8 = bool?
    //     0x924780: ldr             x8, [PP, #0xb48]  ; [pp+0xb48] Type: bool?
    // 0x924784: r3 = Null
    //     0x924784: add             x3, PP, #0x31, lsl #12  ; [pp+0x31490] Null
    //     0x924788: ldr             x3, [x3, #0x490]
    // 0x92478c: r0 = bool?()
    //     0x92478c: bl              #0x8c2694  ; IsType_bool?_Stub
    // 0x924790: ldur            x1, [fp, #-0x18]
    // 0x924794: r0 = LoadClassIdInstr(r1)
    //     0x924794: ldur            x0, [x1, #-1]
    //     0x924798: ubfx            x0, x0, #0xc, #0x14
    // 0x92479c: r16 = 4
    //     0x92479c: movz            x16, #0x4
    // 0x9247a0: stp             x16, x1, [SP]
    // 0x9247a4: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9247a4: movz            x17, #0x32a
    //     0x9247a8: movk            x17, #0x1, lsl #16
    //     0x9247ac: add             lr, x0, x17
    //     0x9247b0: ldr             lr, [x21, lr, lsl #3]
    //     0x9247b4: blr             lr
    // 0x9247b8: mov             x3, x0
    // 0x9247bc: r2 = Null
    //     0x9247bc: mov             x2, NULL
    // 0x9247c0: r1 = Null
    //     0x9247c0: mov             x1, NULL
    // 0x9247c4: stur            x3, [fp, #-0x30]
    // 0x9247c8: r4 = 59
    //     0x9247c8: movz            x4, #0x3b
    // 0x9247cc: branchIfSmi(r0, 0x9247d8)
    //     0x9247cc: tbz             w0, #0, #0x9247d8
    // 0x9247d0: r4 = LoadClassIdInstr(r0)
    //     0x9247d0: ldur            x4, [x0, #-1]
    //     0x9247d4: ubfx            x4, x4, #0xc, #0x14
    // 0x9247d8: sub             x4, x4, #0x59
    // 0x9247dc: cmp             x4, #2
    // 0x9247e0: b.ls            #0x9247f4
    // 0x9247e4: r8 = List<Object?>?
    //     0x9247e4: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x9247e8: r3 = Null
    //     0x9247e8: add             x3, PP, #0x31, lsl #12  ; [pp+0x314a0] Null
    //     0x9247ec: ldr             x3, [x3, #0x4a0]
    // 0x9247f0: r0 = List<Object?>?()
    //     0x9247f0: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x9247f4: ldur            x0, [fp, #-0x30]
    // 0x9247f8: cmp             w0, NULL
    // 0x9247fc: b.ne            #0x924808
    // 0x924800: r5 = Null
    //     0x924800: mov             x5, NULL
    // 0x924804: b               #0x924834
    // 0x924808: r1 = LoadClassIdInstr(r0)
    //     0x924808: ldur            x1, [x0, #-1]
    //     0x92480c: ubfx            x1, x1, #0xc, #0x14
    // 0x924810: r16 = <String?>
    //     0x924810: ldr             x16, [PP, #0x2718]  ; [pp+0x2718] TypeArguments: <String?>
    // 0x924814: stp             x0, x16, [SP]
    // 0x924818: mov             x0, x1
    // 0x92481c: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x92481c: ldr             x4, [PP, #0x9e0]  ; [pp+0x9e0] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x924820: r0 = GDT[cid_x0 + 0xb268]()
    //     0x924820: movz            x17, #0xb268
    //     0x924824: add             lr, x0, x17
    //     0x924828: ldr             lr, [x21, lr, lsl #3]
    //     0x92482c: blr             lr
    // 0x924830: mov             x5, x0
    // 0x924834: ldur            x1, [fp, #-0x18]
    // 0x924838: stur            x5, [fp, #-0x30]
    // 0x92483c: r0 = LoadClassIdInstr(r1)
    //     0x92483c: ldur            x0, [x1, #-1]
    //     0x924840: ubfx            x0, x0, #0xc, #0x14
    // 0x924844: r16 = 6
    //     0x924844: movz            x16, #0x6
    // 0x924848: stp             x16, x1, [SP]
    // 0x92484c: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x92484c: movz            x17, #0x32a
    //     0x924850: movk            x17, #0x1, lsl #16
    //     0x924854: add             lr, x0, x17
    //     0x924858: ldr             lr, [x21, lr, lsl #3]
    //     0x92485c: blr             lr
    // 0x924860: cmp             w0, NULL
    // 0x924864: b.ne            #0x924870
    // 0x924868: r6 = Null
    //     0x924868: mov             x6, NULL
    // 0x92486c: b               #0x924914
    // 0x924870: ldur            x1, [fp, #-0x18]
    // 0x924874: r0 = LoadClassIdInstr(r1)
    //     0x924874: ldur            x0, [x1, #-1]
    //     0x924878: ubfx            x0, x0, #0xc, #0x14
    // 0x92487c: r16 = 6
    //     0x92487c: movz            x16, #0x6
    // 0x924880: stp             x16, x1, [SP]
    // 0x924884: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924884: movz            x17, #0x32a
    //     0x924888: movk            x17, #0x1, lsl #16
    //     0x92488c: add             lr, x0, x17
    //     0x924890: ldr             lr, [x21, lr, lsl #3]
    //     0x924894: blr             lr
    // 0x924898: mov             x3, x0
    // 0x92489c: stur            x3, [fp, #-0x38]
    // 0x9248a0: cmp             w3, NULL
    // 0x9248a4: b.eq            #0x9249f4
    // 0x9248a8: r3 as int
    //     0x9248a8: mov             x0, x3
    //     0x9248ac: mov             x2, NULL
    //     0x9248b0: mov             x1, NULL
    //     0x9248b4: tbz             w0, #0, #0x9248dc
    //     0x9248b8: ldur            x4, [x0, #-1]
    //     0x9248bc: ubfx            x4, x4, #0xc, #0x14
    //     0x9248c0: sub             x4, x4, #0x3b
    //     0x9248c4: cmp             x4, #1
    //     0x9248c8: b.ls            #0x9248dc
    //     0x9248cc: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    //     0x9248d0: add             x3, PP, #0x31, lsl #12  ; [pp+0x314b0] Null
    //     0x9248d4: ldr             x3, [x3, #0x4b0]
    //     0x9248d8: bl              #0x9595b0  ; IsType_int_Stub
    // 0x9248dc: ldur            x0, [fp, #-0x38]
    // 0x9248e0: r2 = LoadInt32Instr(r0)
    //     0x9248e0: sbfx            x2, x0, #1, #0x1f
    //     0x9248e4: tbz             w0, #0, #0x9248ec
    //     0x9248e8: ldur            x2, [x0, #7]
    // 0x9248ec: mov             x1, x2
    // 0x9248f0: r0 = 3
    //     0x9248f0: movz            x0, #0x3
    // 0x9248f4: cmp             x1, x0
    // 0x9248f8: b.hs            #0x9249f8
    // 0x9248fc: r0 = const [Instance of 'Teb', Instance of 'Teb', Instance of 'Teb']
    //     0x9248fc: add             x0, PP, #0x31, lsl #12  ; [pp+0x314c0] List<Teb>(3)
    //     0x924900: ldr             x0, [x0, #0x4c0]
    // 0x924904: ArrayLoad: r1 = r0[r2]  ; Unknown_4
    //     0x924904: add             x16, x0, x2, lsl #2
    //     0x924908: ldur            w1, [x16, #0xf]
    // 0x92490c: DecompressPointer r1
    //     0x92490c: add             x1, x1, HEAP, lsl #32
    // 0x924910: mov             x6, x1
    // 0x924914: ldur            x0, [fp, #-0x18]
    // 0x924918: ldur            x2, [fp, #-0x10]
    // 0x92491c: ldur            x1, [fp, #-0x20]
    // 0x924920: ldur            x3, [fp, #-0x28]
    // 0x924924: ldur            x5, [fp, #-0x30]
    // 0x924928: stur            x6, [fp, #-0x38]
    // 0x92492c: r4 = LoadClassIdInstr(r0)
    //     0x92492c: ldur            x4, [x0, #-1]
    //     0x924930: ubfx            x4, x4, #0xc, #0x14
    // 0x924934: r16 = 8
    //     0x924934: movz            x16, #0x8
    // 0x924938: stp             x16, x0, [SP]
    // 0x92493c: mov             x0, x4
    // 0x924940: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924940: movz            x17, #0x32a
    //     0x924944: movk            x17, #0x1, lsl #16
    //     0x924948: add             lr, x0, x17
    //     0x92494c: ldr             lr, [x21, lr, lsl #3]
    //     0x924950: blr             lr
    // 0x924954: mov             x3, x0
    // 0x924958: r2 = Null
    //     0x924958: mov             x2, NULL
    // 0x92495c: r1 = Null
    //     0x92495c: mov             x1, NULL
    // 0x924960: stur            x3, [fp, #-0x18]
    // 0x924964: r4 = 59
    //     0x924964: movz            x4, #0x3b
    // 0x924968: branchIfSmi(r0, 0x924974)
    //     0x924968: tbz             w0, #0, #0x924974
    // 0x92496c: r4 = LoadClassIdInstr(r0)
    //     0x92496c: ldur            x4, [x0, #-1]
    //     0x924970: ubfx            x4, x4, #0xc, #0x14
    // 0x924974: sub             x4, x4, #0x5d
    // 0x924978: cmp             x4, #1
    // 0x92497c: b.ls            #0x924990
    // 0x924980: r8 = String?
    //     0x924980: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x924984: r3 = Null
    //     0x924984: add             x3, PP, #0x31, lsl #12  ; [pp+0x314c8] Null
    //     0x924988: ldr             x3, [x3, #0x4c8]
    // 0x92498c: r0 = String?()
    //     0x92498c: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x924990: ldur            x0, [fp, #-0x10]
    // 0x924994: LoadField: r1 = r0->field_f
    //     0x924994: ldur            w1, [x0, #0xf]
    // 0x924998: DecompressPointer r1
    //     0x924998: add             x1, x1, HEAP, lsl #32
    // 0x92499c: ldur            x0, [fp, #-0x20]
    // 0x9249a0: cmp             w0, NULL
    // 0x9249a4: b.eq            #0x9249fc
    // 0x9249a8: ldur            x3, [fp, #-0x28]
    // 0x9249ac: cmp             w3, NULL
    // 0x9249b0: b.eq            #0x924a00
    // 0x9249b4: ldur            x5, [fp, #-0x30]
    // 0x9249b8: cmp             w5, NULL
    // 0x9249bc: b.eq            #0x924a04
    // 0x9249c0: ldur            x6, [fp, #-0x38]
    // 0x9249c4: cmp             w6, NULL
    // 0x9249c8: b.eq            #0x924a08
    // 0x9249cc: r2 = LoadInt32Instr(r0)
    //     0x9249cc: sbfx            x2, x0, #1, #0x1f
    //     0x9249d0: tbz             w0, #0, #0x9249d8
    //     0x9249d4: ldur            x2, [x0, #7]
    // 0x9249d8: ldur            x7, [fp, #-0x18]
    // 0x9249dc: r0 = call 0x64e3e4
    //     0x9249dc: bl              #0x64e3e4
    // 0x9249e0: r0 = Null
    //     0x9249e0: mov             x0, NULL
    // 0x9249e4: r0 = ReturnAsyncNotFuture()
    //     0x9249e4: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x9249e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9249e8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9249ec: b               #0x92468c
    // 0x9249f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9249f0: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9249f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9249f4: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9249f8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9249f8: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
    // 0x9249fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9249fc: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x924a00: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924a00: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x924a04: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924a04: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x924a08: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924a08: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 345, size: 0xc, field offset: 0x8
abstract class Ifb extends Object {
}

// class id: 347, size: 0x8, field offset: 0x8
abstract class Hfb extends Object {

  [closure] static Future<String> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x924bb4, size: 0x290
    // 0x924bb4: EnterFrame
    //     0x924bb4: stp             fp, lr, [SP, #-0x10]!
    //     0x924bb8: mov             fp, SP
    // 0x924bbc: AllocStack(0x40)
    //     0x924bbc: sub             SP, SP, #0x40
    // 0x924bc0: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x924bc0: stur            NULL, [fp, #-8]
    //     0x924bc4: movz            x0, #0
    //     0x924bc8: add             x1, fp, w0, sxtw #2
    //     0x924bcc: ldr             x1, [x1, #0x18]
    //     0x924bd0: add             x2, fp, w0, sxtw #2
    //     0x924bd4: ldr             x2, [x2, #0x10]
    //     0x924bd8: stur            x2, [fp, #-0x18]
    //     0x924bdc: ldur            w3, [x1, #0x17]
    //     0x924be0: add             x3, x3, HEAP, lsl #32
    //     0x924be4: stur            x3, [fp, #-0x10]
    // 0x924be8: CheckStackOverflow
    //     0x924be8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x924bec: cmp             SP, x16
    //     0x924bf0: b.ls            #0x924e28
    // 0x924bf4: InitAsync() -> Future<String>
    //     0x924bf4: ldr             x0, [PP, #0x290]  ; [pp+0x290] TypeArguments: <String>
    //     0x924bf8: bl              #0x8c1de0  ; InitAsyncStub
    // 0x924bfc: ldur            x0, [fp, #-0x18]
    // 0x924c00: r2 = Null
    //     0x924c00: mov             x2, NULL
    // 0x924c04: r1 = Null
    //     0x924c04: mov             x1, NULL
    // 0x924c08: r4 = 59
    //     0x924c08: movz            x4, #0x3b
    // 0x924c0c: branchIfSmi(r0, 0x924c18)
    //     0x924c0c: tbz             w0, #0, #0x924c18
    // 0x924c10: r4 = LoadClassIdInstr(r0)
    //     0x924c10: ldur            x4, [x0, #-1]
    //     0x924c14: ubfx            x4, x4, #0xc, #0x14
    // 0x924c18: sub             x4, x4, #0x59
    // 0x924c1c: cmp             x4, #2
    // 0x924c20: b.ls            #0x924c34
    // 0x924c24: r8 = List<Object?>?
    //     0x924c24: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x924c28: r3 = Null
    //     0x924c28: add             x3, PP, #0x31, lsl #12  ; [pp+0x315e8] Null
    //     0x924c2c: ldr             x3, [x3, #0x5e8]
    // 0x924c30: r0 = List<Object?>?()
    //     0x924c30: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x924c34: ldur            x1, [fp, #-0x18]
    // 0x924c38: cmp             w1, NULL
    // 0x924c3c: b.eq            #0x924e30
    // 0x924c40: r0 = LoadClassIdInstr(r1)
    //     0x924c40: ldur            x0, [x1, #-1]
    //     0x924c44: ubfx            x0, x0, #0xc, #0x14
    // 0x924c48: stp             xzr, x1, [SP]
    // 0x924c4c: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924c4c: movz            x17, #0x32a
    //     0x924c50: movk            x17, #0x1, lsl #16
    //     0x924c54: add             lr, x0, x17
    //     0x924c58: ldr             lr, [x21, lr, lsl #3]
    //     0x924c5c: blr             lr
    // 0x924c60: mov             x3, x0
    // 0x924c64: r2 = Null
    //     0x924c64: mov             x2, NULL
    // 0x924c68: r1 = Null
    //     0x924c68: mov             x1, NULL
    // 0x924c6c: stur            x3, [fp, #-0x20]
    // 0x924c70: branchIfSmi(r0, 0x924c98)
    //     0x924c70: tbz             w0, #0, #0x924c98
    // 0x924c74: r4 = LoadClassIdInstr(r0)
    //     0x924c74: ldur            x4, [x0, #-1]
    //     0x924c78: ubfx            x4, x4, #0xc, #0x14
    // 0x924c7c: sub             x4, x4, #0x3b
    // 0x924c80: cmp             x4, #1
    // 0x924c84: b.ls            #0x924c98
    // 0x924c88: r8 = int?
    //     0x924c88: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x924c8c: r3 = Null
    //     0x924c8c: add             x3, PP, #0x31, lsl #12  ; [pp+0x315f8] Null
    //     0x924c90: ldr             x3, [x3, #0x5f8]
    // 0x924c94: r0 = int?()
    //     0x924c94: bl              #0x959574  ; IsType_int?_Stub
    // 0x924c98: ldur            x1, [fp, #-0x18]
    // 0x924c9c: r0 = LoadClassIdInstr(r1)
    //     0x924c9c: ldur            x0, [x1, #-1]
    //     0x924ca0: ubfx            x0, x0, #0xc, #0x14
    // 0x924ca4: r16 = 2
    //     0x924ca4: movz            x16, #0x2
    // 0x924ca8: stp             x16, x1, [SP]
    // 0x924cac: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924cac: movz            x17, #0x32a
    //     0x924cb0: movk            x17, #0x1, lsl #16
    //     0x924cb4: add             lr, x0, x17
    //     0x924cb8: ldr             lr, [x21, lr, lsl #3]
    //     0x924cbc: blr             lr
    // 0x924cc0: mov             x3, x0
    // 0x924cc4: r2 = Null
    //     0x924cc4: mov             x2, NULL
    // 0x924cc8: r1 = Null
    //     0x924cc8: mov             x1, NULL
    // 0x924ccc: stur            x3, [fp, #-0x28]
    // 0x924cd0: r4 = 59
    //     0x924cd0: movz            x4, #0x3b
    // 0x924cd4: branchIfSmi(r0, 0x924ce0)
    //     0x924cd4: tbz             w0, #0, #0x924ce0
    // 0x924cd8: r4 = LoadClassIdInstr(r0)
    //     0x924cd8: ldur            x4, [x0, #-1]
    //     0x924cdc: ubfx            x4, x4, #0xc, #0x14
    // 0x924ce0: sub             x4, x4, #0x5d
    // 0x924ce4: cmp             x4, #1
    // 0x924ce8: b.ls            #0x924cfc
    // 0x924cec: r8 = String?
    //     0x924cec: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x924cf0: r3 = Null
    //     0x924cf0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31608] Null
    //     0x924cf4: ldr             x3, [x3, #0x608]
    // 0x924cf8: r0 = String?()
    //     0x924cf8: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x924cfc: ldur            x1, [fp, #-0x18]
    // 0x924d00: r0 = LoadClassIdInstr(r1)
    //     0x924d00: ldur            x0, [x1, #-1]
    //     0x924d04: ubfx            x0, x0, #0xc, #0x14
    // 0x924d08: r16 = 4
    //     0x924d08: movz            x16, #0x4
    // 0x924d0c: stp             x16, x1, [SP]
    // 0x924d10: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924d10: movz            x17, #0x32a
    //     0x924d14: movk            x17, #0x1, lsl #16
    //     0x924d18: add             lr, x0, x17
    //     0x924d1c: ldr             lr, [x21, lr, lsl #3]
    //     0x924d20: blr             lr
    // 0x924d24: mov             x3, x0
    // 0x924d28: r2 = Null
    //     0x924d28: mov             x2, NULL
    // 0x924d2c: r1 = Null
    //     0x924d2c: mov             x1, NULL
    // 0x924d30: stur            x3, [fp, #-0x30]
    // 0x924d34: r4 = 59
    //     0x924d34: movz            x4, #0x3b
    // 0x924d38: branchIfSmi(r0, 0x924d44)
    //     0x924d38: tbz             w0, #0, #0x924d44
    // 0x924d3c: r4 = LoadClassIdInstr(r0)
    //     0x924d3c: ldur            x4, [x0, #-1]
    //     0x924d40: ubfx            x4, x4, #0xc, #0x14
    // 0x924d44: sub             x4, x4, #0x5d
    // 0x924d48: cmp             x4, #1
    // 0x924d4c: b.ls            #0x924d60
    // 0x924d50: r8 = String?
    //     0x924d50: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x924d54: r3 = Null
    //     0x924d54: add             x3, PP, #0x31, lsl #12  ; [pp+0x31618] Null
    //     0x924d58: ldr             x3, [x3, #0x618]
    // 0x924d5c: r0 = String?()
    //     0x924d5c: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x924d60: ldur            x0, [fp, #-0x18]
    // 0x924d64: r1 = LoadClassIdInstr(r0)
    //     0x924d64: ldur            x1, [x0, #-1]
    //     0x924d68: ubfx            x1, x1, #0xc, #0x14
    // 0x924d6c: r16 = 6
    //     0x924d6c: movz            x16, #0x6
    // 0x924d70: stp             x16, x0, [SP]
    // 0x924d74: mov             x0, x1
    // 0x924d78: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924d78: movz            x17, #0x32a
    //     0x924d7c: movk            x17, #0x1, lsl #16
    //     0x924d80: add             lr, x0, x17
    //     0x924d84: ldr             lr, [x21, lr, lsl #3]
    //     0x924d88: blr             lr
    // 0x924d8c: mov             x3, x0
    // 0x924d90: r2 = Null
    //     0x924d90: mov             x2, NULL
    // 0x924d94: r1 = Null
    //     0x924d94: mov             x1, NULL
    // 0x924d98: stur            x3, [fp, #-0x18]
    // 0x924d9c: r4 = 59
    //     0x924d9c: movz            x4, #0x3b
    // 0x924da0: branchIfSmi(r0, 0x924dac)
    //     0x924da0: tbz             w0, #0, #0x924dac
    // 0x924da4: r4 = LoadClassIdInstr(r0)
    //     0x924da4: ldur            x4, [x0, #-1]
    //     0x924da8: ubfx            x4, x4, #0xc, #0x14
    // 0x924dac: sub             x4, x4, #0x5d
    // 0x924db0: cmp             x4, #1
    // 0x924db4: b.ls            #0x924dc8
    // 0x924db8: r8 = String?
    //     0x924db8: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x924dbc: r3 = Null
    //     0x924dbc: add             x3, PP, #0x31, lsl #12  ; [pp+0x31628] Null
    //     0x924dc0: ldr             x3, [x3, #0x628]
    // 0x924dc4: r0 = String?()
    //     0x924dc4: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x924dc8: ldur            x0, [fp, #-0x10]
    // 0x924dcc: LoadField: r1 = r0->field_f
    //     0x924dcc: ldur            w1, [x0, #0xf]
    // 0x924dd0: DecompressPointer r1
    //     0x924dd0: add             x1, x1, HEAP, lsl #32
    // 0x924dd4: ldur            x2, [fp, #-0x20]
    // 0x924dd8: cmp             w2, NULL
    // 0x924ddc: b.eq            #0x924e34
    // 0x924de0: ldur            x3, [fp, #-0x28]
    // 0x924de4: cmp             w3, NULL
    // 0x924de8: b.eq            #0x924e38
    // 0x924dec: ldur            x5, [fp, #-0x30]
    // 0x924df0: cmp             w5, NULL
    // 0x924df4: b.eq            #0x924e3c
    // 0x924df8: ldur            x6, [fp, #-0x18]
    // 0x924dfc: cmp             w6, NULL
    // 0x924e00: b.eq            #0x924e40
    // 0x924e04: r4 = LoadInt32Instr(r2)
    //     0x924e04: sbfx            x4, x2, #1, #0x1f
    //     0x924e08: tbz             w2, #0, #0x924e10
    //     0x924e0c: ldur            x4, [x2, #7]
    // 0x924e10: mov             x2, x4
    // 0x924e14: r0 = call 0x64e90c
    //     0x924e14: bl              #0x64e90c
    // 0x924e18: mov             x1, x0
    // 0x924e1c: stur            x1, [fp, #-0x18]
    // 0x924e20: r0 = Await()
    //     0x924e20: bl              #0x8c1bb8  ; AwaitStub
    // 0x924e24: r0 = ReturnAsync()
    //     0x924e24: b               #0x8c3a30  ; ReturnAsyncStub
    // 0x924e28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x924e28: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x924e2c: b               #0x924bf4
    // 0x924e30: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924e30: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x924e34: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924e34: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x924e38: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924e38: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x924e3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924e3c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x924e40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924e40: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<bool> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x924e44, size: 0x21c
    // 0x924e44: EnterFrame
    //     0x924e44: stp             fp, lr, [SP, #-0x10]!
    //     0x924e48: mov             fp, SP
    // 0x924e4c: AllocStack(0x38)
    //     0x924e4c: sub             SP, SP, #0x38
    // 0x924e50: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x924e50: stur            NULL, [fp, #-8]
    //     0x924e54: movz            x0, #0
    //     0x924e58: add             x1, fp, w0, sxtw #2
    //     0x924e5c: ldr             x1, [x1, #0x18]
    //     0x924e60: add             x2, fp, w0, sxtw #2
    //     0x924e64: ldr             x2, [x2, #0x10]
    //     0x924e68: stur            x2, [fp, #-0x18]
    //     0x924e6c: ldur            w3, [x1, #0x17]
    //     0x924e70: add             x3, x3, HEAP, lsl #32
    //     0x924e74: stur            x3, [fp, #-0x10]
    // 0x924e78: CheckStackOverflow
    //     0x924e78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x924e7c: cmp             SP, x16
    //     0x924e80: b.ls            #0x925048
    // 0x924e84: InitAsync() -> Future<bool>
    //     0x924e84: ldr             x0, [PP, #0x31a0]  ; [pp+0x31a0] TypeArguments: <bool>
    //     0x924e88: bl              #0x8c1de0  ; InitAsyncStub
    // 0x924e8c: ldur            x0, [fp, #-0x18]
    // 0x924e90: r2 = Null
    //     0x924e90: mov             x2, NULL
    // 0x924e94: r1 = Null
    //     0x924e94: mov             x1, NULL
    // 0x924e98: r4 = 59
    //     0x924e98: movz            x4, #0x3b
    // 0x924e9c: branchIfSmi(r0, 0x924ea8)
    //     0x924e9c: tbz             w0, #0, #0x924ea8
    // 0x924ea0: r4 = LoadClassIdInstr(r0)
    //     0x924ea0: ldur            x4, [x0, #-1]
    //     0x924ea4: ubfx            x4, x4, #0xc, #0x14
    // 0x924ea8: sub             x4, x4, #0x59
    // 0x924eac: cmp             x4, #2
    // 0x924eb0: b.ls            #0x924ec4
    // 0x924eb4: r8 = List<Object?>?
    //     0x924eb4: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x924eb8: r3 = Null
    //     0x924eb8: add             x3, PP, #0x31, lsl #12  ; [pp+0x31640] Null
    //     0x924ebc: ldr             x3, [x3, #0x640]
    // 0x924ec0: r0 = List<Object?>?()
    //     0x924ec0: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x924ec4: ldur            x1, [fp, #-0x18]
    // 0x924ec8: cmp             w1, NULL
    // 0x924ecc: b.eq            #0x925050
    // 0x924ed0: r0 = LoadClassIdInstr(r1)
    //     0x924ed0: ldur            x0, [x1, #-1]
    //     0x924ed4: ubfx            x0, x0, #0xc, #0x14
    // 0x924ed8: stp             xzr, x1, [SP]
    // 0x924edc: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924edc: movz            x17, #0x32a
    //     0x924ee0: movk            x17, #0x1, lsl #16
    //     0x924ee4: add             lr, x0, x17
    //     0x924ee8: ldr             lr, [x21, lr, lsl #3]
    //     0x924eec: blr             lr
    // 0x924ef0: mov             x3, x0
    // 0x924ef4: r2 = Null
    //     0x924ef4: mov             x2, NULL
    // 0x924ef8: r1 = Null
    //     0x924ef8: mov             x1, NULL
    // 0x924efc: stur            x3, [fp, #-0x20]
    // 0x924f00: branchIfSmi(r0, 0x924f28)
    //     0x924f00: tbz             w0, #0, #0x924f28
    // 0x924f04: r4 = LoadClassIdInstr(r0)
    //     0x924f04: ldur            x4, [x0, #-1]
    //     0x924f08: ubfx            x4, x4, #0xc, #0x14
    // 0x924f0c: sub             x4, x4, #0x3b
    // 0x924f10: cmp             x4, #1
    // 0x924f14: b.ls            #0x924f28
    // 0x924f18: r8 = int?
    //     0x924f18: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x924f1c: r3 = Null
    //     0x924f1c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31650] Null
    //     0x924f20: ldr             x3, [x3, #0x650]
    // 0x924f24: r0 = int?()
    //     0x924f24: bl              #0x959574  ; IsType_int?_Stub
    // 0x924f28: ldur            x1, [fp, #-0x18]
    // 0x924f2c: r0 = LoadClassIdInstr(r1)
    //     0x924f2c: ldur            x0, [x1, #-1]
    //     0x924f30: ubfx            x0, x0, #0xc, #0x14
    // 0x924f34: r16 = 2
    //     0x924f34: movz            x16, #0x2
    // 0x924f38: stp             x16, x1, [SP]
    // 0x924f3c: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924f3c: movz            x17, #0x32a
    //     0x924f40: movk            x17, #0x1, lsl #16
    //     0x924f44: add             lr, x0, x17
    //     0x924f48: ldr             lr, [x21, lr, lsl #3]
    //     0x924f4c: blr             lr
    // 0x924f50: mov             x3, x0
    // 0x924f54: r2 = Null
    //     0x924f54: mov             x2, NULL
    // 0x924f58: r1 = Null
    //     0x924f58: mov             x1, NULL
    // 0x924f5c: stur            x3, [fp, #-0x28]
    // 0x924f60: r4 = 59
    //     0x924f60: movz            x4, #0x3b
    // 0x924f64: branchIfSmi(r0, 0x924f70)
    //     0x924f64: tbz             w0, #0, #0x924f70
    // 0x924f68: r4 = LoadClassIdInstr(r0)
    //     0x924f68: ldur            x4, [x0, #-1]
    //     0x924f6c: ubfx            x4, x4, #0xc, #0x14
    // 0x924f70: sub             x4, x4, #0x5d
    // 0x924f74: cmp             x4, #1
    // 0x924f78: b.ls            #0x924f8c
    // 0x924f7c: r8 = String?
    //     0x924f7c: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x924f80: r3 = Null
    //     0x924f80: add             x3, PP, #0x31, lsl #12  ; [pp+0x31660] Null
    //     0x924f84: ldr             x3, [x3, #0x660]
    // 0x924f88: r0 = String?()
    //     0x924f88: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x924f8c: ldur            x0, [fp, #-0x18]
    // 0x924f90: r1 = LoadClassIdInstr(r0)
    //     0x924f90: ldur            x1, [x0, #-1]
    //     0x924f94: ubfx            x1, x1, #0xc, #0x14
    // 0x924f98: r16 = 4
    //     0x924f98: movz            x16, #0x4
    // 0x924f9c: stp             x16, x0, [SP]
    // 0x924fa0: mov             x0, x1
    // 0x924fa4: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924fa4: movz            x17, #0x32a
    //     0x924fa8: movk            x17, #0x1, lsl #16
    //     0x924fac: add             lr, x0, x17
    //     0x924fb0: ldr             lr, [x21, lr, lsl #3]
    //     0x924fb4: blr             lr
    // 0x924fb8: mov             x3, x0
    // 0x924fbc: r2 = Null
    //     0x924fbc: mov             x2, NULL
    // 0x924fc0: r1 = Null
    //     0x924fc0: mov             x1, NULL
    // 0x924fc4: stur            x3, [fp, #-0x18]
    // 0x924fc8: r4 = 59
    //     0x924fc8: movz            x4, #0x3b
    // 0x924fcc: branchIfSmi(r0, 0x924fd8)
    //     0x924fcc: tbz             w0, #0, #0x924fd8
    // 0x924fd0: r4 = LoadClassIdInstr(r0)
    //     0x924fd0: ldur            x4, [x0, #-1]
    //     0x924fd4: ubfx            x4, x4, #0xc, #0x14
    // 0x924fd8: sub             x4, x4, #0x5d
    // 0x924fdc: cmp             x4, #1
    // 0x924fe0: b.ls            #0x924ff4
    // 0x924fe4: r8 = String?
    //     0x924fe4: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x924fe8: r3 = Null
    //     0x924fe8: add             x3, PP, #0x31, lsl #12  ; [pp+0x31670] Null
    //     0x924fec: ldr             x3, [x3, #0x670]
    // 0x924ff0: r0 = String?()
    //     0x924ff0: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x924ff4: ldur            x0, [fp, #-0x10]
    // 0x924ff8: LoadField: r1 = r0->field_f
    //     0x924ff8: ldur            w1, [x0, #0xf]
    // 0x924ffc: DecompressPointer r1
    //     0x924ffc: add             x1, x1, HEAP, lsl #32
    // 0x925000: ldur            x2, [fp, #-0x20]
    // 0x925004: cmp             w2, NULL
    // 0x925008: b.eq            #0x925054
    // 0x92500c: ldur            x3, [fp, #-0x28]
    // 0x925010: cmp             w3, NULL
    // 0x925014: b.eq            #0x925058
    // 0x925018: ldur            x5, [fp, #-0x18]
    // 0x92501c: cmp             w5, NULL
    // 0x925020: b.eq            #0x92505c
    // 0x925024: r4 = LoadInt32Instr(r2)
    //     0x925024: sbfx            x4, x2, #1, #0x1f
    //     0x925028: tbz             w2, #0, #0x925030
    //     0x92502c: ldur            x4, [x2, #7]
    // 0x925030: mov             x2, x4
    // 0x925034: r0 = call 0x64e9a8
    //     0x925034: bl              #0x64e9a8
    // 0x925038: mov             x1, x0
    // 0x92503c: stur            x1, [fp, #-0x18]
    // 0x925040: r0 = Await()
    //     0x925040: bl              #0x8c1bb8  ; AwaitStub
    // 0x925044: r0 = ReturnAsync()
    //     0x925044: b               #0x8c3a30  ; ReturnAsyncStub
    // 0x925048: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x925048: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92504c: b               #0x924e84
    // 0x925050: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925050: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925054: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925054: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925058: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925058: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92505c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92505c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x925060, size: 0x220
    // 0x925060: EnterFrame
    //     0x925060: stp             fp, lr, [SP, #-0x10]!
    //     0x925064: mov             fp, SP
    // 0x925068: AllocStack(0x38)
    //     0x925068: sub             SP, SP, #0x38
    // 0x92506c: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x92506c: stur            NULL, [fp, #-8]
    //     0x925070: movz            x0, #0
    //     0x925074: add             x1, fp, w0, sxtw #2
    //     0x925078: ldr             x1, [x1, #0x18]
    //     0x92507c: add             x2, fp, w0, sxtw #2
    //     0x925080: ldr             x2, [x2, #0x10]
    //     0x925084: stur            x2, [fp, #-0x18]
    //     0x925088: ldur            w3, [x1, #0x17]
    //     0x92508c: add             x3, x3, HEAP, lsl #32
    //     0x925090: stur            x3, [fp, #-0x10]
    // 0x925094: CheckStackOverflow
    //     0x925094: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x925098: cmp             SP, x16
    //     0x92509c: b.ls            #0x925268
    // 0x9250a0: InitAsync() -> Future<Null?>
    //     0x9250a0: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x9250a4: bl              #0x8c1de0  ; InitAsyncStub
    // 0x9250a8: ldur            x0, [fp, #-0x18]
    // 0x9250ac: r2 = Null
    //     0x9250ac: mov             x2, NULL
    // 0x9250b0: r1 = Null
    //     0x9250b0: mov             x1, NULL
    // 0x9250b4: r4 = 59
    //     0x9250b4: movz            x4, #0x3b
    // 0x9250b8: branchIfSmi(r0, 0x9250c4)
    //     0x9250b8: tbz             w0, #0, #0x9250c4
    // 0x9250bc: r4 = LoadClassIdInstr(r0)
    //     0x9250bc: ldur            x4, [x0, #-1]
    //     0x9250c0: ubfx            x4, x4, #0xc, #0x14
    // 0x9250c4: sub             x4, x4, #0x59
    // 0x9250c8: cmp             x4, #2
    // 0x9250cc: b.ls            #0x9250e0
    // 0x9250d0: r8 = List<Object?>?
    //     0x9250d0: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x9250d4: r3 = Null
    //     0x9250d4: add             x3, PP, #0x31, lsl #12  ; [pp+0x31680] Null
    //     0x9250d8: ldr             x3, [x3, #0x680]
    // 0x9250dc: r0 = List<Object?>?()
    //     0x9250dc: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x9250e0: ldur            x1, [fp, #-0x18]
    // 0x9250e4: cmp             w1, NULL
    // 0x9250e8: b.eq            #0x925270
    // 0x9250ec: r0 = LoadClassIdInstr(r1)
    //     0x9250ec: ldur            x0, [x1, #-1]
    //     0x9250f0: ubfx            x0, x0, #0xc, #0x14
    // 0x9250f4: stp             xzr, x1, [SP]
    // 0x9250f8: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9250f8: movz            x17, #0x32a
    //     0x9250fc: movk            x17, #0x1, lsl #16
    //     0x925100: add             lr, x0, x17
    //     0x925104: ldr             lr, [x21, lr, lsl #3]
    //     0x925108: blr             lr
    // 0x92510c: mov             x3, x0
    // 0x925110: r2 = Null
    //     0x925110: mov             x2, NULL
    // 0x925114: r1 = Null
    //     0x925114: mov             x1, NULL
    // 0x925118: stur            x3, [fp, #-0x20]
    // 0x92511c: branchIfSmi(r0, 0x925144)
    //     0x92511c: tbz             w0, #0, #0x925144
    // 0x925120: r4 = LoadClassIdInstr(r0)
    //     0x925120: ldur            x4, [x0, #-1]
    //     0x925124: ubfx            x4, x4, #0xc, #0x14
    // 0x925128: sub             x4, x4, #0x3b
    // 0x92512c: cmp             x4, #1
    // 0x925130: b.ls            #0x925144
    // 0x925134: r8 = int?
    //     0x925134: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x925138: r3 = Null
    //     0x925138: add             x3, PP, #0x31, lsl #12  ; [pp+0x31690] Null
    //     0x92513c: ldr             x3, [x3, #0x690]
    // 0x925140: r0 = int?()
    //     0x925140: bl              #0x959574  ; IsType_int?_Stub
    // 0x925144: ldur            x1, [fp, #-0x18]
    // 0x925148: r0 = LoadClassIdInstr(r1)
    //     0x925148: ldur            x0, [x1, #-1]
    //     0x92514c: ubfx            x0, x0, #0xc, #0x14
    // 0x925150: r16 = 2
    //     0x925150: movz            x16, #0x2
    // 0x925154: stp             x16, x1, [SP]
    // 0x925158: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x925158: movz            x17, #0x32a
    //     0x92515c: movk            x17, #0x1, lsl #16
    //     0x925160: add             lr, x0, x17
    //     0x925164: ldr             lr, [x21, lr, lsl #3]
    //     0x925168: blr             lr
    // 0x92516c: mov             x3, x0
    // 0x925170: r2 = Null
    //     0x925170: mov             x2, NULL
    // 0x925174: r1 = Null
    //     0x925174: mov             x1, NULL
    // 0x925178: stur            x3, [fp, #-0x28]
    // 0x92517c: r4 = 59
    //     0x92517c: movz            x4, #0x3b
    // 0x925180: branchIfSmi(r0, 0x92518c)
    //     0x925180: tbz             w0, #0, #0x92518c
    // 0x925184: r4 = LoadClassIdInstr(r0)
    //     0x925184: ldur            x4, [x0, #-1]
    //     0x925188: ubfx            x4, x4, #0xc, #0x14
    // 0x92518c: sub             x4, x4, #0x5d
    // 0x925190: cmp             x4, #1
    // 0x925194: b.ls            #0x9251a8
    // 0x925198: r8 = String?
    //     0x925198: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x92519c: r3 = Null
    //     0x92519c: add             x3, PP, #0x31, lsl #12  ; [pp+0x316a0] Null
    //     0x9251a0: ldr             x3, [x3, #0x6a0]
    // 0x9251a4: r0 = String?()
    //     0x9251a4: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x9251a8: ldur            x0, [fp, #-0x18]
    // 0x9251ac: r1 = LoadClassIdInstr(r0)
    //     0x9251ac: ldur            x1, [x0, #-1]
    //     0x9251b0: ubfx            x1, x1, #0xc, #0x14
    // 0x9251b4: r16 = 4
    //     0x9251b4: movz            x16, #0x4
    // 0x9251b8: stp             x16, x0, [SP]
    // 0x9251bc: mov             x0, x1
    // 0x9251c0: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9251c0: movz            x17, #0x32a
    //     0x9251c4: movk            x17, #0x1, lsl #16
    //     0x9251c8: add             lr, x0, x17
    //     0x9251cc: ldr             lr, [x21, lr, lsl #3]
    //     0x9251d0: blr             lr
    // 0x9251d4: mov             x3, x0
    // 0x9251d8: r2 = Null
    //     0x9251d8: mov             x2, NULL
    // 0x9251dc: r1 = Null
    //     0x9251dc: mov             x1, NULL
    // 0x9251e0: stur            x3, [fp, #-0x18]
    // 0x9251e4: r4 = 59
    //     0x9251e4: movz            x4, #0x3b
    // 0x9251e8: branchIfSmi(r0, 0x9251f4)
    //     0x9251e8: tbz             w0, #0, #0x9251f4
    // 0x9251ec: r4 = LoadClassIdInstr(r0)
    //     0x9251ec: ldur            x4, [x0, #-1]
    //     0x9251f0: ubfx            x4, x4, #0xc, #0x14
    // 0x9251f4: sub             x4, x4, #0x5d
    // 0x9251f8: cmp             x4, #1
    // 0x9251fc: b.ls            #0x925210
    // 0x925200: r8 = String?
    //     0x925200: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x925204: r3 = Null
    //     0x925204: add             x3, PP, #0x31, lsl #12  ; [pp+0x316b0] Null
    //     0x925208: ldr             x3, [x3, #0x6b0]
    // 0x92520c: r0 = String?()
    //     0x92520c: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x925210: ldur            x0, [fp, #-0x10]
    // 0x925214: LoadField: r1 = r0->field_f
    //     0x925214: ldur            w1, [x0, #0xf]
    // 0x925218: DecompressPointer r1
    //     0x925218: add             x1, x1, HEAP, lsl #32
    // 0x92521c: ldur            x2, [fp, #-0x20]
    // 0x925220: cmp             w2, NULL
    // 0x925224: b.eq            #0x925274
    // 0x925228: ldur            x3, [fp, #-0x28]
    // 0x92522c: cmp             w3, NULL
    // 0x925230: b.eq            #0x925278
    // 0x925234: ldur            x5, [fp, #-0x18]
    // 0x925238: cmp             w5, NULL
    // 0x92523c: b.eq            #0x92527c
    // 0x925240: r4 = LoadInt32Instr(r2)
    //     0x925240: sbfx            x4, x2, #1, #0x1f
    //     0x925244: tbz             w2, #0, #0x92524c
    //     0x925248: ldur            x4, [x2, #7]
    // 0x92524c: mov             x2, x4
    // 0x925250: r0 = call 0x64ea3c
    //     0x925250: bl              #0x64ea3c
    // 0x925254: mov             x1, x0
    // 0x925258: stur            x1, [fp, #-0x18]
    // 0x92525c: r0 = Await()
    //     0x92525c: bl              #0x8c1bb8  ; AwaitStub
    // 0x925260: r0 = Null
    //     0x925260: mov             x0, NULL
    // 0x925264: r0 = ReturnAsyncNotFuture()
    //     0x925264: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x925268: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x925268: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92526c: b               #0x9250a0
    // 0x925270: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925270: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925274: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925274: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925278: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925278: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92527c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92527c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x925280, size: 0x19c
    // 0x925280: EnterFrame
    //     0x925280: stp             fp, lr, [SP, #-0x10]!
    //     0x925284: mov             fp, SP
    // 0x925288: AllocStack(0x30)
    //     0x925288: sub             SP, SP, #0x30
    // 0x92528c: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x92528c: stur            NULL, [fp, #-8]
    //     0x925290: movz            x0, #0
    //     0x925294: add             x1, fp, w0, sxtw #2
    //     0x925298: ldr             x1, [x1, #0x18]
    //     0x92529c: add             x2, fp, w0, sxtw #2
    //     0x9252a0: ldr             x2, [x2, #0x10]
    //     0x9252a4: stur            x2, [fp, #-0x18]
    //     0x9252a8: ldur            w3, [x1, #0x17]
    //     0x9252ac: add             x3, x3, HEAP, lsl #32
    //     0x9252b0: stur            x3, [fp, #-0x10]
    // 0x9252b4: CheckStackOverflow
    //     0x9252b4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9252b8: cmp             SP, x16
    //     0x9252bc: b.ls            #0x925408
    // 0x9252c0: InitAsync() -> Future<Null?>
    //     0x9252c0: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x9252c4: bl              #0x8c1de0  ; InitAsyncStub
    // 0x9252c8: ldur            x0, [fp, #-0x18]
    // 0x9252cc: r2 = Null
    //     0x9252cc: mov             x2, NULL
    // 0x9252d0: r1 = Null
    //     0x9252d0: mov             x1, NULL
    // 0x9252d4: r4 = 59
    //     0x9252d4: movz            x4, #0x3b
    // 0x9252d8: branchIfSmi(r0, 0x9252e4)
    //     0x9252d8: tbz             w0, #0, #0x9252e4
    // 0x9252dc: r4 = LoadClassIdInstr(r0)
    //     0x9252dc: ldur            x4, [x0, #-1]
    //     0x9252e0: ubfx            x4, x4, #0xc, #0x14
    // 0x9252e4: sub             x4, x4, #0x59
    // 0x9252e8: cmp             x4, #2
    // 0x9252ec: b.ls            #0x925300
    // 0x9252f0: r8 = List<Object?>?
    //     0x9252f0: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x9252f4: r3 = Null
    //     0x9252f4: add             x3, PP, #0x31, lsl #12  ; [pp+0x316c0] Null
    //     0x9252f8: ldr             x3, [x3, #0x6c0]
    // 0x9252fc: r0 = List<Object?>?()
    //     0x9252fc: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x925300: ldur            x1, [fp, #-0x18]
    // 0x925304: cmp             w1, NULL
    // 0x925308: b.eq            #0x925410
    // 0x92530c: r0 = LoadClassIdInstr(r1)
    //     0x92530c: ldur            x0, [x1, #-1]
    //     0x925310: ubfx            x0, x0, #0xc, #0x14
    // 0x925314: stp             xzr, x1, [SP]
    // 0x925318: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x925318: movz            x17, #0x32a
    //     0x92531c: movk            x17, #0x1, lsl #16
    //     0x925320: add             lr, x0, x17
    //     0x925324: ldr             lr, [x21, lr, lsl #3]
    //     0x925328: blr             lr
    // 0x92532c: mov             x3, x0
    // 0x925330: r2 = Null
    //     0x925330: mov             x2, NULL
    // 0x925334: r1 = Null
    //     0x925334: mov             x1, NULL
    // 0x925338: stur            x3, [fp, #-0x20]
    // 0x92533c: branchIfSmi(r0, 0x925364)
    //     0x92533c: tbz             w0, #0, #0x925364
    // 0x925340: r4 = LoadClassIdInstr(r0)
    //     0x925340: ldur            x4, [x0, #-1]
    //     0x925344: ubfx            x4, x4, #0xc, #0x14
    // 0x925348: sub             x4, x4, #0x3b
    // 0x92534c: cmp             x4, #1
    // 0x925350: b.ls            #0x925364
    // 0x925354: r8 = int?
    //     0x925354: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x925358: r3 = Null
    //     0x925358: add             x3, PP, #0x31, lsl #12  ; [pp+0x316d0] Null
    //     0x92535c: ldr             x3, [x3, #0x6d0]
    // 0x925360: r0 = int?()
    //     0x925360: bl              #0x959574  ; IsType_int?_Stub
    // 0x925364: ldur            x0, [fp, #-0x18]
    // 0x925368: r1 = LoadClassIdInstr(r0)
    //     0x925368: ldur            x1, [x0, #-1]
    //     0x92536c: ubfx            x1, x1, #0xc, #0x14
    // 0x925370: r16 = 2
    //     0x925370: movz            x16, #0x2
    // 0x925374: stp             x16, x0, [SP]
    // 0x925378: mov             x0, x1
    // 0x92537c: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x92537c: movz            x17, #0x32a
    //     0x925380: movk            x17, #0x1, lsl #16
    //     0x925384: add             lr, x0, x17
    //     0x925388: ldr             lr, [x21, lr, lsl #3]
    //     0x92538c: blr             lr
    // 0x925390: mov             x3, x0
    // 0x925394: r2 = Null
    //     0x925394: mov             x2, NULL
    // 0x925398: r1 = Null
    //     0x925398: mov             x1, NULL
    // 0x92539c: stur            x3, [fp, #-0x18]
    // 0x9253a0: r4 = 59
    //     0x9253a0: movz            x4, #0x3b
    // 0x9253a4: branchIfSmi(r0, 0x9253b0)
    //     0x9253a4: tbz             w0, #0, #0x9253b0
    // 0x9253a8: r4 = LoadClassIdInstr(r0)
    //     0x9253a8: ldur            x4, [x0, #-1]
    //     0x9253ac: ubfx            x4, x4, #0xc, #0x14
    // 0x9253b0: cmp             x4, #0x174
    // 0x9253b4: b.eq            #0x9253cc
    // 0x9253b8: r8 = Reb?
    //     0x9253b8: add             x8, PP, #0x31, lsl #12  ; [pp+0x316e0] Type: Reb?
    //     0x9253bc: ldr             x8, [x8, #0x6e0]
    // 0x9253c0: r3 = Null
    //     0x9253c0: add             x3, PP, #0x31, lsl #12  ; [pp+0x316e8] Null
    //     0x9253c4: ldr             x3, [x3, #0x6e8]
    // 0x9253c8: r0 = DefaultNullableTypeTest()
    //     0x9253c8: bl              #0x94d980  ; DefaultNullableTypeTestStub
    // 0x9253cc: ldur            x0, [fp, #-0x10]
    // 0x9253d0: LoadField: r1 = r0->field_f
    //     0x9253d0: ldur            w1, [x0, #0xf]
    // 0x9253d4: DecompressPointer r1
    //     0x9253d4: add             x1, x1, HEAP, lsl #32
    // 0x9253d8: ldur            x0, [fp, #-0x20]
    // 0x9253dc: cmp             w0, NULL
    // 0x9253e0: b.eq            #0x925414
    // 0x9253e4: ldur            x3, [fp, #-0x18]
    // 0x9253e8: cmp             w3, NULL
    // 0x9253ec: b.eq            #0x925418
    // 0x9253f0: r2 = LoadInt32Instr(r0)
    //     0x9253f0: sbfx            x2, x0, #1, #0x1f
    //     0x9253f4: tbz             w0, #0, #0x9253fc
    //     0x9253f8: ldur            x2, [x0, #7]
    // 0x9253fc: r0 = call 0x64ead0
    //     0x9253fc: bl              #0x64ead0
    // 0x925400: r0 = Null
    //     0x925400: mov             x0, NULL
    // 0x925404: r0 = ReturnAsyncNotFuture()
    //     0x925404: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x925408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x925408: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92540c: b               #0x9252c0
    // 0x925410: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925410: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925414: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925414: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925418: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925418: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x92541c, size: 0x128
    // 0x92541c: EnterFrame
    //     0x92541c: stp             fp, lr, [SP, #-0x10]!
    //     0x925420: mov             fp, SP
    // 0x925424: AllocStack(0x28)
    //     0x925424: sub             SP, SP, #0x28
    // 0x925428: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x925428: stur            NULL, [fp, #-8]
    //     0x92542c: movz            x0, #0
    //     0x925430: add             x1, fp, w0, sxtw #2
    //     0x925434: ldr             x1, [x1, #0x18]
    //     0x925438: add             x2, fp, w0, sxtw #2
    //     0x92543c: ldr             x2, [x2, #0x10]
    //     0x925440: stur            x2, [fp, #-0x18]
    //     0x925444: ldur            w3, [x1, #0x17]
    //     0x925448: add             x3, x3, HEAP, lsl #32
    //     0x92544c: stur            x3, [fp, #-0x10]
    // 0x925450: CheckStackOverflow
    //     0x925450: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x925454: cmp             SP, x16
    //     0x925458: b.ls            #0x925534
    // 0x92545c: InitAsync() -> Future<Null?>
    //     0x92545c: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x925460: bl              #0x8c1de0  ; InitAsyncStub
    // 0x925464: ldur            x0, [fp, #-0x18]
    // 0x925468: r2 = Null
    //     0x925468: mov             x2, NULL
    // 0x92546c: r1 = Null
    //     0x92546c: mov             x1, NULL
    // 0x925470: r4 = 59
    //     0x925470: movz            x4, #0x3b
    // 0x925474: branchIfSmi(r0, 0x925480)
    //     0x925474: tbz             w0, #0, #0x925480
    // 0x925478: r4 = LoadClassIdInstr(r0)
    //     0x925478: ldur            x4, [x0, #-1]
    //     0x92547c: ubfx            x4, x4, #0xc, #0x14
    // 0x925480: sub             x4, x4, #0x59
    // 0x925484: cmp             x4, #2
    // 0x925488: b.ls            #0x92549c
    // 0x92548c: r8 = List<Object?>?
    //     0x92548c: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x925490: r3 = Null
    //     0x925490: add             x3, PP, #0x31, lsl #12  ; [pp+0x316f8] Null
    //     0x925494: ldr             x3, [x3, #0x6f8]
    // 0x925498: r0 = List<Object?>?()
    //     0x925498: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x92549c: ldur            x0, [fp, #-0x18]
    // 0x9254a0: cmp             w0, NULL
    // 0x9254a4: b.eq            #0x92553c
    // 0x9254a8: r1 = LoadClassIdInstr(r0)
    //     0x9254a8: ldur            x1, [x0, #-1]
    //     0x9254ac: ubfx            x1, x1, #0xc, #0x14
    // 0x9254b0: stp             xzr, x0, [SP]
    // 0x9254b4: mov             x0, x1
    // 0x9254b8: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9254b8: movz            x17, #0x32a
    //     0x9254bc: movk            x17, #0x1, lsl #16
    //     0x9254c0: add             lr, x0, x17
    //     0x9254c4: ldr             lr, [x21, lr, lsl #3]
    //     0x9254c8: blr             lr
    // 0x9254cc: mov             x3, x0
    // 0x9254d0: r2 = Null
    //     0x9254d0: mov             x2, NULL
    // 0x9254d4: r1 = Null
    //     0x9254d4: mov             x1, NULL
    // 0x9254d8: stur            x3, [fp, #-0x18]
    // 0x9254dc: branchIfSmi(r0, 0x925504)
    //     0x9254dc: tbz             w0, #0, #0x925504
    // 0x9254e0: r4 = LoadClassIdInstr(r0)
    //     0x9254e0: ldur            x4, [x0, #-1]
    //     0x9254e4: ubfx            x4, x4, #0xc, #0x14
    // 0x9254e8: sub             x4, x4, #0x3b
    // 0x9254ec: cmp             x4, #1
    // 0x9254f0: b.ls            #0x925504
    // 0x9254f4: r8 = int?
    //     0x9254f4: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9254f8: r3 = Null
    //     0x9254f8: add             x3, PP, #0x31, lsl #12  ; [pp+0x31708] Null
    //     0x9254fc: ldr             x3, [x3, #0x708]
    // 0x925500: r0 = int?()
    //     0x925500: bl              #0x959574  ; IsType_int?_Stub
    // 0x925504: ldur            x0, [fp, #-0x10]
    // 0x925508: LoadField: r1 = r0->field_f
    //     0x925508: ldur            w1, [x0, #0xf]
    // 0x92550c: DecompressPointer r1
    //     0x92550c: add             x1, x1, HEAP, lsl #32
    // 0x925510: ldur            x0, [fp, #-0x18]
    // 0x925514: cmp             w0, NULL
    // 0x925518: b.eq            #0x925540
    // 0x92551c: r2 = LoadInt32Instr(r0)
    //     0x92551c: sbfx            x2, x0, #1, #0x1f
    //     0x925520: tbz             w0, #0, #0x925528
    //     0x925524: ldur            x2, [x0, #7]
    // 0x925528: r0 = call 0x64eb5c
    //     0x925528: bl              #0x64eb5c
    // 0x92552c: r0 = Null
    //     0x92552c: mov             x0, NULL
    // 0x925530: r0 = ReturnAsyncNotFuture()
    //     0x925530: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x925534: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x925534: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x925538: b               #0x92545c
    // 0x92553c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92553c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925540: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925540: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x925544, size: 0x220
    // 0x925544: EnterFrame
    //     0x925544: stp             fp, lr, [SP, #-0x10]!
    //     0x925548: mov             fp, SP
    // 0x92554c: AllocStack(0x38)
    //     0x92554c: sub             SP, SP, #0x38
    // 0x925550: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x925550: stur            NULL, [fp, #-8]
    //     0x925554: movz            x0, #0
    //     0x925558: add             x1, fp, w0, sxtw #2
    //     0x92555c: ldr             x1, [x1, #0x18]
    //     0x925560: add             x2, fp, w0, sxtw #2
    //     0x925564: ldr             x2, [x2, #0x10]
    //     0x925568: stur            x2, [fp, #-0x18]
    //     0x92556c: ldur            w3, [x1, #0x17]
    //     0x925570: add             x3, x3, HEAP, lsl #32
    //     0x925574: stur            x3, [fp, #-0x10]
    // 0x925578: CheckStackOverflow
    //     0x925578: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92557c: cmp             SP, x16
    //     0x925580: b.ls            #0x92574c
    // 0x925584: InitAsync() -> Future<Null?>
    //     0x925584: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x925588: bl              #0x8c1de0  ; InitAsyncStub
    // 0x92558c: ldur            x0, [fp, #-0x18]
    // 0x925590: r2 = Null
    //     0x925590: mov             x2, NULL
    // 0x925594: r1 = Null
    //     0x925594: mov             x1, NULL
    // 0x925598: r4 = 59
    //     0x925598: movz            x4, #0x3b
    // 0x92559c: branchIfSmi(r0, 0x9255a8)
    //     0x92559c: tbz             w0, #0, #0x9255a8
    // 0x9255a0: r4 = LoadClassIdInstr(r0)
    //     0x9255a0: ldur            x4, [x0, #-1]
    //     0x9255a4: ubfx            x4, x4, #0xc, #0x14
    // 0x9255a8: sub             x4, x4, #0x59
    // 0x9255ac: cmp             x4, #2
    // 0x9255b0: b.ls            #0x9255c4
    // 0x9255b4: r8 = List<Object?>?
    //     0x9255b4: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x9255b8: r3 = Null
    //     0x9255b8: add             x3, PP, #0x31, lsl #12  ; [pp+0x31718] Null
    //     0x9255bc: ldr             x3, [x3, #0x718]
    // 0x9255c0: r0 = List<Object?>?()
    //     0x9255c0: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x9255c4: ldur            x1, [fp, #-0x18]
    // 0x9255c8: cmp             w1, NULL
    // 0x9255cc: b.eq            #0x925754
    // 0x9255d0: r0 = LoadClassIdInstr(r1)
    //     0x9255d0: ldur            x0, [x1, #-1]
    //     0x9255d4: ubfx            x0, x0, #0xc, #0x14
    // 0x9255d8: stp             xzr, x1, [SP]
    // 0x9255dc: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9255dc: movz            x17, #0x32a
    //     0x9255e0: movk            x17, #0x1, lsl #16
    //     0x9255e4: add             lr, x0, x17
    //     0x9255e8: ldr             lr, [x21, lr, lsl #3]
    //     0x9255ec: blr             lr
    // 0x9255f0: mov             x3, x0
    // 0x9255f4: r2 = Null
    //     0x9255f4: mov             x2, NULL
    // 0x9255f8: r1 = Null
    //     0x9255f8: mov             x1, NULL
    // 0x9255fc: stur            x3, [fp, #-0x20]
    // 0x925600: branchIfSmi(r0, 0x925628)
    //     0x925600: tbz             w0, #0, #0x925628
    // 0x925604: r4 = LoadClassIdInstr(r0)
    //     0x925604: ldur            x4, [x0, #-1]
    //     0x925608: ubfx            x4, x4, #0xc, #0x14
    // 0x92560c: sub             x4, x4, #0x3b
    // 0x925610: cmp             x4, #1
    // 0x925614: b.ls            #0x925628
    // 0x925618: r8 = int?
    //     0x925618: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x92561c: r3 = Null
    //     0x92561c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31728] Null
    //     0x925620: ldr             x3, [x3, #0x728]
    // 0x925624: r0 = int?()
    //     0x925624: bl              #0x959574  ; IsType_int?_Stub
    // 0x925628: ldur            x1, [fp, #-0x18]
    // 0x92562c: r0 = LoadClassIdInstr(r1)
    //     0x92562c: ldur            x0, [x1, #-1]
    //     0x925630: ubfx            x0, x0, #0xc, #0x14
    // 0x925634: r16 = 2
    //     0x925634: movz            x16, #0x2
    // 0x925638: stp             x16, x1, [SP]
    // 0x92563c: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x92563c: movz            x17, #0x32a
    //     0x925640: movk            x17, #0x1, lsl #16
    //     0x925644: add             lr, x0, x17
    //     0x925648: ldr             lr, [x21, lr, lsl #3]
    //     0x92564c: blr             lr
    // 0x925650: mov             x3, x0
    // 0x925654: r2 = Null
    //     0x925654: mov             x2, NULL
    // 0x925658: r1 = Null
    //     0x925658: mov             x1, NULL
    // 0x92565c: stur            x3, [fp, #-0x28]
    // 0x925660: branchIfSmi(r0, 0x925688)
    //     0x925660: tbz             w0, #0, #0x925688
    // 0x925664: r4 = LoadClassIdInstr(r0)
    //     0x925664: ldur            x4, [x0, #-1]
    //     0x925668: ubfx            x4, x4, #0xc, #0x14
    // 0x92566c: sub             x4, x4, #0x3b
    // 0x925670: cmp             x4, #1
    // 0x925674: b.ls            #0x925688
    // 0x925678: r8 = int?
    //     0x925678: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x92567c: r3 = Null
    //     0x92567c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31738] Null
    //     0x925680: ldr             x3, [x3, #0x738]
    // 0x925684: r0 = int?()
    //     0x925684: bl              #0x959574  ; IsType_int?_Stub
    // 0x925688: ldur            x0, [fp, #-0x18]
    // 0x92568c: r1 = LoadClassIdInstr(r0)
    //     0x92568c: ldur            x1, [x0, #-1]
    //     0x925690: ubfx            x1, x1, #0xc, #0x14
    // 0x925694: r16 = 4
    //     0x925694: movz            x16, #0x4
    // 0x925698: stp             x16, x0, [SP]
    // 0x92569c: mov             x0, x1
    // 0x9256a0: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9256a0: movz            x17, #0x32a
    //     0x9256a4: movk            x17, #0x1, lsl #16
    //     0x9256a8: add             lr, x0, x17
    //     0x9256ac: ldr             lr, [x21, lr, lsl #3]
    //     0x9256b0: blr             lr
    // 0x9256b4: mov             x3, x0
    // 0x9256b8: r2 = Null
    //     0x9256b8: mov             x2, NULL
    // 0x9256bc: r1 = Null
    //     0x9256bc: mov             x1, NULL
    // 0x9256c0: stur            x3, [fp, #-0x18]
    // 0x9256c4: r4 = 59
    //     0x9256c4: movz            x4, #0x3b
    // 0x9256c8: branchIfSmi(r0, 0x9256d4)
    //     0x9256c8: tbz             w0, #0, #0x9256d4
    // 0x9256cc: r4 = LoadClassIdInstr(r0)
    //     0x9256cc: ldur            x4, [x0, #-1]
    //     0x9256d0: ubfx            x4, x4, #0xc, #0x14
    // 0x9256d4: sub             x4, x4, #0x5d
    // 0x9256d8: cmp             x4, #1
    // 0x9256dc: b.ls            #0x9256f0
    // 0x9256e0: r8 = String?
    //     0x9256e0: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x9256e4: r3 = Null
    //     0x9256e4: add             x3, PP, #0x31, lsl #12  ; [pp+0x31748] Null
    //     0x9256e8: ldr             x3, [x3, #0x748]
    // 0x9256ec: r0 = String?()
    //     0x9256ec: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x9256f0: ldur            x0, [fp, #-0x10]
    // 0x9256f4: LoadField: r1 = r0->field_f
    //     0x9256f4: ldur            w1, [x0, #0xf]
    // 0x9256f8: DecompressPointer r1
    //     0x9256f8: add             x1, x1, HEAP, lsl #32
    // 0x9256fc: ldur            x0, [fp, #-0x20]
    // 0x925700: cmp             w0, NULL
    // 0x925704: b.eq            #0x925758
    // 0x925708: ldur            x2, [fp, #-0x28]
    // 0x92570c: cmp             w2, NULL
    // 0x925710: b.eq            #0x92575c
    // 0x925714: ldur            x5, [fp, #-0x18]
    // 0x925718: cmp             w5, NULL
    // 0x92571c: b.eq            #0x925760
    // 0x925720: r3 = LoadInt32Instr(r0)
    //     0x925720: sbfx            x3, x0, #1, #0x1f
    //     0x925724: tbz             w0, #0, #0x92572c
    //     0x925728: ldur            x3, [x0, #7]
    // 0x92572c: r0 = LoadInt32Instr(r2)
    //     0x92572c: sbfx            x0, x2, #1, #0x1f
    //     0x925730: tbz             w2, #0, #0x925738
    //     0x925734: ldur            x0, [x2, #7]
    // 0x925738: mov             x2, x3
    // 0x92573c: mov             x3, x0
    // 0x925740: r0 = call 0x64ebe8
    //     0x925740: bl              #0x64ebe8
    // 0x925744: r0 = Null
    //     0x925744: mov             x0, NULL
    // 0x925748: r0 = ReturnAsyncNotFuture()
    //     0x925748: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x92574c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92574c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x925750: b               #0x925584
    // 0x925754: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925754: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925758: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925758: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92575c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92575c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925760: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925760: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x925764, size: 0x128
    // 0x925764: EnterFrame
    //     0x925764: stp             fp, lr, [SP, #-0x10]!
    //     0x925768: mov             fp, SP
    // 0x92576c: AllocStack(0x28)
    //     0x92576c: sub             SP, SP, #0x28
    // 0x925770: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x925770: stur            NULL, [fp, #-8]
    //     0x925774: movz            x0, #0
    //     0x925778: add             x1, fp, w0, sxtw #2
    //     0x92577c: ldr             x1, [x1, #0x18]
    //     0x925780: add             x2, fp, w0, sxtw #2
    //     0x925784: ldr             x2, [x2, #0x10]
    //     0x925788: stur            x2, [fp, #-0x18]
    //     0x92578c: ldur            w3, [x1, #0x17]
    //     0x925790: add             x3, x3, HEAP, lsl #32
    //     0x925794: stur            x3, [fp, #-0x10]
    // 0x925798: CheckStackOverflow
    //     0x925798: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92579c: cmp             SP, x16
    //     0x9257a0: b.ls            #0x92587c
    // 0x9257a4: InitAsync() -> Future<Null?>
    //     0x9257a4: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x9257a8: bl              #0x8c1de0  ; InitAsyncStub
    // 0x9257ac: ldur            x0, [fp, #-0x18]
    // 0x9257b0: r2 = Null
    //     0x9257b0: mov             x2, NULL
    // 0x9257b4: r1 = Null
    //     0x9257b4: mov             x1, NULL
    // 0x9257b8: r4 = 59
    //     0x9257b8: movz            x4, #0x3b
    // 0x9257bc: branchIfSmi(r0, 0x9257c8)
    //     0x9257bc: tbz             w0, #0, #0x9257c8
    // 0x9257c0: r4 = LoadClassIdInstr(r0)
    //     0x9257c0: ldur            x4, [x0, #-1]
    //     0x9257c4: ubfx            x4, x4, #0xc, #0x14
    // 0x9257c8: sub             x4, x4, #0x59
    // 0x9257cc: cmp             x4, #2
    // 0x9257d0: b.ls            #0x9257e4
    // 0x9257d4: r8 = List<Object?>?
    //     0x9257d4: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x9257d8: r3 = Null
    //     0x9257d8: add             x3, PP, #0x31, lsl #12  ; [pp+0x31770] Null
    //     0x9257dc: ldr             x3, [x3, #0x770]
    // 0x9257e0: r0 = List<Object?>?()
    //     0x9257e0: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x9257e4: ldur            x0, [fp, #-0x18]
    // 0x9257e8: cmp             w0, NULL
    // 0x9257ec: b.eq            #0x925884
    // 0x9257f0: r1 = LoadClassIdInstr(r0)
    //     0x9257f0: ldur            x1, [x0, #-1]
    //     0x9257f4: ubfx            x1, x1, #0xc, #0x14
    // 0x9257f8: stp             xzr, x0, [SP]
    // 0x9257fc: mov             x0, x1
    // 0x925800: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x925800: movz            x17, #0x32a
    //     0x925804: movk            x17, #0x1, lsl #16
    //     0x925808: add             lr, x0, x17
    //     0x92580c: ldr             lr, [x21, lr, lsl #3]
    //     0x925810: blr             lr
    // 0x925814: mov             x3, x0
    // 0x925818: r2 = Null
    //     0x925818: mov             x2, NULL
    // 0x92581c: r1 = Null
    //     0x92581c: mov             x1, NULL
    // 0x925820: stur            x3, [fp, #-0x18]
    // 0x925824: branchIfSmi(r0, 0x92584c)
    //     0x925824: tbz             w0, #0, #0x92584c
    // 0x925828: r4 = LoadClassIdInstr(r0)
    //     0x925828: ldur            x4, [x0, #-1]
    //     0x92582c: ubfx            x4, x4, #0xc, #0x14
    // 0x925830: sub             x4, x4, #0x3b
    // 0x925834: cmp             x4, #1
    // 0x925838: b.ls            #0x92584c
    // 0x92583c: r8 = int?
    //     0x92583c: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x925840: r3 = Null
    //     0x925840: add             x3, PP, #0x31, lsl #12  ; [pp+0x31780] Null
    //     0x925844: ldr             x3, [x3, #0x780]
    // 0x925848: r0 = int?()
    //     0x925848: bl              #0x959574  ; IsType_int?_Stub
    // 0x92584c: ldur            x0, [fp, #-0x10]
    // 0x925850: LoadField: r1 = r0->field_f
    //     0x925850: ldur            w1, [x0, #0xf]
    // 0x925854: DecompressPointer r1
    //     0x925854: add             x1, x1, HEAP, lsl #32
    // 0x925858: ldur            x0, [fp, #-0x18]
    // 0x92585c: cmp             w0, NULL
    // 0x925860: b.eq            #0x925888
    // 0x925864: r2 = LoadInt32Instr(r0)
    //     0x925864: sbfx            x2, x0, #1, #0x1f
    //     0x925868: tbz             w0, #0, #0x925870
    //     0x92586c: ldur            x2, [x0, #7]
    // 0x925870: r0 = call 0x64ecec
    //     0x925870: bl              #0x64ecec
    // 0x925874: r0 = Null
    //     0x925874: mov             x0, NULL
    // 0x925878: r0 = ReturnAsyncNotFuture()
    //     0x925878: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x92587c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92587c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x925880: b               #0x9257a4
    // 0x925884: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925884: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925888: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925888: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x92588c, size: 0x228
    // 0x92588c: EnterFrame
    //     0x92588c: stp             fp, lr, [SP, #-0x10]!
    //     0x925890: mov             fp, SP
    // 0x925894: AllocStack(0x38)
    //     0x925894: sub             SP, SP, #0x38
    // 0x925898: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x925898: stur            NULL, [fp, #-8]
    //     0x92589c: movz            x0, #0
    //     0x9258a0: add             x1, fp, w0, sxtw #2
    //     0x9258a4: ldr             x1, [x1, #0x18]
    //     0x9258a8: add             x2, fp, w0, sxtw #2
    //     0x9258ac: ldr             x2, [x2, #0x10]
    //     0x9258b0: stur            x2, [fp, #-0x18]
    //     0x9258b4: ldur            w3, [x1, #0x17]
    //     0x9258b8: add             x3, x3, HEAP, lsl #32
    //     0x9258bc: stur            x3, [fp, #-0x10]
    // 0x9258c0: CheckStackOverflow
    //     0x9258c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9258c4: cmp             SP, x16
    //     0x9258c8: b.ls            #0x925a9c
    // 0x9258cc: InitAsync() -> Future<Null?>
    //     0x9258cc: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x9258d0: bl              #0x8c1de0  ; InitAsyncStub
    // 0x9258d4: ldur            x0, [fp, #-0x18]
    // 0x9258d8: r2 = Null
    //     0x9258d8: mov             x2, NULL
    // 0x9258dc: r1 = Null
    //     0x9258dc: mov             x1, NULL
    // 0x9258e0: r4 = 59
    //     0x9258e0: movz            x4, #0x3b
    // 0x9258e4: branchIfSmi(r0, 0x9258f0)
    //     0x9258e4: tbz             w0, #0, #0x9258f0
    // 0x9258e8: r4 = LoadClassIdInstr(r0)
    //     0x9258e8: ldur            x4, [x0, #-1]
    //     0x9258ec: ubfx            x4, x4, #0xc, #0x14
    // 0x9258f0: sub             x4, x4, #0x59
    // 0x9258f4: cmp             x4, #2
    // 0x9258f8: b.ls            #0x92590c
    // 0x9258fc: r8 = List<Object?>?
    //     0x9258fc: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x925900: r3 = Null
    //     0x925900: add             x3, PP, #0x31, lsl #12  ; [pp+0x31790] Null
    //     0x925904: ldr             x3, [x3, #0x790]
    // 0x925908: r0 = List<Object?>?()
    //     0x925908: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x92590c: ldur            x1, [fp, #-0x18]
    // 0x925910: cmp             w1, NULL
    // 0x925914: b.eq            #0x925aa4
    // 0x925918: r0 = LoadClassIdInstr(r1)
    //     0x925918: ldur            x0, [x1, #-1]
    //     0x92591c: ubfx            x0, x0, #0xc, #0x14
    // 0x925920: stp             xzr, x1, [SP]
    // 0x925924: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x925924: movz            x17, #0x32a
    //     0x925928: movk            x17, #0x1, lsl #16
    //     0x92592c: add             lr, x0, x17
    //     0x925930: ldr             lr, [x21, lr, lsl #3]
    //     0x925934: blr             lr
    // 0x925938: mov             x3, x0
    // 0x92593c: r2 = Null
    //     0x92593c: mov             x2, NULL
    // 0x925940: r1 = Null
    //     0x925940: mov             x1, NULL
    // 0x925944: stur            x3, [fp, #-0x20]
    // 0x925948: branchIfSmi(r0, 0x925970)
    //     0x925948: tbz             w0, #0, #0x925970
    // 0x92594c: r4 = LoadClassIdInstr(r0)
    //     0x92594c: ldur            x4, [x0, #-1]
    //     0x925950: ubfx            x4, x4, #0xc, #0x14
    // 0x925954: sub             x4, x4, #0x3b
    // 0x925958: cmp             x4, #1
    // 0x92595c: b.ls            #0x925970
    // 0x925960: r8 = int?
    //     0x925960: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x925964: r3 = Null
    //     0x925964: add             x3, PP, #0x31, lsl #12  ; [pp+0x317a0] Null
    //     0x925968: ldr             x3, [x3, #0x7a0]
    // 0x92596c: r0 = int?()
    //     0x92596c: bl              #0x959574  ; IsType_int?_Stub
    // 0x925970: ldur            x1, [fp, #-0x18]
    // 0x925974: r0 = LoadClassIdInstr(r1)
    //     0x925974: ldur            x0, [x1, #-1]
    //     0x925978: ubfx            x0, x0, #0xc, #0x14
    // 0x92597c: r16 = 2
    //     0x92597c: movz            x16, #0x2
    // 0x925980: stp             x16, x1, [SP]
    // 0x925984: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x925984: movz            x17, #0x32a
    //     0x925988: movk            x17, #0x1, lsl #16
    //     0x92598c: add             lr, x0, x17
    //     0x925990: ldr             lr, [x21, lr, lsl #3]
    //     0x925994: blr             lr
    // 0x925998: mov             x3, x0
    // 0x92599c: r2 = Null
    //     0x92599c: mov             x2, NULL
    // 0x9259a0: r1 = Null
    //     0x9259a0: mov             x1, NULL
    // 0x9259a4: stur            x3, [fp, #-0x28]
    // 0x9259a8: branchIfSmi(r0, 0x9259d0)
    //     0x9259a8: tbz             w0, #0, #0x9259d0
    // 0x9259ac: r4 = LoadClassIdInstr(r0)
    //     0x9259ac: ldur            x4, [x0, #-1]
    //     0x9259b0: ubfx            x4, x4, #0xc, #0x14
    // 0x9259b4: sub             x4, x4, #0x3b
    // 0x9259b8: cmp             x4, #1
    // 0x9259bc: b.ls            #0x9259d0
    // 0x9259c0: r8 = int?
    //     0x9259c0: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9259c4: r3 = Null
    //     0x9259c4: add             x3, PP, #0x31, lsl #12  ; [pp+0x317b0] Null
    //     0x9259c8: ldr             x3, [x3, #0x7b0]
    // 0x9259cc: r0 = int?()
    //     0x9259cc: bl              #0x959574  ; IsType_int?_Stub
    // 0x9259d0: ldur            x0, [fp, #-0x18]
    // 0x9259d4: r1 = LoadClassIdInstr(r0)
    //     0x9259d4: ldur            x1, [x0, #-1]
    //     0x9259d8: ubfx            x1, x1, #0xc, #0x14
    // 0x9259dc: r16 = 4
    //     0x9259dc: movz            x16, #0x4
    // 0x9259e0: stp             x16, x0, [SP]
    // 0x9259e4: mov             x0, x1
    // 0x9259e8: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9259e8: movz            x17, #0x32a
    //     0x9259ec: movk            x17, #0x1, lsl #16
    //     0x9259f0: add             lr, x0, x17
    //     0x9259f4: ldr             lr, [x21, lr, lsl #3]
    //     0x9259f8: blr             lr
    // 0x9259fc: mov             x3, x0
    // 0x925a00: r2 = Null
    //     0x925a00: mov             x2, NULL
    // 0x925a04: r1 = Null
    //     0x925a04: mov             x1, NULL
    // 0x925a08: stur            x3, [fp, #-0x18]
    // 0x925a0c: branchIfSmi(r0, 0x925a34)
    //     0x925a0c: tbz             w0, #0, #0x925a34
    // 0x925a10: r4 = LoadClassIdInstr(r0)
    //     0x925a10: ldur            x4, [x0, #-1]
    //     0x925a14: ubfx            x4, x4, #0xc, #0x14
    // 0x925a18: sub             x4, x4, #0x3b
    // 0x925a1c: cmp             x4, #1
    // 0x925a20: b.ls            #0x925a34
    // 0x925a24: r8 = int?
    //     0x925a24: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x925a28: r3 = Null
    //     0x925a28: add             x3, PP, #0x31, lsl #12  ; [pp+0x317c0] Null
    //     0x925a2c: ldr             x3, [x3, #0x7c0]
    // 0x925a30: r0 = int?()
    //     0x925a30: bl              #0x959574  ; IsType_int?_Stub
    // 0x925a34: ldur            x0, [fp, #-0x10]
    // 0x925a38: LoadField: r1 = r0->field_f
    //     0x925a38: ldur            w1, [x0, #0xf]
    // 0x925a3c: DecompressPointer r1
    //     0x925a3c: add             x1, x1, HEAP, lsl #32
    // 0x925a40: ldur            x0, [fp, #-0x20]
    // 0x925a44: cmp             w0, NULL
    // 0x925a48: b.eq            #0x925aa8
    // 0x925a4c: ldur            x2, [fp, #-0x28]
    // 0x925a50: cmp             w2, NULL
    // 0x925a54: b.eq            #0x925aac
    // 0x925a58: ldur            x3, [fp, #-0x18]
    // 0x925a5c: cmp             w3, NULL
    // 0x925a60: b.eq            #0x925ab0
    // 0x925a64: r4 = LoadInt32Instr(r0)
    //     0x925a64: sbfx            x4, x0, #1, #0x1f
    //     0x925a68: tbz             w0, #0, #0x925a70
    //     0x925a6c: ldur            x4, [x0, #7]
    // 0x925a70: r0 = LoadInt32Instr(r2)
    //     0x925a70: sbfx            x0, x2, #1, #0x1f
    //     0x925a74: tbz             w2, #0, #0x925a7c
    //     0x925a78: ldur            x0, [x2, #7]
    // 0x925a7c: r5 = LoadInt32Instr(r3)
    //     0x925a7c: sbfx            x5, x3, #1, #0x1f
    //     0x925a80: tbz             w3, #0, #0x925a88
    //     0x925a84: ldur            x5, [x3, #7]
    // 0x925a88: mov             x2, x4
    // 0x925a8c: mov             x3, x0
    // 0x925a90: r0 = call 0x64ed78
    //     0x925a90: bl              #0x64ed78
    // 0x925a94: r0 = Null
    //     0x925a94: mov             x0, NULL
    // 0x925a98: r0 = ReturnAsyncNotFuture()
    //     0x925a98: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x925a9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x925a9c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x925aa0: b               #0x9258cc
    // 0x925aa4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925aa4: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925aa8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925aa8: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925aac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925aac: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925ab0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925ab0: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x925ab4, size: 0x1ac
    // 0x925ab4: EnterFrame
    //     0x925ab4: stp             fp, lr, [SP, #-0x10]!
    //     0x925ab8: mov             fp, SP
    // 0x925abc: AllocStack(0x30)
    //     0x925abc: sub             SP, SP, #0x30
    // 0x925ac0: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x925ac0: stur            NULL, [fp, #-8]
    //     0x925ac4: movz            x0, #0
    //     0x925ac8: add             x1, fp, w0, sxtw #2
    //     0x925acc: ldr             x1, [x1, #0x18]
    //     0x925ad0: add             x2, fp, w0, sxtw #2
    //     0x925ad4: ldr             x2, [x2, #0x10]
    //     0x925ad8: stur            x2, [fp, #-0x18]
    //     0x925adc: ldur            w3, [x1, #0x17]
    //     0x925ae0: add             x3, x3, HEAP, lsl #32
    //     0x925ae4: stur            x3, [fp, #-0x10]
    // 0x925ae8: CheckStackOverflow
    //     0x925ae8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x925aec: cmp             SP, x16
    //     0x925af0: b.ls            #0x925c4c
    // 0x925af4: InitAsync() -> Future<Null?>
    //     0x925af4: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x925af8: bl              #0x8c1de0  ; InitAsyncStub
    // 0x925afc: ldur            x0, [fp, #-0x18]
    // 0x925b00: r2 = Null
    //     0x925b00: mov             x2, NULL
    // 0x925b04: r1 = Null
    //     0x925b04: mov             x1, NULL
    // 0x925b08: r4 = 59
    //     0x925b08: movz            x4, #0x3b
    // 0x925b0c: branchIfSmi(r0, 0x925b18)
    //     0x925b0c: tbz             w0, #0, #0x925b18
    // 0x925b10: r4 = LoadClassIdInstr(r0)
    //     0x925b10: ldur            x4, [x0, #-1]
    //     0x925b14: ubfx            x4, x4, #0xc, #0x14
    // 0x925b18: sub             x4, x4, #0x59
    // 0x925b1c: cmp             x4, #2
    // 0x925b20: b.ls            #0x925b34
    // 0x925b24: r8 = List<Object?>?
    //     0x925b24: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x925b28: r3 = Null
    //     0x925b28: add             x3, PP, #0x31, lsl #12  ; [pp+0x317e0] Null
    //     0x925b2c: ldr             x3, [x3, #0x7e0]
    // 0x925b30: r0 = List<Object?>?()
    //     0x925b30: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x925b34: ldur            x1, [fp, #-0x18]
    // 0x925b38: cmp             w1, NULL
    // 0x925b3c: b.eq            #0x925c54
    // 0x925b40: r0 = LoadClassIdInstr(r1)
    //     0x925b40: ldur            x0, [x1, #-1]
    //     0x925b44: ubfx            x0, x0, #0xc, #0x14
    // 0x925b48: stp             xzr, x1, [SP]
    // 0x925b4c: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x925b4c: movz            x17, #0x32a
    //     0x925b50: movk            x17, #0x1, lsl #16
    //     0x925b54: add             lr, x0, x17
    //     0x925b58: ldr             lr, [x21, lr, lsl #3]
    //     0x925b5c: blr             lr
    // 0x925b60: mov             x3, x0
    // 0x925b64: r2 = Null
    //     0x925b64: mov             x2, NULL
    // 0x925b68: r1 = Null
    //     0x925b68: mov             x1, NULL
    // 0x925b6c: stur            x3, [fp, #-0x20]
    // 0x925b70: branchIfSmi(r0, 0x925b98)
    //     0x925b70: tbz             w0, #0, #0x925b98
    // 0x925b74: r4 = LoadClassIdInstr(r0)
    //     0x925b74: ldur            x4, [x0, #-1]
    //     0x925b78: ubfx            x4, x4, #0xc, #0x14
    // 0x925b7c: sub             x4, x4, #0x3b
    // 0x925b80: cmp             x4, #1
    // 0x925b84: b.ls            #0x925b98
    // 0x925b88: r8 = int?
    //     0x925b88: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x925b8c: r3 = Null
    //     0x925b8c: add             x3, PP, #0x31, lsl #12  ; [pp+0x317f0] Null
    //     0x925b90: ldr             x3, [x3, #0x7f0]
    // 0x925b94: r0 = int?()
    //     0x925b94: bl              #0x959574  ; IsType_int?_Stub
    // 0x925b98: ldur            x0, [fp, #-0x18]
    // 0x925b9c: r1 = LoadClassIdInstr(r0)
    //     0x925b9c: ldur            x1, [x0, #-1]
    //     0x925ba0: ubfx            x1, x1, #0xc, #0x14
    // 0x925ba4: r16 = 2
    //     0x925ba4: movz            x16, #0x2
    // 0x925ba8: stp             x16, x0, [SP]
    // 0x925bac: mov             x0, x1
    // 0x925bb0: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x925bb0: movz            x17, #0x32a
    //     0x925bb4: movk            x17, #0x1, lsl #16
    //     0x925bb8: add             lr, x0, x17
    //     0x925bbc: ldr             lr, [x21, lr, lsl #3]
    //     0x925bc0: blr             lr
    // 0x925bc4: mov             x3, x0
    // 0x925bc8: r2 = Null
    //     0x925bc8: mov             x2, NULL
    // 0x925bcc: r1 = Null
    //     0x925bcc: mov             x1, NULL
    // 0x925bd0: stur            x3, [fp, #-0x18]
    // 0x925bd4: branchIfSmi(r0, 0x925bfc)
    //     0x925bd4: tbz             w0, #0, #0x925bfc
    // 0x925bd8: r4 = LoadClassIdInstr(r0)
    //     0x925bd8: ldur            x4, [x0, #-1]
    //     0x925bdc: ubfx            x4, x4, #0xc, #0x14
    // 0x925be0: sub             x4, x4, #0x3b
    // 0x925be4: cmp             x4, #1
    // 0x925be8: b.ls            #0x925bfc
    // 0x925bec: r8 = int?
    //     0x925bec: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x925bf0: r3 = Null
    //     0x925bf0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31800] Null
    //     0x925bf4: ldr             x3, [x3, #0x800]
    // 0x925bf8: r0 = int?()
    //     0x925bf8: bl              #0x959574  ; IsType_int?_Stub
    // 0x925bfc: ldur            x0, [fp, #-0x10]
    // 0x925c00: LoadField: r1 = r0->field_f
    //     0x925c00: ldur            w1, [x0, #0xf]
    // 0x925c04: DecompressPointer r1
    //     0x925c04: add             x1, x1, HEAP, lsl #32
    // 0x925c08: ldur            x0, [fp, #-0x20]
    // 0x925c0c: cmp             w0, NULL
    // 0x925c10: b.eq            #0x925c58
    // 0x925c14: ldur            x2, [fp, #-0x18]
    // 0x925c18: cmp             w2, NULL
    // 0x925c1c: b.eq            #0x925c5c
    // 0x925c20: r3 = LoadInt32Instr(r0)
    //     0x925c20: sbfx            x3, x0, #1, #0x1f
    //     0x925c24: tbz             w0, #0, #0x925c2c
    //     0x925c28: ldur            x3, [x0, #7]
    // 0x925c2c: r0 = LoadInt32Instr(r2)
    //     0x925c2c: sbfx            x0, x2, #1, #0x1f
    //     0x925c30: tbz             w2, #0, #0x925c38
    //     0x925c34: ldur            x0, [x2, #7]
    // 0x925c38: mov             x2, x3
    // 0x925c3c: mov             x3, x0
    // 0x925c40: r0 = call 0x64ee88
    //     0x925c40: bl              #0x64ee88
    // 0x925c44: r0 = Null
    //     0x925c44: mov             x0, NULL
    // 0x925c48: r0 = ReturnAsyncNotFuture()
    //     0x925c48: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x925c4c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x925c4c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x925c50: b               #0x925af4
    // 0x925c54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925c54: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925c58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925c58: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x925c5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x925c5c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<List<String?>> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x925f00, size: 0x244
    // 0x925f00: EnterFrame
    //     0x925f00: stp             fp, lr, [SP, #-0x10]!
    //     0x925f04: mov             fp, SP
    // 0x925f08: AllocStack(0x38)
    //     0x925f08: sub             SP, SP, #0x38
    // 0x925f0c: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x925f0c: stur            NULL, [fp, #-8]
    //     0x925f10: movz            x0, #0
    //     0x925f14: add             x1, fp, w0, sxtw #2
    //     0x925f18: ldr             x1, [x1, #0x18]
    //     0x925f1c: add             x2, fp, w0, sxtw #2
    //     0x925f20: ldr             x2, [x2, #0x10]
    //     0x925f24: stur            x2, [fp, #-0x18]
    //     0x925f28: ldur            w3, [x1, #0x17]
    //     0x925f2c: add             x3, x3, HEAP, lsl #32
    //     0x925f30: stur            x3, [fp, #-0x10]
    // 0x925f34: CheckStackOverflow
    //     0x925f34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x925f38: cmp             SP, x16
    //     0x925f3c: b.ls            #0x92612c
    // 0x925f40: InitAsync() -> Future<List<String?>>
    //     0x925f40: add             x0, PP, #0x28, lsl #12  ; [pp+0x288f8] TypeArguments: <List<String?>>
    //     0x925f44: ldr             x0, [x0, #0x8f8]
    //     0x925f48: bl              #0x8c1de0  ; InitAsyncStub
    // 0x925f4c: ldur            x0, [fp, #-0x18]
    // 0x925f50: r2 = Null
    //     0x925f50: mov             x2, NULL
    // 0x925f54: r1 = Null
    //     0x925f54: mov             x1, NULL
    // 0x925f58: r4 = 59
    //     0x925f58: movz            x4, #0x3b
    // 0x925f5c: branchIfSmi(r0, 0x925f68)
    //     0x925f5c: tbz             w0, #0, #0x925f68
    // 0x925f60: r4 = LoadClassIdInstr(r0)
    //     0x925f60: ldur            x4, [x0, #-1]
    //     0x925f64: ubfx            x4, x4, #0xc, #0x14
    // 0x925f68: sub             x4, x4, #0x59
    // 0x925f6c: cmp             x4, #2
    // 0x925f70: b.ls            #0x925f84
    // 0x925f74: r8 = List<Object?>?
    //     0x925f74: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x925f78: r3 = Null
    //     0x925f78: add             x3, PP, #0x31, lsl #12  ; [pp+0x31850] Null
    //     0x925f7c: ldr             x3, [x3, #0x850]
    // 0x925f80: r0 = List<Object?>?()
    //     0x925f80: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x925f84: ldur            x1, [fp, #-0x18]
    // 0x925f88: cmp             w1, NULL
    // 0x925f8c: b.eq            #0x926134
    // 0x925f90: r0 = LoadClassIdInstr(r1)
    //     0x925f90: ldur            x0, [x1, #-1]
    //     0x925f94: ubfx            x0, x0, #0xc, #0x14
    // 0x925f98: stp             xzr, x1, [SP]
    // 0x925f9c: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x925f9c: movz            x17, #0x32a
    //     0x925fa0: movk            x17, #0x1, lsl #16
    //     0x925fa4: add             lr, x0, x17
    //     0x925fa8: ldr             lr, [x21, lr, lsl #3]
    //     0x925fac: blr             lr
    // 0x925fb0: mov             x3, x0
    // 0x925fb4: r2 = Null
    //     0x925fb4: mov             x2, NULL
    // 0x925fb8: r1 = Null
    //     0x925fb8: mov             x1, NULL
    // 0x925fbc: stur            x3, [fp, #-0x20]
    // 0x925fc0: branchIfSmi(r0, 0x925fe8)
    //     0x925fc0: tbz             w0, #0, #0x925fe8
    // 0x925fc4: r4 = LoadClassIdInstr(r0)
    //     0x925fc4: ldur            x4, [x0, #-1]
    //     0x925fc8: ubfx            x4, x4, #0xc, #0x14
    // 0x925fcc: sub             x4, x4, #0x3b
    // 0x925fd0: cmp             x4, #1
    // 0x925fd4: b.ls            #0x925fe8
    // 0x925fd8: r8 = int?
    //     0x925fd8: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x925fdc: r3 = Null
    //     0x925fdc: add             x3, PP, #0x31, lsl #12  ; [pp+0x31860] Null
    //     0x925fe0: ldr             x3, [x3, #0x860]
    // 0x925fe4: r0 = int?()
    //     0x925fe4: bl              #0x959574  ; IsType_int?_Stub
    // 0x925fe8: ldur            x1, [fp, #-0x18]
    // 0x925fec: r0 = LoadClassIdInstr(r1)
    //     0x925fec: ldur            x0, [x1, #-1]
    //     0x925ff0: ubfx            x0, x0, #0xc, #0x14
    // 0x925ff4: r16 = 2
    //     0x925ff4: movz            x16, #0x2
    // 0x925ff8: stp             x16, x1, [SP]
    // 0x925ffc: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x925ffc: movz            x17, #0x32a
    //     0x926000: movk            x17, #0x1, lsl #16
    //     0x926004: add             lr, x0, x17
    //     0x926008: ldr             lr, [x21, lr, lsl #3]
    //     0x92600c: blr             lr
    // 0x926010: mov             x3, x0
    // 0x926014: r2 = Null
    //     0x926014: mov             x2, NULL
    // 0x926018: r1 = Null
    //     0x926018: mov             x1, NULL
    // 0x92601c: stur            x3, [fp, #-0x28]
    // 0x926020: branchIfSmi(r0, 0x926048)
    //     0x926020: tbz             w0, #0, #0x926048
    // 0x926024: r4 = LoadClassIdInstr(r0)
    //     0x926024: ldur            x4, [x0, #-1]
    //     0x926028: ubfx            x4, x4, #0xc, #0x14
    // 0x92602c: sub             x4, x4, #0x3b
    // 0x926030: cmp             x4, #1
    // 0x926034: b.ls            #0x926048
    // 0x926038: r8 = int?
    //     0x926038: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x92603c: r3 = Null
    //     0x92603c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31870] Null
    //     0x926040: ldr             x3, [x3, #0x870]
    // 0x926044: r0 = int?()
    //     0x926044: bl              #0x959574  ; IsType_int?_Stub
    // 0x926048: ldur            x0, [fp, #-0x18]
    // 0x92604c: r1 = LoadClassIdInstr(r0)
    //     0x92604c: ldur            x1, [x0, #-1]
    //     0x926050: ubfx            x1, x1, #0xc, #0x14
    // 0x926054: r16 = 4
    //     0x926054: movz            x16, #0x4
    // 0x926058: stp             x16, x0, [SP]
    // 0x92605c: mov             x0, x1
    // 0x926060: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926060: movz            x17, #0x32a
    //     0x926064: movk            x17, #0x1, lsl #16
    //     0x926068: add             lr, x0, x17
    //     0x92606c: ldr             lr, [x21, lr, lsl #3]
    //     0x926070: blr             lr
    // 0x926074: mov             x3, x0
    // 0x926078: r2 = Null
    //     0x926078: mov             x2, NULL
    // 0x92607c: r1 = Null
    //     0x92607c: mov             x1, NULL
    // 0x926080: stur            x3, [fp, #-0x18]
    // 0x926084: branchIfSmi(r0, 0x9260ac)
    //     0x926084: tbz             w0, #0, #0x9260ac
    // 0x926088: r4 = LoadClassIdInstr(r0)
    //     0x926088: ldur            x4, [x0, #-1]
    //     0x92608c: ubfx            x4, x4, #0xc, #0x14
    // 0x926090: sub             x4, x4, #0x3b
    // 0x926094: cmp             x4, #1
    // 0x926098: b.ls            #0x9260ac
    // 0x92609c: r8 = int?
    //     0x92609c: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9260a0: r3 = Null
    //     0x9260a0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31880] Null
    //     0x9260a4: ldr             x3, [x3, #0x880]
    // 0x9260a8: r0 = int?()
    //     0x9260a8: bl              #0x959574  ; IsType_int?_Stub
    // 0x9260ac: ldur            x0, [fp, #-0x10]
    // 0x9260b0: LoadField: r1 = r0->field_f
    //     0x9260b0: ldur            w1, [x0, #0xf]
    // 0x9260b4: DecompressPointer r1
    //     0x9260b4: add             x1, x1, HEAP, lsl #32
    // 0x9260b8: ldur            x2, [fp, #-0x20]
    // 0x9260bc: cmp             w2, NULL
    // 0x9260c0: b.eq            #0x926138
    // 0x9260c4: ldur            x3, [fp, #-0x28]
    // 0x9260c8: cmp             w3, NULL
    // 0x9260cc: b.eq            #0x92613c
    // 0x9260d0: ldur            x4, [fp, #-0x18]
    // 0x9260d4: cmp             w4, NULL
    // 0x9260d8: b.eq            #0x926140
    // 0x9260dc: r5 = LoadInt32Instr(r2)
    //     0x9260dc: sbfx            x5, x2, #1, #0x1f
    //     0x9260e0: tbz             w2, #0, #0x9260e8
    //     0x9260e4: ldur            x5, [x2, #7]
    // 0x9260e8: r2 = LoadInt32Instr(r3)
    //     0x9260e8: sbfx            x2, x3, #1, #0x1f
    //     0x9260ec: tbz             w3, #0, #0x9260f4
    //     0x9260f0: ldur            x2, [x3, #7]
    // 0x9260f4: r3 = LoadInt32Instr(r4)
    //     0x9260f4: sbfx            x3, x4, #1, #0x1f
    //     0x9260f8: tbz             w4, #0, #0x926100
    //     0x9260fc: ldur            x3, [x4, #7]
    // 0x926100: mov             x16, x3
    // 0x926104: mov             x3, x5
    // 0x926108: mov             x5, x16
    // 0x92610c: mov             x16, x2
    // 0x926110: mov             x2, x3
    // 0x926114: mov             x3, x16
    // 0x926118: r0 = call 0x64f020
    //     0x926118: bl              #0x64f020
    // 0x92611c: mov             x1, x0
    // 0x926120: stur            x1, [fp, #-0x18]
    // 0x926124: r0 = Await()
    //     0x926124: bl              #0x8c1bb8  ; AwaitStub
    // 0x926128: r0 = ReturnAsync()
    //     0x926128: b               #0x8c3a30  ; ReturnAsyncStub
    // 0x92612c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92612c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x926130: b               #0x925f40
    // 0x926134: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926134: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926138: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926138: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92613c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92613c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926140: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926140: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x926144, size: 0x228
    // 0x926144: EnterFrame
    //     0x926144: stp             fp, lr, [SP, #-0x10]!
    //     0x926148: mov             fp, SP
    // 0x92614c: AllocStack(0x38)
    //     0x92614c: sub             SP, SP, #0x38
    // 0x926150: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x926150: stur            NULL, [fp, #-8]
    //     0x926154: movz            x0, #0
    //     0x926158: add             x1, fp, w0, sxtw #2
    //     0x92615c: ldr             x1, [x1, #0x18]
    //     0x926160: add             x2, fp, w0, sxtw #2
    //     0x926164: ldr             x2, [x2, #0x10]
    //     0x926168: stur            x2, [fp, #-0x18]
    //     0x92616c: ldur            w3, [x1, #0x17]
    //     0x926170: add             x3, x3, HEAP, lsl #32
    //     0x926174: stur            x3, [fp, #-0x10]
    // 0x926178: CheckStackOverflow
    //     0x926178: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92617c: cmp             SP, x16
    //     0x926180: b.ls            #0x926354
    // 0x926184: InitAsync() -> Future<Null?>
    //     0x926184: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x926188: bl              #0x8c1de0  ; InitAsyncStub
    // 0x92618c: ldur            x0, [fp, #-0x18]
    // 0x926190: r2 = Null
    //     0x926190: mov             x2, NULL
    // 0x926194: r1 = Null
    //     0x926194: mov             x1, NULL
    // 0x926198: r4 = 59
    //     0x926198: movz            x4, #0x3b
    // 0x92619c: branchIfSmi(r0, 0x9261a8)
    //     0x92619c: tbz             w0, #0, #0x9261a8
    // 0x9261a0: r4 = LoadClassIdInstr(r0)
    //     0x9261a0: ldur            x4, [x0, #-1]
    //     0x9261a4: ubfx            x4, x4, #0xc, #0x14
    // 0x9261a8: sub             x4, x4, #0x59
    // 0x9261ac: cmp             x4, #2
    // 0x9261b0: b.ls            #0x9261c4
    // 0x9261b4: r8 = List<Object?>?
    //     0x9261b4: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x9261b8: r3 = Null
    //     0x9261b8: add             x3, PP, #0x31, lsl #12  ; [pp+0x318c0] Null
    //     0x9261bc: ldr             x3, [x3, #0x8c0]
    // 0x9261c0: r0 = List<Object?>?()
    //     0x9261c0: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x9261c4: ldur            x1, [fp, #-0x18]
    // 0x9261c8: cmp             w1, NULL
    // 0x9261cc: b.eq            #0x92635c
    // 0x9261d0: r0 = LoadClassIdInstr(r1)
    //     0x9261d0: ldur            x0, [x1, #-1]
    //     0x9261d4: ubfx            x0, x0, #0xc, #0x14
    // 0x9261d8: stp             xzr, x1, [SP]
    // 0x9261dc: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9261dc: movz            x17, #0x32a
    //     0x9261e0: movk            x17, #0x1, lsl #16
    //     0x9261e4: add             lr, x0, x17
    //     0x9261e8: ldr             lr, [x21, lr, lsl #3]
    //     0x9261ec: blr             lr
    // 0x9261f0: mov             x3, x0
    // 0x9261f4: r2 = Null
    //     0x9261f4: mov             x2, NULL
    // 0x9261f8: r1 = Null
    //     0x9261f8: mov             x1, NULL
    // 0x9261fc: stur            x3, [fp, #-0x20]
    // 0x926200: branchIfSmi(r0, 0x926228)
    //     0x926200: tbz             w0, #0, #0x926228
    // 0x926204: r4 = LoadClassIdInstr(r0)
    //     0x926204: ldur            x4, [x0, #-1]
    //     0x926208: ubfx            x4, x4, #0xc, #0x14
    // 0x92620c: sub             x4, x4, #0x3b
    // 0x926210: cmp             x4, #1
    // 0x926214: b.ls            #0x926228
    // 0x926218: r8 = int?
    //     0x926218: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x92621c: r3 = Null
    //     0x92621c: add             x3, PP, #0x31, lsl #12  ; [pp+0x318d0] Null
    //     0x926220: ldr             x3, [x3, #0x8d0]
    // 0x926224: r0 = int?()
    //     0x926224: bl              #0x959574  ; IsType_int?_Stub
    // 0x926228: ldur            x1, [fp, #-0x18]
    // 0x92622c: r0 = LoadClassIdInstr(r1)
    //     0x92622c: ldur            x0, [x1, #-1]
    //     0x926230: ubfx            x0, x0, #0xc, #0x14
    // 0x926234: r16 = 2
    //     0x926234: movz            x16, #0x2
    // 0x926238: stp             x16, x1, [SP]
    // 0x92623c: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x92623c: movz            x17, #0x32a
    //     0x926240: movk            x17, #0x1, lsl #16
    //     0x926244: add             lr, x0, x17
    //     0x926248: ldr             lr, [x21, lr, lsl #3]
    //     0x92624c: blr             lr
    // 0x926250: mov             x3, x0
    // 0x926254: r2 = Null
    //     0x926254: mov             x2, NULL
    // 0x926258: r1 = Null
    //     0x926258: mov             x1, NULL
    // 0x92625c: stur            x3, [fp, #-0x28]
    // 0x926260: branchIfSmi(r0, 0x926288)
    //     0x926260: tbz             w0, #0, #0x926288
    // 0x926264: r4 = LoadClassIdInstr(r0)
    //     0x926264: ldur            x4, [x0, #-1]
    //     0x926268: ubfx            x4, x4, #0xc, #0x14
    // 0x92626c: sub             x4, x4, #0x3b
    // 0x926270: cmp             x4, #1
    // 0x926274: b.ls            #0x926288
    // 0x926278: r8 = int?
    //     0x926278: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x92627c: r3 = Null
    //     0x92627c: add             x3, PP, #0x31, lsl #12  ; [pp+0x318e0] Null
    //     0x926280: ldr             x3, [x3, #0x8e0]
    // 0x926284: r0 = int?()
    //     0x926284: bl              #0x959574  ; IsType_int?_Stub
    // 0x926288: ldur            x0, [fp, #-0x18]
    // 0x92628c: r1 = LoadClassIdInstr(r0)
    //     0x92628c: ldur            x1, [x0, #-1]
    //     0x926290: ubfx            x1, x1, #0xc, #0x14
    // 0x926294: r16 = 4
    //     0x926294: movz            x16, #0x4
    // 0x926298: stp             x16, x0, [SP]
    // 0x92629c: mov             x0, x1
    // 0x9262a0: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9262a0: movz            x17, #0x32a
    //     0x9262a4: movk            x17, #0x1, lsl #16
    //     0x9262a8: add             lr, x0, x17
    //     0x9262ac: ldr             lr, [x21, lr, lsl #3]
    //     0x9262b0: blr             lr
    // 0x9262b4: mov             x3, x0
    // 0x9262b8: r2 = Null
    //     0x9262b8: mov             x2, NULL
    // 0x9262bc: r1 = Null
    //     0x9262bc: mov             x1, NULL
    // 0x9262c0: stur            x3, [fp, #-0x18]
    // 0x9262c4: branchIfSmi(r0, 0x9262ec)
    //     0x9262c4: tbz             w0, #0, #0x9262ec
    // 0x9262c8: r4 = LoadClassIdInstr(r0)
    //     0x9262c8: ldur            x4, [x0, #-1]
    //     0x9262cc: ubfx            x4, x4, #0xc, #0x14
    // 0x9262d0: sub             x4, x4, #0x3b
    // 0x9262d4: cmp             x4, #1
    // 0x9262d8: b.ls            #0x9262ec
    // 0x9262dc: r8 = int?
    //     0x9262dc: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9262e0: r3 = Null
    //     0x9262e0: add             x3, PP, #0x31, lsl #12  ; [pp+0x318f0] Null
    //     0x9262e4: ldr             x3, [x3, #0x8f0]
    // 0x9262e8: r0 = int?()
    //     0x9262e8: bl              #0x959574  ; IsType_int?_Stub
    // 0x9262ec: ldur            x0, [fp, #-0x10]
    // 0x9262f0: LoadField: r1 = r0->field_f
    //     0x9262f0: ldur            w1, [x0, #0xf]
    // 0x9262f4: DecompressPointer r1
    //     0x9262f4: add             x1, x1, HEAP, lsl #32
    // 0x9262f8: ldur            x0, [fp, #-0x20]
    // 0x9262fc: cmp             w0, NULL
    // 0x926300: b.eq            #0x926360
    // 0x926304: ldur            x2, [fp, #-0x28]
    // 0x926308: cmp             w2, NULL
    // 0x92630c: b.eq            #0x926364
    // 0x926310: ldur            x3, [fp, #-0x18]
    // 0x926314: cmp             w3, NULL
    // 0x926318: b.eq            #0x926368
    // 0x92631c: r4 = LoadInt32Instr(r0)
    //     0x92631c: sbfx            x4, x0, #1, #0x1f
    //     0x926320: tbz             w0, #0, #0x926328
    //     0x926324: ldur            x4, [x0, #7]
    // 0x926328: r0 = LoadInt32Instr(r2)
    //     0x926328: sbfx            x0, x2, #1, #0x1f
    //     0x92632c: tbz             w2, #0, #0x926334
    //     0x926330: ldur            x0, [x2, #7]
    // 0x926334: r5 = LoadInt32Instr(r3)
    //     0x926334: sbfx            x5, x3, #1, #0x1f
    //     0x926338: tbz             w3, #0, #0x926340
    //     0x92633c: ldur            x5, [x3, #7]
    // 0x926340: mov             x2, x4
    // 0x926344: mov             x3, x0
    // 0x926348: r0 = call 0x64f1e8
    //     0x926348: bl              #0x64f1e8
    // 0x92634c: r0 = Null
    //     0x92634c: mov             x0, NULL
    // 0x926350: r0 = ReturnAsyncNotFuture()
    //     0x926350: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x926354: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x926354: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x926358: b               #0x926184
    // 0x92635c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92635c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926360: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926360: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926364: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926364: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926368: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926368: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 349, size: 0xc, field offset: 0x8
abstract class Ffb extends Object {
}

// class id: 351, size: 0x8, field offset: 0x8
abstract class Efb extends Object {

  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x9279e8, size: 0x37c
    // 0x9279e8: EnterFrame
    //     0x9279e8: stp             fp, lr, [SP, #-0x10]!
    //     0x9279ec: mov             fp, SP
    // 0x9279f0: AllocStack(0x50)
    //     0x9279f0: sub             SP, SP, #0x50
    // 0x9279f4: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x9279f4: stur            NULL, [fp, #-8]
    //     0x9279f8: movz            x0, #0
    //     0x9279fc: add             x1, fp, w0, sxtw #2
    //     0x927a00: ldr             x1, [x1, #0x18]
    //     0x927a04: add             x2, fp, w0, sxtw #2
    //     0x927a08: ldr             x2, [x2, #0x10]
    //     0x927a0c: stur            x2, [fp, #-0x18]
    //     0x927a10: ldur            w3, [x1, #0x17]
    //     0x927a14: add             x3, x3, HEAP, lsl #32
    //     0x927a18: stur            x3, [fp, #-0x10]
    // 0x927a1c: CheckStackOverflow
    //     0x927a1c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x927a20: cmp             SP, x16
    //     0x927a24: b.ls            #0x927d40
    // 0x927a28: InitAsync() -> Future<Null?>
    //     0x927a28: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x927a2c: bl              #0x8c1de0  ; InitAsyncStub
    // 0x927a30: ldur            x0, [fp, #-0x18]
    // 0x927a34: r2 = Null
    //     0x927a34: mov             x2, NULL
    // 0x927a38: r1 = Null
    //     0x927a38: mov             x1, NULL
    // 0x927a3c: r4 = 59
    //     0x927a3c: movz            x4, #0x3b
    // 0x927a40: branchIfSmi(r0, 0x927a4c)
    //     0x927a40: tbz             w0, #0, #0x927a4c
    // 0x927a44: r4 = LoadClassIdInstr(r0)
    //     0x927a44: ldur            x4, [x0, #-1]
    //     0x927a48: ubfx            x4, x4, #0xc, #0x14
    // 0x927a4c: sub             x4, x4, #0x59
    // 0x927a50: cmp             x4, #2
    // 0x927a54: b.ls            #0x927a68
    // 0x927a58: r8 = List<Object?>?
    //     0x927a58: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x927a5c: r3 = Null
    //     0x927a5c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31dd8] Null
    //     0x927a60: ldr             x3, [x3, #0xdd8]
    // 0x927a64: r0 = List<Object?>?()
    //     0x927a64: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x927a68: ldur            x1, [fp, #-0x18]
    // 0x927a6c: cmp             w1, NULL
    // 0x927a70: b.eq            #0x927d48
    // 0x927a74: r0 = LoadClassIdInstr(r1)
    //     0x927a74: ldur            x0, [x1, #-1]
    //     0x927a78: ubfx            x0, x0, #0xc, #0x14
    // 0x927a7c: stp             xzr, x1, [SP]
    // 0x927a80: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927a80: movz            x17, #0x32a
    //     0x927a84: movk            x17, #0x1, lsl #16
    //     0x927a88: add             lr, x0, x17
    //     0x927a8c: ldr             lr, [x21, lr, lsl #3]
    //     0x927a90: blr             lr
    // 0x927a94: mov             x3, x0
    // 0x927a98: r2 = Null
    //     0x927a98: mov             x2, NULL
    // 0x927a9c: r1 = Null
    //     0x927a9c: mov             x1, NULL
    // 0x927aa0: stur            x3, [fp, #-0x20]
    // 0x927aa4: branchIfSmi(r0, 0x927acc)
    //     0x927aa4: tbz             w0, #0, #0x927acc
    // 0x927aa8: r4 = LoadClassIdInstr(r0)
    //     0x927aa8: ldur            x4, [x0, #-1]
    //     0x927aac: ubfx            x4, x4, #0xc, #0x14
    // 0x927ab0: sub             x4, x4, #0x3b
    // 0x927ab4: cmp             x4, #1
    // 0x927ab8: b.ls            #0x927acc
    // 0x927abc: r8 = int?
    //     0x927abc: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x927ac0: r3 = Null
    //     0x927ac0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31de8] Null
    //     0x927ac4: ldr             x3, [x3, #0xde8]
    // 0x927ac8: r0 = int?()
    //     0x927ac8: bl              #0x959574  ; IsType_int?_Stub
    // 0x927acc: ldur            x1, [fp, #-0x18]
    // 0x927ad0: r0 = LoadClassIdInstr(r1)
    //     0x927ad0: ldur            x0, [x1, #-1]
    //     0x927ad4: ubfx            x0, x0, #0xc, #0x14
    // 0x927ad8: r16 = 2
    //     0x927ad8: movz            x16, #0x2
    // 0x927adc: stp             x16, x1, [SP]
    // 0x927ae0: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927ae0: movz            x17, #0x32a
    //     0x927ae4: movk            x17, #0x1, lsl #16
    //     0x927ae8: add             lr, x0, x17
    //     0x927aec: ldr             lr, [x21, lr, lsl #3]
    //     0x927af0: blr             lr
    // 0x927af4: mov             x3, x0
    // 0x927af8: r2 = Null
    //     0x927af8: mov             x2, NULL
    // 0x927afc: r1 = Null
    //     0x927afc: mov             x1, NULL
    // 0x927b00: stur            x3, [fp, #-0x28]
    // 0x927b04: r4 = 59
    //     0x927b04: movz            x4, #0x3b
    // 0x927b08: branchIfSmi(r0, 0x927b14)
    //     0x927b08: tbz             w0, #0, #0x927b14
    // 0x927b0c: r4 = LoadClassIdInstr(r0)
    //     0x927b0c: ldur            x4, [x0, #-1]
    //     0x927b10: ubfx            x4, x4, #0xc, #0x14
    // 0x927b14: sub             x4, x4, #0x5d
    // 0x927b18: cmp             x4, #1
    // 0x927b1c: b.ls            #0x927b30
    // 0x927b20: r8 = String?
    //     0x927b20: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x927b24: r3 = Null
    //     0x927b24: add             x3, PP, #0x31, lsl #12  ; [pp+0x31df8] Null
    //     0x927b28: ldr             x3, [x3, #0xdf8]
    // 0x927b2c: r0 = String?()
    //     0x927b2c: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x927b30: ldur            x1, [fp, #-0x18]
    // 0x927b34: r0 = LoadClassIdInstr(r1)
    //     0x927b34: ldur            x0, [x1, #-1]
    //     0x927b38: ubfx            x0, x0, #0xc, #0x14
    // 0x927b3c: r16 = 4
    //     0x927b3c: movz            x16, #0x4
    // 0x927b40: stp             x16, x1, [SP]
    // 0x927b44: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927b44: movz            x17, #0x32a
    //     0x927b48: movk            x17, #0x1, lsl #16
    //     0x927b4c: add             lr, x0, x17
    //     0x927b50: ldr             lr, [x21, lr, lsl #3]
    //     0x927b54: blr             lr
    // 0x927b58: mov             x3, x0
    // 0x927b5c: r2 = Null
    //     0x927b5c: mov             x2, NULL
    // 0x927b60: r1 = Null
    //     0x927b60: mov             x1, NULL
    // 0x927b64: stur            x3, [fp, #-0x30]
    // 0x927b68: r4 = 59
    //     0x927b68: movz            x4, #0x3b
    // 0x927b6c: branchIfSmi(r0, 0x927b78)
    //     0x927b6c: tbz             w0, #0, #0x927b78
    // 0x927b70: r4 = LoadClassIdInstr(r0)
    //     0x927b70: ldur            x4, [x0, #-1]
    //     0x927b74: ubfx            x4, x4, #0xc, #0x14
    // 0x927b78: sub             x4, x4, #0x5d
    // 0x927b7c: cmp             x4, #1
    // 0x927b80: b.ls            #0x927b94
    // 0x927b84: r8 = String?
    //     0x927b84: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x927b88: r3 = Null
    //     0x927b88: add             x3, PP, #0x31, lsl #12  ; [pp+0x31e08] Null
    //     0x927b8c: ldr             x3, [x3, #0xe08]
    // 0x927b90: r0 = String?()
    //     0x927b90: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x927b94: ldur            x1, [fp, #-0x18]
    // 0x927b98: r0 = LoadClassIdInstr(r1)
    //     0x927b98: ldur            x0, [x1, #-1]
    //     0x927b9c: ubfx            x0, x0, #0xc, #0x14
    // 0x927ba0: r16 = 6
    //     0x927ba0: movz            x16, #0x6
    // 0x927ba4: stp             x16, x1, [SP]
    // 0x927ba8: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927ba8: movz            x17, #0x32a
    //     0x927bac: movk            x17, #0x1, lsl #16
    //     0x927bb0: add             lr, x0, x17
    //     0x927bb4: ldr             lr, [x21, lr, lsl #3]
    //     0x927bb8: blr             lr
    // 0x927bbc: mov             x3, x0
    // 0x927bc0: r2 = Null
    //     0x927bc0: mov             x2, NULL
    // 0x927bc4: r1 = Null
    //     0x927bc4: mov             x1, NULL
    // 0x927bc8: stur            x3, [fp, #-0x38]
    // 0x927bcc: r4 = 59
    //     0x927bcc: movz            x4, #0x3b
    // 0x927bd0: branchIfSmi(r0, 0x927bdc)
    //     0x927bd0: tbz             w0, #0, #0x927bdc
    // 0x927bd4: r4 = LoadClassIdInstr(r0)
    //     0x927bd4: ldur            x4, [x0, #-1]
    //     0x927bd8: ubfx            x4, x4, #0xc, #0x14
    // 0x927bdc: sub             x4, x4, #0x5d
    // 0x927be0: cmp             x4, #1
    // 0x927be4: b.ls            #0x927bf8
    // 0x927be8: r8 = String?
    //     0x927be8: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x927bec: r3 = Null
    //     0x927bec: add             x3, PP, #0x31, lsl #12  ; [pp+0x31e18] Null
    //     0x927bf0: ldr             x3, [x3, #0xe18]
    // 0x927bf4: r0 = String?()
    //     0x927bf4: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x927bf8: ldur            x1, [fp, #-0x18]
    // 0x927bfc: r0 = LoadClassIdInstr(r1)
    //     0x927bfc: ldur            x0, [x1, #-1]
    //     0x927c00: ubfx            x0, x0, #0xc, #0x14
    // 0x927c04: r16 = 8
    //     0x927c04: movz            x16, #0x8
    // 0x927c08: stp             x16, x1, [SP]
    // 0x927c0c: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927c0c: movz            x17, #0x32a
    //     0x927c10: movk            x17, #0x1, lsl #16
    //     0x927c14: add             lr, x0, x17
    //     0x927c18: ldr             lr, [x21, lr, lsl #3]
    //     0x927c1c: blr             lr
    // 0x927c20: mov             x3, x0
    // 0x927c24: r2 = Null
    //     0x927c24: mov             x2, NULL
    // 0x927c28: r1 = Null
    //     0x927c28: mov             x1, NULL
    // 0x927c2c: stur            x3, [fp, #-0x40]
    // 0x927c30: r4 = 59
    //     0x927c30: movz            x4, #0x3b
    // 0x927c34: branchIfSmi(r0, 0x927c40)
    //     0x927c34: tbz             w0, #0, #0x927c40
    // 0x927c38: r4 = LoadClassIdInstr(r0)
    //     0x927c38: ldur            x4, [x0, #-1]
    //     0x927c3c: ubfx            x4, x4, #0xc, #0x14
    // 0x927c40: sub             x4, x4, #0x5d
    // 0x927c44: cmp             x4, #1
    // 0x927c48: b.ls            #0x927c5c
    // 0x927c4c: r8 = String?
    //     0x927c4c: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x927c50: r3 = Null
    //     0x927c50: add             x3, PP, #0x31, lsl #12  ; [pp+0x31e28] Null
    //     0x927c54: ldr             x3, [x3, #0xe28]
    // 0x927c58: r0 = String?()
    //     0x927c58: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x927c5c: ldur            x0, [fp, #-0x18]
    // 0x927c60: r1 = LoadClassIdInstr(r0)
    //     0x927c60: ldur            x1, [x0, #-1]
    //     0x927c64: ubfx            x1, x1, #0xc, #0x14
    // 0x927c68: r16 = 10
    //     0x927c68: movz            x16, #0xa
    // 0x927c6c: stp             x16, x0, [SP]
    // 0x927c70: mov             x0, x1
    // 0x927c74: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927c74: movz            x17, #0x32a
    //     0x927c78: movk            x17, #0x1, lsl #16
    //     0x927c7c: add             lr, x0, x17
    //     0x927c80: ldr             lr, [x21, lr, lsl #3]
    //     0x927c84: blr             lr
    // 0x927c88: mov             x3, x0
    // 0x927c8c: r2 = Null
    //     0x927c8c: mov             x2, NULL
    // 0x927c90: r1 = Null
    //     0x927c90: mov             x1, NULL
    // 0x927c94: stur            x3, [fp, #-0x18]
    // 0x927c98: branchIfSmi(r0, 0x927cc0)
    //     0x927c98: tbz             w0, #0, #0x927cc0
    // 0x927c9c: r4 = LoadClassIdInstr(r0)
    //     0x927c9c: ldur            x4, [x0, #-1]
    //     0x927ca0: ubfx            x4, x4, #0xc, #0x14
    // 0x927ca4: sub             x4, x4, #0x3b
    // 0x927ca8: cmp             x4, #1
    // 0x927cac: b.ls            #0x927cc0
    // 0x927cb0: r8 = int?
    //     0x927cb0: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x927cb4: r3 = Null
    //     0x927cb4: add             x3, PP, #0x31, lsl #12  ; [pp+0x31e38] Null
    //     0x927cb8: ldr             x3, [x3, #0xe38]
    // 0x927cbc: r0 = int?()
    //     0x927cbc: bl              #0x959574  ; IsType_int?_Stub
    // 0x927cc0: ldur            x0, [fp, #-0x10]
    // 0x927cc4: LoadField: r1 = r0->field_f
    //     0x927cc4: ldur            w1, [x0, #0xf]
    // 0x927cc8: DecompressPointer r1
    //     0x927cc8: add             x1, x1, HEAP, lsl #32
    // 0x927ccc: ldur            x0, [fp, #-0x20]
    // 0x927cd0: cmp             w0, NULL
    // 0x927cd4: b.eq            #0x927d4c
    // 0x927cd8: ldur            x3, [fp, #-0x28]
    // 0x927cdc: cmp             w3, NULL
    // 0x927ce0: b.eq            #0x927d50
    // 0x927ce4: ldur            x5, [fp, #-0x30]
    // 0x927ce8: cmp             w5, NULL
    // 0x927cec: b.eq            #0x927d54
    // 0x927cf0: ldur            x6, [fp, #-0x38]
    // 0x927cf4: cmp             w6, NULL
    // 0x927cf8: b.eq            #0x927d58
    // 0x927cfc: ldur            x7, [fp, #-0x40]
    // 0x927d00: cmp             w7, NULL
    // 0x927d04: b.eq            #0x927d5c
    // 0x927d08: ldur            x2, [fp, #-0x18]
    // 0x927d0c: cmp             w2, NULL
    // 0x927d10: b.eq            #0x927d60
    // 0x927d14: r4 = LoadInt32Instr(r0)
    //     0x927d14: sbfx            x4, x0, #1, #0x1f
    //     0x927d18: tbz             w0, #0, #0x927d20
    //     0x927d1c: ldur            x4, [x0, #7]
    // 0x927d20: r0 = LoadInt32Instr(r2)
    //     0x927d20: sbfx            x0, x2, #1, #0x1f
    //     0x927d24: tbz             w2, #0, #0x927d2c
    //     0x927d28: ldur            x0, [x2, #7]
    // 0x927d2c: str             x0, [SP]
    // 0x927d30: mov             x2, x4
    // 0x927d34: r0 = call 0x65033c
    //     0x927d34: bl              #0x65033c
    // 0x927d38: r0 = Null
    //     0x927d38: mov             x0, NULL
    // 0x927d3c: r0 = ReturnAsyncNotFuture()
    //     0x927d3c: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x927d40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x927d40: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x927d44: b               #0x927a28
    // 0x927d48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927d48: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x927d4c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927d4c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x927d50: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927d50: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x927d54: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927d54: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x927d58: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927d58: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x927d5c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927d5c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x927d60: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927d60: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 353, size: 0xc, field offset: 0x8
abstract class Dfb extends Object {
}

// class id: 355, size: 0x8, field offset: 0x8
abstract class Cfb extends Object {

  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x92636c, size: 0x310
    // 0x92636c: EnterFrame
    //     0x92636c: stp             fp, lr, [SP, #-0x10]!
    //     0x926370: mov             fp, SP
    // 0x926374: AllocStack(0x48)
    //     0x926374: sub             SP, SP, #0x48
    // 0x926378: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x926378: stur            NULL, [fp, #-8]
    //     0x92637c: movz            x0, #0
    //     0x926380: add             x1, fp, w0, sxtw #2
    //     0x926384: ldr             x1, [x1, #0x18]
    //     0x926388: add             x2, fp, w0, sxtw #2
    //     0x92638c: ldr             x2, [x2, #0x10]
    //     0x926390: stur            x2, [fp, #-0x18]
    //     0x926394: ldur            w3, [x1, #0x17]
    //     0x926398: add             x3, x3, HEAP, lsl #32
    //     0x92639c: stur            x3, [fp, #-0x10]
    // 0x9263a0: CheckStackOverflow
    //     0x9263a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9263a4: cmp             SP, x16
    //     0x9263a8: b.ls            #0x92665c
    // 0x9263ac: InitAsync() -> Future<Null?>
    //     0x9263ac: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x9263b0: bl              #0x8c1de0  ; InitAsyncStub
    // 0x9263b4: ldur            x0, [fp, #-0x18]
    // 0x9263b8: r2 = Null
    //     0x9263b8: mov             x2, NULL
    // 0x9263bc: r1 = Null
    //     0x9263bc: mov             x1, NULL
    // 0x9263c0: r4 = 59
    //     0x9263c0: movz            x4, #0x3b
    // 0x9263c4: branchIfSmi(r0, 0x9263d0)
    //     0x9263c4: tbz             w0, #0, #0x9263d0
    // 0x9263c8: r4 = LoadClassIdInstr(r0)
    //     0x9263c8: ldur            x4, [x0, #-1]
    //     0x9263cc: ubfx            x4, x4, #0xc, #0x14
    // 0x9263d0: sub             x4, x4, #0x59
    // 0x9263d4: cmp             x4, #2
    // 0x9263d8: b.ls            #0x9263ec
    // 0x9263dc: r8 = List<Object?>?
    //     0x9263dc: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x9263e0: r3 = Null
    //     0x9263e0: add             x3, PP, #0x31, lsl #12  ; [pp+0x319c0] Null
    //     0x9263e4: ldr             x3, [x3, #0x9c0]
    // 0x9263e8: r0 = List<Object?>?()
    //     0x9263e8: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x9263ec: ldur            x1, [fp, #-0x18]
    // 0x9263f0: cmp             w1, NULL
    // 0x9263f4: b.eq            #0x926664
    // 0x9263f8: r0 = LoadClassIdInstr(r1)
    //     0x9263f8: ldur            x0, [x1, #-1]
    //     0x9263fc: ubfx            x0, x0, #0xc, #0x14
    // 0x926400: stp             xzr, x1, [SP]
    // 0x926404: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926404: movz            x17, #0x32a
    //     0x926408: movk            x17, #0x1, lsl #16
    //     0x92640c: add             lr, x0, x17
    //     0x926410: ldr             lr, [x21, lr, lsl #3]
    //     0x926414: blr             lr
    // 0x926418: mov             x3, x0
    // 0x92641c: r2 = Null
    //     0x92641c: mov             x2, NULL
    // 0x926420: r1 = Null
    //     0x926420: mov             x1, NULL
    // 0x926424: stur            x3, [fp, #-0x20]
    // 0x926428: branchIfSmi(r0, 0x926450)
    //     0x926428: tbz             w0, #0, #0x926450
    // 0x92642c: r4 = LoadClassIdInstr(r0)
    //     0x92642c: ldur            x4, [x0, #-1]
    //     0x926430: ubfx            x4, x4, #0xc, #0x14
    // 0x926434: sub             x4, x4, #0x3b
    // 0x926438: cmp             x4, #1
    // 0x92643c: b.ls            #0x926450
    // 0x926440: r8 = int?
    //     0x926440: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x926444: r3 = Null
    //     0x926444: add             x3, PP, #0x31, lsl #12  ; [pp+0x319d0] Null
    //     0x926448: ldr             x3, [x3, #0x9d0]
    // 0x92644c: r0 = int?()
    //     0x92644c: bl              #0x959574  ; IsType_int?_Stub
    // 0x926450: ldur            x1, [fp, #-0x18]
    // 0x926454: r0 = LoadClassIdInstr(r1)
    //     0x926454: ldur            x0, [x1, #-1]
    //     0x926458: ubfx            x0, x0, #0xc, #0x14
    // 0x92645c: r16 = 2
    //     0x92645c: movz            x16, #0x2
    // 0x926460: stp             x16, x1, [SP]
    // 0x926464: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926464: movz            x17, #0x32a
    //     0x926468: movk            x17, #0x1, lsl #16
    //     0x92646c: add             lr, x0, x17
    //     0x926470: ldr             lr, [x21, lr, lsl #3]
    //     0x926474: blr             lr
    // 0x926478: mov             x3, x0
    // 0x92647c: r2 = Null
    //     0x92647c: mov             x2, NULL
    // 0x926480: r1 = Null
    //     0x926480: mov             x1, NULL
    // 0x926484: stur            x3, [fp, #-0x28]
    // 0x926488: branchIfSmi(r0, 0x9264b0)
    //     0x926488: tbz             w0, #0, #0x9264b0
    // 0x92648c: r4 = LoadClassIdInstr(r0)
    //     0x92648c: ldur            x4, [x0, #-1]
    //     0x926490: ubfx            x4, x4, #0xc, #0x14
    // 0x926494: sub             x4, x4, #0x3b
    // 0x926498: cmp             x4, #1
    // 0x92649c: b.ls            #0x9264b0
    // 0x9264a0: r8 = int?
    //     0x9264a0: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9264a4: r3 = Null
    //     0x9264a4: add             x3, PP, #0x31, lsl #12  ; [pp+0x319e0] Null
    //     0x9264a8: ldr             x3, [x3, #0x9e0]
    // 0x9264ac: r0 = int?()
    //     0x9264ac: bl              #0x959574  ; IsType_int?_Stub
    // 0x9264b0: ldur            x1, [fp, #-0x18]
    // 0x9264b4: r0 = LoadClassIdInstr(r1)
    //     0x9264b4: ldur            x0, [x1, #-1]
    //     0x9264b8: ubfx            x0, x0, #0xc, #0x14
    // 0x9264bc: r16 = 4
    //     0x9264bc: movz            x16, #0x4
    // 0x9264c0: stp             x16, x1, [SP]
    // 0x9264c4: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9264c4: movz            x17, #0x32a
    //     0x9264c8: movk            x17, #0x1, lsl #16
    //     0x9264cc: add             lr, x0, x17
    //     0x9264d0: ldr             lr, [x21, lr, lsl #3]
    //     0x9264d4: blr             lr
    // 0x9264d8: mov             x3, x0
    // 0x9264dc: r2 = Null
    //     0x9264dc: mov             x2, NULL
    // 0x9264e0: r1 = Null
    //     0x9264e0: mov             x1, NULL
    // 0x9264e4: stur            x3, [fp, #-0x30]
    // 0x9264e8: branchIfSmi(r0, 0x926510)
    //     0x9264e8: tbz             w0, #0, #0x926510
    // 0x9264ec: r4 = LoadClassIdInstr(r0)
    //     0x9264ec: ldur            x4, [x0, #-1]
    //     0x9264f0: ubfx            x4, x4, #0xc, #0x14
    // 0x9264f4: sub             x4, x4, #0x3b
    // 0x9264f8: cmp             x4, #1
    // 0x9264fc: b.ls            #0x926510
    // 0x926500: r8 = int?
    //     0x926500: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x926504: r3 = Null
    //     0x926504: add             x3, PP, #0x31, lsl #12  ; [pp+0x319f0] Null
    //     0x926508: ldr             x3, [x3, #0x9f0]
    // 0x92650c: r0 = int?()
    //     0x92650c: bl              #0x959574  ; IsType_int?_Stub
    // 0x926510: ldur            x1, [fp, #-0x18]
    // 0x926514: r0 = LoadClassIdInstr(r1)
    //     0x926514: ldur            x0, [x1, #-1]
    //     0x926518: ubfx            x0, x0, #0xc, #0x14
    // 0x92651c: r16 = 6
    //     0x92651c: movz            x16, #0x6
    // 0x926520: stp             x16, x1, [SP]
    // 0x926524: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926524: movz            x17, #0x32a
    //     0x926528: movk            x17, #0x1, lsl #16
    //     0x92652c: add             lr, x0, x17
    //     0x926530: ldr             lr, [x21, lr, lsl #3]
    //     0x926534: blr             lr
    // 0x926538: mov             x3, x0
    // 0x92653c: r2 = Null
    //     0x92653c: mov             x2, NULL
    // 0x926540: r1 = Null
    //     0x926540: mov             x1, NULL
    // 0x926544: stur            x3, [fp, #-0x38]
    // 0x926548: r4 = 59
    //     0x926548: movz            x4, #0x3b
    // 0x92654c: branchIfSmi(r0, 0x926558)
    //     0x92654c: tbz             w0, #0, #0x926558
    // 0x926550: r4 = LoadClassIdInstr(r0)
    //     0x926550: ldur            x4, [x0, #-1]
    //     0x926554: ubfx            x4, x4, #0xc, #0x14
    // 0x926558: sub             x4, x4, #0x5d
    // 0x92655c: cmp             x4, #1
    // 0x926560: b.ls            #0x926574
    // 0x926564: r8 = String?
    //     0x926564: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x926568: r3 = Null
    //     0x926568: add             x3, PP, #0x31, lsl #12  ; [pp+0x31a00] Null
    //     0x92656c: ldr             x3, [x3, #0xa00]
    // 0x926570: r0 = String?()
    //     0x926570: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x926574: ldur            x0, [fp, #-0x18]
    // 0x926578: r1 = LoadClassIdInstr(r0)
    //     0x926578: ldur            x1, [x0, #-1]
    //     0x92657c: ubfx            x1, x1, #0xc, #0x14
    // 0x926580: r16 = 8
    //     0x926580: movz            x16, #0x8
    // 0x926584: stp             x16, x0, [SP]
    // 0x926588: mov             x0, x1
    // 0x92658c: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x92658c: movz            x17, #0x32a
    //     0x926590: movk            x17, #0x1, lsl #16
    //     0x926594: add             lr, x0, x17
    //     0x926598: ldr             lr, [x21, lr, lsl #3]
    //     0x92659c: blr             lr
    // 0x9265a0: mov             x3, x0
    // 0x9265a4: r2 = Null
    //     0x9265a4: mov             x2, NULL
    // 0x9265a8: r1 = Null
    //     0x9265a8: mov             x1, NULL
    // 0x9265ac: stur            x3, [fp, #-0x18]
    // 0x9265b0: r4 = 59
    //     0x9265b0: movz            x4, #0x3b
    // 0x9265b4: branchIfSmi(r0, 0x9265c0)
    //     0x9265b4: tbz             w0, #0, #0x9265c0
    // 0x9265b8: r4 = LoadClassIdInstr(r0)
    //     0x9265b8: ldur            x4, [x0, #-1]
    //     0x9265bc: ubfx            x4, x4, #0xc, #0x14
    // 0x9265c0: sub             x4, x4, #0x5d
    // 0x9265c4: cmp             x4, #1
    // 0x9265c8: b.ls            #0x9265dc
    // 0x9265cc: r8 = String?
    //     0x9265cc: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x9265d0: r3 = Null
    //     0x9265d0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31a10] Null
    //     0x9265d4: ldr             x3, [x3, #0xa10]
    // 0x9265d8: r0 = String?()
    //     0x9265d8: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x9265dc: ldur            x0, [fp, #-0x10]
    // 0x9265e0: LoadField: r1 = r0->field_f
    //     0x9265e0: ldur            w1, [x0, #0xf]
    // 0x9265e4: DecompressPointer r1
    //     0x9265e4: add             x1, x1, HEAP, lsl #32
    // 0x9265e8: ldur            x0, [fp, #-0x20]
    // 0x9265ec: cmp             w0, NULL
    // 0x9265f0: b.eq            #0x926668
    // 0x9265f4: ldur            x2, [fp, #-0x28]
    // 0x9265f8: cmp             w2, NULL
    // 0x9265fc: b.eq            #0x92666c
    // 0x926600: ldur            x3, [fp, #-0x30]
    // 0x926604: cmp             w3, NULL
    // 0x926608: b.eq            #0x926670
    // 0x92660c: ldur            x6, [fp, #-0x38]
    // 0x926610: cmp             w6, NULL
    // 0x926614: b.eq            #0x926674
    // 0x926618: ldur            x7, [fp, #-0x18]
    // 0x92661c: cmp             w7, NULL
    // 0x926620: b.eq            #0x926678
    // 0x926624: r4 = LoadInt32Instr(r0)
    //     0x926624: sbfx            x4, x0, #1, #0x1f
    //     0x926628: tbz             w0, #0, #0x926630
    //     0x92662c: ldur            x4, [x0, #7]
    // 0x926630: r0 = LoadInt32Instr(r2)
    //     0x926630: sbfx            x0, x2, #1, #0x1f
    //     0x926634: tbz             w2, #0, #0x92663c
    //     0x926638: ldur            x0, [x2, #7]
    // 0x92663c: r5 = LoadInt32Instr(r3)
    //     0x92663c: sbfx            x5, x3, #1, #0x1f
    //     0x926640: tbz             w3, #0, #0x926648
    //     0x926644: ldur            x5, [x3, #7]
    // 0x926648: mov             x2, x4
    // 0x92664c: mov             x3, x0
    // 0x926650: r0 = call 0x64f5dc
    //     0x926650: bl              #0x64f5dc
    // 0x926654: r0 = Null
    //     0x926654: mov             x0, NULL
    // 0x926658: r0 = ReturnAsyncNotFuture()
    //     0x926658: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x92665c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x92665c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x926660: b               #0x9263ac
    // 0x926664: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926664: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926668: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926668: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92666c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92666c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926670: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926670: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926674: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926674: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926678: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926678: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x92667c, size: 0x290
    // 0x92667c: EnterFrame
    //     0x92667c: stp             fp, lr, [SP, #-0x10]!
    //     0x926680: mov             fp, SP
    // 0x926684: AllocStack(0x40)
    //     0x926684: sub             SP, SP, #0x40
    // 0x926688: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x926688: stur            NULL, [fp, #-8]
    //     0x92668c: movz            x0, #0
    //     0x926690: add             x1, fp, w0, sxtw #2
    //     0x926694: ldr             x1, [x1, #0x18]
    //     0x926698: add             x2, fp, w0, sxtw #2
    //     0x92669c: ldr             x2, [x2, #0x10]
    //     0x9266a0: stur            x2, [fp, #-0x18]
    //     0x9266a4: ldur            w3, [x1, #0x17]
    //     0x9266a8: add             x3, x3, HEAP, lsl #32
    //     0x9266ac: stur            x3, [fp, #-0x10]
    // 0x9266b0: CheckStackOverflow
    //     0x9266b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9266b4: cmp             SP, x16
    //     0x9266b8: b.ls            #0x9268f0
    // 0x9266bc: InitAsync() -> Future<Null?>
    //     0x9266bc: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x9266c0: bl              #0x8c1de0  ; InitAsyncStub
    // 0x9266c4: ldur            x0, [fp, #-0x18]
    // 0x9266c8: r2 = Null
    //     0x9266c8: mov             x2, NULL
    // 0x9266cc: r1 = Null
    //     0x9266cc: mov             x1, NULL
    // 0x9266d0: r4 = 59
    //     0x9266d0: movz            x4, #0x3b
    // 0x9266d4: branchIfSmi(r0, 0x9266e0)
    //     0x9266d4: tbz             w0, #0, #0x9266e0
    // 0x9266d8: r4 = LoadClassIdInstr(r0)
    //     0x9266d8: ldur            x4, [x0, #-1]
    //     0x9266dc: ubfx            x4, x4, #0xc, #0x14
    // 0x9266e0: sub             x4, x4, #0x59
    // 0x9266e4: cmp             x4, #2
    // 0x9266e8: b.ls            #0x9266fc
    // 0x9266ec: r8 = List<Object?>?
    //     0x9266ec: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x9266f0: r3 = Null
    //     0x9266f0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31a60] Null
    //     0x9266f4: ldr             x3, [x3, #0xa60]
    // 0x9266f8: r0 = List<Object?>?()
    //     0x9266f8: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x9266fc: ldur            x1, [fp, #-0x18]
    // 0x926700: cmp             w1, NULL
    // 0x926704: b.eq            #0x9268f8
    // 0x926708: r0 = LoadClassIdInstr(r1)
    //     0x926708: ldur            x0, [x1, #-1]
    //     0x92670c: ubfx            x0, x0, #0xc, #0x14
    // 0x926710: stp             xzr, x1, [SP]
    // 0x926714: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926714: movz            x17, #0x32a
    //     0x926718: movk            x17, #0x1, lsl #16
    //     0x92671c: add             lr, x0, x17
    //     0x926720: ldr             lr, [x21, lr, lsl #3]
    //     0x926724: blr             lr
    // 0x926728: mov             x3, x0
    // 0x92672c: r2 = Null
    //     0x92672c: mov             x2, NULL
    // 0x926730: r1 = Null
    //     0x926730: mov             x1, NULL
    // 0x926734: stur            x3, [fp, #-0x20]
    // 0x926738: branchIfSmi(r0, 0x926760)
    //     0x926738: tbz             w0, #0, #0x926760
    // 0x92673c: r4 = LoadClassIdInstr(r0)
    //     0x92673c: ldur            x4, [x0, #-1]
    //     0x926740: ubfx            x4, x4, #0xc, #0x14
    // 0x926744: sub             x4, x4, #0x3b
    // 0x926748: cmp             x4, #1
    // 0x92674c: b.ls            #0x926760
    // 0x926750: r8 = int?
    //     0x926750: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x926754: r3 = Null
    //     0x926754: add             x3, PP, #0x31, lsl #12  ; [pp+0x31a70] Null
    //     0x926758: ldr             x3, [x3, #0xa70]
    // 0x92675c: r0 = int?()
    //     0x92675c: bl              #0x959574  ; IsType_int?_Stub
    // 0x926760: ldur            x1, [fp, #-0x18]
    // 0x926764: r0 = LoadClassIdInstr(r1)
    //     0x926764: ldur            x0, [x1, #-1]
    //     0x926768: ubfx            x0, x0, #0xc, #0x14
    // 0x92676c: r16 = 2
    //     0x92676c: movz            x16, #0x2
    // 0x926770: stp             x16, x1, [SP]
    // 0x926774: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926774: movz            x17, #0x32a
    //     0x926778: movk            x17, #0x1, lsl #16
    //     0x92677c: add             lr, x0, x17
    //     0x926780: ldr             lr, [x21, lr, lsl #3]
    //     0x926784: blr             lr
    // 0x926788: mov             x3, x0
    // 0x92678c: r2 = Null
    //     0x92678c: mov             x2, NULL
    // 0x926790: r1 = Null
    //     0x926790: mov             x1, NULL
    // 0x926794: stur            x3, [fp, #-0x28]
    // 0x926798: branchIfSmi(r0, 0x9267c0)
    //     0x926798: tbz             w0, #0, #0x9267c0
    // 0x92679c: r4 = LoadClassIdInstr(r0)
    //     0x92679c: ldur            x4, [x0, #-1]
    //     0x9267a0: ubfx            x4, x4, #0xc, #0x14
    // 0x9267a4: sub             x4, x4, #0x3b
    // 0x9267a8: cmp             x4, #1
    // 0x9267ac: b.ls            #0x9267c0
    // 0x9267b0: r8 = int?
    //     0x9267b0: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9267b4: r3 = Null
    //     0x9267b4: add             x3, PP, #0x31, lsl #12  ; [pp+0x31a80] Null
    //     0x9267b8: ldr             x3, [x3, #0xa80]
    // 0x9267bc: r0 = int?()
    //     0x9267bc: bl              #0x959574  ; IsType_int?_Stub
    // 0x9267c0: ldur            x1, [fp, #-0x18]
    // 0x9267c4: r0 = LoadClassIdInstr(r1)
    //     0x9267c4: ldur            x0, [x1, #-1]
    //     0x9267c8: ubfx            x0, x0, #0xc, #0x14
    // 0x9267cc: r16 = 4
    //     0x9267cc: movz            x16, #0x4
    // 0x9267d0: stp             x16, x1, [SP]
    // 0x9267d4: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9267d4: movz            x17, #0x32a
    //     0x9267d8: movk            x17, #0x1, lsl #16
    //     0x9267dc: add             lr, x0, x17
    //     0x9267e0: ldr             lr, [x21, lr, lsl #3]
    //     0x9267e4: blr             lr
    // 0x9267e8: mov             x3, x0
    // 0x9267ec: r2 = Null
    //     0x9267ec: mov             x2, NULL
    // 0x9267f0: r1 = Null
    //     0x9267f0: mov             x1, NULL
    // 0x9267f4: stur            x3, [fp, #-0x30]
    // 0x9267f8: r4 = 59
    //     0x9267f8: movz            x4, #0x3b
    // 0x9267fc: branchIfSmi(r0, 0x926808)
    //     0x9267fc: tbz             w0, #0, #0x926808
    // 0x926800: r4 = LoadClassIdInstr(r0)
    //     0x926800: ldur            x4, [x0, #-1]
    //     0x926804: ubfx            x4, x4, #0xc, #0x14
    // 0x926808: sub             x4, x4, #0x5d
    // 0x92680c: cmp             x4, #1
    // 0x926810: b.ls            #0x926824
    // 0x926814: r8 = String?
    //     0x926814: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x926818: r3 = Null
    //     0x926818: add             x3, PP, #0x31, lsl #12  ; [pp+0x31a90] Null
    //     0x92681c: ldr             x3, [x3, #0xa90]
    // 0x926820: r0 = String?()
    //     0x926820: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x926824: ldur            x0, [fp, #-0x18]
    // 0x926828: r1 = LoadClassIdInstr(r0)
    //     0x926828: ldur            x1, [x0, #-1]
    //     0x92682c: ubfx            x1, x1, #0xc, #0x14
    // 0x926830: r16 = 6
    //     0x926830: movz            x16, #0x6
    // 0x926834: stp             x16, x0, [SP]
    // 0x926838: mov             x0, x1
    // 0x92683c: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x92683c: movz            x17, #0x32a
    //     0x926840: movk            x17, #0x1, lsl #16
    //     0x926844: add             lr, x0, x17
    //     0x926848: ldr             lr, [x21, lr, lsl #3]
    //     0x92684c: blr             lr
    // 0x926850: mov             x3, x0
    // 0x926854: r2 = Null
    //     0x926854: mov             x2, NULL
    // 0x926858: r1 = Null
    //     0x926858: mov             x1, NULL
    // 0x92685c: stur            x3, [fp, #-0x18]
    // 0x926860: r4 = 59
    //     0x926860: movz            x4, #0x3b
    // 0x926864: branchIfSmi(r0, 0x926870)
    //     0x926864: tbz             w0, #0, #0x926870
    // 0x926868: r4 = LoadClassIdInstr(r0)
    //     0x926868: ldur            x4, [x0, #-1]
    //     0x92686c: ubfx            x4, x4, #0xc, #0x14
    // 0x926870: cmp             x4, #0x3e
    // 0x926874: b.eq            #0x926888
    // 0x926878: r8 = bool?
    //     0x926878: ldr             x8, [PP, #0xb48]  ; [pp+0xb48] Type: bool?
    // 0x92687c: r3 = Null
    //     0x92687c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31aa0] Null
    //     0x926880: ldr             x3, [x3, #0xaa0]
    // 0x926884: r0 = bool?()
    //     0x926884: bl              #0x8c2694  ; IsType_bool?_Stub
    // 0x926888: ldur            x0, [fp, #-0x10]
    // 0x92688c: LoadField: r1 = r0->field_f
    //     0x92688c: ldur            w1, [x0, #0xf]
    // 0x926890: DecompressPointer r1
    //     0x926890: add             x1, x1, HEAP, lsl #32
    // 0x926894: ldur            x0, [fp, #-0x20]
    // 0x926898: cmp             w0, NULL
    // 0x92689c: b.eq            #0x9268fc
    // 0x9268a0: ldur            x2, [fp, #-0x28]
    // 0x9268a4: cmp             w2, NULL
    // 0x9268a8: b.eq            #0x926900
    // 0x9268ac: ldur            x5, [fp, #-0x30]
    // 0x9268b0: cmp             w5, NULL
    // 0x9268b4: b.eq            #0x926904
    // 0x9268b8: ldur            x6, [fp, #-0x18]
    // 0x9268bc: cmp             w6, NULL
    // 0x9268c0: b.eq            #0x926908
    // 0x9268c4: r3 = LoadInt32Instr(r0)
    //     0x9268c4: sbfx            x3, x0, #1, #0x1f
    //     0x9268c8: tbz             w0, #0, #0x9268d0
    //     0x9268cc: ldur            x3, [x0, #7]
    // 0x9268d0: r0 = LoadInt32Instr(r2)
    //     0x9268d0: sbfx            x0, x2, #1, #0x1f
    //     0x9268d4: tbz             w2, #0, #0x9268dc
    //     0x9268d8: ldur            x0, [x2, #7]
    // 0x9268dc: mov             x2, x3
    // 0x9268e0: mov             x3, x0
    // 0x9268e4: r0 = call 0x64f78c
    //     0x9268e4: bl              #0x64f78c
    // 0x9268e8: r0 = Null
    //     0x9268e8: mov             x0, NULL
    // 0x9268ec: r0 = ReturnAsyncNotFuture()
    //     0x9268ec: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x9268f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9268f0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9268f4: b               #0x9266bc
    // 0x9268f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9268f8: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9268fc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9268fc: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926900: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926900: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926904: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926904: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926908: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926908: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x92690c, size: 0x220
    // 0x92690c: EnterFrame
    //     0x92690c: stp             fp, lr, [SP, #-0x10]!
    //     0x926910: mov             fp, SP
    // 0x926914: AllocStack(0x38)
    //     0x926914: sub             SP, SP, #0x38
    // 0x926918: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x926918: stur            NULL, [fp, #-8]
    //     0x92691c: movz            x0, #0
    //     0x926920: add             x1, fp, w0, sxtw #2
    //     0x926924: ldr             x1, [x1, #0x18]
    //     0x926928: add             x2, fp, w0, sxtw #2
    //     0x92692c: ldr             x2, [x2, #0x10]
    //     0x926930: stur            x2, [fp, #-0x18]
    //     0x926934: ldur            w3, [x1, #0x17]
    //     0x926938: add             x3, x3, HEAP, lsl #32
    //     0x92693c: stur            x3, [fp, #-0x10]
    // 0x926940: CheckStackOverflow
    //     0x926940: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x926944: cmp             SP, x16
    //     0x926948: b.ls            #0x926b14
    // 0x92694c: InitAsync() -> Future<Null?>
    //     0x92694c: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x926950: bl              #0x8c1de0  ; InitAsyncStub
    // 0x926954: ldur            x0, [fp, #-0x18]
    // 0x926958: r2 = Null
    //     0x926958: mov             x2, NULL
    // 0x92695c: r1 = Null
    //     0x92695c: mov             x1, NULL
    // 0x926960: r4 = 59
    //     0x926960: movz            x4, #0x3b
    // 0x926964: branchIfSmi(r0, 0x926970)
    //     0x926964: tbz             w0, #0, #0x926970
    // 0x926968: r4 = LoadClassIdInstr(r0)
    //     0x926968: ldur            x4, [x0, #-1]
    //     0x92696c: ubfx            x4, x4, #0xc, #0x14
    // 0x926970: sub             x4, x4, #0x59
    // 0x926974: cmp             x4, #2
    // 0x926978: b.ls            #0x92698c
    // 0x92697c: r8 = List<Object?>?
    //     0x92697c: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x926980: r3 = Null
    //     0x926980: add             x3, PP, #0x31, lsl #12  ; [pp+0x31ad0] Null
    //     0x926984: ldr             x3, [x3, #0xad0]
    // 0x926988: r0 = List<Object?>?()
    //     0x926988: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x92698c: ldur            x1, [fp, #-0x18]
    // 0x926990: cmp             w1, NULL
    // 0x926994: b.eq            #0x926b1c
    // 0x926998: r0 = LoadClassIdInstr(r1)
    //     0x926998: ldur            x0, [x1, #-1]
    //     0x92699c: ubfx            x0, x0, #0xc, #0x14
    // 0x9269a0: stp             xzr, x1, [SP]
    // 0x9269a4: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9269a4: movz            x17, #0x32a
    //     0x9269a8: movk            x17, #0x1, lsl #16
    //     0x9269ac: add             lr, x0, x17
    //     0x9269b0: ldr             lr, [x21, lr, lsl #3]
    //     0x9269b4: blr             lr
    // 0x9269b8: mov             x3, x0
    // 0x9269bc: r2 = Null
    //     0x9269bc: mov             x2, NULL
    // 0x9269c0: r1 = Null
    //     0x9269c0: mov             x1, NULL
    // 0x9269c4: stur            x3, [fp, #-0x20]
    // 0x9269c8: branchIfSmi(r0, 0x9269f0)
    //     0x9269c8: tbz             w0, #0, #0x9269f0
    // 0x9269cc: r4 = LoadClassIdInstr(r0)
    //     0x9269cc: ldur            x4, [x0, #-1]
    //     0x9269d0: ubfx            x4, x4, #0xc, #0x14
    // 0x9269d4: sub             x4, x4, #0x3b
    // 0x9269d8: cmp             x4, #1
    // 0x9269dc: b.ls            #0x9269f0
    // 0x9269e0: r8 = int?
    //     0x9269e0: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9269e4: r3 = Null
    //     0x9269e4: add             x3, PP, #0x31, lsl #12  ; [pp+0x31ae0] Null
    //     0x9269e8: ldr             x3, [x3, #0xae0]
    // 0x9269ec: r0 = int?()
    //     0x9269ec: bl              #0x959574  ; IsType_int?_Stub
    // 0x9269f0: ldur            x1, [fp, #-0x18]
    // 0x9269f4: r0 = LoadClassIdInstr(r1)
    //     0x9269f4: ldur            x0, [x1, #-1]
    //     0x9269f8: ubfx            x0, x0, #0xc, #0x14
    // 0x9269fc: r16 = 2
    //     0x9269fc: movz            x16, #0x2
    // 0x926a00: stp             x16, x1, [SP]
    // 0x926a04: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926a04: movz            x17, #0x32a
    //     0x926a08: movk            x17, #0x1, lsl #16
    //     0x926a0c: add             lr, x0, x17
    //     0x926a10: ldr             lr, [x21, lr, lsl #3]
    //     0x926a14: blr             lr
    // 0x926a18: mov             x3, x0
    // 0x926a1c: r2 = Null
    //     0x926a1c: mov             x2, NULL
    // 0x926a20: r1 = Null
    //     0x926a20: mov             x1, NULL
    // 0x926a24: stur            x3, [fp, #-0x28]
    // 0x926a28: branchIfSmi(r0, 0x926a50)
    //     0x926a28: tbz             w0, #0, #0x926a50
    // 0x926a2c: r4 = LoadClassIdInstr(r0)
    //     0x926a2c: ldur            x4, [x0, #-1]
    //     0x926a30: ubfx            x4, x4, #0xc, #0x14
    // 0x926a34: sub             x4, x4, #0x3b
    // 0x926a38: cmp             x4, #1
    // 0x926a3c: b.ls            #0x926a50
    // 0x926a40: r8 = int?
    //     0x926a40: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x926a44: r3 = Null
    //     0x926a44: add             x3, PP, #0x31, lsl #12  ; [pp+0x31af0] Null
    //     0x926a48: ldr             x3, [x3, #0xaf0]
    // 0x926a4c: r0 = int?()
    //     0x926a4c: bl              #0x959574  ; IsType_int?_Stub
    // 0x926a50: ldur            x0, [fp, #-0x18]
    // 0x926a54: r1 = LoadClassIdInstr(r0)
    //     0x926a54: ldur            x1, [x0, #-1]
    //     0x926a58: ubfx            x1, x1, #0xc, #0x14
    // 0x926a5c: r16 = 4
    //     0x926a5c: movz            x16, #0x4
    // 0x926a60: stp             x16, x0, [SP]
    // 0x926a64: mov             x0, x1
    // 0x926a68: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926a68: movz            x17, #0x32a
    //     0x926a6c: movk            x17, #0x1, lsl #16
    //     0x926a70: add             lr, x0, x17
    //     0x926a74: ldr             lr, [x21, lr, lsl #3]
    //     0x926a78: blr             lr
    // 0x926a7c: mov             x3, x0
    // 0x926a80: r2 = Null
    //     0x926a80: mov             x2, NULL
    // 0x926a84: r1 = Null
    //     0x926a84: mov             x1, NULL
    // 0x926a88: stur            x3, [fp, #-0x18]
    // 0x926a8c: r4 = 59
    //     0x926a8c: movz            x4, #0x3b
    // 0x926a90: branchIfSmi(r0, 0x926a9c)
    //     0x926a90: tbz             w0, #0, #0x926a9c
    // 0x926a94: r4 = LoadClassIdInstr(r0)
    //     0x926a94: ldur            x4, [x0, #-1]
    //     0x926a98: ubfx            x4, x4, #0xc, #0x14
    // 0x926a9c: sub             x4, x4, #0x5d
    // 0x926aa0: cmp             x4, #1
    // 0x926aa4: b.ls            #0x926ab8
    // 0x926aa8: r8 = String?
    //     0x926aa8: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x926aac: r3 = Null
    //     0x926aac: add             x3, PP, #0x31, lsl #12  ; [pp+0x31b00] Null
    //     0x926ab0: ldr             x3, [x3, #0xb00]
    // 0x926ab4: r0 = String?()
    //     0x926ab4: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x926ab8: ldur            x0, [fp, #-0x10]
    // 0x926abc: LoadField: r1 = r0->field_f
    //     0x926abc: ldur            w1, [x0, #0xf]
    // 0x926ac0: DecompressPointer r1
    //     0x926ac0: add             x1, x1, HEAP, lsl #32
    // 0x926ac4: ldur            x0, [fp, #-0x20]
    // 0x926ac8: cmp             w0, NULL
    // 0x926acc: b.eq            #0x926b20
    // 0x926ad0: ldur            x2, [fp, #-0x28]
    // 0x926ad4: cmp             w2, NULL
    // 0x926ad8: b.eq            #0x926b24
    // 0x926adc: ldur            x5, [fp, #-0x18]
    // 0x926ae0: cmp             w5, NULL
    // 0x926ae4: b.eq            #0x926b28
    // 0x926ae8: r3 = LoadInt32Instr(r0)
    //     0x926ae8: sbfx            x3, x0, #1, #0x1f
    //     0x926aec: tbz             w0, #0, #0x926af4
    //     0x926af0: ldur            x3, [x0, #7]
    // 0x926af4: r0 = LoadInt32Instr(r2)
    //     0x926af4: sbfx            x0, x2, #1, #0x1f
    //     0x926af8: tbz             w2, #0, #0x926b00
    //     0x926afc: ldur            x0, [x2, #7]
    // 0x926b00: mov             x2, x3
    // 0x926b04: mov             x3, x0
    // 0x926b08: r0 = call 0x64f8c4
    //     0x926b08: bl              #0x64f8c4
    // 0x926b0c: r0 = Null
    //     0x926b0c: mov             x0, NULL
    // 0x926b10: r0 = ReturnAsyncNotFuture()
    //     0x926b10: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x926b14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x926b14: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x926b18: b               #0x92694c
    // 0x926b1c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926b1c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926b20: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926b20: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926b24: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926b24: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926b28: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926b28: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x926b2c, size: 0x220
    // 0x926b2c: EnterFrame
    //     0x926b2c: stp             fp, lr, [SP, #-0x10]!
    //     0x926b30: mov             fp, SP
    // 0x926b34: AllocStack(0x38)
    //     0x926b34: sub             SP, SP, #0x38
    // 0x926b38: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x926b38: stur            NULL, [fp, #-8]
    //     0x926b3c: movz            x0, #0
    //     0x926b40: add             x1, fp, w0, sxtw #2
    //     0x926b44: ldr             x1, [x1, #0x18]
    //     0x926b48: add             x2, fp, w0, sxtw #2
    //     0x926b4c: ldr             x2, [x2, #0x10]
    //     0x926b50: stur            x2, [fp, #-0x18]
    //     0x926b54: ldur            w3, [x1, #0x17]
    //     0x926b58: add             x3, x3, HEAP, lsl #32
    //     0x926b5c: stur            x3, [fp, #-0x10]
    // 0x926b60: CheckStackOverflow
    //     0x926b60: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x926b64: cmp             SP, x16
    //     0x926b68: b.ls            #0x926d34
    // 0x926b6c: InitAsync() -> Future<Null?>
    //     0x926b6c: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x926b70: bl              #0x8c1de0  ; InitAsyncStub
    // 0x926b74: ldur            x0, [fp, #-0x18]
    // 0x926b78: r2 = Null
    //     0x926b78: mov             x2, NULL
    // 0x926b7c: r1 = Null
    //     0x926b7c: mov             x1, NULL
    // 0x926b80: r4 = 59
    //     0x926b80: movz            x4, #0x3b
    // 0x926b84: branchIfSmi(r0, 0x926b90)
    //     0x926b84: tbz             w0, #0, #0x926b90
    // 0x926b88: r4 = LoadClassIdInstr(r0)
    //     0x926b88: ldur            x4, [x0, #-1]
    //     0x926b8c: ubfx            x4, x4, #0xc, #0x14
    // 0x926b90: sub             x4, x4, #0x59
    // 0x926b94: cmp             x4, #2
    // 0x926b98: b.ls            #0x926bac
    // 0x926b9c: r8 = List<Object?>?
    //     0x926b9c: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x926ba0: r3 = Null
    //     0x926ba0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31b30] Null
    //     0x926ba4: ldr             x3, [x3, #0xb30]
    // 0x926ba8: r0 = List<Object?>?()
    //     0x926ba8: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x926bac: ldur            x1, [fp, #-0x18]
    // 0x926bb0: cmp             w1, NULL
    // 0x926bb4: b.eq            #0x926d3c
    // 0x926bb8: r0 = LoadClassIdInstr(r1)
    //     0x926bb8: ldur            x0, [x1, #-1]
    //     0x926bbc: ubfx            x0, x0, #0xc, #0x14
    // 0x926bc0: stp             xzr, x1, [SP]
    // 0x926bc4: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926bc4: movz            x17, #0x32a
    //     0x926bc8: movk            x17, #0x1, lsl #16
    //     0x926bcc: add             lr, x0, x17
    //     0x926bd0: ldr             lr, [x21, lr, lsl #3]
    //     0x926bd4: blr             lr
    // 0x926bd8: mov             x3, x0
    // 0x926bdc: r2 = Null
    //     0x926bdc: mov             x2, NULL
    // 0x926be0: r1 = Null
    //     0x926be0: mov             x1, NULL
    // 0x926be4: stur            x3, [fp, #-0x20]
    // 0x926be8: branchIfSmi(r0, 0x926c10)
    //     0x926be8: tbz             w0, #0, #0x926c10
    // 0x926bec: r4 = LoadClassIdInstr(r0)
    //     0x926bec: ldur            x4, [x0, #-1]
    //     0x926bf0: ubfx            x4, x4, #0xc, #0x14
    // 0x926bf4: sub             x4, x4, #0x3b
    // 0x926bf8: cmp             x4, #1
    // 0x926bfc: b.ls            #0x926c10
    // 0x926c00: r8 = int?
    //     0x926c00: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x926c04: r3 = Null
    //     0x926c04: add             x3, PP, #0x31, lsl #12  ; [pp+0x31b40] Null
    //     0x926c08: ldr             x3, [x3, #0xb40]
    // 0x926c0c: r0 = int?()
    //     0x926c0c: bl              #0x959574  ; IsType_int?_Stub
    // 0x926c10: ldur            x1, [fp, #-0x18]
    // 0x926c14: r0 = LoadClassIdInstr(r1)
    //     0x926c14: ldur            x0, [x1, #-1]
    //     0x926c18: ubfx            x0, x0, #0xc, #0x14
    // 0x926c1c: r16 = 2
    //     0x926c1c: movz            x16, #0x2
    // 0x926c20: stp             x16, x1, [SP]
    // 0x926c24: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926c24: movz            x17, #0x32a
    //     0x926c28: movk            x17, #0x1, lsl #16
    //     0x926c2c: add             lr, x0, x17
    //     0x926c30: ldr             lr, [x21, lr, lsl #3]
    //     0x926c34: blr             lr
    // 0x926c38: mov             x3, x0
    // 0x926c3c: r2 = Null
    //     0x926c3c: mov             x2, NULL
    // 0x926c40: r1 = Null
    //     0x926c40: mov             x1, NULL
    // 0x926c44: stur            x3, [fp, #-0x28]
    // 0x926c48: branchIfSmi(r0, 0x926c70)
    //     0x926c48: tbz             w0, #0, #0x926c70
    // 0x926c4c: r4 = LoadClassIdInstr(r0)
    //     0x926c4c: ldur            x4, [x0, #-1]
    //     0x926c50: ubfx            x4, x4, #0xc, #0x14
    // 0x926c54: sub             x4, x4, #0x3b
    // 0x926c58: cmp             x4, #1
    // 0x926c5c: b.ls            #0x926c70
    // 0x926c60: r8 = int?
    //     0x926c60: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x926c64: r3 = Null
    //     0x926c64: add             x3, PP, #0x31, lsl #12  ; [pp+0x31b50] Null
    //     0x926c68: ldr             x3, [x3, #0xb50]
    // 0x926c6c: r0 = int?()
    //     0x926c6c: bl              #0x959574  ; IsType_int?_Stub
    // 0x926c70: ldur            x0, [fp, #-0x18]
    // 0x926c74: r1 = LoadClassIdInstr(r0)
    //     0x926c74: ldur            x1, [x0, #-1]
    //     0x926c78: ubfx            x1, x1, #0xc, #0x14
    // 0x926c7c: r16 = 4
    //     0x926c7c: movz            x16, #0x4
    // 0x926c80: stp             x16, x0, [SP]
    // 0x926c84: mov             x0, x1
    // 0x926c88: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926c88: movz            x17, #0x32a
    //     0x926c8c: movk            x17, #0x1, lsl #16
    //     0x926c90: add             lr, x0, x17
    //     0x926c94: ldr             lr, [x21, lr, lsl #3]
    //     0x926c98: blr             lr
    // 0x926c9c: mov             x3, x0
    // 0x926ca0: r2 = Null
    //     0x926ca0: mov             x2, NULL
    // 0x926ca4: r1 = Null
    //     0x926ca4: mov             x1, NULL
    // 0x926ca8: stur            x3, [fp, #-0x18]
    // 0x926cac: r4 = 59
    //     0x926cac: movz            x4, #0x3b
    // 0x926cb0: branchIfSmi(r0, 0x926cbc)
    //     0x926cb0: tbz             w0, #0, #0x926cbc
    // 0x926cb4: r4 = LoadClassIdInstr(r0)
    //     0x926cb4: ldur            x4, [x0, #-1]
    //     0x926cb8: ubfx            x4, x4, #0xc, #0x14
    // 0x926cbc: cmp             x4, #0x178
    // 0x926cc0: b.eq            #0x926cd8
    // 0x926cc4: r8 = nfb?
    //     0x926cc4: add             x8, PP, #0x31, lsl #12  ; [pp+0x31b60] Type: nfb?
    //     0x926cc8: ldr             x8, [x8, #0xb60]
    // 0x926ccc: r3 = Null
    //     0x926ccc: add             x3, PP, #0x31, lsl #12  ; [pp+0x31b68] Null
    //     0x926cd0: ldr             x3, [x3, #0xb68]
    // 0x926cd4: r0 = DefaultNullableTypeTest()
    //     0x926cd4: bl              #0x94d980  ; DefaultNullableTypeTestStub
    // 0x926cd8: ldur            x0, [fp, #-0x10]
    // 0x926cdc: LoadField: r1 = r0->field_f
    //     0x926cdc: ldur            w1, [x0, #0xf]
    // 0x926ce0: DecompressPointer r1
    //     0x926ce0: add             x1, x1, HEAP, lsl #32
    // 0x926ce4: ldur            x0, [fp, #-0x20]
    // 0x926ce8: cmp             w0, NULL
    // 0x926cec: b.eq            #0x926d40
    // 0x926cf0: ldur            x2, [fp, #-0x28]
    // 0x926cf4: cmp             w2, NULL
    // 0x926cf8: b.eq            #0x926d44
    // 0x926cfc: ldur            x5, [fp, #-0x18]
    // 0x926d00: cmp             w5, NULL
    // 0x926d04: b.eq            #0x926d48
    // 0x926d08: r3 = LoadInt32Instr(r0)
    //     0x926d08: sbfx            x3, x0, #1, #0x1f
    //     0x926d0c: tbz             w0, #0, #0x926d14
    //     0x926d10: ldur            x3, [x0, #7]
    // 0x926d14: r0 = LoadInt32Instr(r2)
    //     0x926d14: sbfx            x0, x2, #1, #0x1f
    //     0x926d18: tbz             w2, #0, #0x926d20
    //     0x926d1c: ldur            x0, [x2, #7]
    // 0x926d20: mov             x2, x3
    // 0x926d24: mov             x3, x0
    // 0x926d28: r0 = call 0x64f9f4
    //     0x926d28: bl              #0x64f9f4
    // 0x926d2c: r0 = Null
    //     0x926d2c: mov             x0, NULL
    // 0x926d30: r0 = ReturnAsyncNotFuture()
    //     0x926d30: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x926d34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x926d34: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x926d38: b               #0x926b6c
    // 0x926d3c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926d3c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926d40: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926d40: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926d44: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926d44: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x926d48: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x926d48: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x926d58, size: 0x310
    // 0x926d58: EnterFrame
    //     0x926d58: stp             fp, lr, [SP, #-0x10]!
    //     0x926d5c: mov             fp, SP
    // 0x926d60: AllocStack(0x48)
    //     0x926d60: sub             SP, SP, #0x48
    // 0x926d64: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x926d64: stur            NULL, [fp, #-8]
    //     0x926d68: movz            x0, #0
    //     0x926d6c: add             x1, fp, w0, sxtw #2
    //     0x926d70: ldr             x1, [x1, #0x18]
    //     0x926d74: add             x2, fp, w0, sxtw #2
    //     0x926d78: ldr             x2, [x2, #0x10]
    //     0x926d7c: stur            x2, [fp, #-0x18]
    //     0x926d80: ldur            w3, [x1, #0x17]
    //     0x926d84: add             x3, x3, HEAP, lsl #32
    //     0x926d88: stur            x3, [fp, #-0x10]
    // 0x926d8c: CheckStackOverflow
    //     0x926d8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x926d90: cmp             SP, x16
    //     0x926d94: b.ls            #0x927048
    // 0x926d98: InitAsync() -> Future<Null?>
    //     0x926d98: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x926d9c: bl              #0x8c1de0  ; InitAsyncStub
    // 0x926da0: ldur            x0, [fp, #-0x18]
    // 0x926da4: r2 = Null
    //     0x926da4: mov             x2, NULL
    // 0x926da8: r1 = Null
    //     0x926da8: mov             x1, NULL
    // 0x926dac: r4 = 59
    //     0x926dac: movz            x4, #0x3b
    // 0x926db0: branchIfSmi(r0, 0x926dbc)
    //     0x926db0: tbz             w0, #0, #0x926dbc
    // 0x926db4: r4 = LoadClassIdInstr(r0)
    //     0x926db4: ldur            x4, [x0, #-1]
    //     0x926db8: ubfx            x4, x4, #0xc, #0x14
    // 0x926dbc: sub             x4, x4, #0x59
    // 0x926dc0: cmp             x4, #2
    // 0x926dc4: b.ls            #0x926dd8
    // 0x926dc8: r8 = List<Object?>?
    //     0x926dc8: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x926dcc: r3 = Null
    //     0x926dcc: add             x3, PP, #0x31, lsl #12  ; [pp+0x31b98] Null
    //     0x926dd0: ldr             x3, [x3, #0xb98]
    // 0x926dd4: r0 = List<Object?>?()
    //     0x926dd4: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x926dd8: ldur            x1, [fp, #-0x18]
    // 0x926ddc: cmp             w1, NULL
    // 0x926de0: b.eq            #0x927050
    // 0x926de4: r0 = LoadClassIdInstr(r1)
    //     0x926de4: ldur            x0, [x1, #-1]
    //     0x926de8: ubfx            x0, x0, #0xc, #0x14
    // 0x926dec: stp             xzr, x1, [SP]
    // 0x926df0: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926df0: movz            x17, #0x32a
    //     0x926df4: movk            x17, #0x1, lsl #16
    //     0x926df8: add             lr, x0, x17
    //     0x926dfc: ldr             lr, [x21, lr, lsl #3]
    //     0x926e00: blr             lr
    // 0x926e04: mov             x3, x0
    // 0x926e08: r2 = Null
    //     0x926e08: mov             x2, NULL
    // 0x926e0c: r1 = Null
    //     0x926e0c: mov             x1, NULL
    // 0x926e10: stur            x3, [fp, #-0x20]
    // 0x926e14: branchIfSmi(r0, 0x926e3c)
    //     0x926e14: tbz             w0, #0, #0x926e3c
    // 0x926e18: r4 = LoadClassIdInstr(r0)
    //     0x926e18: ldur            x4, [x0, #-1]
    //     0x926e1c: ubfx            x4, x4, #0xc, #0x14
    // 0x926e20: sub             x4, x4, #0x3b
    // 0x926e24: cmp             x4, #1
    // 0x926e28: b.ls            #0x926e3c
    // 0x926e2c: r8 = int?
    //     0x926e2c: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x926e30: r3 = Null
    //     0x926e30: add             x3, PP, #0x31, lsl #12  ; [pp+0x31ba8] Null
    //     0x926e34: ldr             x3, [x3, #0xba8]
    // 0x926e38: r0 = int?()
    //     0x926e38: bl              #0x959574  ; IsType_int?_Stub
    // 0x926e3c: ldur            x1, [fp, #-0x18]
    // 0x926e40: r0 = LoadClassIdInstr(r1)
    //     0x926e40: ldur            x0, [x1, #-1]
    //     0x926e44: ubfx            x0, x0, #0xc, #0x14
    // 0x926e48: r16 = 2
    //     0x926e48: movz            x16, #0x2
    // 0x926e4c: stp             x16, x1, [SP]
    // 0x926e50: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926e50: movz            x17, #0x32a
    //     0x926e54: movk            x17, #0x1, lsl #16
    //     0x926e58: add             lr, x0, x17
    //     0x926e5c: ldr             lr, [x21, lr, lsl #3]
    //     0x926e60: blr             lr
    // 0x926e64: mov             x3, x0
    // 0x926e68: r2 = Null
    //     0x926e68: mov             x2, NULL
    // 0x926e6c: r1 = Null
    //     0x926e6c: mov             x1, NULL
    // 0x926e70: stur            x3, [fp, #-0x28]
    // 0x926e74: branchIfSmi(r0, 0x926e9c)
    //     0x926e74: tbz             w0, #0, #0x926e9c
    // 0x926e78: r4 = LoadClassIdInstr(r0)
    //     0x926e78: ldur            x4, [x0, #-1]
    //     0x926e7c: ubfx            x4, x4, #0xc, #0x14
    // 0x926e80: sub             x4, x4, #0x3b
    // 0x926e84: cmp             x4, #1
    // 0x926e88: b.ls            #0x926e9c
    // 0x926e8c: r8 = int?
    //     0x926e8c: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x926e90: r3 = Null
    //     0x926e90: add             x3, PP, #0x31, lsl #12  ; [pp+0x31bb8] Null
    //     0x926e94: ldr             x3, [x3, #0xbb8]
    // 0x926e98: r0 = int?()
    //     0x926e98: bl              #0x959574  ; IsType_int?_Stub
    // 0x926e9c: ldur            x1, [fp, #-0x18]
    // 0x926ea0: r0 = LoadClassIdInstr(r1)
    //     0x926ea0: ldur            x0, [x1, #-1]
    //     0x926ea4: ubfx            x0, x0, #0xc, #0x14
    // 0x926ea8: r16 = 4
    //     0x926ea8: movz            x16, #0x4
    // 0x926eac: stp             x16, x1, [SP]
    // 0x926eb0: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926eb0: movz            x17, #0x32a
    //     0x926eb4: movk            x17, #0x1, lsl #16
    //     0x926eb8: add             lr, x0, x17
    //     0x926ebc: ldr             lr, [x21, lr, lsl #3]
    //     0x926ec0: blr             lr
    // 0x926ec4: mov             x3, x0
    // 0x926ec8: r2 = Null
    //     0x926ec8: mov             x2, NULL
    // 0x926ecc: r1 = Null
    //     0x926ecc: mov             x1, NULL
    // 0x926ed0: stur            x3, [fp, #-0x30]
    // 0x926ed4: branchIfSmi(r0, 0x926efc)
    //     0x926ed4: tbz             w0, #0, #0x926efc
    // 0x926ed8: r4 = LoadClassIdInstr(r0)
    //     0x926ed8: ldur            x4, [x0, #-1]
    //     0x926edc: ubfx            x4, x4, #0xc, #0x14
    // 0x926ee0: sub             x4, x4, #0x3b
    // 0x926ee4: cmp             x4, #1
    // 0x926ee8: b.ls            #0x926efc
    // 0x926eec: r8 = int?
    //     0x926eec: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x926ef0: r3 = Null
    //     0x926ef0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31bc8] Null
    //     0x926ef4: ldr             x3, [x3, #0xbc8]
    // 0x926ef8: r0 = int?()
    //     0x926ef8: bl              #0x959574  ; IsType_int?_Stub
    // 0x926efc: ldur            x1, [fp, #-0x18]
    // 0x926f00: r0 = LoadClassIdInstr(r1)
    //     0x926f00: ldur            x0, [x1, #-1]
    //     0x926f04: ubfx            x0, x0, #0xc, #0x14
    // 0x926f08: r16 = 6
    //     0x926f08: movz            x16, #0x6
    // 0x926f0c: stp             x16, x1, [SP]
    // 0x926f10: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926f10: movz            x17, #0x32a
    //     0x926f14: movk            x17, #0x1, lsl #16
    //     0x926f18: add             lr, x0, x17
    //     0x926f1c: ldr             lr, [x21, lr, lsl #3]
    //     0x926f20: blr             lr
    // 0x926f24: mov             x3, x0
    // 0x926f28: r2 = Null
    //     0x926f28: mov             x2, NULL
    // 0x926f2c: r1 = Null
    //     0x926f2c: mov             x1, NULL
    // 0x926f30: stur            x3, [fp, #-0x38]
    // 0x926f34: r4 = 59
    //     0x926f34: movz            x4, #0x3b
    // 0x926f38: branchIfSmi(r0, 0x926f44)
    //     0x926f38: tbz             w0, #0, #0x926f44
    // 0x926f3c: r4 = LoadClassIdInstr(r0)
    //     0x926f3c: ldur            x4, [x0, #-1]
    //     0x926f40: ubfx            x4, x4, #0xc, #0x14
    // 0x926f44: sub             x4, x4, #0x5d
    // 0x926f48: cmp             x4, #1
    // 0x926f4c: b.ls            #0x926f60
    // 0x926f50: r8 = String?
    //     0x926f50: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x926f54: r3 = Null
    //     0x926f54: add             x3, PP, #0x31, lsl #12  ; [pp+0x31bd8] Null
    //     0x926f58: ldr             x3, [x3, #0xbd8]
    // 0x926f5c: r0 = String?()
    //     0x926f5c: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x926f60: ldur            x0, [fp, #-0x18]
    // 0x926f64: r1 = LoadClassIdInstr(r0)
    //     0x926f64: ldur            x1, [x0, #-1]
    //     0x926f68: ubfx            x1, x1, #0xc, #0x14
    // 0x926f6c: r16 = 8
    //     0x926f6c: movz            x16, #0x8
    // 0x926f70: stp             x16, x0, [SP]
    // 0x926f74: mov             x0, x1
    // 0x926f78: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x926f78: movz            x17, #0x32a
    //     0x926f7c: movk            x17, #0x1, lsl #16
    //     0x926f80: add             lr, x0, x17
    //     0x926f84: ldr             lr, [x21, lr, lsl #3]
    //     0x926f88: blr             lr
    // 0x926f8c: mov             x3, x0
    // 0x926f90: r2 = Null
    //     0x926f90: mov             x2, NULL
    // 0x926f94: r1 = Null
    //     0x926f94: mov             x1, NULL
    // 0x926f98: stur            x3, [fp, #-0x18]
    // 0x926f9c: r4 = 59
    //     0x926f9c: movz            x4, #0x3b
    // 0x926fa0: branchIfSmi(r0, 0x926fac)
    //     0x926fa0: tbz             w0, #0, #0x926fac
    // 0x926fa4: r4 = LoadClassIdInstr(r0)
    //     0x926fa4: ldur            x4, [x0, #-1]
    //     0x926fa8: ubfx            x4, x4, #0xc, #0x14
    // 0x926fac: sub             x4, x4, #0x5d
    // 0x926fb0: cmp             x4, #1
    // 0x926fb4: b.ls            #0x926fc8
    // 0x926fb8: r8 = String?
    //     0x926fb8: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x926fbc: r3 = Null
    //     0x926fbc: add             x3, PP, #0x31, lsl #12  ; [pp+0x31be8] Null
    //     0x926fc0: ldr             x3, [x3, #0xbe8]
    // 0x926fc4: r0 = String?()
    //     0x926fc4: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x926fc8: ldur            x0, [fp, #-0x10]
    // 0x926fcc: LoadField: r1 = r0->field_f
    //     0x926fcc: ldur            w1, [x0, #0xf]
    // 0x926fd0: DecompressPointer r1
    //     0x926fd0: add             x1, x1, HEAP, lsl #32
    // 0x926fd4: ldur            x0, [fp, #-0x20]
    // 0x926fd8: cmp             w0, NULL
    // 0x926fdc: b.eq            #0x927054
    // 0x926fe0: ldur            x2, [fp, #-0x28]
    // 0x926fe4: cmp             w2, NULL
    // 0x926fe8: b.eq            #0x927058
    // 0x926fec: ldur            x3, [fp, #-0x30]
    // 0x926ff0: cmp             w3, NULL
    // 0x926ff4: b.eq            #0x92705c
    // 0x926ff8: ldur            x6, [fp, #-0x38]
    // 0x926ffc: cmp             w6, NULL
    // 0x927000: b.eq            #0x927060
    // 0x927004: ldur            x7, [fp, #-0x18]
    // 0x927008: cmp             w7, NULL
    // 0x92700c: b.eq            #0x927064
    // 0x927010: r4 = LoadInt32Instr(r0)
    //     0x927010: sbfx            x4, x0, #1, #0x1f
    //     0x927014: tbz             w0, #0, #0x92701c
    //     0x927018: ldur            x4, [x0, #7]
    // 0x92701c: r0 = LoadInt32Instr(r2)
    //     0x92701c: sbfx            x0, x2, #1, #0x1f
    //     0x927020: tbz             w2, #0, #0x927028
    //     0x927024: ldur            x0, [x2, #7]
    // 0x927028: r5 = LoadInt32Instr(r3)
    //     0x927028: sbfx            x5, x3, #1, #0x1f
    //     0x92702c: tbz             w3, #0, #0x927034
    //     0x927030: ldur            x5, [x3, #7]
    // 0x927034: mov             x2, x4
    // 0x927038: mov             x3, x0
    // 0x92703c: r0 = call 0x64fbcc
    //     0x92703c: bl              #0x64fbcc
    // 0x927040: r0 = Null
    //     0x927040: mov             x0, NULL
    // 0x927044: r0 = ReturnAsyncNotFuture()
    //     0x927044: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x927048: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x927048: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x92704c: b               #0x926d98
    // 0x927050: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927050: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x927054: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927054: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x927058: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927058: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92705c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92705c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x927060: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927060: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x927064: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927064: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x927068, size: 0x294
    // 0x927068: EnterFrame
    //     0x927068: stp             fp, lr, [SP, #-0x10]!
    //     0x92706c: mov             fp, SP
    // 0x927070: AllocStack(0x40)
    //     0x927070: sub             SP, SP, #0x40
    // 0x927074: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x927074: stur            NULL, [fp, #-8]
    //     0x927078: movz            x0, #0
    //     0x92707c: add             x1, fp, w0, sxtw #2
    //     0x927080: ldr             x1, [x1, #0x18]
    //     0x927084: add             x2, fp, w0, sxtw #2
    //     0x927088: ldr             x2, [x2, #0x10]
    //     0x92708c: stur            x2, [fp, #-0x18]
    //     0x927090: ldur            w3, [x1, #0x17]
    //     0x927094: add             x3, x3, HEAP, lsl #32
    //     0x927098: stur            x3, [fp, #-0x10]
    // 0x92709c: CheckStackOverflow
    //     0x92709c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9270a0: cmp             SP, x16
    //     0x9270a4: b.ls            #0x9272e0
    // 0x9270a8: InitAsync() -> Future<Null?>
    //     0x9270a8: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x9270ac: bl              #0x8c1de0  ; InitAsyncStub
    // 0x9270b0: ldur            x0, [fp, #-0x18]
    // 0x9270b4: r2 = Null
    //     0x9270b4: mov             x2, NULL
    // 0x9270b8: r1 = Null
    //     0x9270b8: mov             x1, NULL
    // 0x9270bc: r4 = 59
    //     0x9270bc: movz            x4, #0x3b
    // 0x9270c0: branchIfSmi(r0, 0x9270cc)
    //     0x9270c0: tbz             w0, #0, #0x9270cc
    // 0x9270c4: r4 = LoadClassIdInstr(r0)
    //     0x9270c4: ldur            x4, [x0, #-1]
    //     0x9270c8: ubfx            x4, x4, #0xc, #0x14
    // 0x9270cc: sub             x4, x4, #0x59
    // 0x9270d0: cmp             x4, #2
    // 0x9270d4: b.ls            #0x9270e8
    // 0x9270d8: r8 = List<Object?>?
    //     0x9270d8: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x9270dc: r3 = Null
    //     0x9270dc: add             x3, PP, #0x31, lsl #12  ; [pp+0x31c18] Null
    //     0x9270e0: ldr             x3, [x3, #0xc18]
    // 0x9270e4: r0 = List<Object?>?()
    //     0x9270e4: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x9270e8: ldur            x1, [fp, #-0x18]
    // 0x9270ec: cmp             w1, NULL
    // 0x9270f0: b.eq            #0x9272e8
    // 0x9270f4: r0 = LoadClassIdInstr(r1)
    //     0x9270f4: ldur            x0, [x1, #-1]
    //     0x9270f8: ubfx            x0, x0, #0xc, #0x14
    // 0x9270fc: stp             xzr, x1, [SP]
    // 0x927100: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927100: movz            x17, #0x32a
    //     0x927104: movk            x17, #0x1, lsl #16
    //     0x927108: add             lr, x0, x17
    //     0x92710c: ldr             lr, [x21, lr, lsl #3]
    //     0x927110: blr             lr
    // 0x927114: mov             x3, x0
    // 0x927118: r2 = Null
    //     0x927118: mov             x2, NULL
    // 0x92711c: r1 = Null
    //     0x92711c: mov             x1, NULL
    // 0x927120: stur            x3, [fp, #-0x20]
    // 0x927124: branchIfSmi(r0, 0x92714c)
    //     0x927124: tbz             w0, #0, #0x92714c
    // 0x927128: r4 = LoadClassIdInstr(r0)
    //     0x927128: ldur            x4, [x0, #-1]
    //     0x92712c: ubfx            x4, x4, #0xc, #0x14
    // 0x927130: sub             x4, x4, #0x3b
    // 0x927134: cmp             x4, #1
    // 0x927138: b.ls            #0x92714c
    // 0x92713c: r8 = int?
    //     0x92713c: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x927140: r3 = Null
    //     0x927140: add             x3, PP, #0x31, lsl #12  ; [pp+0x31c28] Null
    //     0x927144: ldr             x3, [x3, #0xc28]
    // 0x927148: r0 = int?()
    //     0x927148: bl              #0x959574  ; IsType_int?_Stub
    // 0x92714c: ldur            x1, [fp, #-0x18]
    // 0x927150: r0 = LoadClassIdInstr(r1)
    //     0x927150: ldur            x0, [x1, #-1]
    //     0x927154: ubfx            x0, x0, #0xc, #0x14
    // 0x927158: r16 = 2
    //     0x927158: movz            x16, #0x2
    // 0x92715c: stp             x16, x1, [SP]
    // 0x927160: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927160: movz            x17, #0x32a
    //     0x927164: movk            x17, #0x1, lsl #16
    //     0x927168: add             lr, x0, x17
    //     0x92716c: ldr             lr, [x21, lr, lsl #3]
    //     0x927170: blr             lr
    // 0x927174: mov             x3, x0
    // 0x927178: r2 = Null
    //     0x927178: mov             x2, NULL
    // 0x92717c: r1 = Null
    //     0x92717c: mov             x1, NULL
    // 0x927180: stur            x3, [fp, #-0x28]
    // 0x927184: branchIfSmi(r0, 0x9271ac)
    //     0x927184: tbz             w0, #0, #0x9271ac
    // 0x927188: r4 = LoadClassIdInstr(r0)
    //     0x927188: ldur            x4, [x0, #-1]
    //     0x92718c: ubfx            x4, x4, #0xc, #0x14
    // 0x927190: sub             x4, x4, #0x3b
    // 0x927194: cmp             x4, #1
    // 0x927198: b.ls            #0x9271ac
    // 0x92719c: r8 = int?
    //     0x92719c: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9271a0: r3 = Null
    //     0x9271a0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31c38] Null
    //     0x9271a4: ldr             x3, [x3, #0xc38]
    // 0x9271a8: r0 = int?()
    //     0x9271a8: bl              #0x959574  ; IsType_int?_Stub
    // 0x9271ac: ldur            x1, [fp, #-0x18]
    // 0x9271b0: r0 = LoadClassIdInstr(r1)
    //     0x9271b0: ldur            x0, [x1, #-1]
    //     0x9271b4: ubfx            x0, x0, #0xc, #0x14
    // 0x9271b8: r16 = 4
    //     0x9271b8: movz            x16, #0x4
    // 0x9271bc: stp             x16, x1, [SP]
    // 0x9271c0: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9271c0: movz            x17, #0x32a
    //     0x9271c4: movk            x17, #0x1, lsl #16
    //     0x9271c8: add             lr, x0, x17
    //     0x9271cc: ldr             lr, [x21, lr, lsl #3]
    //     0x9271d0: blr             lr
    // 0x9271d4: mov             x3, x0
    // 0x9271d8: r2 = Null
    //     0x9271d8: mov             x2, NULL
    // 0x9271dc: r1 = Null
    //     0x9271dc: mov             x1, NULL
    // 0x9271e0: stur            x3, [fp, #-0x30]
    // 0x9271e4: r4 = 59
    //     0x9271e4: movz            x4, #0x3b
    // 0x9271e8: branchIfSmi(r0, 0x9271f4)
    //     0x9271e8: tbz             w0, #0, #0x9271f4
    // 0x9271ec: r4 = LoadClassIdInstr(r0)
    //     0x9271ec: ldur            x4, [x0, #-1]
    //     0x9271f0: ubfx            x4, x4, #0xc, #0x14
    // 0x9271f4: cmp             x4, #0x178
    // 0x9271f8: b.eq            #0x927210
    // 0x9271fc: r8 = nfb?
    //     0x9271fc: add             x8, PP, #0x31, lsl #12  ; [pp+0x31b60] Type: nfb?
    //     0x927200: ldr             x8, [x8, #0xb60]
    // 0x927204: r3 = Null
    //     0x927204: add             x3, PP, #0x31, lsl #12  ; [pp+0x31c48] Null
    //     0x927208: ldr             x3, [x3, #0xc48]
    // 0x92720c: r0 = DefaultNullableTypeTest()
    //     0x92720c: bl              #0x94d980  ; DefaultNullableTypeTestStub
    // 0x927210: ldur            x0, [fp, #-0x18]
    // 0x927214: r1 = LoadClassIdInstr(r0)
    //     0x927214: ldur            x1, [x0, #-1]
    //     0x927218: ubfx            x1, x1, #0xc, #0x14
    // 0x92721c: r16 = 6
    //     0x92721c: movz            x16, #0x6
    // 0x927220: stp             x16, x0, [SP]
    // 0x927224: mov             x0, x1
    // 0x927228: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927228: movz            x17, #0x32a
    //     0x92722c: movk            x17, #0x1, lsl #16
    //     0x927230: add             lr, x0, x17
    //     0x927234: ldr             lr, [x21, lr, lsl #3]
    //     0x927238: blr             lr
    // 0x92723c: mov             x3, x0
    // 0x927240: r2 = Null
    //     0x927240: mov             x2, NULL
    // 0x927244: r1 = Null
    //     0x927244: mov             x1, NULL
    // 0x927248: stur            x3, [fp, #-0x18]
    // 0x92724c: r4 = 59
    //     0x92724c: movz            x4, #0x3b
    // 0x927250: branchIfSmi(r0, 0x92725c)
    //     0x927250: tbz             w0, #0, #0x92725c
    // 0x927254: r4 = LoadClassIdInstr(r0)
    //     0x927254: ldur            x4, [x0, #-1]
    //     0x927258: ubfx            x4, x4, #0xc, #0x14
    // 0x92725c: cmp             x4, #0x176
    // 0x927260: b.eq            #0x927278
    // 0x927264: r8 = pfb?
    //     0x927264: add             x8, PP, #0x31, lsl #12  ; [pp+0x31c58] Type: pfb?
    //     0x927268: ldr             x8, [x8, #0xc58]
    // 0x92726c: r3 = Null
    //     0x92726c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31c60] Null
    //     0x927270: ldr             x3, [x3, #0xc60]
    // 0x927274: r0 = DefaultNullableTypeTest()
    //     0x927274: bl              #0x94d980  ; DefaultNullableTypeTestStub
    // 0x927278: ldur            x0, [fp, #-0x10]
    // 0x92727c: LoadField: r1 = r0->field_f
    //     0x92727c: ldur            w1, [x0, #0xf]
    // 0x927280: DecompressPointer r1
    //     0x927280: add             x1, x1, HEAP, lsl #32
    // 0x927284: ldur            x0, [fp, #-0x20]
    // 0x927288: cmp             w0, NULL
    // 0x92728c: b.eq            #0x9272ec
    // 0x927290: ldur            x2, [fp, #-0x28]
    // 0x927294: cmp             w2, NULL
    // 0x927298: b.eq            #0x9272f0
    // 0x92729c: ldur            x5, [fp, #-0x30]
    // 0x9272a0: cmp             w5, NULL
    // 0x9272a4: b.eq            #0x9272f4
    // 0x9272a8: ldur            x6, [fp, #-0x18]
    // 0x9272ac: cmp             w6, NULL
    // 0x9272b0: b.eq            #0x9272f8
    // 0x9272b4: r3 = LoadInt32Instr(r0)
    //     0x9272b4: sbfx            x3, x0, #1, #0x1f
    //     0x9272b8: tbz             w0, #0, #0x9272c0
    //     0x9272bc: ldur            x3, [x0, #7]
    // 0x9272c0: r0 = LoadInt32Instr(r2)
    //     0x9272c0: sbfx            x0, x2, #1, #0x1f
    //     0x9272c4: tbz             w2, #0, #0x9272cc
    //     0x9272c8: ldur            x0, [x2, #7]
    // 0x9272cc: mov             x2, x3
    // 0x9272d0: mov             x3, x0
    // 0x9272d4: r0 = call 0x64fd24
    //     0x9272d4: bl              #0x64fd24
    // 0x9272d8: r0 = Null
    //     0x9272d8: mov             x0, NULL
    // 0x9272dc: r0 = ReturnAsyncNotFuture()
    //     0x9272dc: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x9272e0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9272e0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9272e4: b               #0x9270a8
    // 0x9272e8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9272e8: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9272ec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9272ec: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9272f0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9272f0: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9272f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9272f4: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9272f8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9272f8: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x927308, size: 0x294
    // 0x927308: EnterFrame
    //     0x927308: stp             fp, lr, [SP, #-0x10]!
    //     0x92730c: mov             fp, SP
    // 0x927310: AllocStack(0x40)
    //     0x927310: sub             SP, SP, #0x40
    // 0x927314: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x927314: stur            NULL, [fp, #-8]
    //     0x927318: movz            x0, #0
    //     0x92731c: add             x1, fp, w0, sxtw #2
    //     0x927320: ldr             x1, [x1, #0x18]
    //     0x927324: add             x2, fp, w0, sxtw #2
    //     0x927328: ldr             x2, [x2, #0x10]
    //     0x92732c: stur            x2, [fp, #-0x18]
    //     0x927330: ldur            w3, [x1, #0x17]
    //     0x927334: add             x3, x3, HEAP, lsl #32
    //     0x927338: stur            x3, [fp, #-0x10]
    // 0x92733c: CheckStackOverflow
    //     0x92733c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x927340: cmp             SP, x16
    //     0x927344: b.ls            #0x927580
    // 0x927348: InitAsync() -> Future<Null?>
    //     0x927348: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x92734c: bl              #0x8c1de0  ; InitAsyncStub
    // 0x927350: ldur            x0, [fp, #-0x18]
    // 0x927354: r2 = Null
    //     0x927354: mov             x2, NULL
    // 0x927358: r1 = Null
    //     0x927358: mov             x1, NULL
    // 0x92735c: r4 = 59
    //     0x92735c: movz            x4, #0x3b
    // 0x927360: branchIfSmi(r0, 0x92736c)
    //     0x927360: tbz             w0, #0, #0x92736c
    // 0x927364: r4 = LoadClassIdInstr(r0)
    //     0x927364: ldur            x4, [x0, #-1]
    //     0x927368: ubfx            x4, x4, #0xc, #0x14
    // 0x92736c: sub             x4, x4, #0x59
    // 0x927370: cmp             x4, #2
    // 0x927374: b.ls            #0x927388
    // 0x927378: r8 = List<Object?>?
    //     0x927378: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x92737c: r3 = Null
    //     0x92737c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31c90] Null
    //     0x927380: ldr             x3, [x3, #0xc90]
    // 0x927384: r0 = List<Object?>?()
    //     0x927384: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x927388: ldur            x1, [fp, #-0x18]
    // 0x92738c: cmp             w1, NULL
    // 0x927390: b.eq            #0x927588
    // 0x927394: r0 = LoadClassIdInstr(r1)
    //     0x927394: ldur            x0, [x1, #-1]
    //     0x927398: ubfx            x0, x0, #0xc, #0x14
    // 0x92739c: stp             xzr, x1, [SP]
    // 0x9273a0: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9273a0: movz            x17, #0x32a
    //     0x9273a4: movk            x17, #0x1, lsl #16
    //     0x9273a8: add             lr, x0, x17
    //     0x9273ac: ldr             lr, [x21, lr, lsl #3]
    //     0x9273b0: blr             lr
    // 0x9273b4: mov             x3, x0
    // 0x9273b8: r2 = Null
    //     0x9273b8: mov             x2, NULL
    // 0x9273bc: r1 = Null
    //     0x9273bc: mov             x1, NULL
    // 0x9273c0: stur            x3, [fp, #-0x20]
    // 0x9273c4: branchIfSmi(r0, 0x9273ec)
    //     0x9273c4: tbz             w0, #0, #0x9273ec
    // 0x9273c8: r4 = LoadClassIdInstr(r0)
    //     0x9273c8: ldur            x4, [x0, #-1]
    //     0x9273cc: ubfx            x4, x4, #0xc, #0x14
    // 0x9273d0: sub             x4, x4, #0x3b
    // 0x9273d4: cmp             x4, #1
    // 0x9273d8: b.ls            #0x9273ec
    // 0x9273dc: r8 = int?
    //     0x9273dc: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9273e0: r3 = Null
    //     0x9273e0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31ca0] Null
    //     0x9273e4: ldr             x3, [x3, #0xca0]
    // 0x9273e8: r0 = int?()
    //     0x9273e8: bl              #0x959574  ; IsType_int?_Stub
    // 0x9273ec: ldur            x1, [fp, #-0x18]
    // 0x9273f0: r0 = LoadClassIdInstr(r1)
    //     0x9273f0: ldur            x0, [x1, #-1]
    //     0x9273f4: ubfx            x0, x0, #0xc, #0x14
    // 0x9273f8: r16 = 2
    //     0x9273f8: movz            x16, #0x2
    // 0x9273fc: stp             x16, x1, [SP]
    // 0x927400: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927400: movz            x17, #0x32a
    //     0x927404: movk            x17, #0x1, lsl #16
    //     0x927408: add             lr, x0, x17
    //     0x92740c: ldr             lr, [x21, lr, lsl #3]
    //     0x927410: blr             lr
    // 0x927414: mov             x3, x0
    // 0x927418: r2 = Null
    //     0x927418: mov             x2, NULL
    // 0x92741c: r1 = Null
    //     0x92741c: mov             x1, NULL
    // 0x927420: stur            x3, [fp, #-0x28]
    // 0x927424: branchIfSmi(r0, 0x92744c)
    //     0x927424: tbz             w0, #0, #0x92744c
    // 0x927428: r4 = LoadClassIdInstr(r0)
    //     0x927428: ldur            x4, [x0, #-1]
    //     0x92742c: ubfx            x4, x4, #0xc, #0x14
    // 0x927430: sub             x4, x4, #0x3b
    // 0x927434: cmp             x4, #1
    // 0x927438: b.ls            #0x92744c
    // 0x92743c: r8 = int?
    //     0x92743c: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x927440: r3 = Null
    //     0x927440: add             x3, PP, #0x31, lsl #12  ; [pp+0x31cb0] Null
    //     0x927444: ldr             x3, [x3, #0xcb0]
    // 0x927448: r0 = int?()
    //     0x927448: bl              #0x959574  ; IsType_int?_Stub
    // 0x92744c: ldur            x1, [fp, #-0x18]
    // 0x927450: r0 = LoadClassIdInstr(r1)
    //     0x927450: ldur            x0, [x1, #-1]
    //     0x927454: ubfx            x0, x0, #0xc, #0x14
    // 0x927458: r16 = 4
    //     0x927458: movz            x16, #0x4
    // 0x92745c: stp             x16, x1, [SP]
    // 0x927460: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927460: movz            x17, #0x32a
    //     0x927464: movk            x17, #0x1, lsl #16
    //     0x927468: add             lr, x0, x17
    //     0x92746c: ldr             lr, [x21, lr, lsl #3]
    //     0x927470: blr             lr
    // 0x927474: mov             x3, x0
    // 0x927478: r2 = Null
    //     0x927478: mov             x2, NULL
    // 0x92747c: r1 = Null
    //     0x92747c: mov             x1, NULL
    // 0x927480: stur            x3, [fp, #-0x30]
    // 0x927484: r4 = 59
    //     0x927484: movz            x4, #0x3b
    // 0x927488: branchIfSmi(r0, 0x927494)
    //     0x927488: tbz             w0, #0, #0x927494
    // 0x92748c: r4 = LoadClassIdInstr(r0)
    //     0x92748c: ldur            x4, [x0, #-1]
    //     0x927490: ubfx            x4, x4, #0xc, #0x14
    // 0x927494: cmp             x4, #0x178
    // 0x927498: b.eq            #0x9274b0
    // 0x92749c: r8 = nfb?
    //     0x92749c: add             x8, PP, #0x31, lsl #12  ; [pp+0x31b60] Type: nfb?
    //     0x9274a0: ldr             x8, [x8, #0xb60]
    // 0x9274a4: r3 = Null
    //     0x9274a4: add             x3, PP, #0x31, lsl #12  ; [pp+0x31cc0] Null
    //     0x9274a8: ldr             x3, [x3, #0xcc0]
    // 0x9274ac: r0 = DefaultNullableTypeTest()
    //     0x9274ac: bl              #0x94d980  ; DefaultNullableTypeTestStub
    // 0x9274b0: ldur            x0, [fp, #-0x18]
    // 0x9274b4: r1 = LoadClassIdInstr(r0)
    //     0x9274b4: ldur            x1, [x0, #-1]
    //     0x9274b8: ubfx            x1, x1, #0xc, #0x14
    // 0x9274bc: r16 = 6
    //     0x9274bc: movz            x16, #0x6
    // 0x9274c0: stp             x16, x0, [SP]
    // 0x9274c4: mov             x0, x1
    // 0x9274c8: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9274c8: movz            x17, #0x32a
    //     0x9274cc: movk            x17, #0x1, lsl #16
    //     0x9274d0: add             lr, x0, x17
    //     0x9274d4: ldr             lr, [x21, lr, lsl #3]
    //     0x9274d8: blr             lr
    // 0x9274dc: mov             x3, x0
    // 0x9274e0: r2 = Null
    //     0x9274e0: mov             x2, NULL
    // 0x9274e4: r1 = Null
    //     0x9274e4: mov             x1, NULL
    // 0x9274e8: stur            x3, [fp, #-0x18]
    // 0x9274ec: r4 = 59
    //     0x9274ec: movz            x4, #0x3b
    // 0x9274f0: branchIfSmi(r0, 0x9274fc)
    //     0x9274f0: tbz             w0, #0, #0x9274fc
    // 0x9274f4: r4 = LoadClassIdInstr(r0)
    //     0x9274f4: ldur            x4, [x0, #-1]
    //     0x9274f8: ubfx            x4, x4, #0xc, #0x14
    // 0x9274fc: cmp             x4, #0x177
    // 0x927500: b.eq            #0x927518
    // 0x927504: r8 = ofb?
    //     0x927504: add             x8, PP, #0x31, lsl #12  ; [pp+0x31cd0] Type: ofb?
    //     0x927508: ldr             x8, [x8, #0xcd0]
    // 0x92750c: r3 = Null
    //     0x92750c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31cd8] Null
    //     0x927510: ldr             x3, [x3, #0xcd8]
    // 0x927514: r0 = DefaultNullableTypeTest()
    //     0x927514: bl              #0x94d980  ; DefaultNullableTypeTestStub
    // 0x927518: ldur            x0, [fp, #-0x10]
    // 0x92751c: LoadField: r1 = r0->field_f
    //     0x92751c: ldur            w1, [x0, #0xf]
    // 0x927520: DecompressPointer r1
    //     0x927520: add             x1, x1, HEAP, lsl #32
    // 0x927524: ldur            x0, [fp, #-0x20]
    // 0x927528: cmp             w0, NULL
    // 0x92752c: b.eq            #0x92758c
    // 0x927530: ldur            x2, [fp, #-0x28]
    // 0x927534: cmp             w2, NULL
    // 0x927538: b.eq            #0x927590
    // 0x92753c: ldur            x5, [fp, #-0x30]
    // 0x927540: cmp             w5, NULL
    // 0x927544: b.eq            #0x927594
    // 0x927548: ldur            x6, [fp, #-0x18]
    // 0x92754c: cmp             w6, NULL
    // 0x927550: b.eq            #0x927598
    // 0x927554: r3 = LoadInt32Instr(r0)
    //     0x927554: sbfx            x3, x0, #1, #0x1f
    //     0x927558: tbz             w0, #0, #0x927560
    //     0x92755c: ldur            x3, [x0, #7]
    // 0x927560: r0 = LoadInt32Instr(r2)
    //     0x927560: sbfx            x0, x2, #1, #0x1f
    //     0x927564: tbz             w2, #0, #0x92756c
    //     0x927568: ldur            x0, [x2, #7]
    // 0x92756c: mov             x2, x3
    // 0x927570: mov             x3, x0
    // 0x927574: r0 = call 0x64fec4
    //     0x927574: bl              #0x64fec4
    // 0x927578: r0 = Null
    //     0x927578: mov             x0, NULL
    // 0x92757c: r0 = ReturnAsyncNotFuture()
    //     0x92757c: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x927580: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x927580: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x927584: b               #0x927348
    // 0x927588: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927588: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x92758c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x92758c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x927590: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927590: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x927594: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927594: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x927598: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927598: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x9275a8, size: 0x220
    // 0x9275a8: EnterFrame
    //     0x9275a8: stp             fp, lr, [SP, #-0x10]!
    //     0x9275ac: mov             fp, SP
    // 0x9275b0: AllocStack(0x38)
    //     0x9275b0: sub             SP, SP, #0x38
    // 0x9275b4: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x9275b4: stur            NULL, [fp, #-8]
    //     0x9275b8: movz            x0, #0
    //     0x9275bc: add             x1, fp, w0, sxtw #2
    //     0x9275c0: ldr             x1, [x1, #0x18]
    //     0x9275c4: add             x2, fp, w0, sxtw #2
    //     0x9275c8: ldr             x2, [x2, #0x10]
    //     0x9275cc: stur            x2, [fp, #-0x18]
    //     0x9275d0: ldur            w3, [x1, #0x17]
    //     0x9275d4: add             x3, x3, HEAP, lsl #32
    //     0x9275d8: stur            x3, [fp, #-0x10]
    // 0x9275dc: CheckStackOverflow
    //     0x9275dc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9275e0: cmp             SP, x16
    //     0x9275e4: b.ls            #0x9277b0
    // 0x9275e8: InitAsync() -> Future<Null?>
    //     0x9275e8: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x9275ec: bl              #0x8c1de0  ; InitAsyncStub
    // 0x9275f0: ldur            x0, [fp, #-0x18]
    // 0x9275f4: r2 = Null
    //     0x9275f4: mov             x2, NULL
    // 0x9275f8: r1 = Null
    //     0x9275f8: mov             x1, NULL
    // 0x9275fc: r4 = 59
    //     0x9275fc: movz            x4, #0x3b
    // 0x927600: branchIfSmi(r0, 0x92760c)
    //     0x927600: tbz             w0, #0, #0x92760c
    // 0x927604: r4 = LoadClassIdInstr(r0)
    //     0x927604: ldur            x4, [x0, #-1]
    //     0x927608: ubfx            x4, x4, #0xc, #0x14
    // 0x92760c: sub             x4, x4, #0x59
    // 0x927610: cmp             x4, #2
    // 0x927614: b.ls            #0x927628
    // 0x927618: r8 = List<Object?>?
    //     0x927618: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x92761c: r3 = Null
    //     0x92761c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31d08] Null
    //     0x927620: ldr             x3, [x3, #0xd08]
    // 0x927624: r0 = List<Object?>?()
    //     0x927624: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x927628: ldur            x1, [fp, #-0x18]
    // 0x92762c: cmp             w1, NULL
    // 0x927630: b.eq            #0x9277b8
    // 0x927634: r0 = LoadClassIdInstr(r1)
    //     0x927634: ldur            x0, [x1, #-1]
    //     0x927638: ubfx            x0, x0, #0xc, #0x14
    // 0x92763c: stp             xzr, x1, [SP]
    // 0x927640: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927640: movz            x17, #0x32a
    //     0x927644: movk            x17, #0x1, lsl #16
    //     0x927648: add             lr, x0, x17
    //     0x92764c: ldr             lr, [x21, lr, lsl #3]
    //     0x927650: blr             lr
    // 0x927654: mov             x3, x0
    // 0x927658: r2 = Null
    //     0x927658: mov             x2, NULL
    // 0x92765c: r1 = Null
    //     0x92765c: mov             x1, NULL
    // 0x927660: stur            x3, [fp, #-0x20]
    // 0x927664: branchIfSmi(r0, 0x92768c)
    //     0x927664: tbz             w0, #0, #0x92768c
    // 0x927668: r4 = LoadClassIdInstr(r0)
    //     0x927668: ldur            x4, [x0, #-1]
    //     0x92766c: ubfx            x4, x4, #0xc, #0x14
    // 0x927670: sub             x4, x4, #0x3b
    // 0x927674: cmp             x4, #1
    // 0x927678: b.ls            #0x92768c
    // 0x92767c: r8 = int?
    //     0x92767c: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x927680: r3 = Null
    //     0x927680: add             x3, PP, #0x31, lsl #12  ; [pp+0x31d18] Null
    //     0x927684: ldr             x3, [x3, #0xd18]
    // 0x927688: r0 = int?()
    //     0x927688: bl              #0x959574  ; IsType_int?_Stub
    // 0x92768c: ldur            x1, [fp, #-0x18]
    // 0x927690: r0 = LoadClassIdInstr(r1)
    //     0x927690: ldur            x0, [x1, #-1]
    //     0x927694: ubfx            x0, x0, #0xc, #0x14
    // 0x927698: r16 = 2
    //     0x927698: movz            x16, #0x2
    // 0x92769c: stp             x16, x1, [SP]
    // 0x9276a0: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9276a0: movz            x17, #0x32a
    //     0x9276a4: movk            x17, #0x1, lsl #16
    //     0x9276a8: add             lr, x0, x17
    //     0x9276ac: ldr             lr, [x21, lr, lsl #3]
    //     0x9276b0: blr             lr
    // 0x9276b4: mov             x3, x0
    // 0x9276b8: r2 = Null
    //     0x9276b8: mov             x2, NULL
    // 0x9276bc: r1 = Null
    //     0x9276bc: mov             x1, NULL
    // 0x9276c0: stur            x3, [fp, #-0x28]
    // 0x9276c4: branchIfSmi(r0, 0x9276ec)
    //     0x9276c4: tbz             w0, #0, #0x9276ec
    // 0x9276c8: r4 = LoadClassIdInstr(r0)
    //     0x9276c8: ldur            x4, [x0, #-1]
    //     0x9276cc: ubfx            x4, x4, #0xc, #0x14
    // 0x9276d0: sub             x4, x4, #0x3b
    // 0x9276d4: cmp             x4, #1
    // 0x9276d8: b.ls            #0x9276ec
    // 0x9276dc: r8 = int?
    //     0x9276dc: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9276e0: r3 = Null
    //     0x9276e0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31d28] Null
    //     0x9276e4: ldr             x3, [x3, #0xd28]
    // 0x9276e8: r0 = int?()
    //     0x9276e8: bl              #0x959574  ; IsType_int?_Stub
    // 0x9276ec: ldur            x0, [fp, #-0x18]
    // 0x9276f0: r1 = LoadClassIdInstr(r0)
    //     0x9276f0: ldur            x1, [x0, #-1]
    //     0x9276f4: ubfx            x1, x1, #0xc, #0x14
    // 0x9276f8: r16 = 4
    //     0x9276f8: movz            x16, #0x4
    // 0x9276fc: stp             x16, x0, [SP]
    // 0x927700: mov             x0, x1
    // 0x927704: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927704: movz            x17, #0x32a
    //     0x927708: movk            x17, #0x1, lsl #16
    //     0x92770c: add             lr, x0, x17
    //     0x927710: ldr             lr, [x21, lr, lsl #3]
    //     0x927714: blr             lr
    // 0x927718: mov             x3, x0
    // 0x92771c: r2 = Null
    //     0x92771c: mov             x2, NULL
    // 0x927720: r1 = Null
    //     0x927720: mov             x1, NULL
    // 0x927724: stur            x3, [fp, #-0x18]
    // 0x927728: r4 = 59
    //     0x927728: movz            x4, #0x3b
    // 0x92772c: branchIfSmi(r0, 0x927738)
    //     0x92772c: tbz             w0, #0, #0x927738
    // 0x927730: r4 = LoadClassIdInstr(r0)
    //     0x927730: ldur            x4, [x0, #-1]
    //     0x927734: ubfx            x4, x4, #0xc, #0x14
    // 0x927738: sub             x4, x4, #0x5d
    // 0x92773c: cmp             x4, #1
    // 0x927740: b.ls            #0x927754
    // 0x927744: r8 = String?
    //     0x927744: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x927748: r3 = Null
    //     0x927748: add             x3, PP, #0x31, lsl #12  ; [pp+0x31d38] Null
    //     0x92774c: ldr             x3, [x3, #0xd38]
    // 0x927750: r0 = String?()
    //     0x927750: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x927754: ldur            x0, [fp, #-0x10]
    // 0x927758: LoadField: r1 = r0->field_f
    //     0x927758: ldur            w1, [x0, #0xf]
    // 0x92775c: DecompressPointer r1
    //     0x92775c: add             x1, x1, HEAP, lsl #32
    // 0x927760: ldur            x0, [fp, #-0x20]
    // 0x927764: cmp             w0, NULL
    // 0x927768: b.eq            #0x9277bc
    // 0x92776c: ldur            x2, [fp, #-0x28]
    // 0x927770: cmp             w2, NULL
    // 0x927774: b.eq            #0x9277c0
    // 0x927778: ldur            x5, [fp, #-0x18]
    // 0x92777c: cmp             w5, NULL
    // 0x927780: b.eq            #0x9277c4
    // 0x927784: r3 = LoadInt32Instr(r0)
    //     0x927784: sbfx            x3, x0, #1, #0x1f
    //     0x927788: tbz             w0, #0, #0x927790
    //     0x92778c: ldur            x3, [x0, #7]
    // 0x927790: r0 = LoadInt32Instr(r2)
    //     0x927790: sbfx            x0, x2, #1, #0x1f
    //     0x927794: tbz             w2, #0, #0x92779c
    //     0x927798: ldur            x0, [x2, #7]
    // 0x92779c: mov             x2, x3
    // 0x9277a0: mov             x3, x0
    // 0x9277a4: r0 = call 0x650050
    //     0x9277a4: bl              #0x650050
    // 0x9277a8: r0 = Null
    //     0x9277a8: mov             x0, NULL
    // 0x9277ac: r0 = ReturnAsyncNotFuture()
    //     0x9277ac: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x9277b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9277b0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9277b4: b               #0x9275e8
    // 0x9277b8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9277b8: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9277bc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9277bc: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9277c0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9277c0: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9277c4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9277c4: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x9277c8, size: 0x220
    // 0x9277c8: EnterFrame
    //     0x9277c8: stp             fp, lr, [SP, #-0x10]!
    //     0x9277cc: mov             fp, SP
    // 0x9277d0: AllocStack(0x38)
    //     0x9277d0: sub             SP, SP, #0x38
    // 0x9277d4: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x9277d4: stur            NULL, [fp, #-8]
    //     0x9277d8: movz            x0, #0
    //     0x9277dc: add             x1, fp, w0, sxtw #2
    //     0x9277e0: ldr             x1, [x1, #0x18]
    //     0x9277e4: add             x2, fp, w0, sxtw #2
    //     0x9277e8: ldr             x2, [x2, #0x10]
    //     0x9277ec: stur            x2, [fp, #-0x18]
    //     0x9277f0: ldur            w3, [x1, #0x17]
    //     0x9277f4: add             x3, x3, HEAP, lsl #32
    //     0x9277f8: stur            x3, [fp, #-0x10]
    // 0x9277fc: CheckStackOverflow
    //     0x9277fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x927800: cmp             SP, x16
    //     0x927804: b.ls            #0x9279d0
    // 0x927808: InitAsync() -> Future<Null?>
    //     0x927808: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x92780c: bl              #0x8c1de0  ; InitAsyncStub
    // 0x927810: ldur            x0, [fp, #-0x18]
    // 0x927814: r2 = Null
    //     0x927814: mov             x2, NULL
    // 0x927818: r1 = Null
    //     0x927818: mov             x1, NULL
    // 0x92781c: r4 = 59
    //     0x92781c: movz            x4, #0x3b
    // 0x927820: branchIfSmi(r0, 0x92782c)
    //     0x927820: tbz             w0, #0, #0x92782c
    // 0x927824: r4 = LoadClassIdInstr(r0)
    //     0x927824: ldur            x4, [x0, #-1]
    //     0x927828: ubfx            x4, x4, #0xc, #0x14
    // 0x92782c: sub             x4, x4, #0x59
    // 0x927830: cmp             x4, #2
    // 0x927834: b.ls            #0x927848
    // 0x927838: r8 = List<Object?>?
    //     0x927838: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x92783c: r3 = Null
    //     0x92783c: add             x3, PP, #0x31, lsl #12  ; [pp+0x31d68] Null
    //     0x927840: ldr             x3, [x3, #0xd68]
    // 0x927844: r0 = List<Object?>?()
    //     0x927844: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x927848: ldur            x1, [fp, #-0x18]
    // 0x92784c: cmp             w1, NULL
    // 0x927850: b.eq            #0x9279d8
    // 0x927854: r0 = LoadClassIdInstr(r1)
    //     0x927854: ldur            x0, [x1, #-1]
    //     0x927858: ubfx            x0, x0, #0xc, #0x14
    // 0x92785c: stp             xzr, x1, [SP]
    // 0x927860: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927860: movz            x17, #0x32a
    //     0x927864: movk            x17, #0x1, lsl #16
    //     0x927868: add             lr, x0, x17
    //     0x92786c: ldr             lr, [x21, lr, lsl #3]
    //     0x927870: blr             lr
    // 0x927874: mov             x3, x0
    // 0x927878: r2 = Null
    //     0x927878: mov             x2, NULL
    // 0x92787c: r1 = Null
    //     0x92787c: mov             x1, NULL
    // 0x927880: stur            x3, [fp, #-0x20]
    // 0x927884: branchIfSmi(r0, 0x9278ac)
    //     0x927884: tbz             w0, #0, #0x9278ac
    // 0x927888: r4 = LoadClassIdInstr(r0)
    //     0x927888: ldur            x4, [x0, #-1]
    //     0x92788c: ubfx            x4, x4, #0xc, #0x14
    // 0x927890: sub             x4, x4, #0x3b
    // 0x927894: cmp             x4, #1
    // 0x927898: b.ls            #0x9278ac
    // 0x92789c: r8 = int?
    //     0x92789c: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9278a0: r3 = Null
    //     0x9278a0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31d78] Null
    //     0x9278a4: ldr             x3, [x3, #0xd78]
    // 0x9278a8: r0 = int?()
    //     0x9278a8: bl              #0x959574  ; IsType_int?_Stub
    // 0x9278ac: ldur            x1, [fp, #-0x18]
    // 0x9278b0: r0 = LoadClassIdInstr(r1)
    //     0x9278b0: ldur            x0, [x1, #-1]
    //     0x9278b4: ubfx            x0, x0, #0xc, #0x14
    // 0x9278b8: r16 = 2
    //     0x9278b8: movz            x16, #0x2
    // 0x9278bc: stp             x16, x1, [SP]
    // 0x9278c0: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9278c0: movz            x17, #0x32a
    //     0x9278c4: movk            x17, #0x1, lsl #16
    //     0x9278c8: add             lr, x0, x17
    //     0x9278cc: ldr             lr, [x21, lr, lsl #3]
    //     0x9278d0: blr             lr
    // 0x9278d4: mov             x3, x0
    // 0x9278d8: r2 = Null
    //     0x9278d8: mov             x2, NULL
    // 0x9278dc: r1 = Null
    //     0x9278dc: mov             x1, NULL
    // 0x9278e0: stur            x3, [fp, #-0x28]
    // 0x9278e4: branchIfSmi(r0, 0x92790c)
    //     0x9278e4: tbz             w0, #0, #0x92790c
    // 0x9278e8: r4 = LoadClassIdInstr(r0)
    //     0x9278e8: ldur            x4, [x0, #-1]
    //     0x9278ec: ubfx            x4, x4, #0xc, #0x14
    // 0x9278f0: sub             x4, x4, #0x3b
    // 0x9278f4: cmp             x4, #1
    // 0x9278f8: b.ls            #0x92790c
    // 0x9278fc: r8 = int?
    //     0x9278fc: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x927900: r3 = Null
    //     0x927900: add             x3, PP, #0x31, lsl #12  ; [pp+0x31d88] Null
    //     0x927904: ldr             x3, [x3, #0xd88]
    // 0x927908: r0 = int?()
    //     0x927908: bl              #0x959574  ; IsType_int?_Stub
    // 0x92790c: ldur            x0, [fp, #-0x18]
    // 0x927910: r1 = LoadClassIdInstr(r0)
    //     0x927910: ldur            x1, [x0, #-1]
    //     0x927914: ubfx            x1, x1, #0xc, #0x14
    // 0x927918: r16 = 4
    //     0x927918: movz            x16, #0x4
    // 0x92791c: stp             x16, x0, [SP]
    // 0x927920: mov             x0, x1
    // 0x927924: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927924: movz            x17, #0x32a
    //     0x927928: movk            x17, #0x1, lsl #16
    //     0x92792c: add             lr, x0, x17
    //     0x927930: ldr             lr, [x21, lr, lsl #3]
    //     0x927934: blr             lr
    // 0x927938: mov             x3, x0
    // 0x92793c: r2 = Null
    //     0x92793c: mov             x2, NULL
    // 0x927940: r1 = Null
    //     0x927940: mov             x1, NULL
    // 0x927944: stur            x3, [fp, #-0x18]
    // 0x927948: r4 = 59
    //     0x927948: movz            x4, #0x3b
    // 0x92794c: branchIfSmi(r0, 0x927958)
    //     0x92794c: tbz             w0, #0, #0x927958
    // 0x927950: r4 = LoadClassIdInstr(r0)
    //     0x927950: ldur            x4, [x0, #-1]
    //     0x927954: ubfx            x4, x4, #0xc, #0x14
    // 0x927958: sub             x4, x4, #0x5d
    // 0x92795c: cmp             x4, #1
    // 0x927960: b.ls            #0x927974
    // 0x927964: r8 = String?
    //     0x927964: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x927968: r3 = Null
    //     0x927968: add             x3, PP, #0x31, lsl #12  ; [pp+0x31d98] Null
    //     0x92796c: ldr             x3, [x3, #0xd98]
    // 0x927970: r0 = String?()
    //     0x927970: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x927974: ldur            x0, [fp, #-0x10]
    // 0x927978: LoadField: r1 = r0->field_f
    //     0x927978: ldur            w1, [x0, #0xf]
    // 0x92797c: DecompressPointer r1
    //     0x92797c: add             x1, x1, HEAP, lsl #32
    // 0x927980: ldur            x0, [fp, #-0x20]
    // 0x927984: cmp             w0, NULL
    // 0x927988: b.eq            #0x9279dc
    // 0x92798c: ldur            x2, [fp, #-0x28]
    // 0x927990: cmp             w2, NULL
    // 0x927994: b.eq            #0x9279e0
    // 0x927998: ldur            x5, [fp, #-0x18]
    // 0x92799c: cmp             w5, NULL
    // 0x9279a0: b.eq            #0x9279e4
    // 0x9279a4: r3 = LoadInt32Instr(r0)
    //     0x9279a4: sbfx            x3, x0, #1, #0x1f
    //     0x9279a8: tbz             w0, #0, #0x9279b0
    //     0x9279ac: ldur            x3, [x0, #7]
    // 0x9279b0: r0 = LoadInt32Instr(r2)
    //     0x9279b0: sbfx            x0, x2, #1, #0x1f
    //     0x9279b4: tbz             w2, #0, #0x9279bc
    //     0x9279b8: ldur            x0, [x2, #7]
    // 0x9279bc: mov             x2, x3
    // 0x9279c0: mov             x3, x0
    // 0x9279c4: r0 = call 0x650180
    //     0x9279c4: bl              #0x650180
    // 0x9279c8: r0 = Null
    //     0x9279c8: mov             x0, NULL
    // 0x9279cc: r0 = ReturnAsyncNotFuture()
    //     0x9279cc: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x9279d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9279d0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9279d4: b               #0x927808
    // 0x9279d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9279d8: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9279dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9279dc: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9279e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9279e0: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9279e4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9279e4: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 357, size: 0xc, field offset: 0x8
abstract class Afb extends Object {
}

// class id: 359, size: 0x8, field offset: 0x8
abstract class zfb extends Object {

  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x924a18, size: 0x19c
    // 0x924a18: EnterFrame
    //     0x924a18: stp             fp, lr, [SP, #-0x10]!
    //     0x924a1c: mov             fp, SP
    // 0x924a20: AllocStack(0x30)
    //     0x924a20: sub             SP, SP, #0x30
    // 0x924a24: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x924a24: stur            NULL, [fp, #-8]
    //     0x924a28: movz            x0, #0
    //     0x924a2c: add             x1, fp, w0, sxtw #2
    //     0x924a30: ldr             x1, [x1, #0x18]
    //     0x924a34: add             x2, fp, w0, sxtw #2
    //     0x924a38: ldr             x2, [x2, #0x10]
    //     0x924a3c: stur            x2, [fp, #-0x18]
    //     0x924a40: ldur            w3, [x1, #0x17]
    //     0x924a44: add             x3, x3, HEAP, lsl #32
    //     0x924a48: stur            x3, [fp, #-0x10]
    // 0x924a4c: CheckStackOverflow
    //     0x924a4c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x924a50: cmp             SP, x16
    //     0x924a54: b.ls            #0x924ba0
    // 0x924a58: InitAsync() -> Future<Null?>
    //     0x924a58: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x924a5c: bl              #0x8c1de0  ; InitAsyncStub
    // 0x924a60: ldur            x0, [fp, #-0x18]
    // 0x924a64: r2 = Null
    //     0x924a64: mov             x2, NULL
    // 0x924a68: r1 = Null
    //     0x924a68: mov             x1, NULL
    // 0x924a6c: r4 = 59
    //     0x924a6c: movz            x4, #0x3b
    // 0x924a70: branchIfSmi(r0, 0x924a7c)
    //     0x924a70: tbz             w0, #0, #0x924a7c
    // 0x924a74: r4 = LoadClassIdInstr(r0)
    //     0x924a74: ldur            x4, [x0, #-1]
    //     0x924a78: ubfx            x4, x4, #0xc, #0x14
    // 0x924a7c: sub             x4, x4, #0x59
    // 0x924a80: cmp             x4, #2
    // 0x924a84: b.ls            #0x924a98
    // 0x924a88: r8 = List<Object?>?
    //     0x924a88: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x924a8c: r3 = Null
    //     0x924a8c: add             x3, PP, #0x31, lsl #12  ; [pp+0x314e8] Null
    //     0x924a90: ldr             x3, [x3, #0x4e8]
    // 0x924a94: r0 = List<Object?>?()
    //     0x924a94: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x924a98: ldur            x1, [fp, #-0x18]
    // 0x924a9c: cmp             w1, NULL
    // 0x924aa0: b.eq            #0x924ba8
    // 0x924aa4: r0 = LoadClassIdInstr(r1)
    //     0x924aa4: ldur            x0, [x1, #-1]
    //     0x924aa8: ubfx            x0, x0, #0xc, #0x14
    // 0x924aac: stp             xzr, x1, [SP]
    // 0x924ab0: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924ab0: movz            x17, #0x32a
    //     0x924ab4: movk            x17, #0x1, lsl #16
    //     0x924ab8: add             lr, x0, x17
    //     0x924abc: ldr             lr, [x21, lr, lsl #3]
    //     0x924ac0: blr             lr
    // 0x924ac4: mov             x3, x0
    // 0x924ac8: r2 = Null
    //     0x924ac8: mov             x2, NULL
    // 0x924acc: r1 = Null
    //     0x924acc: mov             x1, NULL
    // 0x924ad0: stur            x3, [fp, #-0x20]
    // 0x924ad4: branchIfSmi(r0, 0x924afc)
    //     0x924ad4: tbz             w0, #0, #0x924afc
    // 0x924ad8: r4 = LoadClassIdInstr(r0)
    //     0x924ad8: ldur            x4, [x0, #-1]
    //     0x924adc: ubfx            x4, x4, #0xc, #0x14
    // 0x924ae0: sub             x4, x4, #0x3b
    // 0x924ae4: cmp             x4, #1
    // 0x924ae8: b.ls            #0x924afc
    // 0x924aec: r8 = int?
    //     0x924aec: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x924af0: r3 = Null
    //     0x924af0: add             x3, PP, #0x31, lsl #12  ; [pp+0x314f8] Null
    //     0x924af4: ldr             x3, [x3, #0x4f8]
    // 0x924af8: r0 = int?()
    //     0x924af8: bl              #0x959574  ; IsType_int?_Stub
    // 0x924afc: ldur            x0, [fp, #-0x18]
    // 0x924b00: r1 = LoadClassIdInstr(r0)
    //     0x924b00: ldur            x1, [x0, #-1]
    //     0x924b04: ubfx            x1, x1, #0xc, #0x14
    // 0x924b08: r16 = 2
    //     0x924b08: movz            x16, #0x2
    // 0x924b0c: stp             x16, x0, [SP]
    // 0x924b10: mov             x0, x1
    // 0x924b14: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924b14: movz            x17, #0x32a
    //     0x924b18: movk            x17, #0x1, lsl #16
    //     0x924b1c: add             lr, x0, x17
    //     0x924b20: ldr             lr, [x21, lr, lsl #3]
    //     0x924b24: blr             lr
    // 0x924b28: mov             x3, x0
    // 0x924b2c: r2 = Null
    //     0x924b2c: mov             x2, NULL
    // 0x924b30: r1 = Null
    //     0x924b30: mov             x1, NULL
    // 0x924b34: stur            x3, [fp, #-0x18]
    // 0x924b38: r4 = 59
    //     0x924b38: movz            x4, #0x3b
    // 0x924b3c: branchIfSmi(r0, 0x924b48)
    //     0x924b3c: tbz             w0, #0, #0x924b48
    // 0x924b40: r4 = LoadClassIdInstr(r0)
    //     0x924b40: ldur            x4, [x0, #-1]
    //     0x924b44: ubfx            x4, x4, #0xc, #0x14
    // 0x924b48: sub             x4, x4, #0x5d
    // 0x924b4c: cmp             x4, #1
    // 0x924b50: b.ls            #0x924b64
    // 0x924b54: r8 = String?
    //     0x924b54: ldr             x8, [PP, #0xba8]  ; [pp+0xba8] Type: String?
    // 0x924b58: r3 = Null
    //     0x924b58: add             x3, PP, #0x31, lsl #12  ; [pp+0x31508] Null
    //     0x924b5c: ldr             x3, [x3, #0x508]
    // 0x924b60: r0 = String?()
    //     0x924b60: bl              #0x8baae0  ; IsType_String?_Stub
    // 0x924b64: ldur            x0, [fp, #-0x10]
    // 0x924b68: LoadField: r1 = r0->field_f
    //     0x924b68: ldur            w1, [x0, #0xf]
    // 0x924b6c: DecompressPointer r1
    //     0x924b6c: add             x1, x1, HEAP, lsl #32
    // 0x924b70: ldur            x0, [fp, #-0x20]
    // 0x924b74: cmp             w0, NULL
    // 0x924b78: b.eq            #0x924bac
    // 0x924b7c: ldur            x3, [fp, #-0x18]
    // 0x924b80: cmp             w3, NULL
    // 0x924b84: b.eq            #0x924bb0
    // 0x924b88: r2 = LoadInt32Instr(r0)
    //     0x924b88: sbfx            x2, x0, #1, #0x1f
    //     0x924b8c: tbz             w0, #0, #0x924b94
    //     0x924b90: ldur            x2, [x0, #7]
    // 0x924b94: r0 = call 0x64e51c
    //     0x924b94: bl              #0x64e51c
    // 0x924b98: r0 = Null
    //     0x924b98: mov             x0, NULL
    // 0x924b9c: r0 = ReturnAsyncNotFuture()
    //     0x924b9c: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x924ba0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x924ba0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x924ba4: b               #0x924a58
    // 0x924ba8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924ba8: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x924bac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924bac: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x924bb0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924bb0: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 361, size: 0xc, field offset: 0x8
abstract class yfb extends Object {
}

// class id: 363, size: 0xc, field offset: 0x8
abstract class xfb extends Object {
}

// class id: 365, size: 0x8, field offset: 0x8
abstract class wfb extends Object {

  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x9240bc, size: 0x328
    // 0x9240bc: EnterFrame
    //     0x9240bc: stp             fp, lr, [SP, #-0x10]!
    //     0x9240c0: mov             fp, SP
    // 0x9240c4: AllocStack(0x48)
    //     0x9240c4: sub             SP, SP, #0x48
    // 0x9240c8: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x9240c8: stur            NULL, [fp, #-8]
    //     0x9240cc: movz            x0, #0
    //     0x9240d0: add             x1, fp, w0, sxtw #2
    //     0x9240d4: ldr             x1, [x1, #0x18]
    //     0x9240d8: add             x2, fp, w0, sxtw #2
    //     0x9240dc: ldr             x2, [x2, #0x10]
    //     0x9240e0: stur            x2, [fp, #-0x18]
    //     0x9240e4: ldur            w3, [x1, #0x17]
    //     0x9240e8: add             x3, x3, HEAP, lsl #32
    //     0x9240ec: stur            x3, [fp, #-0x10]
    // 0x9240f0: CheckStackOverflow
    //     0x9240f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9240f4: cmp             SP, x16
    //     0x9240f8: b.ls            #0x9243c4
    // 0x9240fc: InitAsync() -> Future<Null?>
    //     0x9240fc: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x924100: bl              #0x8c1de0  ; InitAsyncStub
    // 0x924104: ldur            x0, [fp, #-0x18]
    // 0x924108: r2 = Null
    //     0x924108: mov             x2, NULL
    // 0x92410c: r1 = Null
    //     0x92410c: mov             x1, NULL
    // 0x924110: r4 = 59
    //     0x924110: movz            x4, #0x3b
    // 0x924114: branchIfSmi(r0, 0x924120)
    //     0x924114: tbz             w0, #0, #0x924120
    // 0x924118: r4 = LoadClassIdInstr(r0)
    //     0x924118: ldur            x4, [x0, #-1]
    //     0x92411c: ubfx            x4, x4, #0xc, #0x14
    // 0x924120: sub             x4, x4, #0x59
    // 0x924124: cmp             x4, #2
    // 0x924128: b.ls            #0x92413c
    // 0x92412c: r8 = List<Object?>?
    //     0x92412c: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x924130: r3 = Null
    //     0x924130: add             x3, PP, #0x31, lsl #12  ; [pp+0x31358] Null
    //     0x924134: ldr             x3, [x3, #0x358]
    // 0x924138: r0 = List<Object?>?()
    //     0x924138: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x92413c: ldur            x1, [fp, #-0x18]
    // 0x924140: cmp             w1, NULL
    // 0x924144: b.eq            #0x9243cc
    // 0x924148: r0 = LoadClassIdInstr(r1)
    //     0x924148: ldur            x0, [x1, #-1]
    //     0x92414c: ubfx            x0, x0, #0xc, #0x14
    // 0x924150: stp             xzr, x1, [SP]
    // 0x924154: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924154: movz            x17, #0x32a
    //     0x924158: movk            x17, #0x1, lsl #16
    //     0x92415c: add             lr, x0, x17
    //     0x924160: ldr             lr, [x21, lr, lsl #3]
    //     0x924164: blr             lr
    // 0x924168: mov             x3, x0
    // 0x92416c: r2 = Null
    //     0x92416c: mov             x2, NULL
    // 0x924170: r1 = Null
    //     0x924170: mov             x1, NULL
    // 0x924174: stur            x3, [fp, #-0x20]
    // 0x924178: branchIfSmi(r0, 0x9241a0)
    //     0x924178: tbz             w0, #0, #0x9241a0
    // 0x92417c: r4 = LoadClassIdInstr(r0)
    //     0x92417c: ldur            x4, [x0, #-1]
    //     0x924180: ubfx            x4, x4, #0xc, #0x14
    // 0x924184: sub             x4, x4, #0x3b
    // 0x924188: cmp             x4, #1
    // 0x92418c: b.ls            #0x9241a0
    // 0x924190: r8 = int?
    //     0x924190: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x924194: r3 = Null
    //     0x924194: add             x3, PP, #0x31, lsl #12  ; [pp+0x31368] Null
    //     0x924198: ldr             x3, [x3, #0x368]
    // 0x92419c: r0 = int?()
    //     0x92419c: bl              #0x959574  ; IsType_int?_Stub
    // 0x9241a0: ldur            x1, [fp, #-0x18]
    // 0x9241a4: r0 = LoadClassIdInstr(r1)
    //     0x9241a4: ldur            x0, [x1, #-1]
    //     0x9241a8: ubfx            x0, x0, #0xc, #0x14
    // 0x9241ac: r16 = 2
    //     0x9241ac: movz            x16, #0x2
    // 0x9241b0: stp             x16, x1, [SP]
    // 0x9241b4: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9241b4: movz            x17, #0x32a
    //     0x9241b8: movk            x17, #0x1, lsl #16
    //     0x9241bc: add             lr, x0, x17
    //     0x9241c0: ldr             lr, [x21, lr, lsl #3]
    //     0x9241c4: blr             lr
    // 0x9241c8: mov             x3, x0
    // 0x9241cc: r2 = Null
    //     0x9241cc: mov             x2, NULL
    // 0x9241d0: r1 = Null
    //     0x9241d0: mov             x1, NULL
    // 0x9241d4: stur            x3, [fp, #-0x28]
    // 0x9241d8: branchIfSmi(r0, 0x924200)
    //     0x9241d8: tbz             w0, #0, #0x924200
    // 0x9241dc: r4 = LoadClassIdInstr(r0)
    //     0x9241dc: ldur            x4, [x0, #-1]
    //     0x9241e0: ubfx            x4, x4, #0xc, #0x14
    // 0x9241e4: sub             x4, x4, #0x3b
    // 0x9241e8: cmp             x4, #1
    // 0x9241ec: b.ls            #0x924200
    // 0x9241f0: r8 = int?
    //     0x9241f0: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9241f4: r3 = Null
    //     0x9241f4: add             x3, PP, #0x31, lsl #12  ; [pp+0x31378] Null
    //     0x9241f8: ldr             x3, [x3, #0x378]
    // 0x9241fc: r0 = int?()
    //     0x9241fc: bl              #0x959574  ; IsType_int?_Stub
    // 0x924200: ldur            x1, [fp, #-0x18]
    // 0x924204: r0 = LoadClassIdInstr(r1)
    //     0x924204: ldur            x0, [x1, #-1]
    //     0x924208: ubfx            x0, x0, #0xc, #0x14
    // 0x92420c: r16 = 4
    //     0x92420c: movz            x16, #0x4
    // 0x924210: stp             x16, x1, [SP]
    // 0x924214: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924214: movz            x17, #0x32a
    //     0x924218: movk            x17, #0x1, lsl #16
    //     0x92421c: add             lr, x0, x17
    //     0x924220: ldr             lr, [x21, lr, lsl #3]
    //     0x924224: blr             lr
    // 0x924228: mov             x3, x0
    // 0x92422c: r2 = Null
    //     0x92422c: mov             x2, NULL
    // 0x924230: r1 = Null
    //     0x924230: mov             x1, NULL
    // 0x924234: stur            x3, [fp, #-0x30]
    // 0x924238: branchIfSmi(r0, 0x924260)
    //     0x924238: tbz             w0, #0, #0x924260
    // 0x92423c: r4 = LoadClassIdInstr(r0)
    //     0x92423c: ldur            x4, [x0, #-1]
    //     0x924240: ubfx            x4, x4, #0xc, #0x14
    // 0x924244: sub             x4, x4, #0x3b
    // 0x924248: cmp             x4, #1
    // 0x92424c: b.ls            #0x924260
    // 0x924250: r8 = int?
    //     0x924250: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x924254: r3 = Null
    //     0x924254: add             x3, PP, #0x31, lsl #12  ; [pp+0x31388] Null
    //     0x924258: ldr             x3, [x3, #0x388]
    // 0x92425c: r0 = int?()
    //     0x92425c: bl              #0x959574  ; IsType_int?_Stub
    // 0x924260: ldur            x1, [fp, #-0x18]
    // 0x924264: r0 = LoadClassIdInstr(r1)
    //     0x924264: ldur            x0, [x1, #-1]
    //     0x924268: ubfx            x0, x0, #0xc, #0x14
    // 0x92426c: r16 = 6
    //     0x92426c: movz            x16, #0x6
    // 0x924270: stp             x16, x1, [SP]
    // 0x924274: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924274: movz            x17, #0x32a
    //     0x924278: movk            x17, #0x1, lsl #16
    //     0x92427c: add             lr, x0, x17
    //     0x924280: ldr             lr, [x21, lr, lsl #3]
    //     0x924284: blr             lr
    // 0x924288: mov             x3, x0
    // 0x92428c: r2 = Null
    //     0x92428c: mov             x2, NULL
    // 0x924290: r1 = Null
    //     0x924290: mov             x1, NULL
    // 0x924294: stur            x3, [fp, #-0x38]
    // 0x924298: branchIfSmi(r0, 0x9242c0)
    //     0x924298: tbz             w0, #0, #0x9242c0
    // 0x92429c: r4 = LoadClassIdInstr(r0)
    //     0x92429c: ldur            x4, [x0, #-1]
    //     0x9242a0: ubfx            x4, x4, #0xc, #0x14
    // 0x9242a4: sub             x4, x4, #0x3b
    // 0x9242a8: cmp             x4, #1
    // 0x9242ac: b.ls            #0x9242c0
    // 0x9242b0: r8 = int?
    //     0x9242b0: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9242b4: r3 = Null
    //     0x9242b4: add             x3, PP, #0x31, lsl #12  ; [pp+0x31398] Null
    //     0x9242b8: ldr             x3, [x3, #0x398]
    // 0x9242bc: r0 = int?()
    //     0x9242bc: bl              #0x959574  ; IsType_int?_Stub
    // 0x9242c0: ldur            x0, [fp, #-0x18]
    // 0x9242c4: r1 = LoadClassIdInstr(r0)
    //     0x9242c4: ldur            x1, [x0, #-1]
    //     0x9242c8: ubfx            x1, x1, #0xc, #0x14
    // 0x9242cc: r16 = 8
    //     0x9242cc: movz            x16, #0x8
    // 0x9242d0: stp             x16, x0, [SP]
    // 0x9242d4: mov             x0, x1
    // 0x9242d8: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x9242d8: movz            x17, #0x32a
    //     0x9242dc: movk            x17, #0x1, lsl #16
    //     0x9242e0: add             lr, x0, x17
    //     0x9242e4: ldr             lr, [x21, lr, lsl #3]
    //     0x9242e8: blr             lr
    // 0x9242ec: mov             x3, x0
    // 0x9242f0: r2 = Null
    //     0x9242f0: mov             x2, NULL
    // 0x9242f4: r1 = Null
    //     0x9242f4: mov             x1, NULL
    // 0x9242f8: stur            x3, [fp, #-0x18]
    // 0x9242fc: branchIfSmi(r0, 0x924324)
    //     0x9242fc: tbz             w0, #0, #0x924324
    // 0x924300: r4 = LoadClassIdInstr(r0)
    //     0x924300: ldur            x4, [x0, #-1]
    //     0x924304: ubfx            x4, x4, #0xc, #0x14
    // 0x924308: sub             x4, x4, #0x3b
    // 0x92430c: cmp             x4, #1
    // 0x924310: b.ls            #0x924324
    // 0x924314: r8 = int?
    //     0x924314: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x924318: r3 = Null
    //     0x924318: add             x3, PP, #0x31, lsl #12  ; [pp+0x313a8] Null
    //     0x92431c: ldr             x3, [x3, #0x3a8]
    // 0x924320: r0 = int?()
    //     0x924320: bl              #0x959574  ; IsType_int?_Stub
    // 0x924324: ldur            x0, [fp, #-0x10]
    // 0x924328: LoadField: r1 = r0->field_f
    //     0x924328: ldur            w1, [x0, #0xf]
    // 0x92432c: DecompressPointer r1
    //     0x92432c: add             x1, x1, HEAP, lsl #32
    // 0x924330: ldur            x0, [fp, #-0x20]
    // 0x924334: cmp             w0, NULL
    // 0x924338: b.eq            #0x9243d0
    // 0x92433c: ldur            x2, [fp, #-0x28]
    // 0x924340: cmp             w2, NULL
    // 0x924344: b.eq            #0x9243d4
    // 0x924348: ldur            x3, [fp, #-0x30]
    // 0x92434c: cmp             w3, NULL
    // 0x924350: b.eq            #0x9243d8
    // 0x924354: ldur            x4, [fp, #-0x38]
    // 0x924358: cmp             w4, NULL
    // 0x92435c: b.eq            #0x9243dc
    // 0x924360: ldur            x5, [fp, #-0x18]
    // 0x924364: cmp             w5, NULL
    // 0x924368: b.eq            #0x9243e0
    // 0x92436c: r6 = LoadInt32Instr(r0)
    //     0x92436c: sbfx            x6, x0, #1, #0x1f
    //     0x924370: tbz             w0, #0, #0x924378
    //     0x924374: ldur            x6, [x0, #7]
    // 0x924378: r0 = LoadInt32Instr(r2)
    //     0x924378: sbfx            x0, x2, #1, #0x1f
    //     0x92437c: tbz             w2, #0, #0x924384
    //     0x924380: ldur            x0, [x2, #7]
    // 0x924384: r2 = LoadInt32Instr(r3)
    //     0x924384: sbfx            x2, x3, #1, #0x1f
    //     0x924388: tbz             w3, #0, #0x924390
    //     0x92438c: ldur            x2, [x3, #7]
    // 0x924390: r3 = LoadInt32Instr(r4)
    //     0x924390: sbfx            x3, x4, #1, #0x1f
    //     0x924394: tbz             w4, #0, #0x92439c
    //     0x924398: ldur            x3, [x4, #7]
    // 0x92439c: r7 = LoadInt32Instr(r5)
    //     0x92439c: sbfx            x7, x5, #1, #0x1f
    //     0x9243a0: tbz             w5, #0, #0x9243a8
    //     0x9243a4: ldur            x7, [x5, #7]
    // 0x9243a8: mov             x5, x2
    // 0x9243ac: mov             x2, x6
    // 0x9243b0: mov             x6, x3
    // 0x9243b4: mov             x3, x0
    // 0x9243b8: r0 = call 0x64dbd0
    //     0x9243b8: bl              #0x64dbd0
    // 0x9243bc: r0 = Null
    //     0x9243bc: mov             x0, NULL
    // 0x9243c0: r0 = ReturnAsyncNotFuture()
    //     0x9243c0: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x9243c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9243c4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9243c8: b               #0x9240fc
    // 0x9243cc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9243cc: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9243d0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9243d0: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9243d4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9243d4: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9243d8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9243d8: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9243dc: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9243dc: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9243e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9243e0: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x9243e4, size: 0x128
    // 0x9243e4: EnterFrame
    //     0x9243e4: stp             fp, lr, [SP, #-0x10]!
    //     0x9243e8: mov             fp, SP
    // 0x9243ec: AllocStack(0x28)
    //     0x9243ec: sub             SP, SP, #0x28
    // 0x9243f0: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x9243f0: stur            NULL, [fp, #-8]
    //     0x9243f4: movz            x0, #0
    //     0x9243f8: add             x1, fp, w0, sxtw #2
    //     0x9243fc: ldr             x1, [x1, #0x18]
    //     0x924400: add             x2, fp, w0, sxtw #2
    //     0x924404: ldr             x2, [x2, #0x10]
    //     0x924408: stur            x2, [fp, #-0x18]
    //     0x92440c: ldur            w3, [x1, #0x17]
    //     0x924410: add             x3, x3, HEAP, lsl #32
    //     0x924414: stur            x3, [fp, #-0x10]
    // 0x924418: CheckStackOverflow
    //     0x924418: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x92441c: cmp             SP, x16
    //     0x924420: b.ls            #0x9244fc
    // 0x924424: InitAsync() -> Future<Null?>
    //     0x924424: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x924428: bl              #0x8c1de0  ; InitAsyncStub
    // 0x92442c: ldur            x0, [fp, #-0x18]
    // 0x924430: r2 = Null
    //     0x924430: mov             x2, NULL
    // 0x924434: r1 = Null
    //     0x924434: mov             x1, NULL
    // 0x924438: r4 = 59
    //     0x924438: movz            x4, #0x3b
    // 0x92443c: branchIfSmi(r0, 0x924448)
    //     0x92443c: tbz             w0, #0, #0x924448
    // 0x924440: r4 = LoadClassIdInstr(r0)
    //     0x924440: ldur            x4, [x0, #-1]
    //     0x924444: ubfx            x4, x4, #0xc, #0x14
    // 0x924448: sub             x4, x4, #0x59
    // 0x92444c: cmp             x4, #2
    // 0x924450: b.ls            #0x924464
    // 0x924454: r8 = List<Object?>?
    //     0x924454: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x924458: r3 = Null
    //     0x924458: add             x3, PP, #0x31, lsl #12  ; [pp+0x31410] Null
    //     0x92445c: ldr             x3, [x3, #0x410]
    // 0x924460: r0 = List<Object?>?()
    //     0x924460: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x924464: ldur            x0, [fp, #-0x18]
    // 0x924468: cmp             w0, NULL
    // 0x92446c: b.eq            #0x924504
    // 0x924470: r1 = LoadClassIdInstr(r0)
    //     0x924470: ldur            x1, [x0, #-1]
    //     0x924474: ubfx            x1, x1, #0xc, #0x14
    // 0x924478: stp             xzr, x0, [SP]
    // 0x92447c: mov             x0, x1
    // 0x924480: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x924480: movz            x17, #0x32a
    //     0x924484: movk            x17, #0x1, lsl #16
    //     0x924488: add             lr, x0, x17
    //     0x92448c: ldr             lr, [x21, lr, lsl #3]
    //     0x924490: blr             lr
    // 0x924494: mov             x3, x0
    // 0x924498: r2 = Null
    //     0x924498: mov             x2, NULL
    // 0x92449c: r1 = Null
    //     0x92449c: mov             x1, NULL
    // 0x9244a0: stur            x3, [fp, #-0x18]
    // 0x9244a4: branchIfSmi(r0, 0x9244cc)
    //     0x9244a4: tbz             w0, #0, #0x9244cc
    // 0x9244a8: r4 = LoadClassIdInstr(r0)
    //     0x9244a8: ldur            x4, [x0, #-1]
    //     0x9244ac: ubfx            x4, x4, #0xc, #0x14
    // 0x9244b0: sub             x4, x4, #0x3b
    // 0x9244b4: cmp             x4, #1
    // 0x9244b8: b.ls            #0x9244cc
    // 0x9244bc: r8 = int?
    //     0x9244bc: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9244c0: r3 = Null
    //     0x9244c0: add             x3, PP, #0x31, lsl #12  ; [pp+0x31420] Null
    //     0x9244c4: ldr             x3, [x3, #0x420]
    // 0x9244c8: r0 = int?()
    //     0x9244c8: bl              #0x959574  ; IsType_int?_Stub
    // 0x9244cc: ldur            x0, [fp, #-0x10]
    // 0x9244d0: LoadField: r1 = r0->field_f
    //     0x9244d0: ldur            w1, [x0, #0xf]
    // 0x9244d4: DecompressPointer r1
    //     0x9244d4: add             x1, x1, HEAP, lsl #32
    // 0x9244d8: ldur            x0, [fp, #-0x18]
    // 0x9244dc: cmp             w0, NULL
    // 0x9244e0: b.eq            #0x924508
    // 0x9244e4: r2 = LoadInt32Instr(r0)
    //     0x9244e4: sbfx            x2, x0, #1, #0x1f
    //     0x9244e8: tbz             w0, #0, #0x9244f0
    //     0x9244ec: ldur            x2, [x0, #7]
    // 0x9244f0: r0 = call 0x64dfac
    //     0x9244f0: bl              #0x64dfac
    // 0x9244f4: r0 = Null
    //     0x9244f4: mov             x0, NULL
    // 0x9244f8: r0 = ReturnAsyncNotFuture()
    //     0x9244f8: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x9244fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9244fc: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x924500: b               #0x924424
    // 0x924504: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924504: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x924508: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x924508: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 366, size: 0xc, field offset: 0x8
abstract class vfb extends Object {
}

// class id: 368, size: 0x8, field offset: 0x8
abstract class tfb extends Object {

  [closure] static Future<Null> <anonymous closure>(dynamic, Object?) async {
    // ** addr: 0x927d64, size: 0x128
    // 0x927d64: EnterFrame
    //     0x927d64: stp             fp, lr, [SP, #-0x10]!
    //     0x927d68: mov             fp, SP
    // 0x927d6c: AllocStack(0x28)
    //     0x927d6c: sub             SP, SP, #0x28
    // 0x927d70: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x927d70: stur            NULL, [fp, #-8]
    //     0x927d74: movz            x0, #0
    //     0x927d78: add             x1, fp, w0, sxtw #2
    //     0x927d7c: ldr             x1, [x1, #0x18]
    //     0x927d80: add             x2, fp, w0, sxtw #2
    //     0x927d84: ldr             x2, [x2, #0x10]
    //     0x927d88: stur            x2, [fp, #-0x18]
    //     0x927d8c: ldur            w3, [x1, #0x17]
    //     0x927d90: add             x3, x3, HEAP, lsl #32
    //     0x927d94: stur            x3, [fp, #-0x10]
    // 0x927d98: CheckStackOverflow
    //     0x927d98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x927d9c: cmp             SP, x16
    //     0x927da0: b.ls            #0x927e7c
    // 0x927da4: InitAsync() -> Future<Null?>
    //     0x927da4: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x927da8: bl              #0x8c1de0  ; InitAsyncStub
    // 0x927dac: ldur            x0, [fp, #-0x18]
    // 0x927db0: r2 = Null
    //     0x927db0: mov             x2, NULL
    // 0x927db4: r1 = Null
    //     0x927db4: mov             x1, NULL
    // 0x927db8: r4 = 59
    //     0x927db8: movz            x4, #0x3b
    // 0x927dbc: branchIfSmi(r0, 0x927dc8)
    //     0x927dbc: tbz             w0, #0, #0x927dc8
    // 0x927dc0: r4 = LoadClassIdInstr(r0)
    //     0x927dc0: ldur            x4, [x0, #-1]
    //     0x927dc4: ubfx            x4, x4, #0xc, #0x14
    // 0x927dc8: sub             x4, x4, #0x59
    // 0x927dcc: cmp             x4, #2
    // 0x927dd0: b.ls            #0x927de4
    // 0x927dd4: r8 = List<Object?>?
    //     0x927dd4: ldr             x8, [PP, #0x31b0]  ; [pp+0x31b0] Type: List<Object?>?
    // 0x927dd8: r3 = Null
    //     0x927dd8: add             x3, PP, #0x31, lsl #12  ; [pp+0x31e70] Null
    //     0x927ddc: ldr             x3, [x3, #0xe70]
    // 0x927de0: r0 = List<Object?>?()
    //     0x927de0: bl              #0x8c3044  ; IsType_List<Object?>?_Stub
    // 0x927de4: ldur            x0, [fp, #-0x18]
    // 0x927de8: cmp             w0, NULL
    // 0x927dec: b.eq            #0x927e84
    // 0x927df0: r1 = LoadClassIdInstr(r0)
    //     0x927df0: ldur            x1, [x0, #-1]
    //     0x927df4: ubfx            x1, x1, #0xc, #0x14
    // 0x927df8: stp             xzr, x0, [SP]
    // 0x927dfc: mov             x0, x1
    // 0x927e00: r0 = GDT[cid_x0 + 0x1032a]()
    //     0x927e00: movz            x17, #0x32a
    //     0x927e04: movk            x17, #0x1, lsl #16
    //     0x927e08: add             lr, x0, x17
    //     0x927e0c: ldr             lr, [x21, lr, lsl #3]
    //     0x927e10: blr             lr
    // 0x927e14: mov             x3, x0
    // 0x927e18: r2 = Null
    //     0x927e18: mov             x2, NULL
    // 0x927e1c: r1 = Null
    //     0x927e1c: mov             x1, NULL
    // 0x927e20: stur            x3, [fp, #-0x18]
    // 0x927e24: branchIfSmi(r0, 0x927e4c)
    //     0x927e24: tbz             w0, #0, #0x927e4c
    // 0x927e28: r4 = LoadClassIdInstr(r0)
    //     0x927e28: ldur            x4, [x0, #-1]
    //     0x927e2c: ubfx            x4, x4, #0xc, #0x14
    // 0x927e30: sub             x4, x4, #0x3b
    // 0x927e34: cmp             x4, #1
    // 0x927e38: b.ls            #0x927e4c
    // 0x927e3c: r8 = int?
    //     0x927e3c: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x927e40: r3 = Null
    //     0x927e40: add             x3, PP, #0x31, lsl #12  ; [pp+0x31e80] Null
    //     0x927e44: ldr             x3, [x3, #0xe80]
    // 0x927e48: r0 = int?()
    //     0x927e48: bl              #0x959574  ; IsType_int?_Stub
    // 0x927e4c: ldur            x0, [fp, #-0x10]
    // 0x927e50: LoadField: r1 = r0->field_f
    //     0x927e50: ldur            w1, [x0, #0xf]
    // 0x927e54: DecompressPointer r1
    //     0x927e54: add             x1, x1, HEAP, lsl #32
    // 0x927e58: ldur            x0, [fp, #-0x18]
    // 0x927e5c: cmp             w0, NULL
    // 0x927e60: b.eq            #0x927e88
    // 0x927e64: r2 = LoadInt32Instr(r0)
    //     0x927e64: sbfx            x2, x0, #1, #0x1f
    //     0x927e68: tbz             w0, #0, #0x927e70
    //     0x927e6c: ldur            x2, [x0, #7]
    // 0x927e70: r0 = call 0x6504b8
    //     0x927e70: bl              #0x6504b8
    // 0x927e74: r0 = Null
    //     0x927e74: mov             x0, NULL
    // 0x927e78: r0 = ReturnAsyncNotFuture()
    //     0x927e78: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x927e7c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x927e7c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x927e80: b               #0x927da4
    // 0x927e84: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927e84: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x927e88: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x927e88: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
}

// class id: 369, size: 0xc, field offset: 0x8
abstract class sfb extends Object {
}

// class id: 371, size: 0xc, field offset: 0x8
class rfb extends Object {
}

// class id: 372, size: 0x1c, field offset: 0x8
class Reb extends Object {
}

// class id: 373, size: 0x18, field offset: 0x8
class qfb extends Object {
}

// class id: 374, size: 0x14, field offset: 0x8
class pfb extends Object {
}

// class id: 375, size: 0x10, field offset: 0x8
class ofb extends Object {
}

// class id: 376, size: 0x20, field offset: 0x8
class nfb extends Object {
}

// class id: 1876, size: 0x8, field offset: 0x8
//   const constructor, 
class _Gfb extends hha {
}

// class id: 1877, size: 0x8, field offset: 0x8
//   const constructor, 
class _Bfb extends hha {
}

// class id: 1878, size: 0x8, field offset: 0x8
//   const constructor, 
class _ufb extends hha {
}

// class id: 5393, size: 0x14, field offset: 0x14
enum Seb extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5394, size: 0x14, field offset: 0x14
enum Teb extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
