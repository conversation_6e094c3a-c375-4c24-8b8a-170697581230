// lib: , url: sRi

// class id: 1048773, size: 0x8
class :: {

  static late GB Okg; // offset: 0x1070
}

// class id: 4382, size: 0x14, field offset: 0x8
class GB extends Object {

  [closure] bool <anonymous closure>(dynamic, oB) {
    // ** addr: 0x6a0f60, size: -0x1
  }
}

// class id: 4383, size: 0x18, field offset: 0x8
class oB extends Object {
}

// class id: 5603, size: 0x14, field offset: 0x14
enum pB extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
