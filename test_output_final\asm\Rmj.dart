// lib: , url: Rmj

// class id: 1049832, size: 0x8
class :: {
}

// class id: 437, size: 0x18, field offset: 0x8
//   const constructor, 
class tdb extends Object {
}

// class id: 438, size: 0xc, field offset: 0x8
//   const constructor, 
class sdb extends Object {
}

// class id: 439, size: 0x14, field offset: 0x8
//   const constructor, 
class rdb extends Object {
}

// class id: 5399, size: 0x14, field offset: 0x14
enum qdb extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
