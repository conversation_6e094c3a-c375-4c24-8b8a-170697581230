// lib: , url: pnj

// class id: 1049849, size: 0x8
class :: {

  static late final Geb web; // offset: 0x145c
  static late final <Y0 extends NativeType>(dynamic, String) => Pointer<Y0> _uVg; // offset: 0x1458
  static late final <Y0 extends NativeType>(dynamic, String) => Pointer<Y0> Ysb; // offset: 0x1454

  [closure] static <Y0 extends NativeType>(dynamic, String) => Pointer<Y0> <anonymous closure>(dynamic) {
    // ** addr: 0x905780, size: 0x140
    // 0x905780: EnterFrame
    //     0x905780: stp             fp, lr, [SP, #-0x10]!
    //     0x905784: mov             fp, SP
    // 0x905788: AllocStack(0x58)
    //     0x905788: sub             SP, SP, #0x58
    // 0x90578c: SetupParameters()
    //     0x90578c: ldr             x0, [fp, #0x10]
    //     0x905790: ldur            w1, [x0, #0x17]
    //     0x905794: add             x1, x1, HEAP, lsl #32
    //     0x905798: stur            x1, [fp, #-0x40]
    // 0x90579c: CheckStackOverflow
    //     0x90579c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9057a0: cmp             SP, x16
    //     0x9057a4: b.ls            #0x9058b8
    // 0x9057a8: r1 = 1
    //     0x9057a8: movz            x1, #0x1
    // 0x9057ac: r0 = AllocateContext()
    //     0x9057ac: bl              #0x94e988  ; AllocateContextStub
    // 0x9057b0: mov             x1, x0
    // 0x9057b4: ldur            x0, [fp, #-0x40]
    // 0x9057b8: stur            x1, [fp, #-0x48]
    // 0x9057bc: StoreField: r1->field_b = r0
    //     0x9057bc: stur            w0, [x1, #0xb]
    // 0x9057c0: r16 = "libwebcrypto.so"
    //     0x9057c0: add             x16, PP, #0x14, lsl #12  ; [pp+0x14f78] "libwebcrypto.so"
    //     0x9057c4: ldr             x16, [x16, #0xf78]
    // 0x9057c8: str             x16, [SP]
    // 0x9057cc: r0 = _Arb()
    //     0x9057cc: bl              #0x905918  ; [dart:ffi] ::_Arb
    // 0x9057d0: stur            x0, [fp, #-0x40]
    // 0x9057d4: r0 = reb()
    //     0x9057d4: bl              #0x90590c  ; AllocaterebStub -> reb (size=0x14)
    // 0x9057d8: mov             x3, x0
    // 0x9057dc: r0 = Sentinel
    //     0x9057dc: ldr             x0, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9057e0: stur            x3, [fp, #-0x50]
    // 0x9057e4: StoreField: r3->field_b = r0
    //     0x9057e4: stur            w0, [x3, #0xb]
    // 0x9057e8: StoreField: r3->field_f = r0
    //     0x9057e8: stur            w0, [x3, #0xf]
    // 0x9057ec: ldur            x2, [fp, #-0x40]
    // 0x9057f0: r1 = Function 'Ysb':.
    //     0x9057f0: add             x1, PP, #0x14, lsl #12  ; [pp+0x14f80] AnonymousClosure: (0x562758), in [dart:ffi] DynamicLibrary::Ysb (0x90599c)
    //     0x9057f4: ldr             x1, [x1, #0xf80]
    // 0x9057f8: r0 = AllocateClosureGeneric()
    //     0x9057f8: bl              #0x94ec6c  ; AllocateClosureGenericStub
    // 0x9057fc: mov             x1, x0
    // 0x905800: ldur            x0, [fp, #-0x50]
    // 0x905804: StoreField: r0->field_7 = r1
    //     0x905804: stur            w1, [x0, #7]
    // 0x905808: mov             x2, x0
    // 0x90580c: r1 = Function 'tVg':.
    //     0x90580c: add             x1, PP, #0x14, lsl #12  ; [pp+0x14f88] AnonymousClosure: (0x56271c), of [onj] reb
    //     0x905810: ldr             x1, [x1, #0xf88]
    // 0x905814: r0 = AllocateClosure()
    //     0x905814: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x905818: ldur            x3, [fp, #-0x48]
    // 0x90581c: StoreField: r3->field_f = r0
    //     0x90581c: stur            w0, [x3, #0xf]
    //     0x905820: ldurb           w16, [x3, #-1]
    //     0x905824: ldurb           w17, [x0, #-1]
    //     0x905828: and             x16, x17, x16, lsr #2
    //     0x90582c: tst             x16, HEAP, lsr #32
    //     0x905830: b.eq            #0x905838
    //     0x905834: bl              #0x94e188  ; WriteBarrierWrappersStub
    // 0x905838: mov             x2, x3
    // 0x90583c: r1 = Function 'Ysb': static.
    //     0x90583c: add             x1, PP, #0x14, lsl #12  ; [pp+0x14f90] AnonymousClosure: static (0x562480), in [pnj] ::Ysb (0x561b84)
    //     0x905840: ldr             x1, [x1, #0xf90]
    // 0x905844: r0 = AllocateClosureGeneric()
    //     0x905844: bl              #0x94ec6c  ; AllocateClosureGenericStub
    // 0x905848: LeaveFrame
    //     0x905848: mov             SP, fp
    //     0x90584c: ldp             fp, lr, [SP], #0x10
    // 0x905850: ret
    //     0x905850: ret             
    // 0x905854: sub             SP, fp, #0x58
    // 0x905858: r2 = 59
    //     0x905858: movz            x2, #0x3b
    // 0x90585c: branchIfSmi(r0, 0x905868)
    //     0x90585c: tbz             w0, #0, #0x905868
    // 0x905860: r2 = LoadClassIdInstr(r0)
    //     0x905860: ldur            x2, [x0, #-1]
    //     0x905864: ubfx            x2, x2, #0xc, #0x14
    // 0x905868: r17 = -5763
    //     0x905868: movn            x17, #0x1682
    // 0x90586c: add             x16, x2, x17
    // 0x905870: cmp             x16, #2
    // 0x905874: b.hi            #0x9058b0
    // 0x905878: r0 = call 0x561bd0
    //     0x905878: bl              #0x561bd0
    // 0x90587c: cmp             w0, NULL
    // 0x905880: b.eq            #0x905890
    // 0x905884: LeaveFrame
    //     0x905884: mov             SP, fp
    //     0x905888: ldp             fp, lr, [SP], #0x10
    // 0x90588c: ret
    //     0x90588c: ret             
    // 0x905890: r0 = UnsupportedError()
    //     0x905890: bl              #0x8bb4dc  ; AllocateUnsupportedErrorStub -> UnsupportedError (size=0x10)
    // 0x905894: mov             x1, x0
    // 0x905898: r0 = "package:webcrypto cannot be used from scripts or `flutter test` unless `flutter pub run webcrypto:setup` has been run for the current root project."
    //     0x905898: add             x0, PP, #0x14, lsl #12  ; [pp+0x14f98] "package:webcrypto cannot be used from scripts or `flutter test` unless `flutter pub run webcrypto:setup` has been run for the current root project."
    //     0x90589c: ldr             x0, [x0, #0xf98]
    // 0x9058a0: StoreField: r1->field_b = r0
    //     0x9058a0: stur            w0, [x1, #0xb]
    // 0x9058a4: mov             x0, x1
    // 0x9058a8: r0 = Throw()
    //     0x9058a8: bl              #0x94dd08  ; ThrowStub
    // 0x9058ac: brk             #0
    // 0x9058b0: r0 = ReThrow()
    //     0x9058b0: bl              #0x94dce4  ; ReThrowStub
    // 0x9058b4: brk             #0
    // 0x9058b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9058b8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9058bc: b               #0x9057a8
  }
  [closure] static Pointer<Y0> Ysb<Y0 extends NativeType>(dynamic, String) {
    // ** addr: 0x562480, size: -0x1
  }
  static <Y0 extends NativeType>(dynamic, String) => Pointer<Y0> Ysb() {
    // ** addr: 0x561b84, size: -0x1
  }
}
