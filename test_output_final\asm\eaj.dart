// lib: , url: Eaj

// class id: 1049232, size: 0x8
class :: {
}

// class id: 3450, size: 0x54, field offset: 0x40
class Gva extends eI
    implements Tea {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x8d7b64, size: 0x290
    // 0x8d7b64: EnterFrame
    //     0x8d7b64: stp             fp, lr, [SP, #-0x10]!
    //     0x8d7b68: mov             fp, SP
    // 0x8d7b6c: AllocStack(0x68)
    //     0x8d7b6c: sub             SP, SP, #0x68
    // 0x8d7b70: SetupParameters()
    //     0x8d7b70: ldr             x0, [fp, #0x10]
    //     0x8d7b74: ldur            w3, [x0, #0x17]
    //     0x8d7b78: add             x3, x3, HEAP, lsl #32
    //     0x8d7b7c: stur            x3, [fp, #-0x58]
    // 0x8d7b80: CheckStackOverflow
    //     0x8d7b80: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d7b84: cmp             SP, x16
    //     0x8d7b88: b.ls            #0x8d7de4
    // 0x8d7b8c: ArrayLoad: r0 = r3[0]  ; List_4
    //     0x8d7b8c: ldur            w0, [x3, #0x17]
    // 0x8d7b90: DecompressPointer r0
    //     0x8d7b90: add             x0, x0, HEAP, lsl #32
    // 0x8d7b94: LoadField: r4 = r3->field_f
    //     0x8d7b94: ldur            w4, [x3, #0xf]
    // 0x8d7b98: DecompressPointer r4
    //     0x8d7b98: add             x4, x4, HEAP, lsl #32
    // 0x8d7b9c: stur            x4, [fp, #-0x50]
    // 0x8d7ba0: cmp             w0, NULL
    // 0x8d7ba4: b.ne            #0x8d7bb4
    // 0x8d7ba8: mov             x1, x4
    // 0x8d7bac: r0 = Null
    //     0x8d7bac: mov             x0, NULL
    // 0x8d7bb0: b               #0x8d7c58
    // 0x8d7bb4: LoadField: r2 = r4->field_43
    //     0x8d7bb4: ldur            w2, [x4, #0x43]
    // 0x8d7bb8: DecompressPointer r2
    //     0x8d7bb8: add             x2, x2, HEAP, lsl #32
    // 0x8d7bbc: LoadField: r0 = r3->field_13
    //     0x8d7bbc: ldur            w0, [x3, #0x13]
    // 0x8d7bc0: DecompressPointer r0
    //     0x8d7bc0: add             x0, x0, HEAP, lsl #32
    // 0x8d7bc4: r1 = LoadInt32Instr(r0)
    //     0x8d7bc4: sbfx            x1, x0, #1, #0x1f
    //     0x8d7bc8: tbz             w0, #0, #0x8d7bd0
    //     0x8d7bcc: ldur            x1, [x0, #7]
    // 0x8d7bd0: sub             x5, x1, #1
    // 0x8d7bd4: r0 = BoxInt64Instr(r5)
    //     0x8d7bd4: sbfiz           x0, x5, #1, #0x1f
    //     0x8d7bd8: cmp             x5, x0, asr #1
    //     0x8d7bdc: b.eq            #0x8d7be8
    //     0x8d7be0: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8d7be4: stur            x5, [x0, #7]
    // 0x8d7be8: mov             x1, x2
    // 0x8d7bec: mov             x2, x0
    // 0x8d7bf0: r0 = call 0x782a58
    //     0x8d7bf0: bl              #0x782a58
    // 0x8d7bf4: cmp             w0, NULL
    // 0x8d7bf8: b.eq            #0x8d7dec
    // 0x8d7bfc: r1 = LoadClassIdInstr(r0)
    //     0x8d7bfc: ldur            x1, [x0, #-1]
    //     0x8d7c00: ubfx            x1, x1, #0xc, #0x14
    // 0x8d7c04: mov             x16, x0
    // 0x8d7c08: mov             x0, x1
    // 0x8d7c0c: mov             x1, x16
    // 0x8d7c10: r0 = GDT[cid_x0 + -0xf6b]()
    //     0x8d7c10: sub             lr, x0, #0xf6b
    //     0x8d7c14: ldr             lr, [x21, lr, lsl #3]
    //     0x8d7c18: blr             lr
    // 0x8d7c1c: mov             x3, x0
    // 0x8d7c20: r2 = Null
    //     0x8d7c20: mov             x2, NULL
    // 0x8d7c24: r1 = Null
    //     0x8d7c24: mov             x1, NULL
    // 0x8d7c28: stur            x3, [fp, #-0x60]
    // 0x8d7c2c: r4 = LoadClassIdInstr(r0)
    //     0x8d7c2c: ldur            x4, [x0, #-1]
    //     0x8d7c30: ubfx            x4, x4, #0xc, #0x14
    // 0x8d7c34: sub             x4, x4, #0x801
    // 0x8d7c38: cmp             x4, #0x8b
    // 0x8d7c3c: b.ls            #0x8d7c50
    // 0x8d7c40: r8 = iI?
    //     0x8d7c40: ldr             x8, [PP, #0x2b90]  ; [pp+0x2b90] Type: iI?
    // 0x8d7c44: r3 = Null
    //     0x8d7c44: add             x3, PP, #0x38, lsl #12  ; [pp+0x38920] Null
    //     0x8d7c48: ldr             x3, [x3, #0x920]
    // 0x8d7c4c: r0 = iI?()
    //     0x8d7c4c: bl              #0x8cc048  ; IsType_iI?_Stub
    // 0x8d7c50: ldur            x0, [fp, #-0x60]
    // 0x8d7c54: ldur            x1, [fp, #-0x50]
    // 0x8d7c58: StoreField: r1->field_47 = r0
    //     0x8d7c58: stur            w0, [x1, #0x47]
    //     0x8d7c5c: ldurb           w16, [x1, #-1]
    //     0x8d7c60: ldurb           w17, [x0, #-1]
    //     0x8d7c64: and             x16, x17, x16, lsr #2
    //     0x8d7c68: tst             x16, HEAP, lsr #32
    //     0x8d7c6c: b.eq            #0x8d7c74
    //     0x8d7c70: bl              #0x94e148  ; WriteBarrierWrappersStub
    // 0x8d7c74: ldur            x3, [fp, #-0x58]
    // 0x8d7c78: LoadField: r4 = r3->field_f
    //     0x8d7c78: ldur            w4, [x3, #0xf]
    // 0x8d7c7c: DecompressPointer r4
    //     0x8d7c7c: add             x4, x4, HEAP, lsl #32
    // 0x8d7c80: stur            x4, [fp, #-0x60]
    // 0x8d7c84: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x8d7c84: ldur            w5, [x4, #0x17]
    // 0x8d7c88: DecompressPointer r5
    //     0x8d7c88: add             x5, x5, HEAP, lsl #32
    // 0x8d7c8c: stur            x5, [fp, #-0x50]
    // 0x8d7c90: cmp             w5, NULL
    // 0x8d7c94: b.eq            #0x8d7df0
    // 0x8d7c98: mov             x0, x5
    // 0x8d7c9c: r2 = Null
    //     0x8d7c9c: mov             x2, NULL
    // 0x8d7ca0: r1 = Null
    //     0x8d7ca0: mov             x1, NULL
    // 0x8d7ca4: r4 = LoadClassIdInstr(r0)
    //     0x8d7ca4: ldur            x4, [x0, #-1]
    //     0x8d7ca8: ubfx            x4, x4, #0xc, #0x14
    // 0x8d7cac: sub             x4, x4, #0xde9
    // 0x8d7cb0: cmp             x4, #6
    // 0x8d7cb4: b.ls            #0x8d7ccc
    // 0x8d7cb8: r8 = Cva
    //     0x8d7cb8: add             x8, PP, #0x38, lsl #12  ; [pp+0x38870] Type: Cva
    //     0x8d7cbc: ldr             x8, [x8, #0x870]
    // 0x8d7cc0: r3 = Null
    //     0x8d7cc0: add             x3, PP, #0x38, lsl #12  ; [pp+0x38930] Null
    //     0x8d7cc4: ldr             x3, [x3, #0x930]
    // 0x8d7cc8: r0 = DefaultTypeTest()
    //     0x8d7cc8: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8d7ccc: ldur            x3, [fp, #-0x58]
    // 0x8d7cd0: LoadField: r1 = r3->field_13
    //     0x8d7cd0: ldur            w1, [x3, #0x13]
    // 0x8d7cd4: DecompressPointer r1
    //     0x8d7cd4: add             x1, x1, HEAP, lsl #32
    // 0x8d7cd8: mov             x0, x1
    // 0x8d7cdc: ldur            x4, [fp, #-0x60]
    // 0x8d7ce0: StoreField: r4->field_4b = r0
    //     0x8d7ce0: stur            w0, [x4, #0x4b]
    //     0x8d7ce4: tbz             w0, #0, #0x8d7d00
    //     0x8d7ce8: ldurb           w16, [x4, #-1]
    //     0x8d7cec: ldurb           w17, [x0, #-1]
    //     0x8d7cf0: and             x16, x17, x16, lsr #2
    //     0x8d7cf4: tst             x16, HEAP, lsr #32
    //     0x8d7cf8: b.eq            #0x8d7d00
    //     0x8d7cfc: bl              #0x94e1a8  ; WriteBarrierWrappersStub
    // 0x8d7d00: LoadField: r0 = r4->field_43
    //     0x8d7d00: ldur            w0, [x4, #0x43]
    // 0x8d7d04: DecompressPointer r0
    //     0x8d7d04: add             x0, x0, HEAP, lsl #32
    // 0x8d7d08: mov             x2, x1
    // 0x8d7d0c: mov             x1, x0
    // 0x8d7d10: r0 = call 0x782a58
    //     0x8d7d10: bl              #0x782a58
    // 0x8d7d14: mov             x4, x0
    // 0x8d7d18: ldur            x0, [fp, #-0x58]
    // 0x8d7d1c: stur            x4, [fp, #-0x68]
    // 0x8d7d20: LoadField: r1 = r0->field_f
    //     0x8d7d20: ldur            w1, [x0, #0xf]
    // 0x8d7d24: DecompressPointer r1
    //     0x8d7d24: add             x1, x1, HEAP, lsl #32
    // 0x8d7d28: LoadField: r2 = r0->field_13
    //     0x8d7d28: ldur            w2, [x0, #0x13]
    // 0x8d7d2c: DecompressPointer r2
    //     0x8d7d2c: add             x2, x2, HEAP, lsl #32
    // 0x8d7d30: r3 = LoadInt32Instr(r2)
    //     0x8d7d30: sbfx            x3, x2, #1, #0x1f
    //     0x8d7d34: tbz             w2, #0, #0x8d7d3c
    //     0x8d7d38: ldur            x3, [x2, #7]
    // 0x8d7d3c: mov             x2, x3
    // 0x8d7d40: ldur            x3, [fp, #-0x50]
    // 0x8d7d44: r0 = call 0x3b4e9c
    //     0x8d7d44: bl              #0x3b4e9c
    // 0x8d7d48: mov             x1, x0
    // 0x8d7d4c: ldur            x0, [fp, #-0x58]
    // 0x8d7d50: LoadField: r5 = r0->field_13
    //     0x8d7d50: ldur            w5, [x0, #0x13]
    // 0x8d7d54: DecompressPointer r5
    //     0x8d7d54: add             x5, x5, HEAP, lsl #32
    // 0x8d7d58: mov             x3, x1
    // 0x8d7d5c: ldur            x1, [fp, #-0x60]
    // 0x8d7d60: ldur            x2, [fp, #-0x68]
    // 0x8d7d64: r0 = call 0x3c7c1c
    //     0x8d7d64: bl              #0x3c7c1c
    // 0x8d7d68: ldur            x1, [fp, #-0x58]
    // 0x8d7d6c: LoadField: r2 = r1->field_f
    //     0x8d7d6c: ldur            w2, [x1, #0xf]
    // 0x8d7d70: DecompressPointer r2
    //     0x8d7d70: add             x2, x2, HEAP, lsl #32
    // 0x8d7d74: StoreField: r2->field_4b = rNULL
    //     0x8d7d74: stur            NULL, [x2, #0x4b]
    // 0x8d7d78: cmp             w0, NULL
    // 0x8d7d7c: b.eq            #0x8d7da0
    // 0x8d7d80: LoadField: r3 = r2->field_43
    //     0x8d7d80: ldur            w3, [x2, #0x43]
    // 0x8d7d84: DecompressPointer r3
    //     0x8d7d84: add             x3, x3, HEAP, lsl #32
    // 0x8d7d88: LoadField: r2 = r1->field_13
    //     0x8d7d88: ldur            w2, [x1, #0x13]
    // 0x8d7d8c: DecompressPointer r2
    //     0x8d7d8c: add             x2, x2, HEAP, lsl #32
    // 0x8d7d90: mov             x1, x3
    // 0x8d7d94: mov             x3, x0
    // 0x8d7d98: r0 = call 0x7a8290
    //     0x8d7d98: bl              #0x7a8290
    // 0x8d7d9c: b               #0x8d7db8
    // 0x8d7da0: LoadField: r0 = r2->field_43
    //     0x8d7da0: ldur            w0, [x2, #0x43]
    // 0x8d7da4: DecompressPointer r0
    //     0x8d7da4: add             x0, x0, HEAP, lsl #32
    // 0x8d7da8: LoadField: r2 = r1->field_13
    //     0x8d7da8: ldur            w2, [x1, #0x13]
    // 0x8d7dac: DecompressPointer r2
    //     0x8d7dac: add             x2, x2, HEAP, lsl #32
    // 0x8d7db0: mov             x1, x0
    // 0x8d7db4: r0 = call 0x773358
    //     0x8d7db4: bl              #0x773358
    // 0x8d7db8: r0 = Null
    //     0x8d7db8: mov             x0, NULL
    // 0x8d7dbc: LeaveFrame
    //     0x8d7dbc: mov             SP, fp
    //     0x8d7dc0: ldp             fp, lr, [SP], #0x10
    // 0x8d7dc4: ret
    //     0x8d7dc4: ret             
    // 0x8d7dc8: sub             SP, fp, #0x68
    // 0x8d7dcc: ldur            x2, [fp, #-0x10]
    // 0x8d7dd0: LoadField: r3 = r2->field_f
    //     0x8d7dd0: ldur            w3, [x2, #0xf]
    // 0x8d7dd4: DecompressPointer r3
    //     0x8d7dd4: add             x3, x3, HEAP, lsl #32
    // 0x8d7dd8: StoreField: r3->field_4b = rNULL
    //     0x8d7dd8: stur            NULL, [x3, #0x4b]
    // 0x8d7ddc: r0 = ReThrow()
    //     0x8d7ddc: bl              #0x94dce4  ; ReThrowStub
    // 0x8d7de0: brk             #0
    // 0x8d7de4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d7de4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d7de8: b               #0x8d7b8c
    // 0x8d7dec: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8d7dec: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8d7df0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8d7df0: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x8d7df4, size: 0xe0
    // 0x8d7df4: EnterFrame
    //     0x8d7df4: stp             fp, lr, [SP, #-0x10]!
    //     0x8d7df8: mov             fp, SP
    // 0x8d7dfc: AllocStack(0x48)
    //     0x8d7dfc: sub             SP, SP, #0x48
    // 0x8d7e00: SetupParameters()
    //     0x8d7e00: ldr             x0, [fp, #0x10]
    //     0x8d7e04: ldur            w3, [x0, #0x17]
    //     0x8d7e08: add             x3, x3, HEAP, lsl #32
    //     0x8d7e0c: stur            x3, [fp, #-0x48]
    // 0x8d7e10: CheckStackOverflow
    //     0x8d7e10: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d7e14: cmp             SP, x16
    //     0x8d7e18: b.ls            #0x8d7ecc
    // 0x8d7e1c: LoadField: r4 = r3->field_f
    //     0x8d7e1c: ldur            w4, [x3, #0xf]
    // 0x8d7e20: DecompressPointer r4
    //     0x8d7e20: add             x4, x4, HEAP, lsl #32
    // 0x8d7e24: stur            x4, [fp, #-0x40]
    // 0x8d7e28: LoadField: r5 = r3->field_13
    //     0x8d7e28: ldur            w5, [x3, #0x13]
    // 0x8d7e2c: DecompressPointer r5
    //     0x8d7e2c: add             x5, x5, HEAP, lsl #32
    // 0x8d7e30: mov             x0, x5
    // 0x8d7e34: stur            x5, [fp, #-0x38]
    // 0x8d7e38: StoreField: r4->field_4b = r0
    //     0x8d7e38: stur            w0, [x4, #0x4b]
    //     0x8d7e3c: tbz             w0, #0, #0x8d7e58
    //     0x8d7e40: ldurb           w16, [x4, #-1]
    //     0x8d7e44: ldurb           w17, [x0, #-1]
    //     0x8d7e48: and             x16, x17, x16, lsr #2
    //     0x8d7e4c: tst             x16, HEAP, lsr #32
    //     0x8d7e50: b.eq            #0x8d7e58
    //     0x8d7e54: bl              #0x94e1a8  ; WriteBarrierWrappersStub
    // 0x8d7e58: LoadField: r1 = r4->field_43
    //     0x8d7e58: ldur            w1, [x4, #0x43]
    // 0x8d7e5c: DecompressPointer r1
    //     0x8d7e5c: add             x1, x1, HEAP, lsl #32
    // 0x8d7e60: mov             x2, x5
    // 0x8d7e64: r0 = call 0x782a58
    //     0x8d7e64: bl              #0x782a58
    // 0x8d7e68: ldur            x1, [fp, #-0x40]
    // 0x8d7e6c: mov             x2, x0
    // 0x8d7e70: ldur            x5, [fp, #-0x38]
    // 0x8d7e74: r3 = Null
    //     0x8d7e74: mov             x3, NULL
    // 0x8d7e78: r0 = call 0x3c7c1c
    //     0x8d7e78: bl              #0x3c7c1c
    // 0x8d7e7c: ldur            x0, [fp, #-0x48]
    // 0x8d7e80: LoadField: r1 = r0->field_f
    //     0x8d7e80: ldur            w1, [x0, #0xf]
    // 0x8d7e84: DecompressPointer r1
    //     0x8d7e84: add             x1, x1, HEAP, lsl #32
    // 0x8d7e88: StoreField: r1->field_4b = rNULL
    //     0x8d7e88: stur            NULL, [x1, #0x4b]
    // 0x8d7e8c: LoadField: r0 = r1->field_43
    //     0x8d7e8c: ldur            w0, [x1, #0x43]
    // 0x8d7e90: DecompressPointer r0
    //     0x8d7e90: add             x0, x0, HEAP, lsl #32
    // 0x8d7e94: mov             x1, x0
    // 0x8d7e98: ldur            x2, [fp, #-0x38]
    // 0x8d7e9c: r0 = call 0x773358
    //     0x8d7e9c: bl              #0x773358
    // 0x8d7ea0: r0 = Null
    //     0x8d7ea0: mov             x0, NULL
    // 0x8d7ea4: LeaveFrame
    //     0x8d7ea4: mov             SP, fp
    //     0x8d7ea8: ldp             fp, lr, [SP], #0x10
    // 0x8d7eac: ret
    //     0x8d7eac: ret             
    // 0x8d7eb0: sub             SP, fp, #0x48
    // 0x8d7eb4: ldur            x2, [fp, #-0x10]
    // 0x8d7eb8: LoadField: r3 = r2->field_f
    //     0x8d7eb8: ldur            w3, [x2, #0xf]
    // 0x8d7ebc: DecompressPointer r3
    //     0x8d7ebc: add             x3, x3, HEAP, lsl #32
    // 0x8d7ec0: StoreField: r3->field_4b = rNULL
    //     0x8d7ec0: stur            NULL, [x3, #0x4b]
    // 0x8d7ec4: r0 = ReThrow()
    //     0x8d7ec4: bl              #0x94dce4  ; ReThrowStub
    // 0x8d7ec8: brk             #0
    // 0x8d7ecc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d7ecc: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d7ed0: b               #0x8d7e1c
  }
  [closure] void iui(dynamic, int) {
    // ** addr: 0x3c9464, size: -0x1
  }
  [closure] nI? <anonymous closure>(dynamic) {
    // ** addr: 0x3c9408, size: -0x1
  }
  [closure] void vge(dynamic, iI) {
    // ** addr: 0x7f7450, size: -0x1
  }
}

// class id: 3478, size: 0x18, field offset: 0x14
//   const constructor, 
class Hva extends Hka<dynamic> {
}

// class id: 3559, size: 0xc, field offset: 0xc
//   const constructor, 
abstract class Bva extends cI {
}

// class id: 3560, size: 0x10, field offset: 0xc
//   const constructor, 
abstract class Cva extends Bva {
}

// class id: 3565, size: 0x14, field offset: 0x10
//   const constructor, 
class Fva extends Cva {
}

// class id: 3567, size: 0x10, field offset: 0x10
//   const constructor, 
class Dva extends Cva {
}
