// lib: , url: OZi

// class id: 1049193, size: 0x8
class :: {
}

// class id: 1693, size: 0xc, field offset: 0x8
class Vra extends Object {

  [closure] bool <anonymous closure>(dynamic, nI) {
    // ** addr: 0x35b77c, size: -0x1
  }
}

// class id: 1694, size: 0xc, field offset: 0x8
//   const constructor, 
class _Ura extends Object {

  bool dyn:get:dk(_Ura) {
    // ** addr: 0x8cfff0, size: 0x40
    // 0x8cfff0: ldr             x1, [SP]
    // 0x8cfff4: LoadField: r2 = r1->field_7
    //     0x8cfff4: ldur            w2, [x1, #7]
    // 0x8cfff8: DecompressPointer r2
    //     0x8cfff8: add             x2, x2, HEAP, lsl #32
    // 0x8cfffc: LoadField: r1 = r2->field_b
    //     0x8cfffc: ldur            w1, [x2, #0xb]
    // 0x8d0000: DecompressPointer r1
    //     0x8d0000: add             x1, x1, HEAP, lsl #32
    // 0x8d0004: cbnz            w1, #0x8d0010
    // 0x8d0008: r0 = false
    //     0x8d0008: add             x0, NULL, #0x30  ; false
    // 0x8d000c: b               #0x8d0014
    // 0x8d0010: r0 = true
    //     0x8d0010: add             x0, NULL, #0x20  ; true
    // 0x8d0014: ret
    //     0x8d0014: ret             
  }
}

// class id: 2617, size: 0x10, field offset: 0x10
//   const constructor, 
abstract class Tra<X0> extends CJ<X0> {
}

// class id: 3732, size: 0x14, field offset: 0xc
//   const constructor, 
class Wra extends Kt {
}
