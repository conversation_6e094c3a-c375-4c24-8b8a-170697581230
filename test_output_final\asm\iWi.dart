// lib: , url: IWi

// class id: 1049025, size: 0x8
class :: {
}

// class id: 2291, size: 0x8, field offset: 0x8
abstract class hZ extends Object {

  [closure] void <anonymous closure>(dynamic, bool) {
    // ** addr: 0x36879c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, bool) {
    // ** addr: 0x3695a4, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, bool) {
    // ** addr: 0x368e7c, size: -0x1
  }
}
