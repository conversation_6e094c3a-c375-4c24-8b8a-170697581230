// lib: , url: LPi

// class id: 1048697, size: 0x8
class :: {
}

// class id: 4507, size: 0x7c, field offset: 0x8
class Uy extends Object {

  Map<String, dynamic> WNb(Uy) {
    // ** addr: 0x903268, size: 0x48
    // 0x903268: EnterFrame
    //     0x903268: stp             fp, lr, [SP, #-0x10]!
    //     0x90326c: mov             fp, SP
    // 0x903270: CheckStackOverflow
    //     0x903270: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x903274: cmp             SP, x16
    //     0x903278: b.ls            #0x903290
    // 0x90327c: ldr             x1, [fp, #0x10]
    // 0x903280: r0 = call 0x55a460
    //     0x903280: bl              #0x55a460
    // 0x903284: LeaveFrame
    //     0x903284: mov             SP, fp
    //     0x903288: ldp             fp, lr, [SP], #0x10
    // 0x90328c: ret
    //     0x90328c: ret             
    // 0x903290: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x903290: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x903294: b               #0x90327c
  }
}
