// lib: , url: FTi

// class id: 1048883, size: 0x8
class :: {
}

// class id: 2496, size: 0x10, field offset: 0x8
//   const constructor, 
class NM extends Object {

  er field_8;
  er field_c;

  NM -(NM, NM) {
    // ** addr: 0x932cd0, size: 0x84
    // 0x932cd0: EnterFrame
    //     0x932cd0: stp             fp, lr, [SP, #-0x10]!
    //     0x932cd4: mov             fp, SP
    // 0x932cd8: CheckStackOverflow
    //     0x932cd8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x932cdc: cmp             SP, x16
    //     0x932ce0: b.ls            #0x932d34
    // 0x932ce4: ldr             x0, [fp, #0x10]
    // 0x932ce8: r2 = Null
    //     0x932ce8: mov             x2, NULL
    // 0x932cec: r1 = Null
    //     0x932cec: mov             x1, NULL
    // 0x932cf0: r4 = 59
    //     0x932cf0: movz            x4, #0x3b
    // 0x932cf4: branchIfSmi(r0, 0x932d00)
    //     0x932cf4: tbz             w0, #0, #0x932d00
    // 0x932cf8: r4 = LoadClassIdInstr(r0)
    //     0x932cf8: ldur            x4, [x0, #-1]
    //     0x932cfc: ubfx            x4, x4, #0xc, #0x14
    // 0x932d00: cmp             x4, #0x9c0
    // 0x932d04: b.eq            #0x932d1c
    // 0x932d08: r8 = NM
    //     0x932d08: add             x8, PP, #0x27, lsl #12  ; [pp+0x27260] Type: NM
    //     0x932d0c: ldr             x8, [x8, #0x260]
    // 0x932d10: r3 = Null
    //     0x932d10: add             x3, PP, #0x27, lsl #12  ; [pp+0x27268] Null
    //     0x932d14: ldr             x3, [x3, #0x268]
    // 0x932d18: r0 = DefaultTypeTest()
    //     0x932d18: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x932d1c: ldr             x1, [fp, #0x18]
    // 0x932d20: ldr             x2, [fp, #0x10]
    // 0x932d24: r0 = call 0x6cf9e8
    //     0x932d24: bl              #0x6cf9e8
    // 0x932d28: LeaveFrame
    //     0x932d28: mov             SP, fp
    //     0x932d2c: ldp             fp, lr, [SP], #0x10
    // 0x932d30: ret
    //     0x932d30: ret             
    // 0x932d34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x932d34: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x932d38: b               #0x932ce4
  }
  NM +(NM, NM) {
    // ** addr: 0x932d54, size: 0x84
    // 0x932d54: EnterFrame
    //     0x932d54: stp             fp, lr, [SP, #-0x10]!
    //     0x932d58: mov             fp, SP
    // 0x932d5c: CheckStackOverflow
    //     0x932d5c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x932d60: cmp             SP, x16
    //     0x932d64: b.ls            #0x932db8
    // 0x932d68: ldr             x0, [fp, #0x10]
    // 0x932d6c: r2 = Null
    //     0x932d6c: mov             x2, NULL
    // 0x932d70: r1 = Null
    //     0x932d70: mov             x1, NULL
    // 0x932d74: r4 = 59
    //     0x932d74: movz            x4, #0x3b
    // 0x932d78: branchIfSmi(r0, 0x932d84)
    //     0x932d78: tbz             w0, #0, #0x932d84
    // 0x932d7c: r4 = LoadClassIdInstr(r0)
    //     0x932d7c: ldur            x4, [x0, #-1]
    //     0x932d80: ubfx            x4, x4, #0xc, #0x14
    // 0x932d84: cmp             x4, #0x9c0
    // 0x932d88: b.eq            #0x932da0
    // 0x932d8c: r8 = NM
    //     0x932d8c: add             x8, PP, #0x27, lsl #12  ; [pp+0x27260] Type: NM
    //     0x932d90: ldr             x8, [x8, #0x260]
    // 0x932d94: r3 = Null
    //     0x932d94: add             x3, PP, #0x27, lsl #12  ; [pp+0x27278] Null
    //     0x932d98: ldr             x3, [x3, #0x278]
    // 0x932d9c: r0 = DefaultTypeTest()
    //     0x932d9c: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x932da0: ldr             x1, [fp, #0x18]
    // 0x932da4: ldr             x2, [fp, #0x10]
    // 0x932da8: r0 = call 0x6cfa74
    //     0x932da8: bl              #0x6cfa74
    // 0x932dac: LeaveFrame
    //     0x932dac: mov             SP, fp
    //     0x932db0: ldp             fp, lr, [SP], #0x10
    // 0x932db4: ret
    //     0x932db4: ret             
    // 0x932db8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x932db8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x932dbc: b               #0x932d68
  }
}

// class id: 2571, size: 0x8, field offset: 0x8
//   transformed mixin,
abstract class _LM extends UJ
     with Ru {
}

// class id: 2572, size: 0x18, field offset: 0x8
abstract class IM extends _LM {
}

// class id: 2574, size: 0x24, field offset: 0x18
abstract class bM extends IM {

  [closure] void tSd(dynamic, int) {
    // ** addr: 0x3ed2a0, size: -0x1
  }
}

// class id: 2586, size: 0x48, field offset: 0x24
abstract class kM extends bM {

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x6d00f0, size: -0x1
  }
  [closure] void jCc(dynamic, YJ) {
    // ** addr: 0x784ec8, size: -0x1
  }
}

// class id: 5581, size: 0x14, field offset: 0x14
enum MM extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5582, size: 0x14, field offset: 0x14
enum KM extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5583, size: 0x14, field offset: 0x14
enum sM extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
