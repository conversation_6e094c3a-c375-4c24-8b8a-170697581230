// lib: , url: OTi

// class id: 1048892, size: 0x8
class :: {
}

// class id: 3789, size: 0x18, field offset: 0xc
//   const constructor, 
class yN extends Kt {

  [closure] static qI <anonymous closure>(dynamic, Wla) {
    // ** addr: 0x67ebbc, size: -0x1
  }
  [closure] static OG <anonymous closure>(dynamic, Wla) {
    // ** addr: 0x67eb4c, size: -0x1
  }
  [closure] static HP <anonymous closure>(dynamic, Wla) {
    // ** addr: 0x67e9d0, size: -0x1
  }
}
