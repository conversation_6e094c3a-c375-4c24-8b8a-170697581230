// lib: , url: FQi

// class id: 1048742, size: 0x8
class :: {
}

// class id: 4459, size: 0x10, field offset: 0x8
//   const constructor, 
class zA<X0> extends Object
    implements wA<X0> {

  xA<Never> field_c;
}

// class id: 4461, size: 0xc, field offset: 0x8
//   const constructor, 
class xA<X0> extends Object
    implements wA<X0> {
}

// class id: 4462, size: 0xc, field offset: 0x8
abstract class wA<X0> extends Object {
}
