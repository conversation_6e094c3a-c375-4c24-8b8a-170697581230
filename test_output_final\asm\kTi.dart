// lib: , url: KTi

// class id: 1048888, size: 0x8
class :: {
}

// class id: 2486, size: 0x10, field offset: 0x8
class JM extends Object {

  [closure] _iN <anonymous closure>(dynamic) {
    // ** addr: 0x6cf3c0, size: -0x1
  }
}

// class id: 2487, size: 0x10, field offset: 0x8
class _hN extends Object
    implements VJ {
}

// class id: 2570, size: 0x24, field offset: 0x8
class _iN extends UJ {
}
