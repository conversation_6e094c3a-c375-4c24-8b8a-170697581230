// lib: , url: hcj

// class id: 1049324, size: 0x8
class :: {
}

// class id: 1212, size: 0xc, field offset: 0x8
class GIa extends Object {
}

// class id: 1214, size: 0xc, field offset: 0x8
class AIa extends Object {

  bool dyn:get:dk(AIa) {
    // ** addr: 0x8dc7c0, size: 0x64
    // 0x8dc7c0: EnterFrame
    //     0x8dc7c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8dc7c4: mov             fp, SP
    // 0x8dc7c8: CheckStackOverflow
    //     0x8dc7c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8dc7cc: cmp             SP, x16
    //     0x8dc7d0: b.ls            #0x8dc804
    // 0x8dc7d4: ldr             x0, [fp, #0x10]
    // 0x8dc7d8: LoadField: r1 = r0->field_7
    //     0x8dc7d8: ldur            w1, [x0, #7]
    // 0x8dc7dc: DecompressPointer r1
    //     0x8dc7dc: add             x1, x1, HEAP, lsl #32
    // 0x8dc7e0: r0 = LoadClassIdInstr(r1)
    //     0x8dc7e0: ldur            x0, [x1, #-1]
    //     0x8dc7e4: ubfx            x0, x0, #0xc, #0x14
    // 0x8dc7e8: r0 = GDT[cid_x0 + 0xaf64]()
    //     0x8dc7e8: movz            x17, #0xaf64
    //     0x8dc7ec: add             lr, x0, x17
    //     0x8dc7f0: ldr             lr, [x21, lr, lsl #3]
    //     0x8dc7f4: blr             lr
    // 0x8dc7f8: LeaveFrame
    //     0x8dc7f8: mov             SP, fp
    //     0x8dc7fc: ldp             fp, lr, [SP], #0x10
    // 0x8dc800: ret
    //     0x8dc800: ret             
    // 0x8dc804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8dc804: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8dc808: b               #0x8dc7d4
  }
  bool dyn:get:isEmpty(AIa) {
    // ** addr: 0x8dc824, size: 0x48
    // 0x8dc824: EnterFrame
    //     0x8dc824: stp             fp, lr, [SP, #-0x10]!
    //     0x8dc828: mov             fp, SP
    // 0x8dc82c: CheckStackOverflow
    //     0x8dc82c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8dc830: cmp             SP, x16
    //     0x8dc834: b.ls            #0x8dc84c
    // 0x8dc838: ldr             x1, [fp, #0x10]
    // 0x8dc83c: r0 = call 0x3fc828
    //     0x8dc83c: bl              #0x3fc828
    // 0x8dc840: LeaveFrame
    //     0x8dc840: mov             SP, fp
    //     0x8dc844: ldp             fp, lr, [SP], #0x10
    // 0x8dc848: ret
    //     0x8dc848: ret             
    // 0x8dc84c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8dc84c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8dc850: b               #0x8dc838
  }
}

// class id: 5749, size: 0x14, field offset: 0xc
class HIa extends Error {
}
