// lib: , url: Obj

// class id: 1049306, size: 0x8
class :: {
}

// class id: 3070, size: 0x1c, field offset: 0x14
abstract class _PHa extends Mt<dynamic> {

  [closure] void _EUf(dynamic) {
    // ** addr: 0x648958, size: -0x1
  }
  [closure] void _IUf(dynamic, r) {
    // ** addr: 0x5542c8, size: -0x1
  }
}

// class id: 3071, size: 0x28, field offset: 0x1c
class _QHa extends _PHa {

  [closure] pI _vUf(dynamic, aoa, int) {
    // ** addr: 0x6072b4, size: -0x1
  }
  [closure] void _yUf(dynamic, int) {
    // ** addr: 0x6071bc, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, Sta) {
    // ** addr: 0x607114, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x607280, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x6073c4, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x5544a4, size: -0x1
  }
}

// class id: 3864, size: 0x90, field offset: 0xc
class OHa extends It {
}

// class id: 5436, size: 0x14, field offset: 0x14
enum NHa extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
