// lib: , url: IXi

// class id: 1049079, size: 0x8
class :: {
}

// class id: 1943, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class Mea extends Object {
}

// class id: 1944, size: 0x34, field offset: 0x8
//   const constructor, 
class Nea extends Mea {
}

// class id: 1945, size: 0x28, field offset: 0x8
//   const constructor, 
class Lea extends Object {
}

// class id: 2020, size: 0x70, field offset: 0x6c
class Rea extends Jea {
}

// class id: 2209, size: 0x24, field offset: 0x20
class Pea extends Qea {
}

// class id: 2449, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class aP extends Object {
}

// class id: 2450, size: 0x2c, field offset: 0x8
//   const constructor, 
class Oea extends aP {
}
