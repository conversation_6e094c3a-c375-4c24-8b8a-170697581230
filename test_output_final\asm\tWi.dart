// lib: , url: TWi

// class id: 1049033, size: 0x8
class :: {
}

// class id: 2267, size: 0x14, field offset: 0x8
class G<PERSON> extends Object
    implements Ja {
}

// class id: 2269, size: 0x18, field offset: 0x8
//   const constructor, 
class xZ extends Object {
}

// class id: 2270, size: 0x20, field offset: 0x8
//   const constructor, 
class wZ extends Object {
}

// class id: 2308, size: 0xc, field offset: 0x8
//   const constructor, 
abstract class AY<X0> extends Object {

  [closure] Null <anonymous closure>(dynamic, X0) {
    // ** addr: 0x900030, size: 0xc0
    // 0x900030: EnterFrame
    //     0x900030: stp             fp, lr, [SP, #-0x10]!
    //     0x900034: mov             fp, SP
    // 0x900038: AllocStack(0x60)
    //     0x900038: sub             SP, SP, #0x60
    // 0x90003c: SetupParameters()
    //     0x90003c: ldr             x0, [fp, #0x18]
    //     0x900040: ldur            w1, [x0, #0x17]
    //     0x900044: add             x1, x1, HEAP, lsl #32
    //     0x900048: stur            x1, [fp, #-0x48]
    // 0x90004c: CheckStackOverflow
    //     0x90004c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x900050: cmp             SP, x16
    //     0x900054: b.ls            #0x9000e8
    // 0x900058: ldr             x0, [fp, #0x10]
    // 0x90005c: StoreField: r1->field_1b = r0
    //     0x90005c: stur            w0, [x1, #0x1b]
    //     0x900060: tbz             w0, #0, #0x90007c
    //     0x900064: ldurb           w16, [x1, #-1]
    //     0x900068: ldurb           w17, [x0, #-1]
    //     0x90006c: and             x16, x17, x16, lsr #2
    //     0x900070: tst             x16, HEAP, lsr #32
    //     0x900074: b.eq            #0x90007c
    //     0x900078: bl              #0x94e148  ; WriteBarrierWrappersStub
    // 0x90007c: LoadField: r2 = r1->field_13
    //     0x90007c: ldur            w2, [x1, #0x13]
    // 0x900080: DecompressPointer r2
    //     0x900080: add             x2, x2, HEAP, lsl #32
    // 0x900084: stur            x2, [fp, #-0x40]
    // 0x900088: LoadField: r0 = r1->field_23
    //     0x900088: ldur            w0, [x1, #0x23]
    // 0x90008c: DecompressPointer r0
    //     0x90008c: add             x0, x0, HEAP, lsl #32
    // 0x900090: ldr             x16, [fp, #0x10]
    // 0x900094: stp             x16, x2, [SP, #8]
    // 0x900098: str             x0, [SP]
    // 0x90009c: mov             x0, x2
    // 0x9000a0: ClosureCall
    //     0x9000a0: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x9000a4: ldur            x2, [x0, #0x1f]
    //     0x9000a8: blr             x2
    // 0x9000ac: b               #0x9000d8
    // 0x9000b0: sub             SP, fp, #0x60
    // 0x9000b4: ldur            x2, [fp, #-0x10]
    // 0x9000b8: LoadField: r3 = r2->field_23
    //     0x9000b8: ldur            w3, [x2, #0x23]
    // 0x9000bc: DecompressPointer r3
    //     0x9000bc: add             x3, x3, HEAP, lsl #32
    // 0x9000c0: stp             x0, x3, [SP, #8]
    // 0x9000c4: str             x1, [SP]
    // 0x9000c8: mov             x0, x3
    // 0x9000cc: ClosureCall
    //     0x9000cc: ldr             x4, [PP, #0x460]  ; [pp+0x460] List(5) [0, 0x3, 0x3, 0x3, Null]
    //     0x9000d0: ldur            x2, [x0, #0x1f]
    //     0x9000d4: blr             x2
    // 0x9000d8: r0 = Null
    //     0x9000d8: mov             x0, NULL
    // 0x9000dc: LeaveFrame
    //     0x9000dc: mov             SP, fp
    //     0x9000e0: ldp             fp, lr, [SP], #0x10
    // 0x9000e4: ret
    //     0x9000e4: ret             
    // 0x9000e8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9000e8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9000ec: b               #0x900058
  }
  [closure] Future<void> yqb(dynamic, Object, ua?) async {
    // ** addr: 0x9000f0, size: 0xb4
    // 0x9000f0: EnterFrame
    //     0x9000f0: stp             fp, lr, [SP, #-0x10]!
    //     0x9000f4: mov             fp, SP
    // 0x9000f8: AllocStack(0x40)
    //     0x9000f8: sub             SP, SP, #0x40
    // 0x9000fc: SetupParameters(AY<X0> this /* r1 */, dynamic _ /* r2, fp-0x20 */, dynamic _ /* r3, fp-0x18 */)
    //     0x9000fc: stur            NULL, [fp, #-8]
    //     0x900100: movz            x0, #0
    //     0x900104: add             x1, fp, w0, sxtw #2
    //     0x900108: ldr             x1, [x1, #0x20]
    //     0x90010c: add             x2, fp, w0, sxtw #2
    //     0x900110: ldr             x2, [x2, #0x18]
    //     0x900114: stur            x2, [fp, #-0x20]
    //     0x900118: add             x3, fp, w0, sxtw #2
    //     0x90011c: ldr             x3, [x3, #0x10]
    //     0x900120: stur            x3, [fp, #-0x18]
    //     0x900124: ldur            w4, [x1, #0x17]
    //     0x900128: add             x4, x4, HEAP, lsl #32
    //     0x90012c: stur            x4, [fp, #-0x10]
    // 0x900130: CheckStackOverflow
    //     0x900130: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x900134: cmp             SP, x16
    //     0x900138: b.ls            #0x90019c
    // 0x90013c: InitAsync() -> Future<void?>
    //     0x90013c: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x900140: bl              #0x8c1de0  ; InitAsyncStub
    // 0x900144: ldur            x0, [fp, #-0x10]
    // 0x900148: LoadField: r1 = r0->field_1f
    //     0x900148: ldur            w1, [x0, #0x1f]
    // 0x90014c: DecompressPointer r1
    //     0x90014c: add             x1, x1, HEAP, lsl #32
    // 0x900150: tbnz            w1, #4, #0x90015c
    // 0x900154: r0 = Null
    //     0x900154: mov             x0, NULL
    // 0x900158: r0 = ReturnAsyncNotFuture()
    //     0x900158: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x90015c: r1 = true
    //     0x90015c: add             x1, NULL, #0x20  ; true
    // 0x900160: StoreField: r0->field_1f = r1
    //     0x900160: stur            w1, [x0, #0x1f]
    // 0x900164: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x900164: ldur            w1, [x0, #0x17]
    // 0x900168: DecompressPointer r1
    //     0x900168: add             x1, x1, HEAP, lsl #32
    // 0x90016c: LoadField: r2 = r0->field_1b
    //     0x90016c: ldur            w2, [x0, #0x1b]
    // 0x900170: DecompressPointer r2
    //     0x900170: add             x2, x2, HEAP, lsl #32
    // 0x900174: stp             x2, x1, [SP, #0x10]
    // 0x900178: ldur            x16, [fp, #-0x20]
    // 0x90017c: ldur            lr, [fp, #-0x18]
    // 0x900180: stp             lr, x16, [SP]
    // 0x900184: mov             x0, x1
    // 0x900188: ClosureCall
    //     0x900188: ldr             x4, [PP, #0x698]  ; [pp+0x698] List(5) [0, 0x4, 0x4, 0x4, Null]
    //     0x90018c: ldur            x2, [x0, #0x1f]
    //     0x900190: blr             x2
    // 0x900194: r0 = Null
    //     0x900194: mov             x0, NULL
    // 0x900198: r0 = ReturnAsyncNotFuture()
    //     0x900198: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x90019c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90019c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9001a0: b               #0x90013c
  }
  [closure] Future<void> <anonymous closure>(dynamic, X0?, Object, ua?) async {
    // ** addr: 0x9001b0, size: 0x100
    // 0x9001b0: EnterFrame
    //     0x9001b0: stp             fp, lr, [SP, #-0x10]!
    //     0x9001b4: mov             fp, SP
    // 0x9001b8: AllocStack(0x30)
    //     0x9001b8: sub             SP, SP, #0x30
    // 0x9001bc: SetupParameters(AY<X0> this /* r1 */, dynamic _ /* r2, fp-0x20 */, dynamic _ /* r3, fp-0x18 */)
    //     0x9001bc: stur            NULL, [fp, #-8]
    //     0x9001c0: movz            x0, #0
    //     0x9001c4: add             x1, fp, w0, sxtw #2
    //     0x9001c8: ldr             x1, [x1, #0x28]
    //     0x9001cc: add             x2, fp, w0, sxtw #2
    //     0x9001d0: ldr             x2, [x2, #0x18]
    //     0x9001d4: stur            x2, [fp, #-0x20]
    //     0x9001d8: add             x3, fp, w0, sxtw #2
    //     0x9001dc: ldr             x3, [x3, #0x10]
    //     0x9001e0: stur            x3, [fp, #-0x18]
    //     0x9001e4: ldur            w4, [x1, #0x17]
    //     0x9001e8: add             x4, x4, HEAP, lsl #32
    //     0x9001ec: stur            x4, [fp, #-0x10]
    // 0x9001f0: CheckStackOverflow
    //     0x9001f0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9001f4: cmp             SP, x16
    //     0x9001f8: b.ls            #0x9002a4
    // 0x9001fc: InitAsync() -> Future<void?>
    //     0x9001fc: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x900200: bl              #0x8c1de0  ; InitAsyncStub
    // 0x900204: r0 = Null
    //     0x900204: mov             x0, NULL
    // 0x900208: r0 = Await()
    //     0x900208: bl              #0x8c1bb8  ; AwaitStub
    // 0x90020c: ldur            x0, [fp, #-0x10]
    // 0x900210: LoadField: r1 = r0->field_13
    //     0x900210: ldur            w1, [x0, #0x13]
    // 0x900214: DecompressPointer r1
    //     0x900214: add             x1, x1, HEAP, lsl #32
    // 0x900218: stur            x1, [fp, #-0x28]
    // 0x90021c: LoadField: r0 = r1->field_7
    //     0x90021c: ldur            w0, [x1, #7]
    // 0x900220: DecompressPointer r0
    //     0x900220: add             x0, x0, HEAP, lsl #32
    // 0x900224: cmp             w0, NULL
    // 0x900228: b.ne            #0x900248
    // 0x90022c: r0 = _EZ()
    //     0x90022c: bl              #0x9006e4  ; Allocate_EZStub -> _EZ (size=0x34)
    // 0x900230: mov             x1, x0
    // 0x900234: stur            x0, [fp, #-0x10]
    // 0x900238: r0 = call 0x532084
    //     0x900238: bl              #0x532084
    // 0x90023c: ldur            x1, [fp, #-0x28]
    // 0x900240: ldur            x2, [fp, #-0x10]
    // 0x900244: r0 = call 0x531e6c
    //     0x900244: bl              #0x531e6c
    // 0x900248: ldur            x0, [fp, #-0x28]
    // 0x90024c: LoadField: r2 = r0->field_7
    //     0x90024c: ldur            w2, [x0, #7]
    // 0x900250: DecompressPointer r2
    //     0x900250: add             x2, x2, HEAP, lsl #32
    // 0x900254: stur            x2, [fp, #-0x10]
    // 0x900258: cmp             w2, NULL
    // 0x90025c: b.eq            #0x9002ac
    // 0x900260: r1 = <List<Object>>
    //     0x900260: ldr             x1, [PP, #0x2468]  ; [pp+0x2468] TypeArguments: <List<Object>>
    // 0x900264: r0 = OI()
    //     0x900264: bl              #0x8c4f9c  ; AllocateOIStub -> OI (size=0x2c)
    // 0x900268: mov             x1, x0
    // 0x90026c: r2 = "while resolving an image"
    //     0x90026c: add             x2, PP, #0x2c, lsl #12  ; [pp+0x2c2d8] "while resolving an image"
    //     0x900270: ldr             x2, [x2, #0x2d8]
    // 0x900274: r3 = Instance_GI
    //     0x900274: ldr             x3, [PP, #0x2478]  ; [pp+0x2478] Obj!GI@6a4711
    // 0x900278: r0 = call 0x305040
    //     0x900278: bl              #0x305040
    // 0x90027c: r16 = true
    //     0x90027c: add             x16, NULL, #0x20  ; true
    // 0x900280: str             x16, [SP]
    // 0x900284: ldur            x1, [fp, #-0x10]
    // 0x900288: ldur            x2, [fp, #-0x20]
    // 0x90028c: ldur            x3, [fp, #-0x18]
    // 0x900290: r4 = const [0, 0x4, 0x1, 0x3, Vuc, 0x3, null]
    //     0x900290: add             x4, PP, #0x2c, lsl #12  ; [pp+0x2c2e0] List(7) [0, 0x4, 0x1, 0x3, "Vuc", 0x3, Null]
    //     0x900294: ldr             x4, [x4, #0x2e0]
    // 0x900298: r0 = __unknown_function__()
    //     0x900298: bl              #0x9002b0  ; [] ::__unknown_function__
    // 0x90029c: r0 = Null
    //     0x90029c: mov             x0, NULL
    // 0x9002a0: r0 = ReturnAsyncNotFuture()
    //     0x9002a0: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x9002a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9002a4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9002a8: b               #0x9001fc
    // 0x9002ac: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9002ac: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] void <anonymous closure>(dynamic, X0, (dynamic, Object, ua?) => void) {
    // ** addr: 0x532160, size: -0x1
  }
  [closure] FZ <anonymous closure>(dynamic) {
    // ** addr: 0x754c9c, size: -0x1
  }
  [closure] FZ <anonymous closure>(dynamic) {
    // ** addr: 0x754bf8, size: -0x1
  }
}

// class id: 2310, size: 0x18, field offset: 0xc
//   const constructor, 
class CZ extends AY<dynamic> {
}

// class id: 2312, size: 0xc, field offset: 0xc
//   const constructor, 
abstract class yZ extends AY<dynamic> {
}

// class id: 2314, size: 0x20, field offset: 0xc
//   const constructor, 
class DZ extends yZ {
}

// class id: 2316, size: 0xc, field offset: 0xc
abstract class zY extends AY<dynamic> {
}

// class id: 2841, size: 0x34, field offset: 0x34
class _EZ extends FZ {
}
