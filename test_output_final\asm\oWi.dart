// lib: , url: OWi

// class id: 1049031, size: 0x8
class :: {

  [closure] static mr <anonymous closure>(dynamic, double) {
    // ** addr: 0x724be4, size: -0x1
  }
  [closure] static bool <anonymous closure>(dynamic, double) {
    // ** addr: 0x725050, size: -0x1
  }
}

// class id: 2275, size: 0x14, field offset: 0x8
//   const constructor, 
abstract class Lr extends Object {
}

// class id: 2276, size: 0x20, field offset: 0x14
//   const constructor, 
class vZ extends Lr {

  _ImmutableList<mr> field_8;
  fu field_14;
  fu field_18;
  Kr field_1c;

  [closure] mr <anonymous closure>(dynamic, mr) {
    // ** addr: 0x724880, size: -0x1
  }
}

// class id: 2278, size: 0x10, field offset: 0x8
class _tZ extends Object {
}
