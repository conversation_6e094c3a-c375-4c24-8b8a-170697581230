// lib: tqj, url: qjj

// class id: 1049649, size: 0x8
class :: {
}

// class id: 784, size: 0xc, field offset: 0xc
class mUa extends OTa {

  static late final vWa iog; // offset: 0x1214

  [closure] static mUa <anonymous closure>(dynamic) {
    // ** addr: 0x4726dc, size: -0x1
  }
  [closure] static mUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>?) {
    // ** addr: 0x4727e0, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x472684, size: -0x1
  }
}
