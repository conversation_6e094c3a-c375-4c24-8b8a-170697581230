// lib: , url: HTi

// class id: 1048885, size: 0x8
class :: {
}

// class id: 2490, size: 0x20, field offset: 0x8
class _UM extends Object {
}

// class id: 2491, size: 0xc, field offset: 0x8
class TM extends Object {
}

// class id: 2492, size: 0x14, field offset: 0x8
class SM extends Object {
}

// class id: 2493, size: 0xc, field offset: 0x8
class RM extends Object {
}

// class id: 2494, size: 0x24, field offset: 0x8
class _QM extends Object {
}

// class id: 2580, size: 0x98, field offset: 0x24
class VM extends bM {

  late double _cBe; // offset: 0x4c
  late double _bBe; // offset: 0x48
  late er _ZAe; // offset: 0x40
  late er _hBe; // offset: 0x60
  late er _mBe; // offset: 0x7c
  late double _fBe; // offset: 0x58
  late double _gBe; // offset: 0x5c
  late double _dBe; // offset: 0x50
  late double _eBe; // offset: 0x54

  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x788bf0, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x7888a8, size: -0x1
  }
  [closure] double <anonymous closure>(dynamic, _QM) {
    // ** addr: 0x789cb8, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x789be4, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x789b20, size: -0x1
  }
  [closure] void jCc(dynamic, YJ) {
    // ** addr: 0x78b588, size: -0x1
  }
}

// class id: 5580, size: 0x14, field offset: 0x14
enum _PM extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
