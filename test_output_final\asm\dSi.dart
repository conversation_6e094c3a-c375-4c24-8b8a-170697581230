// lib: , url: DSi

// class id: 1048830, size: 0x8
class :: {
}

// class id: 3269, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class _DG extends Mt<dynamic>
     with Eu<X0 bound It> {

  [closure] void _wJc(dynamic) {
    // ** addr: 0x3e4ce0, size: -0x1
  }
}

// class id: 3270, size: 0x2c, field offset: 0x1c
class _EG extends _DG {

  late lF<double> _TPd; // offset: 0x24
  late yF _vFc; // offset: 0x20

  [closure] void _Lje(dynamic, DM) {
    // ** addr: 0x5b9048, size: -0x1
  }
  [closure] void _Mje(dynamic, EM) {
    // ** addr: 0x5b900c, size: -0x1
  }
  [closure] void _Nje(dynamic) {
    // ** addr: 0x5b8e04, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, void) {
    // ** addr: 0x5b8f9c, size: -0x1
  }
}

// class id: 4025, size: 0x3c, field offset: 0xc
//   const constructor, 
class CG extends It {
}
