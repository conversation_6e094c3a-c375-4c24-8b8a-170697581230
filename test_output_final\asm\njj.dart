// lib: Ypj, url: Njj

// class id: 1049701, size: 0x8
class :: {
}

// class id: 721, size: 0x8, field offset: 0x8
class oWa extends Object
    implements dSa {

  static late final vWa iog; // offset: 0x11c0

  [closure] static (dynamic) => oWa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x460620, size: -0x1
  }
  [closure] static oWa <anonymous closure>(dynamic) {
    // ** addr: 0x4606bc, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x4605bc, size: -0x1
  }
}
