// lib: , url: OPi

// class id: 1048700, size: 0x8
class :: {
}

// class id: 4503, size: 0x20, field offset: 0x8
class Yy extends Object {

  Map<String, dynamic> WNb(Yy) {
    // ** addr: 0x8f0200, size: 0x48
    // 0x8f0200: EnterFrame
    //     0x8f0200: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0204: mov             fp, SP
    // 0x8f0208: CheckStackOverflow
    //     0x8f0208: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f020c: cmp             SP, x16
    //     0x8f0210: b.ls            #0x8f0228
    // 0x8f0214: ldr             x1, [fp, #0x10]
    // 0x8f0218: r0 = call 0x4005c8
    //     0x8f0218: bl              #0x4005c8
    // 0x8f021c: LeaveFrame
    //     0x8f021c: mov             SP, fp
    //     0x8f0220: ldp             fp, lr, [SP], #0x10
    // 0x8f0224: ret
    //     0x8f0224: ret             
    // 0x8f0228: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f0228: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f022c: b               #0x8f0214
  }
}
