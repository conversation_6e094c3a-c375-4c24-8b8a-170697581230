// lib: , url: NYi

// class id: 1049144, size: 0x8
class :: {
}

// class id: 1798, size: 0x1c, field offset: 0x8
//   const constructor, 
class Wja<X0> extends Object {
}

// class id: 3163, size: 0x1c, field offset: 0x14
class _Zja<C1X0> extends Mt<C1X0> {

  late Wja<C1X0> _dxd; // offset: 0x18

  [closure] Null <anonymous closure>(dynamic, C1X0) {
    // ** addr: 0x52d2fc, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Object, ua) {
    // ** addr: 0x52d18c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x52d230, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x52d398, size: -0x1
  }
}

// class id: 3164, size: 0x1c, field offset: 0x14
class _Uja<C1X0, C1X1> extends Mt<C1X0> {

  late C1X1 _sxd; // offset: 0x18

  [closure] void <anonymous closure>(dynamic, C1X0) {
    // ** addr: 0x52cc3c, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Object, ua) {
    // ** addr: 0x52ca80, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x52c914, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x52c974, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x52cb0c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x52ccc0, size: -0x1
  }
}

// class id: 3929, size: 0x1c, field offset: 0xc
//   const constructor, 
class Yja<X0> extends It {
}

// class id: 3930, size: 0x14, field offset: 0xc
//   const constructor, 
abstract class Tja<X0, X1> extends It {
}

// class id: 3931, size: 0x1c, field offset: 0x14
//   const constructor, 
class Xja<C2X0> extends Tja<C2X0, dynamic> {
}

// class id: 5483, size: 0x14, field offset: 0x14
enum Vja extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
