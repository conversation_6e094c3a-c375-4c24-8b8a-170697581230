// lib: lpj, url: Kij

// class id: 1049617, size: 0x8
class :: {
}

// class id: 822, size: 0x18, field offset: 0x18
class USa extends VSa {

  static late final vWa iog; // offset: 0x1100
  static late final RegExp _pqg; // offset: 0x10fc

  [closure] static (dynamic) => USa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x4813c8, size: -0x1
  }
  [closure] static USa <anonymous closure>(dynamic) {
    // ** addr: 0x48141c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x48133c, size: -0x1
  }
}
