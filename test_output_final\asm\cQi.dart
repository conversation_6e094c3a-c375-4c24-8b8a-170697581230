// lib: , url: CQi

// class id: 1048739, size: 0x8
class :: {
}

// class id: 4465, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _tA extends Object
     with B<X0> {

  void Ij(_tA, (dynamic, bool) => void) {
    // ** addr: 0x8c5f68, size: 0x6c
    // 0x8c5f68: EnterFrame
    //     0x8c5f68: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5f6c: mov             fp, SP
    // 0x8c5f70: CheckStackOverflow
    //     0x8c5f70: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5f74: cmp             SP, x16
    //     0x8c5f78: b.ls            #0x8c5fb4
    // 0x8c5f7c: ldr             x0, [fp, #0x10]
    // 0x8c5f80: r2 = Null
    //     0x8c5f80: mov             x2, NULL
    // 0x8c5f84: r1 = Null
    //     0x8c5f84: mov             x1, NULL
    // 0x8c5f88: r8 = (dynamic this, bool) => void?
    //     0x8c5f88: add             x8, PP, #0x34, lsl #12  ; [pp+0x344a0] FunctionType: (dynamic this, bool) => void?
    //     0x8c5f8c: ldr             x8, [x8, #0x4a0]
    // 0x8c5f90: r3 = Null
    //     0x8c5f90: add             x3, PP, #0x34, lsl #12  ; [pp+0x344a8] Null
    //     0x8c5f94: ldr             x3, [x3, #0x4a8]
    // 0x8c5f98: r0 = DefaultTypeTest()
    //     0x8c5f98: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8c5f9c: ldr             x1, [fp, #0x18]
    // 0x8c5fa0: ldr             x2, [fp, #0x10]
    // 0x8c5fa4: r0 = call 0x3e8518
    //     0x8c5fa4: bl              #0x3e8518
    // 0x8c5fa8: LeaveFrame
    //     0x8c5fa8: mov             SP, fp
    //     0x8c5fac: ldp             fp, lr, [SP], #0x10
    // 0x8c5fb0: ret
    //     0x8c5fb0: ret             
    // 0x8c5fb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5fb4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5fb8: b               #0x8c5f7c
  }
  Iterable<Y0> ok<Y0>(_tA, (dynamic, bool) => Y0) {
    // ** addr: 0x8c5fbc, size: 0x98
    // 0x8c5fbc: EnterFrame
    //     0x8c5fbc: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5fc0: mov             fp, SP
    // 0x8c5fc4: AllocStack(0x20)
    //     0x8c5fc4: sub             SP, SP, #0x20
    // 0x8c5fc8: SetupParameters()
    //     0x8c5fc8: ldur            w0, [x4, #0xf]
    //     0x8c5fcc: add             x0, x0, HEAP, lsl #32
    //     0x8c5fd0: cbnz            w0, #0x8c5fdc
    //     0x8c5fd4: mov             x3, NULL
    //     0x8c5fd8: b               #0x8c5ff0
    //     0x8c5fdc: ldur            w0, [x4, #0x17]
    //     0x8c5fe0: add             x0, x0, HEAP, lsl #32
    //     0x8c5fe4: add             x1, fp, w0, sxtw #2
    //     0x8c5fe8: ldr             x1, [x1, #0x10]
    //     0x8c5fec: mov             x3, x1
    //     0x8c5ff0: stur            x3, [fp, #-8]
    // 0x8c5ff4: CheckStackOverflow
    //     0x8c5ff4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5ff8: cmp             SP, x16
    //     0x8c5ffc: b.ls            #0x8c604c
    // 0x8c6000: ldr             x0, [fp, #0x10]
    // 0x8c6004: mov             x1, x3
    // 0x8c6008: r2 = Null
    //     0x8c6008: mov             x2, NULL
    // 0x8c600c: r8 = (dynamic this, bool) => Y0
    //     0x8c600c: add             x8, PP, #0x34, lsl #12  ; [pp+0x34468] FunctionType: (dynamic this, bool) => Y0
    //     0x8c6010: ldr             x8, [x8, #0x468]
    // 0x8c6014: LoadField: r9 = r8->field_7
    //     0x8c6014: ldur            x9, [x8, #7]
    // 0x8c6018: r3 = Null
    //     0x8c6018: add             x3, PP, #0x34, lsl #12  ; [pp+0x34470] Null
    //     0x8c601c: ldr             x3, [x3, #0x470]
    // 0x8c6020: blr             x9
    // 0x8c6024: ldur            x16, [fp, #-8]
    // 0x8c6028: ldr             lr, [fp, #0x18]
    // 0x8c602c: stp             lr, x16, [SP, #8]
    // 0x8c6030: ldr             x16, [fp, #0x10]
    // 0x8c6034: str             x16, [SP]
    // 0x8c6038: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8c6038: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8c603c: r0 = call 0x3eb500
    //     0x8c603c: bl              #0x3eb500
    // 0x8c6040: LeaveFrame
    //     0x8c6040: mov             SP, fp
    //     0x8c6044: ldp             fp, lr, [SP], #0x10
    // 0x8c6048: ret
    //     0x8c6048: ret             
    // 0x8c604c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c604c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6050: b               #0x8c6000
  }
  bool dyn:get:Kj(_tA) {
    // ** addr: 0x8c606c, size: 0x48
    // 0x8c606c: EnterFrame
    //     0x8c606c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6070: mov             fp, SP
    // 0x8c6074: CheckStackOverflow
    //     0x8c6074: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c6078: cmp             SP, x16
    //     0x8c607c: b.ls            #0x8c6094
    // 0x8c6080: ldr             x1, [fp, #0x10]
    // 0x8c6084: r0 = call 0x3eddc8
    //     0x8c6084: bl              #0x3eddc8
    // 0x8c6088: LeaveFrame
    //     0x8c6088: mov             SP, fp
    //     0x8c608c: ldp             fp, lr, [SP], #0x10
    // 0x8c6090: ret
    //     0x8c6090: ret             
    // 0x8c6094: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6094: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6098: b               #0x8c6080
  }
  int Kk(_tA, (dynamic, bool) => bool, [int]) {
    // ** addr: 0x8c609c, size: 0xdc
    // 0x8c609c: EnterFrame
    //     0x8c609c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c60a0: mov             fp, SP
    // 0x8c60a4: AllocStack(0x20)
    //     0x8c60a4: sub             SP, SP, #0x20
    // 0x8c60a8: SetupParameters(_tA this /* r3, fp-0x18 */, dynamic _ /* r4, fp-0x10 */, [dynamic _ = 0 /* r5, fp-0x8 */])
    //     0x8c60a8: ldur            w0, [x4, #0x13]
    //     0x8c60ac: add             x0, x0, HEAP, lsl #32
    //     0x8c60b0: sub             x1, x0, #4
    //     0x8c60b4: add             x3, fp, w1, sxtw #2
    //     0x8c60b8: ldr             x3, [x3, #0x18]
    //     0x8c60bc: stur            x3, [fp, #-0x18]
    //     0x8c60c0: add             x4, fp, w1, sxtw #2
    //     0x8c60c4: ldr             x4, [x4, #0x10]
    //     0x8c60c8: stur            x4, [fp, #-0x10]
    //     0x8c60cc: cmp             w1, #2
    //     0x8c60d0: b.lt            #0x8c60e4
    //     0x8c60d4: add             x0, fp, w1, sxtw #2
    //     0x8c60d8: ldr             x0, [x0, #8]
    //     0x8c60dc: mov             x5, x0
    //     0x8c60e0: b               #0x8c60e8
    //     0x8c60e4: movz            x5, #0
    //     0x8c60e8: stur            x5, [fp, #-8]
    // 0x8c60ec: CheckStackOverflow
    //     0x8c60ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c60f0: cmp             SP, x16
    //     0x8c60f4: b.ls            #0x8c6170
    // 0x8c60f8: mov             x0, x4
    // 0x8c60fc: r2 = Null
    //     0x8c60fc: mov             x2, NULL
    // 0x8c6100: r1 = Null
    //     0x8c6100: mov             x1, NULL
    // 0x8c6104: r8 = (dynamic this, bool) => bool
    //     0x8c6104: add             x8, PP, #0x34, lsl #12  ; [pp+0x343d0] FunctionType: (dynamic this, bool) => bool
    //     0x8c6108: ldr             x8, [x8, #0x3d0]
    // 0x8c610c: r3 = Null
    //     0x8c610c: add             x3, PP, #0x34, lsl #12  ; [pp+0x343d8] Null
    //     0x8c6110: ldr             x3, [x3, #0x3d8]
    // 0x8c6114: r0 = DefaultTypeTest()
    //     0x8c6114: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8c6118: ldur            x0, [fp, #-8]
    // 0x8c611c: r2 = Null
    //     0x8c611c: mov             x2, NULL
    // 0x8c6120: r1 = Null
    //     0x8c6120: mov             x1, NULL
    // 0x8c6124: branchIfSmi(r0, 0x8c614c)
    //     0x8c6124: tbz             w0, #0, #0x8c614c
    // 0x8c6128: r4 = LoadClassIdInstr(r0)
    //     0x8c6128: ldur            x4, [x0, #-1]
    //     0x8c612c: ubfx            x4, x4, #0xc, #0x14
    // 0x8c6130: sub             x4, x4, #0x3b
    // 0x8c6134: cmp             x4, #1
    // 0x8c6138: b.ls            #0x8c614c
    // 0x8c613c: r8 = int
    //     0x8c613c: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8c6140: r3 = Null
    //     0x8c6140: add             x3, PP, #0x34, lsl #12  ; [pp+0x343e8] Null
    //     0x8c6144: ldr             x3, [x3, #0x3e8]
    // 0x8c6148: r0 = int()
    //     0x8c6148: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8c614c: ldur            x16, [fp, #-8]
    // 0x8c6150: str             x16, [SP]
    // 0x8c6154: ldur            x1, [fp, #-0x18]
    // 0x8c6158: ldur            x2, [fp, #-0x10]
    // 0x8c615c: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8c615c: ldr             x4, [PP, #0x2b0]  ; [pp+0x2b0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8c6160: r0 = call 0x3059d0
    //     0x8c6160: bl              #0x3059d0
    // 0x8c6164: LeaveFrame
    //     0x8c6164: mov             SP, fp
    //     0x8c6168: ldp             fp, lr, [SP], #0x10
    // 0x8c616c: ret
    //     0x8c616c: ret             
    // 0x8c6170: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c6170: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c6174: b               #0x8c60f8
  }
  List<bool> +(_tA, List<bool>) {
    // ** addr: 0x8c6190, size: 0x4c
    // 0x8c6190: EnterFrame
    //     0x8c6190: stp             fp, lr, [SP, #-0x10]!
    //     0x8c6194: mov             fp, SP
    // 0x8c6198: CheckStackOverflow
    //     0x8c6198: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c619c: cmp             SP, x16
    //     0x8c61a0: b.ls            #0x8c61bc
    // 0x8c61a4: ldr             x1, [fp, #0x18]
    // 0x8c61a8: ldr             x2, [fp, #0x10]
    // 0x8c61ac: r0 = call 0x305b30
    //     0x8c61ac: bl              #0x305b30
    // 0x8c61b0: LeaveFrame
    //     0x8c61b0: mov             SP, fp
    //     0x8c61b4: ldp             fp, lr, [SP], #0x10
    // 0x8c61b8: ret
    //     0x8c61b8: ret             
    // 0x8c61bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c61bc: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c61c0: b               #0x8c61a4
  }
  [closure] void add(dynamic, Object?) {
    // ** addr: 0x30598c, size: -0x1
  }
  [closure] bool ek(dynamic, Object?) {
    // ** addr: 0x3dfab4, size: -0x1
  }
}
