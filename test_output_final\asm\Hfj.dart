// lib: wqj, url: hfj

// class id: 1049652, size: 0x8
class :: {
}

// class id: 781, size: 0xc, field offset: 0xc
class sUa extends OTa {

  static late final vWa iog; // offset: 0x1220

  [closure] static sUa <anonymous closure>(dynamic) {
    // ** addr: 0x472268, size: -0x1
  }
  [closure] static sUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>?) {
    // ** addr: 0x47236c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x472210, size: -0x1
  }
}
