// lib: , url: DWi

// class id: 1049020, size: 0x8
class :: {
}

// class id: 2355, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class YY extends PS {
}

// class id: 2357, size: 0x18, field offset: 0x8
//   const constructor, 
class ZY extends YY {

  BO field_8;
  BO field_c;
  BO field_10;
  BO field_14;
}

// class id: 5535, size: 0x14, field offset: 0x14
enum XY extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
