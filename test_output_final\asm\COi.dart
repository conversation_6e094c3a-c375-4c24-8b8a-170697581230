// lib: , url: cOi

// class id: 1048852, size: 0x8
class :: {
}

// class id: 4156, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class mF extends Object {
}

// class id: 4163, size: 0xc, field offset: 0x8
class _hJ extends mF {
}

// class id: 4191, size: 0xc, field offset: 0x8
//   const constructor, 
abstract class nF<X0> extends mF {
}

// class id: 4515, size: 0x24, field offset: 0x8
class Pu extends Object
    implements mF {

  static late final List<((dynamic) => void)?> _Bvc; // offset: 0x7dc

  [closure] void Hvc(dynamic) {
    // ** addr: 0x5250b4, size: -0x1
  }
  [closure] void hbc(dynamic) {
    // ** addr: 0x656b0c, size: -0x1
  }
}

// class id: 4580, size: 0x2c, field offset: 0x24
class iJ<X0> extends Pu
    implements nF<X0> {
}
