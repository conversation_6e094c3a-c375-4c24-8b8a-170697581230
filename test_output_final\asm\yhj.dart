// lib: krj, url: yhj

// class id: 1049575, size: 0x8
class :: {
}

// class id: 882, size: 0x10, field offset: 0x8
//   const constructor, 
class DQa extends Object {

  _Mint field_8;
}

// class id: 883, size: 0x10, field offset: 0x10
//   const constructor, 
class GQa extends DQa {

  _Mint field_8;
}

// class id: 4430, size: 0x8, field offset: 0x8
abstract class MQa extends QA {

  static late MQa _Alc; // offset: 0x13e8
  static late final Object _Esc; // offset: 0x13e4
}

// class id: 5415, size: 0x14, field offset: 0x14
enum EQa extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
