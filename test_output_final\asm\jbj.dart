// lib: , url: Jbj

// class id: 1049301, size: 0x8
class :: {
}

// class id: 2018, size: 0xa0, field offset: 0x6c
class xHa extends Jea {

  [closure] int <anonymous closure>(dynamic, int) {
    // ** addr: 0x3bdfec, size: -0x1
  }
  [closure] double Odd(dynamic, iI) {
    // ** addr: 0x3bdd64, size: -0x1
  }
  [closure] wHa vsi(dynamic) {
    // ** addr: 0x3bdb48, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, double) {
    // ** addr: 0x3bdb20, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, double) {
    // ** addr: 0x3bdaec, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, double) {
    // ** addr: 0x3bd7fc, size: -0x1
  }
  [closure] bool vWd(dynamic) {
    // ** addr: 0x3bd338, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, double) {
    // ** addr: 0x3bd2f4, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, double) {
    // ** addr: 0x3bd2b0, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, double) {
    // ** addr: 0x3bd74c, size: -0x1
  }
}

// class id: 2208, size: 0x28, field offset: 0x20
class wHa extends Qea {
}
