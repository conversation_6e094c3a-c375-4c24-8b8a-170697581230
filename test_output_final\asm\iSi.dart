// lib: , url: ISi

// class id: 1048834, size: 0x8
class :: {
}

// class id: 3268, size: 0x18, field offset: 0x14
class _PG extends Mt<dynamic> {

  [closure] void _SSe(dynamic, QK) {
    // ** addr: 0x5b9444, size: -0x1
  }
  [closure] void _TSe(dynamic, VK) {
    // ** addr: 0x5b9380, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x5b9420, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x5b94e4, size: -0x1
  }
}

// class id: 4024, size: 0x1c, field offset: 0xc
//   const constructor, 
class OG extends It {
}
