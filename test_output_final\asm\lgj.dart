// lib: Noj, url: lgj

// class id: 1049579, size: 0x8
class :: {
}

// class id: 881, size: 0x58, field offset: 0x8
class bRa extends Object {
}

// class id: 3059, size: 0x18, field offset: 0x14
class _aRa extends Mt<dynamic> {

  late final Xra _eCb; // offset: 0x14

  [closure] pI _zSf(dynamic, aoa, int) {
    // ** addr: 0x607bcc, size: -0x1
  }
  [closure] void eSf(dynamic, fRa) {
    // ** addr: 0x607d90, size: -0x1
  }
}

// class id: 3855, size: 0x40, field offset: 0xc
//   const constructor, 
class ZQa extends It {
}
