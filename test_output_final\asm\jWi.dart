// lib: , url: JWi

// class id: 1049026, size: 0x8
class :: {
}

// class id: 2290, size: 0x28, field offset: 0x8
//   const constructor, 
class iZ extends Object {
}

// class id: 4708, size: 0x18, field offset: 0x10
//   const constructor, 
abstract class kP<X0> extends mr {

  mr? [](kP<X0>, X0) {
    // ** addr: 0x8e7f84, size: 0x8c
    // 0x8e7f84: EnterFrame
    //     0x8e7f84: stp             fp, lr, [SP, #-0x10]!
    //     0x8e7f88: mov             fp, SP
    // 0x8e7f8c: CheckStackOverflow
    //     0x8e7f8c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e7f90: cmp             SP, x16
    //     0x8e7f94: b.ls            #0x8e7ff0
    // 0x8e7f98: ldr             x3, [fp, #0x18]
    // 0x8e7f9c: LoadField: r2 = r3->field_f
    //     0x8e7f9c: ldur            w2, [x3, #0xf]
    // 0x8e7fa0: DecompressPointer r2
    //     0x8e7fa0: add             x2, x2, HEAP, lsl #32
    // 0x8e7fa4: ldr             x0, [fp, #0x10]
    // 0x8e7fa8: r1 = Null
    //     0x8e7fa8: mov             x1, NULL
    // 0x8e7fac: cmp             w2, NULL
    // 0x8e7fb0: b.eq            #0x8e7fd0
    // 0x8e7fb4: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8e7fb4: ldur            w4, [x2, #0x17]
    // 0x8e7fb8: DecompressPointer r4
    //     0x8e7fb8: add             x4, x4, HEAP, lsl #32
    // 0x8e7fbc: r8 = X0
    //     0x8e7fbc: ldr             x8, [PP, #0x190]  ; [pp+0x190] TypeParameter: X0
    // 0x8e7fc0: LoadField: r9 = r4->field_7
    //     0x8e7fc0: ldur            x9, [x4, #7]
    // 0x8e7fc4: r3 = Null
    //     0x8e7fc4: add             x3, PP, #0xe, lsl #12  ; [pp+0xea48] Null
    //     0x8e7fc8: ldr             x3, [x3, #0xa48]
    // 0x8e7fcc: blr             x9
    // 0x8e7fd0: ldr             x0, [fp, #0x18]
    // 0x8e7fd4: LoadField: r1 = r0->field_13
    //     0x8e7fd4: ldur            w1, [x0, #0x13]
    // 0x8e7fd8: DecompressPointer r1
    //     0x8e7fd8: add             x1, x1, HEAP, lsl #32
    // 0x8e7fdc: ldr             x2, [fp, #0x10]
    // 0x8e7fe0: r0 = call 0x7e5840
    //     0x8e7fe0: bl              #0x7e5840
    // 0x8e7fe4: LeaveFrame
    //     0x8e7fe4: mov             SP, fp
    //     0x8e7fe8: ldp             fp, lr, [SP], #0x10
    // 0x8e7fec: ret
    //     0x8e7fec: ret             
    // 0x8e7ff0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e7ff0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e7ff4: b               #0x8e7f98
  }
}
