// lib: , url: pgj

// class id: 1049514, size: 0x8
class :: {

  static late final double _ANg; // offset: 0x13b8
}

// class id: 937, size: 0x88, field offset: 0x8
class UOa extends Object {

  static late final num _OMg; // offset: 0x13b0
  static late final int _PMg; // offset: 0x13b4

  [closure] static String? <anonymous closure>(dynamic, XOa) {
    // ** addr: 0x771648, size: -0x1
  }
  [closure] static bool iMg(dynamic, String?) {
    // ** addr: 0x76c924, size: -0x1
  }
  [closure] static String <anonymous closure>(dynamic, XOa) {
    // ** addr: 0x76cb74, size: -0x1
  }
}
