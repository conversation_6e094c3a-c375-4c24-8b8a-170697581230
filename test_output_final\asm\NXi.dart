// lib: , url: NXi

// class id: 1049084, size: 0x8
class :: {
}

// class id: 1939, size: 0x28, field offset: 0x8
//   const constructor, 
class gea extends Object {
}

// class id: 2066, size: 0x68, field offset: 0x68
//   transformed mixin,
abstract class _hfa extends _ifa
     with hI<X0 bound iI, X1 bound Yaa> {
}

// class id: 2067, size: 0x84, field offset: 0x68
class jfa extends _hfa {

  [closure] void xwe(dynamic, sca, er) {
    // ** addr: 0x38cae4, size: -0x1
  }
  [closure] double ABc(dynamic, double) {
    // ** addr: 0x353f4c, size: -0x1
  }
  [closure] double <anonymous closure>(dynamic, iI) {
    // ** addr: 0x353d70, size: -0x1
  }
  [closure] double CBc(dynamic, double) {
    // ** addr: 0x34d540, size: -0x1
  }
  [closure] double BBc(dynamic, double) {
    // ** addr: 0x351074, size: -0x1
  }
  [closure] double zBc(dynamic, double) {
    // ** addr: 0x3566b0, size: -0x1
  }
}

// class id: 2068, size: 0x88, field offset: 0x84
class kfa extends jfa {

  [closure] void xwe(dynamic, sca, er) {
    // ** addr: 0x38caa4, size: -0x1
  }
}

// class id: 2222, size: 0x30, field offset: 0x18
class ffa extends Yaa<dynamic> {
}

// class id: 5510, size: 0x14, field offset: 0x14
enum gfa extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
