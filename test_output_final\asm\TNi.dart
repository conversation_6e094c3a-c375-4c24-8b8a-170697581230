// lib: , url: TNi

// class id: 1048608, size: 0x8
class :: {

  static late final Set<Object> UYe; // offset: 0xca0

  [closure] static pI VYe(dynamic, yF, (dynamic) => void, pI) {
    // ** addr: 0x4339c0, size: -0x1
  }
  [closure] static Future<void> cZe(dynamic) {
    // ** addr: 0x433994, size: -0x1
  }
}

// class id: 2283, size: 0x28, field offset: 0x28
//   const constructor, transformed mixin,
abstract class _hu extends iu
     with Ot {
}

// class id: 2284, size: 0x28, field offset: 0x28
//   const constructor, 
class _ju extends _hu {

  _Mint field_8;
  _Mint field_10;
  _Mint field_18;
  _Mint field_20;
}

// class id: 2297, size: 0x18, field offset: 0x18
//   const constructor, transformed mixin,
abstract class _ku extends lu
     with Ot {
}

// class id: 2298, size: 0x18, field offset: 0x18
//   const constructor, 
class _mu extends _ku {

  gr field_8;
  gr field_c;
  gr field_10;
  gr field_14;
}

// class id: 2306, size: 0x18, field offset: 0x18
//   const constructor, transformed mixin,
abstract class _eu extends fu
     with Ot {
}

// class id: 2307, size: 0x18, field offset: 0x18
//   const constructor, 
class _gu extends _eu {

  _Mint field_8;
  _Mint field_10;
}

// class id: 3040, size: 0x70, field offset: 0x70
//   const constructor, transformed mixin,
abstract class _au extends Rs
     with Ot {
}

// class id: 3041, size: 0x70, field offset: 0x70
//   const constructor, 
class _bu extends _au {

  bool field_8;
}

// class id: 3841, size: 0x38, field offset: 0x38
//   const constructor, transformed mixin,
abstract class _Rt extends St
     with Ot {
}

// class id: 3842, size: 0x38, field offset: 0x38
//   const constructor, 
class _Tt extends _Rt {
}

// class id: 4617, size: 0x8, field offset: 0x8
//   const constructor, transformed mixin,
abstract class _Pt extends Object
     with Ot {
}

// class id: 4618, size: 0xc, field offset: 0x8
//   const constructor, 
class _List<X0> extends _Pt
    implements List<X0> {

  int length(_List<X0>) {
    // ** addr: 0x8fdf00, size: 0x2c
    // 0x8fdf00: EnterFrame
    //     0x8fdf00: stp             fp, lr, [SP, #-0x10]!
    //     0x8fdf04: mov             fp, SP
    // 0x8fdf08: r0 = Ha()
    //     0x8fdf08: bl              #0x8bc680  ; AllocateHaStub -> Ha (size=0x10)
    // 0x8fdf0c: r0 = Throw()
    //     0x8fdf0c: bl              #0x94dd08  ; ThrowStub
    // 0x8fdf10: brk             #0
  }
  void Ij(_List<X0>, (dynamic, X0) => void) {
    // ** addr: 0x8c5a5c, size: 0x5c
    // 0x8c5a5c: EnterFrame
    //     0x8c5a5c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5a60: mov             fp, SP
    // 0x8c5a64: ldr             x0, [fp, #0x18]
    // 0x8c5a68: LoadField: r2 = r0->field_7
    //     0x8c5a68: ldur            w2, [x0, #7]
    // 0x8c5a6c: DecompressPointer r2
    //     0x8c5a6c: add             x2, x2, HEAP, lsl #32
    // 0x8c5a70: ldr             x0, [fp, #0x10]
    // 0x8c5a74: r1 = Null
    //     0x8c5a74: mov             x1, NULL
    // 0x8c5a78: r8 = (dynamic this, X0) => void?
    //     0x8c5a78: add             x8, PP, #0x10, lsl #12  ; [pp+0x10fd8] FunctionType: (dynamic this, X0) => void?
    //     0x8c5a7c: ldr             x8, [x8, #0xfd8]
    // 0x8c5a80: LoadField: r9 = r8->field_7
    //     0x8c5a80: ldur            x9, [x8, #7]
    // 0x8c5a84: r3 = Null
    //     0x8c5a84: add             x3, PP, #0x21, lsl #12  ; [pp+0x21f08] Null
    //     0x8c5a88: ldr             x3, [x3, #0xf08]
    // 0x8c5a8c: blr             x9
    // 0x8c5a90: r0 = Null
    //     0x8c5a90: mov             x0, NULL
    // 0x8c5a94: LeaveFrame
    //     0x8c5a94: mov             SP, fp
    //     0x8c5a98: ldp             fp, lr, [SP], #0x10
    // 0x8c5a9c: ret
    //     0x8c5a9c: ret             
  }
  Iterable<Y0> ok<Y0>(_List<X0>, (dynamic, X0) => Y0) {
    // ** addr: 0x8c5aa0, size: 0x60
    // 0x8c5aa0: EnterFrame
    //     0x8c5aa0: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5aa4: mov             fp, SP
    // 0x8c5aa8: LoadField: r0 = r4->field_f
    //     0x8c5aa8: ldur            w0, [x4, #0xf]
    // 0x8c5aac: DecompressPointer r0
    //     0x8c5aac: add             x0, x0, HEAP, lsl #32
    // 0x8c5ab0: cbnz            w0, #0x8c5abc
    // 0x8c5ab4: r1 = Null
    //     0x8c5ab4: mov             x1, NULL
    // 0x8c5ab8: b               #0x8c5acc
    // 0x8c5abc: ArrayLoad: r0 = r4[0]  ; List_4
    //     0x8c5abc: ldur            w0, [x4, #0x17]
    // 0x8c5ac0: DecompressPointer r0
    //     0x8c5ac0: add             x0, x0, HEAP, lsl #32
    // 0x8c5ac4: add             x1, fp, w0, sxtw #2
    // 0x8c5ac8: ldr             x1, [x1, #0x10]
    // 0x8c5acc: ldr             x0, [fp, #0x18]
    // 0x8c5ad0: LoadField: r2 = r0->field_7
    //     0x8c5ad0: ldur            w2, [x0, #7]
    // 0x8c5ad4: DecompressPointer r2
    //     0x8c5ad4: add             x2, x2, HEAP, lsl #32
    // 0x8c5ad8: ldr             x0, [fp, #0x10]
    // 0x8c5adc: r8 = (dynamic this, X0) => Y0
    //     0x8c5adc: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2f380] FunctionType: (dynamic this, X0) => Y0
    //     0x8c5ae0: ldr             x8, [x8, #0x380]
    // 0x8c5ae4: LoadField: r9 = r8->field_7
    //     0x8c5ae4: ldur            x9, [x8, #7]
    // 0x8c5ae8: r3 = Null
    //     0x8c5ae8: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f388] Null
    //     0x8c5aec: ldr             x3, [x3, #0x388]
    // 0x8c5af0: blr             x9
    // 0x8c5af4: r0 = Ha()
    //     0x8c5af4: bl              #0x8bc680  ; AllocateHaStub -> Ha (size=0x10)
    // 0x8c5af8: r0 = Throw()
    //     0x8c5af8: bl              #0x94dd08  ; ThrowStub
    // 0x8c5afc: brk             #0
  }
  X0 [](_List<X0>, int) {
    // ** addr: 0x8c5b18, size: 0x88
    // 0x8c5b18: EnterFrame
    //     0x8c5b18: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5b1c: mov             fp, SP
    // 0x8c5b20: AllocStack(0x10)
    //     0x8c5b20: sub             SP, SP, #0x10
    // 0x8c5b24: CheckStackOverflow
    //     0x8c5b24: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5b28: cmp             SP, x16
    //     0x8c5b2c: b.ls            #0x8c5b80
    // 0x8c5b30: ldr             x0, [fp, #0x10]
    // 0x8c5b34: r2 = Null
    //     0x8c5b34: mov             x2, NULL
    // 0x8c5b38: r1 = Null
    //     0x8c5b38: mov             x1, NULL
    // 0x8c5b3c: branchIfSmi(r0, 0x8c5b64)
    //     0x8c5b3c: tbz             w0, #0, #0x8c5b64
    // 0x8c5b40: r4 = LoadClassIdInstr(r0)
    //     0x8c5b40: ldur            x4, [x0, #-1]
    //     0x8c5b44: ubfx            x4, x4, #0xc, #0x14
    // 0x8c5b48: sub             x4, x4, #0x3b
    // 0x8c5b4c: cmp             x4, #1
    // 0x8c5b50: b.ls            #0x8c5b64
    // 0x8c5b54: r8 = int
    //     0x8c5b54: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8c5b58: r3 = Null
    //     0x8c5b58: add             x3, PP, #0xe, lsl #12  ; [pp+0xe8d8] Null
    //     0x8c5b5c: ldr             x3, [x3, #0x8d8]
    // 0x8c5b60: r0 = int()
    //     0x8c5b60: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8c5b64: ldr             x16, [fp, #0x18]
    // 0x8c5b68: ldr             lr, [fp, #0x10]
    // 0x8c5b6c: stp             lr, x16, [SP]
    // 0x8c5b70: r0 = <anonymous closure>()
    //     0x8c5b70: bl              #0x841180  ; [ocj] RIa::<anonymous closure>
    // 0x8c5b74: LeaveFrame
    //     0x8c5b74: mov             SP, fp
    //     0x8c5b78: ldp             fp, lr, [SP], #0x10
    // 0x8c5b7c: ret
    //     0x8c5b7c: ret             
    // 0x8c5b80: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5b80: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5b84: b               #0x8c5b30
  }
  int Kk(_List<X0>, (dynamic, X0) => bool, [int]) {
    // ** addr: 0x8c5b88, size: 0xb0
    // 0x8c5b88: EnterFrame
    //     0x8c5b88: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5b8c: mov             fp, SP
    // 0x8c5b90: AllocStack(0x8)
    //     0x8c5b90: sub             SP, SP, #8
    // 0x8c5b94: SetupParameters(_List<X0> this /* r0 */, dynamic _ /* r2 */, [dynamic _ = 0 /* r3, fp-0x8 */])
    //     0x8c5b94: ldur            w0, [x4, #0x13]
    //     0x8c5b98: add             x0, x0, HEAP, lsl #32
    //     0x8c5b9c: sub             x1, x0, #4
    //     0x8c5ba0: add             x0, fp, w1, sxtw #2
    //     0x8c5ba4: ldr             x0, [x0, #0x18]
    //     0x8c5ba8: add             x2, fp, w1, sxtw #2
    //     0x8c5bac: ldr             x2, [x2, #0x10]
    //     0x8c5bb0: cmp             w1, #2
    //     0x8c5bb4: b.lt            #0x8c5bc4
    //     0x8c5bb8: add             x3, fp, w1, sxtw #2
    //     0x8c5bbc: ldr             x3, [x3, #8]
    //     0x8c5bc0: b               #0x8c5bc8
    //     0x8c5bc4: movz            x3, #0
    //     0x8c5bc8: stur            x3, [fp, #-8]
    // 0x8c5bcc: LoadField: r1 = r0->field_7
    //     0x8c5bcc: ldur            w1, [x0, #7]
    // 0x8c5bd0: DecompressPointer r1
    //     0x8c5bd0: add             x1, x1, HEAP, lsl #32
    // 0x8c5bd4: mov             x0, x2
    // 0x8c5bd8: mov             x2, x1
    // 0x8c5bdc: r1 = Null
    //     0x8c5bdc: mov             x1, NULL
    // 0x8c5be0: r8 = (dynamic this, X0) => bool
    //     0x8c5be0: add             x8, PP, #0x27, lsl #12  ; [pp+0x27140] FunctionType: (dynamic this, X0) => bool
    //     0x8c5be4: ldr             x8, [x8, #0x140]
    // 0x8c5be8: LoadField: r9 = r8->field_7
    //     0x8c5be8: ldur            x9, [x8, #7]
    // 0x8c5bec: r3 = Null
    //     0x8c5bec: add             x3, PP, #0x27, lsl #12  ; [pp+0x27148] Null
    //     0x8c5bf0: ldr             x3, [x3, #0x148]
    // 0x8c5bf4: blr             x9
    // 0x8c5bf8: ldur            x0, [fp, #-8]
    // 0x8c5bfc: r2 = Null
    //     0x8c5bfc: mov             x2, NULL
    // 0x8c5c00: r1 = Null
    //     0x8c5c00: mov             x1, NULL
    // 0x8c5c04: branchIfSmi(r0, 0x8c5c2c)
    //     0x8c5c04: tbz             w0, #0, #0x8c5c2c
    // 0x8c5c08: r4 = LoadClassIdInstr(r0)
    //     0x8c5c08: ldur            x4, [x0, #-1]
    //     0x8c5c0c: ubfx            x4, x4, #0xc, #0x14
    // 0x8c5c10: sub             x4, x4, #0x3b
    // 0x8c5c14: cmp             x4, #1
    // 0x8c5c18: b.ls            #0x8c5c2c
    // 0x8c5c1c: r8 = int
    //     0x8c5c1c: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8c5c20: r3 = Null
    //     0x8c5c20: add             x3, PP, #0x27, lsl #12  ; [pp+0x27158] Null
    //     0x8c5c24: ldr             x3, [x3, #0x158]
    // 0x8c5c28: r0 = int()
    //     0x8c5c28: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8c5c2c: r0 = Ha()
    //     0x8c5c2c: bl              #0x8bc680  ; AllocateHaStub -> Ha (size=0x10)
    // 0x8c5c30: r0 = Throw()
    //     0x8c5c30: bl              #0x94dd08  ; ThrowStub
    // 0x8c5c34: brk             #0
  }
  void []=(_List<X0>, int, X0) {
    // ** addr: 0x8c5c50, size: 0x9c
    // 0x8c5c50: EnterFrame
    //     0x8c5c50: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5c54: mov             fp, SP
    // 0x8c5c58: ldr             x0, [fp, #0x18]
    // 0x8c5c5c: r2 = Null
    //     0x8c5c5c: mov             x2, NULL
    // 0x8c5c60: r1 = Null
    //     0x8c5c60: mov             x1, NULL
    // 0x8c5c64: branchIfSmi(r0, 0x8c5c8c)
    //     0x8c5c64: tbz             w0, #0, #0x8c5c8c
    // 0x8c5c68: r4 = LoadClassIdInstr(r0)
    //     0x8c5c68: ldur            x4, [x0, #-1]
    //     0x8c5c6c: ubfx            x4, x4, #0xc, #0x14
    // 0x8c5c70: sub             x4, x4, #0x3b
    // 0x8c5c74: cmp             x4, #1
    // 0x8c5c78: b.ls            #0x8c5c8c
    // 0x8c5c7c: r8 = int
    //     0x8c5c7c: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8c5c80: r3 = Null
    //     0x8c5c80: add             x3, PP, #0x27, lsl #12  ; [pp+0x27168] Null
    //     0x8c5c84: ldr             x3, [x3, #0x168]
    // 0x8c5c88: r0 = int()
    //     0x8c5c88: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8c5c8c: ldr             x0, [fp, #0x20]
    // 0x8c5c90: LoadField: r2 = r0->field_7
    //     0x8c5c90: ldur            w2, [x0, #7]
    // 0x8c5c94: DecompressPointer r2
    //     0x8c5c94: add             x2, x2, HEAP, lsl #32
    // 0x8c5c98: ldr             x0, [fp, #0x10]
    // 0x8c5c9c: r1 = Null
    //     0x8c5c9c: mov             x1, NULL
    // 0x8c5ca0: cmp             w2, NULL
    // 0x8c5ca4: b.eq            #0x8c5cc4
    // 0x8c5ca8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8c5ca8: ldur            w4, [x2, #0x17]
    // 0x8c5cac: DecompressPointer r4
    //     0x8c5cac: add             x4, x4, HEAP, lsl #32
    // 0x8c5cb0: r8 = X0
    //     0x8c5cb0: ldr             x8, [PP, #0x190]  ; [pp+0x190] TypeParameter: X0
    // 0x8c5cb4: LoadField: r9 = r4->field_7
    //     0x8c5cb4: ldur            x9, [x4, #7]
    // 0x8c5cb8: r3 = Null
    //     0x8c5cb8: add             x3, PP, #0x27, lsl #12  ; [pp+0x27178] Null
    //     0x8c5cbc: ldr             x3, [x3, #0x178]
    // 0x8c5cc0: blr             x9
    // 0x8c5cc4: r0 = Null
    //     0x8c5cc4: mov             x0, NULL
    // 0x8c5cc8: LeaveFrame
    //     0x8c5cc8: mov             SP, fp
    //     0x8c5ccc: ldp             fp, lr, [SP], #0x10
    // 0x8c5cd0: ret
    //     0x8c5cd0: ret             
  }
  List<X0> +(_List<X0>, List<X0>) {
    // ** addr: 0x8c5cec, size: 0x4c
    // 0x8c5cec: EnterFrame
    //     0x8c5cec: stp             fp, lr, [SP, #-0x10]!
    //     0x8c5cf0: mov             fp, SP
    // 0x8c5cf4: CheckStackOverflow
    //     0x8c5cf4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c5cf8: cmp             SP, x16
    //     0x8c5cfc: b.ls            #0x8c5d18
    // 0x8c5d00: ldr             x1, [fp, #0x18]
    // 0x8c5d04: ldr             x2, [fp, #0x10]
    // 0x8c5d08: r0 = call 0x3058dc
    //     0x8c5d08: bl              #0x3058dc
    // 0x8c5d0c: LeaveFrame
    //     0x8c5d0c: mov             SP, fp
    //     0x8c5d10: ldp             fp, lr, [SP], #0x10
    // 0x8c5d14: ret
    //     0x8c5d14: ret             
    // 0x8c5d18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c5d18: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c5d1c: b               #0x8c5d00
  }
  [closure] bool ek(dynamic, Object?) {
    // ** addr: 0x3df76c, size: -0x1
  }
  [closure] void add(dynamic, Object?) {
    // ** addr: 0x305884, size: -0x1
  }
}

// class id: 4619, size: 0x8, field offset: 0x8
//   const constructor, 
class _Qt extends _Pt {
}

// class id: 4620, size: 0x8, field offset: 0x8
abstract class Ot extends Object {
}

// class id: 4627, size: 0x14, field offset: 0x14
//   const constructor, transformed mixin,
abstract class _Yt extends Dt
     with Ot {
}

// class id: 4628, size: 0x14, field offset: 0x14
//   const constructor, 
class _Zt extends _Yt {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 4630, size: 0x14, field offset: 0x14
//   const constructor, transformed mixin,
abstract class _Wt extends Ct
     with Ot {
}

// class id: 4631, size: 0x14, field offset: 0x14
//   const constructor, 
class _Xt extends _Wt {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 4713, size: 0x10, field offset: 0x10
//   const constructor, transformed mixin,
abstract class _cu extends mr
     with Ot {
}

// class id: 4714, size: 0x10, field offset: 0x10
//   const constructor, 
class _du extends _cu {

  _Mint field_8;
}

// class id: 5648, size: 0x10, field offset: 0x10
//   const constructor, transformed mixin,
abstract class _Ut extends Ea
     with Ot {
}

// class id: 5649, size: 0x10, field offset: 0x10
//   const constructor, 
class _Vt extends _Ut {

  _Mint field_8;
}
