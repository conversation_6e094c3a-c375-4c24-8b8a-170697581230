// lib: , url: LTi

// class id: 1048889, size: 0x8
class :: {
}

// class id: 2480, size: 0x1c, field offset: 0x8
class uM extends Object {
}

// class id: 2483, size: 0x10, field offset: 0x8
//   const constructor, 
class _jN extends Object {
}

// class id: 2484, size: 0x1c, field offset: 0x8
//   const constructor, 
class tM extends Object {

  er field_8;
  _Double field_c;
  Ea field_14;
  er field_18;
}

// class id: 2485, size: 0xc, field offset: 0x8
//   const constructor, 
class oK extends Object {

  er field_8;

  oK +(oK, oK) {
    // ** addr: 0x9131e4, size: 0x84
    // 0x9131e4: EnterFrame
    //     0x9131e4: stp             fp, lr, [SP, #-0x10]!
    //     0x9131e8: mov             fp, SP
    // 0x9131ec: CheckStackOverflow
    //     0x9131ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9131f0: cmp             SP, x16
    //     0x9131f4: b.ls            #0x913248
    // 0x9131f8: ldr             x0, [fp, #0x10]
    // 0x9131fc: r2 = Null
    //     0x9131fc: mov             x2, NULL
    // 0x913200: r1 = Null
    //     0x913200: mov             x1, NULL
    // 0x913204: r4 = 59
    //     0x913204: movz            x4, #0x3b
    // 0x913208: branchIfSmi(r0, 0x913214)
    //     0x913208: tbz             w0, #0, #0x913214
    // 0x91320c: r4 = LoadClassIdInstr(r0)
    //     0x91320c: ldur            x4, [x0, #-1]
    //     0x913210: ubfx            x4, x4, #0xc, #0x14
    // 0x913214: cmp             x4, #0x9b5
    // 0x913218: b.eq            #0x913230
    // 0x91321c: r8 = oK
    //     0x91321c: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2f480] Type: oK
    //     0x913220: ldr             x8, [x8, #0x480]
    // 0x913224: r3 = Null
    //     0x913224: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f488] Null
    //     0x913228: ldr             x3, [x3, #0x488]
    // 0x91322c: r0 = DefaultTypeTest()
    //     0x91322c: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x913230: ldr             x1, [fp, #0x18]
    // 0x913234: ldr             x2, [fp, #0x10]
    // 0x913238: r0 = call 0x5f2e14
    //     0x913238: bl              #0x5f2e14
    // 0x91323c: LeaveFrame
    //     0x91323c: mov             SP, fp
    //     0x913240: ldp             fp, lr, [SP], #0x10
    // 0x913244: ret
    //     0x913244: ret             
    // 0x913248: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x913248: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91324c: b               #0x9131f8
  }
  oK -(oK, oK) {
    // ** addr: 0x913274, size: 0x84
    // 0x913274: EnterFrame
    //     0x913274: stp             fp, lr, [SP, #-0x10]!
    //     0x913278: mov             fp, SP
    // 0x91327c: CheckStackOverflow
    //     0x91327c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x913280: cmp             SP, x16
    //     0x913284: b.ls            #0x9132d8
    // 0x913288: ldr             x0, [fp, #0x10]
    // 0x91328c: r2 = Null
    //     0x91328c: mov             x2, NULL
    // 0x913290: r1 = Null
    //     0x913290: mov             x1, NULL
    // 0x913294: r4 = 59
    //     0x913294: movz            x4, #0x3b
    // 0x913298: branchIfSmi(r0, 0x9132a4)
    //     0x913298: tbz             w0, #0, #0x9132a4
    // 0x91329c: r4 = LoadClassIdInstr(r0)
    //     0x91329c: ldur            x4, [x0, #-1]
    //     0x9132a0: ubfx            x4, x4, #0xc, #0x14
    // 0x9132a4: cmp             x4, #0x9b5
    // 0x9132a8: b.eq            #0x9132c0
    // 0x9132ac: r8 = oK
    //     0x9132ac: add             x8, PP, #0x2f, lsl #12  ; [pp+0x2f480] Type: oK
    //     0x9132b0: ldr             x8, [x8, #0x480]
    // 0x9132b4: r3 = Null
    //     0x9132b4: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f498] Null
    //     0x9132b8: ldr             x3, [x3, #0x498]
    // 0x9132bc: r0 = DefaultTypeTest()
    //     0x9132bc: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x9132c0: ldr             x1, [fp, #0x18]
    // 0x9132c4: ldr             x2, [fp, #0x10]
    // 0x9132c8: r0 = call 0x5f2e6c
    //     0x9132c8: bl              #0x5f2e6c
    // 0x9132cc: LeaveFrame
    //     0x9132cc: mov             SP, fp
    //     0x9132d0: ldp             fp, lr, [SP], #0x10
    // 0x9132d4: ret
    //     0x9132d4: ret             
    // 0x9132d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9132d8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9132dc: b               #0x913288
  }
}
