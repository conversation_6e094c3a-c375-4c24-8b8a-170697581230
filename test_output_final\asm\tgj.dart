// lib: hrj, url: tgj

// class id: 1049518, size: 0x8
class :: {

  [closure] static String zBg(dynamic, String?) {
    // ** addr: 0x766cb0, size: -0x1
  }
  [closure] static String wMg(dynamic, String) {
    // ** addr: 0x766c80, size: -0x1
  }
  [closure] static String vMg(dynamic, String) {
    // ** addr: 0x766c50, size: -0x1
  }
  [closure] static String <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x766aac, size: -0x1
  }
  [closure] static String <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7668b0, size: -0x1
  }
  [closure] static String <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x7668a4, size: -0x1
  }
}

// class id: 931, size: 0xc, field offset: 0x8
class lPa extends Object
    implements Ja {
}

// class id: 932, size: 0x8, field offset: 0x8
abstract class kPa extends Object {
}

// class id: 933, size: 0x14, field offset: 0x8
class jPa<X0> extends Object
    implements kPa {

  bool il(jPa<X0>, String) {
    // ** addr: 0x8d84c0, size: 0x84
    // 0x8d84c0: EnterFrame
    //     0x8d84c0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d84c4: mov             fp, SP
    // 0x8d84c8: CheckStackOverflow
    //     0x8d84c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d84cc: cmp             SP, x16
    //     0x8d84d0: b.ls            #0x8d8524
    // 0x8d84d4: ldr             x0, [fp, #0x10]
    // 0x8d84d8: r2 = Null
    //     0x8d84d8: mov             x2, NULL
    // 0x8d84dc: r1 = Null
    //     0x8d84dc: mov             x1, NULL
    // 0x8d84e0: r4 = 59
    //     0x8d84e0: movz            x4, #0x3b
    // 0x8d84e4: branchIfSmi(r0, 0x8d84f0)
    //     0x8d84e4: tbz             w0, #0, #0x8d84f0
    // 0x8d84e8: r4 = LoadClassIdInstr(r0)
    //     0x8d84e8: ldur            x4, [x0, #-1]
    //     0x8d84ec: ubfx            x4, x4, #0xc, #0x14
    // 0x8d84f0: sub             x4, x4, #0x5d
    // 0x8d84f4: cmp             x4, #1
    // 0x8d84f8: b.ls            #0x8d850c
    // 0x8d84fc: r8 = String
    //     0x8d84fc: ldr             x8, [PP, #0x328]  ; [pp+0x328] Type: String
    // 0x8d8500: r3 = Null
    //     0x8d8500: add             x3, PP, #0x40, lsl #12  ; [pp+0x40a18] Null
    //     0x8d8504: ldr             x3, [x3, #0xa18]
    // 0x8d8508: r0 = String()
    //     0x8d8508: bl              #0x958918  ; IsType_String_Stub
    // 0x8d850c: ldr             x1, [fp, #0x18]
    // 0x8d8510: ldr             x2, [fp, #0x10]
    // 0x8d8514: r0 = call 0x3c4ac8
    //     0x8d8514: bl              #0x3c4ac8
    // 0x8d8518: LeaveFrame
    //     0x8d8518: mov             SP, fp
    //     0x8d851c: ldp             fp, lr, [SP], #0x10
    // 0x8d8520: ret
    //     0x8d8520: ret             
    // 0x8d8524: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d8524: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d8528: b               #0x8d84d4
  }
  X0 [](jPa<X0>, String) {
    // ** addr: 0x8d8550, size: 0x84
    // 0x8d8550: EnterFrame
    //     0x8d8550: stp             fp, lr, [SP, #-0x10]!
    //     0x8d8554: mov             fp, SP
    // 0x8d8558: CheckStackOverflow
    //     0x8d8558: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d855c: cmp             SP, x16
    //     0x8d8560: b.ls            #0x8d85b4
    // 0x8d8564: ldr             x0, [fp, #0x10]
    // 0x8d8568: r2 = Null
    //     0x8d8568: mov             x2, NULL
    // 0x8d856c: r1 = Null
    //     0x8d856c: mov             x1, NULL
    // 0x8d8570: r4 = 59
    //     0x8d8570: movz            x4, #0x3b
    // 0x8d8574: branchIfSmi(r0, 0x8d8580)
    //     0x8d8574: tbz             w0, #0, #0x8d8580
    // 0x8d8578: r4 = LoadClassIdInstr(r0)
    //     0x8d8578: ldur            x4, [x0, #-1]
    //     0x8d857c: ubfx            x4, x4, #0xc, #0x14
    // 0x8d8580: sub             x4, x4, #0x5d
    // 0x8d8584: cmp             x4, #1
    // 0x8d8588: b.ls            #0x8d859c
    // 0x8d858c: r8 = String
    //     0x8d858c: ldr             x8, [PP, #0x328]  ; [pp+0x328] Type: String
    // 0x8d8590: r3 = Null
    //     0x8d8590: add             x3, PP, #0x40, lsl #12  ; [pp+0x40a30] Null
    //     0x8d8594: ldr             x3, [x3, #0xa30]
    // 0x8d8598: r0 = String()
    //     0x8d8598: bl              #0x958918  ; IsType_String_Stub
    // 0x8d859c: ldr             x1, [fp, #0x18]
    // 0x8d85a0: ldr             x2, [fp, #0x10]
    // 0x8d85a4: r0 = call 0x3c5010
    //     0x8d85a4: bl              #0x3c5010
    // 0x8d85a8: LeaveFrame
    //     0x8d85a8: mov             SP, fp
    //     0x8d85ac: ldp             fp, lr, [SP], #0x10
    // 0x8d85b0: ret
    //     0x8d85b0: ret             
    // 0x8d85b4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d85b4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d85b8: b               #0x8d8564
  }
}
