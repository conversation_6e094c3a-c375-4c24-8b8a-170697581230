// lib: , url: Gej

// class id: 1049439, size: 0x8
class :: {
}

// class id: 1013, size: 0x64, field offset: 0x8
class cNa extends Object {

  late int Puf; // offset: 0x10
  late int kvf; // offset: 0x60

  [closure] void _qvf(dynamic, XMa, List<dynamic>) {
    // ** addr: 0x854614, size: -0x1
  }
  [closure] void _rvf(dynamic, XMa, List<int>) {
    // ** addr: 0x8544c4, size: -0x1
  }
  [closure] void _svf(dynamic, XMa, List<dynamic>) {
    // ** addr: 0x854028, size: -0x1
  }
  [closure] void _tvf(dynamic, XMa, List<int>) {
    // ** addr: 0x8536e0, size: -0x1
  }
  [closure] void _ovf(dynamic, XMa, List<dynamic>) {
    // ** addr: 0x852da4, size: -0x1
  }
}
