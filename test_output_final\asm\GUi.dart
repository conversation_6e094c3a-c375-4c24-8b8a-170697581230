// lib: Jqj, url: gUi

// class id: 1049665, size: 0x8
class :: {
}

// class id: 768, size: 0xc, field offset: 0xc
class SUa extends OTa {

  static late final vWa iog; // offset: 0x1254

  [closure] static SUa <anonymous closure>(dynamic) {
    // ** addr: 0x470e54, size: -0x1
  }
  [closure] static SUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x470f6c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x470dfc, size: -0x1
  }
}
