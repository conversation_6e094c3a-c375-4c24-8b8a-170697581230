// lib: , url: uYi

// class id: 1049295, size: 0x8
class :: {
}

// class id: 3081, size: 0x24, field offset: 0x14
class _YGa extends Mt<dynamic> {

  late er lCg; // offset: 0x20
  late er Ype; // offset: 0x1c

  [closure] void IEc(dynamic, qK) {
    // ** addr: 0x6062a0, size: -0x1
  }
  [closure] void Qif(dynamic, mK) {
    // ** addr: 0x605de0, size: -0x1
  }
  [closure] void JEc(dynamic, lK) {
    // ** addr: 0x605b2c, size: -0x1
  }
}

// class id: 3872, size: 0x20, field offset: 0xc
//   const constructor, 
class XGa extends It {
}
