// lib: , url: LOi

// class id: 1048647, size: 0x8
class :: {
}

// class id: 3320, size: 0x28, field offset: 0x14
class _vw extends Mt<dynamic> {

  [closure] pI <anonymous closure>(dynamic, aoa, int) {
    // ** addr: 0x588b0c, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x588ac0, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x588634, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x588814, size: -0x1
  }
}

// class id: 3395, size: 0x54, field offset: 0x30
class _tw extends Wu<dynamic> {

  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x8ee338, size: 0x144
    // 0x8ee338: EnterFrame
    //     0x8ee338: stp             fp, lr, [SP, #-0x10]!
    //     0x8ee33c: mov             fp, SP
    // 0x8ee340: AllocStack(0x20)
    //     0x8ee340: sub             SP, SP, #0x20
    // 0x8ee344: SetupParameters(_tw this /* r1 */)
    //     0x8ee344: stur            NULL, [fp, #-8]
    //     0x8ee348: movz            x0, #0
    //     0x8ee34c: add             x1, fp, w0, sxtw #2
    //     0x8ee350: ldr             x1, [x1, #0x10]
    //     0x8ee354: ldur            w2, [x1, #0x17]
    //     0x8ee358: add             x2, x2, HEAP, lsl #32
    //     0x8ee35c: stur            x2, [fp, #-0x10]
    // 0x8ee360: CheckStackOverflow
    //     0x8ee360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ee364: cmp             SP, x16
    //     0x8ee368: b.ls            #0x8ee470
    // 0x8ee36c: InitAsync() -> Future<void?>
    //     0x8ee36c: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x8ee370: bl              #0x8c1de0  ; InitAsyncStub
    // 0x8ee374: ldur            x0, [fp, #-0x10]
    // 0x8ee378: LoadField: r2 = r0->field_b
    //     0x8ee378: ldur            w2, [x0, #0xb]
    // 0x8ee37c: DecompressPointer r2
    //     0x8ee37c: add             x2, x2, HEAP, lsl #32
    // 0x8ee380: stur            x2, [fp, #-0x18]
    // 0x8ee384: LoadField: r1 = r2->field_f
    //     0x8ee384: ldur            w1, [x2, #0xf]
    // 0x8ee388: DecompressPointer r1
    //     0x8ee388: add             x1, x1, HEAP, lsl #32
    // 0x8ee38c: LoadField: r3 = r1->field_f
    //     0x8ee38c: ldur            w3, [x1, #0xf]
    // 0x8ee390: DecompressPointer r3
    //     0x8ee390: add             x3, x3, HEAP, lsl #32
    // 0x8ee394: cmp             w3, NULL
    // 0x8ee398: b.eq            #0x8ee478
    // 0x8ee39c: mov             x1, x3
    // 0x8ee3a0: r0 = call 0x437ca4
    //     0x8ee3a0: bl              #0x437ca4
    // 0x8ee3a4: tbnz            w0, #4, #0x8ee468
    // 0x8ee3a8: ldur            x3, [fp, #-0x10]
    // 0x8ee3ac: ldur            x4, [fp, #-0x18]
    // 0x8ee3b0: LoadField: r0 = r4->field_f
    //     0x8ee3b0: ldur            w0, [x4, #0xf]
    // 0x8ee3b4: DecompressPointer r0
    //     0x8ee3b4: add             x0, x0, HEAP, lsl #32
    // 0x8ee3b8: LoadField: r1 = r0->field_3b
    //     0x8ee3b8: ldur            w1, [x0, #0x3b]
    // 0x8ee3bc: DecompressPointer r1
    //     0x8ee3bc: add             x1, x1, HEAP, lsl #32
    // 0x8ee3c0: LoadField: r2 = r3->field_f
    //     0x8ee3c0: ldur            w2, [x3, #0xf]
    // 0x8ee3c4: DecompressPointer r2
    //     0x8ee3c4: add             x2, x2, HEAP, lsl #32
    // 0x8ee3c8: r0 = LoadClassIdInstr(r1)
    //     0x8ee3c8: ldur            x0, [x1, #-1]
    //     0x8ee3cc: ubfx            x0, x0, #0xc, #0x14
    // 0x8ee3d0: r0 = GDT[cid_x0 + 0x1079f]()
    //     0x8ee3d0: movz            x17, #0x79f
    //     0x8ee3d4: movk            x17, #0x1, lsl #16
    //     0x8ee3d8: add             lr, x0, x17
    //     0x8ee3dc: ldr             lr, [x21, lr, lsl #3]
    //     0x8ee3e0: blr             lr
    // 0x8ee3e4: r0 = InitLateStaticField(0xcc8) // [aQi] Bz::qkc
    //     0x8ee3e4: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ee3e8: ldr             x0, [x0, #0x1990]
    //     0x8ee3ec: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ee3f0: cmp             w0, w16
    //     0x8ee3f4: b.ne            #0x8ee400
    //     0x8ee3f8: ldr             x2, [PP, #0x6b28]  ; [pp+0x6b28] Field <Bz.qkc>: static late final (offset: 0xcc8)
    //     0x8ee3fc: bl              #0x94dbac  ; InitLateFinalStaticFieldStub
    // 0x8ee400: mov             x1, x0
    // 0x8ee404: ldur            x0, [fp, #-0x18]
    // 0x8ee408: LoadField: r2 = r0->field_f
    //     0x8ee408: ldur            w2, [x0, #0xf]
    // 0x8ee40c: DecompressPointer r2
    //     0x8ee40c: add             x2, x2, HEAP, lsl #32
    // 0x8ee410: LoadField: r3 = r2->field_3b
    //     0x8ee410: ldur            w3, [x2, #0x3b]
    // 0x8ee414: DecompressPointer r3
    //     0x8ee414: add             x3, x3, HEAP, lsl #32
    // 0x8ee418: mov             x2, x3
    // 0x8ee41c: r0 = __unknown_function__()
    //     0x8ee41c: bl              #0x8ee29c  ; [] ::__unknown_function__
    // 0x8ee420: mov             x1, x0
    // 0x8ee424: stur            x1, [fp, #-0x20]
    // 0x8ee428: r0 = Await()
    //     0x8ee428: bl              #0x8c1bb8  ; AwaitStub
    // 0x8ee42c: ldur            x0, [fp, #-0x18]
    // 0x8ee430: LoadField: r3 = r0->field_f
    //     0x8ee430: ldur            w3, [x0, #0xf]
    // 0x8ee434: DecompressPointer r3
    //     0x8ee434: add             x3, x3, HEAP, lsl #32
    // 0x8ee438: stur            x3, [fp, #-0x10]
    // 0x8ee43c: LoadField: r0 = r3->field_f
    //     0x8ee43c: ldur            w0, [x3, #0xf]
    // 0x8ee440: DecompressPointer r0
    //     0x8ee440: add             x0, x0, HEAP, lsl #32
    // 0x8ee444: cmp             w0, NULL
    // 0x8ee448: b.eq            #0x8ee468
    // 0x8ee44c: r1 = Function '<anonymous closure>':.
    //     0x8ee44c: add             x1, PP, #0x1f, lsl #12  ; [pp+0x1f9f8] Function: [dart:ui] Shader::Shader._ (0x8aec68)
    //     0x8ee450: ldr             x1, [x1, #0x9f8]
    // 0x8ee454: r2 = Null
    //     0x8ee454: mov             x2, NULL
    // 0x8ee458: r0 = AllocateClosure()
    //     0x8ee458: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x8ee45c: ldur            x1, [fp, #-0x10]
    // 0x8ee460: mov             x2, x0
    // 0x8ee464: r0 = call 0x30b424
    //     0x8ee464: bl              #0x30b424
    // 0x8ee468: r0 = Null
    //     0x8ee468: mov             x0, NULL
    // 0x8ee46c: r0 = ReturnAsyncNotFuture()
    //     0x8ee46c: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x8ee470: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ee470: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ee474: b               #0x8ee36c
    // 0x8ee478: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8ee478: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x8ee47c, size: 0xe8
    // 0x8ee47c: EnterFrame
    //     0x8ee47c: stp             fp, lr, [SP, #-0x10]!
    //     0x8ee480: mov             fp, SP
    // 0x8ee484: AllocStack(0x18)
    //     0x8ee484: sub             SP, SP, #0x18
    // 0x8ee488: SetupParameters(_tw this /* r1 */)
    //     0x8ee488: stur            NULL, [fp, #-8]
    //     0x8ee48c: movz            x0, #0
    //     0x8ee490: add             x1, fp, w0, sxtw #2
    //     0x8ee494: ldr             x1, [x1, #0x10]
    //     0x8ee498: ldur            w2, [x1, #0x17]
    //     0x8ee49c: add             x2, x2, HEAP, lsl #32
    //     0x8ee4a0: stur            x2, [fp, #-0x10]
    // 0x8ee4a4: CheckStackOverflow
    //     0x8ee4a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ee4a8: cmp             SP, x16
    //     0x8ee4ac: b.ls            #0x8ee55c
    // 0x8ee4b0: InitAsync() -> Future<void?>
    //     0x8ee4b0: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x8ee4b4: bl              #0x8c1de0  ; InitAsyncStub
    // 0x8ee4b8: ldur            x2, [fp, #-0x10]
    // 0x8ee4bc: LoadField: r0 = r2->field_f
    //     0x8ee4bc: ldur            w0, [x2, #0xf]
    // 0x8ee4c0: DecompressPointer r0
    //     0x8ee4c0: add             x0, x0, HEAP, lsl #32
    // 0x8ee4c4: LoadField: r1 = r0->field_3b
    //     0x8ee4c4: ldur            w1, [x0, #0x3b]
    // 0x8ee4c8: DecompressPointer r1
    //     0x8ee4c8: add             x1, x1, HEAP, lsl #32
    // 0x8ee4cc: r0 = LoadClassIdInstr(r1)
    //     0x8ee4cc: ldur            x0, [x1, #-1]
    //     0x8ee4d0: ubfx            x0, x0, #0xc, #0x14
    // 0x8ee4d4: r0 = GDT[cid_x0 + 0x108da]()
    //     0x8ee4d4: movz            x17, #0x8da
    //     0x8ee4d8: movk            x17, #0x1, lsl #16
    //     0x8ee4dc: add             lr, x0, x17
    //     0x8ee4e0: ldr             lr, [x21, lr, lsl #3]
    //     0x8ee4e4: blr             lr
    // 0x8ee4e8: r0 = InitLateStaticField(0xcc8) // [aQi] Bz::qkc
    //     0x8ee4e8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ee4ec: ldr             x0, [x0, #0x1990]
    //     0x8ee4f0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ee4f4: cmp             w0, w16
    //     0x8ee4f8: b.ne            #0x8ee504
    //     0x8ee4fc: ldr             x2, [PP, #0x6b28]  ; [pp+0x6b28] Field <Bz.qkc>: static late final (offset: 0xcc8)
    //     0x8ee500: bl              #0x94dbac  ; InitLateFinalStaticFieldStub
    // 0x8ee504: mov             x1, x0
    // 0x8ee508: r0 = __unknown_function__()
    //     0x8ee508: bl              #0x8ee564  ; [] ::__unknown_function__
    // 0x8ee50c: mov             x1, x0
    // 0x8ee510: stur            x1, [fp, #-0x18]
    // 0x8ee514: r0 = Await()
    //     0x8ee514: bl              #0x8c1bb8  ; AwaitStub
    // 0x8ee518: ldur            x0, [fp, #-0x10]
    // 0x8ee51c: LoadField: r3 = r0->field_f
    //     0x8ee51c: ldur            w3, [x0, #0xf]
    // 0x8ee520: DecompressPointer r3
    //     0x8ee520: add             x3, x3, HEAP, lsl #32
    // 0x8ee524: stur            x3, [fp, #-0x18]
    // 0x8ee528: LoadField: r0 = r3->field_f
    //     0x8ee528: ldur            w0, [x3, #0xf]
    // 0x8ee52c: DecompressPointer r0
    //     0x8ee52c: add             x0, x0, HEAP, lsl #32
    // 0x8ee530: cmp             w0, NULL
    // 0x8ee534: b.eq            #0x8ee554
    // 0x8ee538: r1 = Function '<anonymous closure>':.
    //     0x8ee538: add             x1, PP, #0x1f, lsl #12  ; [pp+0x1fa00] Function: [dart:ui] Shader::Shader._ (0x8aec68)
    //     0x8ee53c: ldr             x1, [x1, #0xa00]
    // 0x8ee540: r2 = Null
    //     0x8ee540: mov             x2, NULL
    // 0x8ee544: r0 = AllocateClosure()
    //     0x8ee544: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x8ee548: ldur            x1, [fp, #-0x18]
    // 0x8ee54c: mov             x2, x0
    // 0x8ee550: r0 = call 0x30b424
    //     0x8ee550: bl              #0x30b424
    // 0x8ee554: r0 = Null
    //     0x8ee554: mov             x0, NULL
    // 0x8ee558: r0 = ReturnAsyncNotFuture()
    //     0x8ee558: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x8ee55c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ee55c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ee560: b               #0x8ee4b0
  }
  [closure] qoa <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x48d5c8, size: -0x1
  }
  [closure] qoa <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x48d0b0, size: -0x1
  }
  [closure] fla <anonymous closure>(dynamic, int) {
    // ** addr: 0x48c72c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x48cea4, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x48d3cc, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x48dbac, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x6ba688, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0x6c5b64, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0x6c5b1c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x6c5a78, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x6c5a30, size: -0x1
  }
}

// class id: 4061, size: 0x18, field offset: 0xc
class uw extends It {
}

// class id: 4127, size: 0x10, field offset: 0x10
class sw extends Tu {
}
