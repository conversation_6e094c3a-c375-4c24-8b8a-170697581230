// lib: Hoj, url: Tmj

// class id: 1049834, size: 0x8
class :: {
}

// class id: 431, size: 0xc, field offset: 0x8
class zdb extends Object
    implements xdb {

  zdb +(zdb, zdb) {
    // ** addr: 0x8cec20, size: 0x84
    // 0x8cec20: EnterFrame
    //     0x8cec20: stp             fp, lr, [SP, #-0x10]!
    //     0x8cec24: mov             fp, SP
    // 0x8cec28: CheckStackOverflow
    //     0x8cec28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cec2c: cmp             SP, x16
    //     0x8cec30: b.ls            #0x8cec84
    // 0x8cec34: ldr             x0, [fp, #0x10]
    // 0x8cec38: r2 = Null
    //     0x8cec38: mov             x2, NULL
    // 0x8cec3c: r1 = Null
    //     0x8cec3c: mov             x1, NULL
    // 0x8cec40: r4 = 59
    //     0x8cec40: movz            x4, #0x3b
    // 0x8cec44: branchIfSmi(r0, 0x8cec50)
    //     0x8cec44: tbz             w0, #0, #0x8cec50
    // 0x8cec48: r4 = LoadClassIdInstr(r0)
    //     0x8cec48: ldur            x4, [x0, #-1]
    //     0x8cec4c: ubfx            x4, x4, #0xc, #0x14
    // 0x8cec50: cmp             x4, #0x1af
    // 0x8cec54: b.eq            #0x8cec6c
    // 0x8cec58: r8 = zdb
    //     0x8cec58: add             x8, PP, #0x14, lsl #12  ; [pp+0x14408] Type: zdb
    //     0x8cec5c: ldr             x8, [x8, #0x408]
    // 0x8cec60: r3 = Null
    //     0x8cec60: add             x3, PP, #0x14, lsl #12  ; [pp+0x14410] Null
    //     0x8cec64: ldr             x3, [x3, #0x410]
    // 0x8cec68: r0 = DefaultTypeTest()
    //     0x8cec68: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8cec6c: ldr             x1, [fp, #0x18]
    // 0x8cec70: ldr             x2, [fp, #0x10]
    // 0x8cec74: r0 = call 0x33f738
    //     0x8cec74: bl              #0x33f738
    // 0x8cec78: LeaveFrame
    //     0x8cec78: mov             SP, fp
    //     0x8cec7c: ldp             fp, lr, [SP], #0x10
    // 0x8cec80: ret
    //     0x8cec80: ret             
    // 0x8cec84: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cec84: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cec88: b               #0x8cec34
  }
  double [](zdb, int) {
    // ** addr: 0x8ceca4, size: 0xd8
    // 0x8ceca4: EnterFrame
    //     0x8ceca4: stp             fp, lr, [SP, #-0x10]!
    //     0x8ceca8: mov             fp, SP
    // 0x8cecac: ldr             x0, [fp, #0x10]
    // 0x8cecb0: r2 = Null
    //     0x8cecb0: mov             x2, NULL
    // 0x8cecb4: r1 = Null
    //     0x8cecb4: mov             x1, NULL
    // 0x8cecb8: branchIfSmi(r0, 0x8cece0)
    //     0x8cecb8: tbz             w0, #0, #0x8cece0
    // 0x8cecbc: r4 = LoadClassIdInstr(r0)
    //     0x8cecbc: ldur            x4, [x0, #-1]
    //     0x8cecc0: ubfx            x4, x4, #0xc, #0x14
    // 0x8cecc4: sub             x4, x4, #0x3b
    // 0x8cecc8: cmp             x4, #1
    // 0x8ceccc: b.ls            #0x8cece0
    // 0x8cecd0: r8 = int
    //     0x8cecd0: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8cecd4: r3 = Null
    //     0x8cecd4: add             x3, PP, #0x12, lsl #12  ; [pp+0x12908] Null
    //     0x8cecd8: ldr             x3, [x3, #0x908]
    // 0x8cecdc: r0 = int()
    //     0x8cecdc: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8cece0: ldr             x2, [fp, #0x18]
    // 0x8cece4: LoadField: r3 = r2->field_7
    //     0x8cece4: ldur            w3, [x2, #7]
    // 0x8cece8: DecompressPointer r3
    //     0x8cece8: add             x3, x3, HEAP, lsl #32
    // 0x8cecec: LoadField: r2 = r3->field_13
    //     0x8cecec: ldur            w2, [x3, #0x13]
    // 0x8cecf0: DecompressPointer r2
    //     0x8cecf0: add             x2, x2, HEAP, lsl #32
    // 0x8cecf4: ldr             x4, [fp, #0x10]
    // 0x8cecf8: r5 = LoadInt32Instr(r4)
    //     0x8cecf8: sbfx            x5, x4, #1, #0x1f
    //     0x8cecfc: tbz             w4, #0, #0x8ced04
    //     0x8ced00: ldur            x5, [x4, #7]
    // 0x8ced04: r0 = LoadInt32Instr(r2)
    //     0x8ced04: sbfx            x0, x2, #1, #0x1f
    // 0x8ced08: mov             x1, x5
    // 0x8ced0c: cmp             x1, x0
    // 0x8ced10: b.hs            #0x8ced50
    // 0x8ced14: ArrayLoad: d0 = r3[r5]  ; List_8
    //     0x8ced14: add             x16, x3, x5, lsl #3
    //     0x8ced18: ldur            d0, [x16, #0x17]
    // 0x8ced1c: r0 = inline_Allocate_Double()
    //     0x8ced1c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8ced20: add             x0, x0, #0x10
    //     0x8ced24: cmp             x1, x0
    //     0x8ced28: b.ls            #0x8ced54
    //     0x8ced2c: str             x0, [THR, #0x50]  ; THR::top
    //     0x8ced30: sub             x0, x0, #0xf
    //     0x8ced34: movz            x1, #0xd15c
    //     0x8ced38: movk            x1, #0x3, lsl #16
    //     0x8ced3c: stur            x1, [x0, #-1]
    // 0x8ced40: StoreField: r0->field_7 = d0
    //     0x8ced40: stur            d0, [x0, #7]
    // 0x8ced44: LeaveFrame
    //     0x8ced44: mov             SP, fp
    //     0x8ced48: ldp             fp, lr, [SP], #0x10
    // 0x8ced4c: ret
    //     0x8ced4c: ret             
    // 0x8ced50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8ced50: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8ced54: SaveReg d0
    //     0x8ced54: str             q0, [SP, #-0x10]!
    // 0x8ced58: r0 = AllocateDouble()
    //     0x8ced58: bl              #0x94f97c  ; AllocateDoubleStub
    // 0x8ced5c: RestoreReg d0
    //     0x8ced5c: ldr             q0, [SP], #0x10
    // 0x8ced60: b               #0x8ced40
  }
  zdb -(zdb, zdb) {
    // ** addr: 0x8ced7c, size: 0x84
    // 0x8ced7c: EnterFrame
    //     0x8ced7c: stp             fp, lr, [SP, #-0x10]!
    //     0x8ced80: mov             fp, SP
    // 0x8ced84: CheckStackOverflow
    //     0x8ced84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ced88: cmp             SP, x16
    //     0x8ced8c: b.ls            #0x8cede0
    // 0x8ced90: ldr             x0, [fp, #0x10]
    // 0x8ced94: r2 = Null
    //     0x8ced94: mov             x2, NULL
    // 0x8ced98: r1 = Null
    //     0x8ced98: mov             x1, NULL
    // 0x8ced9c: r4 = 59
    //     0x8ced9c: movz            x4, #0x3b
    // 0x8ceda0: branchIfSmi(r0, 0x8cedac)
    //     0x8ceda0: tbz             w0, #0, #0x8cedac
    // 0x8ceda4: r4 = LoadClassIdInstr(r0)
    //     0x8ceda4: ldur            x4, [x0, #-1]
    //     0x8ceda8: ubfx            x4, x4, #0xc, #0x14
    // 0x8cedac: cmp             x4, #0x1af
    // 0x8cedb0: b.eq            #0x8cedc8
    // 0x8cedb4: r8 = zdb
    //     0x8cedb4: add             x8, PP, #0x14, lsl #12  ; [pp+0x14408] Type: zdb
    //     0x8cedb8: ldr             x8, [x8, #0x408]
    // 0x8cedbc: r3 = Null
    //     0x8cedbc: add             x3, PP, #0x14, lsl #12  ; [pp+0x14420] Null
    //     0x8cedc0: ldr             x3, [x3, #0x420]
    // 0x8cedc4: r0 = DefaultTypeTest()
    //     0x8cedc4: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8cedc8: ldr             x1, [fp, #0x18]
    // 0x8cedcc: ldr             x2, [fp, #0x10]
    // 0x8cedd0: r0 = call 0x33f938
    //     0x8cedd0: bl              #0x33f938
    // 0x8cedd4: LeaveFrame
    //     0x8cedd4: mov             SP, fp
    //     0x8cedd8: ldp             fp, lr, [SP], #0x10
    // 0x8ceddc: ret
    //     0x8ceddc: ret             
    // 0x8cede0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cede0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cede4: b               #0x8ced90
  }
  zdb *(zdb, double) {
    // ** addr: 0x8cee00, size: 0x50
    // 0x8cee00: EnterFrame
    //     0x8cee00: stp             fp, lr, [SP, #-0x10]!
    //     0x8cee04: mov             fp, SP
    // 0x8cee08: CheckStackOverflow
    //     0x8cee08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cee0c: cmp             SP, x16
    //     0x8cee10: b.ls            #0x8cee30
    // 0x8cee14: ldr             x0, [fp, #0x10]
    // 0x8cee18: LoadField: d0 = r0->field_7
    //     0x8cee18: ldur            d0, [x0, #7]
    // 0x8cee1c: ldr             x1, [fp, #0x18]
    // 0x8cee20: r0 = call 0x33fa80
    //     0x8cee20: bl              #0x33fa80
    // 0x8cee24: LeaveFrame
    //     0x8cee24: mov             SP, fp
    //     0x8cee28: ldp             fp, lr, [SP], #0x10
    // 0x8cee2c: ret
    //     0x8cee2c: ret             
    // 0x8cee30: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cee30: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cee34: b               #0x8cee14
  }
  void []=(zdb, int, double) {
    // ** addr: 0x8cee50, size: 0x94
    // 0x8cee50: EnterFrame
    //     0x8cee50: stp             fp, lr, [SP, #-0x10]!
    //     0x8cee54: mov             fp, SP
    // 0x8cee58: ldr             x0, [fp, #0x18]
    // 0x8cee5c: r2 = Null
    //     0x8cee5c: mov             x2, NULL
    // 0x8cee60: r1 = Null
    //     0x8cee60: mov             x1, NULL
    // 0x8cee64: branchIfSmi(r0, 0x8cee8c)
    //     0x8cee64: tbz             w0, #0, #0x8cee8c
    // 0x8cee68: r4 = LoadClassIdInstr(r0)
    //     0x8cee68: ldur            x4, [x0, #-1]
    //     0x8cee6c: ubfx            x4, x4, #0xc, #0x14
    // 0x8cee70: sub             x4, x4, #0x3b
    // 0x8cee74: cmp             x4, #1
    // 0x8cee78: b.ls            #0x8cee8c
    // 0x8cee7c: r8 = int
    //     0x8cee7c: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8cee80: r3 = Null
    //     0x8cee80: add             x3, PP, #0x28, lsl #12  ; [pp+0x28298] Null
    //     0x8cee84: ldr             x3, [x3, #0x298]
    // 0x8cee88: r0 = int()
    //     0x8cee88: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8cee8c: ldr             x0, [fp, #0x10]
    // 0x8cee90: r2 = Null
    //     0x8cee90: mov             x2, NULL
    // 0x8cee94: r1 = Null
    //     0x8cee94: mov             x1, NULL
    // 0x8cee98: r4 = 59
    //     0x8cee98: movz            x4, #0x3b
    // 0x8cee9c: branchIfSmi(r0, 0x8ceea8)
    //     0x8cee9c: tbz             w0, #0, #0x8ceea8
    // 0x8ceea0: r4 = LoadClassIdInstr(r0)
    //     0x8ceea0: ldur            x4, [x0, #-1]
    //     0x8ceea4: ubfx            x4, x4, #0xc, #0x14
    // 0x8ceea8: cmp             x4, #0x3d
    // 0x8ceeac: b.eq            #0x8ceec0
    // 0x8ceeb0: r8 = double
    //     0x8ceeb0: ldr             x8, [PP, #0xd88]  ; [pp+0xd88] Type: double
    // 0x8ceeb4: r3 = Null
    //     0x8ceeb4: add             x3, PP, #0x28, lsl #12  ; [pp+0x282a8] Null
    //     0x8ceeb8: ldr             x3, [x3, #0x2a8]
    // 0x8ceebc: r0 = double()
    //     0x8ceebc: bl              #0x9589c8  ; IsType_double_Stub
    // 0x8ceec0: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x8ceec0: ldr             x0, [PP, #0x828]  ; [pp+0x828] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x8ceec4: r0 = Throw()
    //     0x8ceec4: bl              #0x94dd08  ; ThrowStub
    // 0x8ceec8: brk             #0
  }
  double dyn:get:length(zdb) {
    // ** addr: 0x8ceee4, size: 0x80
    // 0x8ceee4: EnterFrame
    //     0x8ceee4: stp             fp, lr, [SP, #-0x10]!
    //     0x8ceee8: mov             fp, SP
    // 0x8ceeec: CheckStackOverflow
    //     0x8ceeec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ceef0: cmp             SP, x16
    //     0x8ceef4: b.ls            #0x8cef34
    // 0x8ceef8: ldr             x1, [fp, #0x10]
    // 0x8ceefc: r0 = call 0x33fb54
    //     0x8ceefc: bl              #0x33fb54
    // 0x8cef00: r0 = inline_Allocate_Double()
    //     0x8cef00: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8cef04: add             x0, x0, #0x10
    //     0x8cef08: cmp             x1, x0
    //     0x8cef0c: b.ls            #0x8cef3c
    //     0x8cef10: str             x0, [THR, #0x50]  ; THR::top
    //     0x8cef14: sub             x0, x0, #0xf
    //     0x8cef18: movz            x1, #0xd15c
    //     0x8cef1c: movk            x1, #0x3, lsl #16
    //     0x8cef20: stur            x1, [x0, #-1]
    // 0x8cef24: StoreField: r0->field_7 = d0
    //     0x8cef24: stur            d0, [x0, #7]
    // 0x8cef28: LeaveFrame
    //     0x8cef28: mov             SP, fp
    //     0x8cef2c: ldp             fp, lr, [SP], #0x10
    // 0x8cef30: ret
    //     0x8cef30: ret             
    // 0x8cef34: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cef34: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cef38: b               #0x8ceef8
    // 0x8cef3c: SaveReg d0
    //     0x8cef3c: str             q0, [SP, #-0x10]!
    // 0x8cef40: r0 = AllocateDouble()
    //     0x8cef40: bl              #0x94f97c  ; AllocateDoubleStub
    // 0x8cef44: RestoreReg d0
    //     0x8cef44: ldr             q0, [SP], #0x10
    // 0x8cef48: b               #0x8cef24
  }
}

// class id: 432, size: 0xc, field offset: 0x8
class bO extends Object
    implements xdb {

  bO +(bO, bO) {
    // ** addr: 0x8d3700, size: 0x84
    // 0x8d3700: EnterFrame
    //     0x8d3700: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3704: mov             fp, SP
    // 0x8d3708: CheckStackOverflow
    //     0x8d3708: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d370c: cmp             SP, x16
    //     0x8d3710: b.ls            #0x8d3764
    // 0x8d3714: ldr             x0, [fp, #0x10]
    // 0x8d3718: r2 = Null
    //     0x8d3718: mov             x2, NULL
    // 0x8d371c: r1 = Null
    //     0x8d371c: mov             x1, NULL
    // 0x8d3720: r4 = 59
    //     0x8d3720: movz            x4, #0x3b
    // 0x8d3724: branchIfSmi(r0, 0x8d3730)
    //     0x8d3724: tbz             w0, #0, #0x8d3730
    // 0x8d3728: r4 = LoadClassIdInstr(r0)
    //     0x8d3728: ldur            x4, [x0, #-1]
    //     0x8d372c: ubfx            x4, x4, #0xc, #0x14
    // 0x8d3730: cmp             x4, #0x1b0
    // 0x8d3734: b.eq            #0x8d374c
    // 0x8d3738: r8 = bO
    //     0x8d3738: add             x8, PP, #0x14, lsl #12  ; [pp+0x143e0] Type: bO
    //     0x8d373c: ldr             x8, [x8, #0x3e0]
    // 0x8d3740: r3 = Null
    //     0x8d3740: add             x3, PP, #0x14, lsl #12  ; [pp+0x143e8] Null
    //     0x8d3744: ldr             x3, [x3, #0x3e8]
    // 0x8d3748: r0 = DefaultTypeTest()
    //     0x8d3748: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8d374c: ldr             x1, [fp, #0x18]
    // 0x8d3750: ldr             x2, [fp, #0x10]
    // 0x8d3754: r0 = call 0x372f08
    //     0x8d3754: bl              #0x372f08
    // 0x8d3758: LeaveFrame
    //     0x8d3758: mov             SP, fp
    //     0x8d375c: ldp             fp, lr, [SP], #0x10
    // 0x8d3760: ret
    //     0x8d3760: ret             
    // 0x8d3764: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3764: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3768: b               #0x8d3714
  }
  double [](bO, int) {
    // ** addr: 0x8d3784, size: 0xd8
    // 0x8d3784: EnterFrame
    //     0x8d3784: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3788: mov             fp, SP
    // 0x8d378c: ldr             x0, [fp, #0x10]
    // 0x8d3790: r2 = Null
    //     0x8d3790: mov             x2, NULL
    // 0x8d3794: r1 = Null
    //     0x8d3794: mov             x1, NULL
    // 0x8d3798: branchIfSmi(r0, 0x8d37c0)
    //     0x8d3798: tbz             w0, #0, #0x8d37c0
    // 0x8d379c: r4 = LoadClassIdInstr(r0)
    //     0x8d379c: ldur            x4, [x0, #-1]
    //     0x8d37a0: ubfx            x4, x4, #0xc, #0x14
    // 0x8d37a4: sub             x4, x4, #0x3b
    // 0x8d37a8: cmp             x4, #1
    // 0x8d37ac: b.ls            #0x8d37c0
    // 0x8d37b0: r8 = int
    //     0x8d37b0: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8d37b4: r3 = Null
    //     0x8d37b4: add             x3, PP, #0xf, lsl #12  ; [pp+0xf148] Null
    //     0x8d37b8: ldr             x3, [x3, #0x148]
    // 0x8d37bc: r0 = int()
    //     0x8d37bc: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8d37c0: ldr             x2, [fp, #0x18]
    // 0x8d37c4: LoadField: r3 = r2->field_7
    //     0x8d37c4: ldur            w3, [x2, #7]
    // 0x8d37c8: DecompressPointer r3
    //     0x8d37c8: add             x3, x3, HEAP, lsl #32
    // 0x8d37cc: LoadField: r2 = r3->field_13
    //     0x8d37cc: ldur            w2, [x3, #0x13]
    // 0x8d37d0: DecompressPointer r2
    //     0x8d37d0: add             x2, x2, HEAP, lsl #32
    // 0x8d37d4: ldr             x4, [fp, #0x10]
    // 0x8d37d8: r5 = LoadInt32Instr(r4)
    //     0x8d37d8: sbfx            x5, x4, #1, #0x1f
    //     0x8d37dc: tbz             w4, #0, #0x8d37e4
    //     0x8d37e0: ldur            x5, [x4, #7]
    // 0x8d37e4: r0 = LoadInt32Instr(r2)
    //     0x8d37e4: sbfx            x0, x2, #1, #0x1f
    // 0x8d37e8: mov             x1, x5
    // 0x8d37ec: cmp             x1, x0
    // 0x8d37f0: b.hs            #0x8d3830
    // 0x8d37f4: ArrayLoad: d0 = r3[r5]  ; List_8
    //     0x8d37f4: add             x16, x3, x5, lsl #3
    //     0x8d37f8: ldur            d0, [x16, #0x17]
    // 0x8d37fc: r0 = inline_Allocate_Double()
    //     0x8d37fc: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8d3800: add             x0, x0, #0x10
    //     0x8d3804: cmp             x1, x0
    //     0x8d3808: b.ls            #0x8d3834
    //     0x8d380c: str             x0, [THR, #0x50]  ; THR::top
    //     0x8d3810: sub             x0, x0, #0xf
    //     0x8d3814: movz            x1, #0xd15c
    //     0x8d3818: movk            x1, #0x3, lsl #16
    //     0x8d381c: stur            x1, [x0, #-1]
    // 0x8d3820: StoreField: r0->field_7 = d0
    //     0x8d3820: stur            d0, [x0, #7]
    // 0x8d3824: LeaveFrame
    //     0x8d3824: mov             SP, fp
    //     0x8d3828: ldp             fp, lr, [SP], #0x10
    // 0x8d382c: ret
    //     0x8d382c: ret             
    // 0x8d3830: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8d3830: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8d3834: SaveReg d0
    //     0x8d3834: str             q0, [SP, #-0x10]!
    // 0x8d3838: r0 = AllocateDouble()
    //     0x8d3838: bl              #0x94f97c  ; AllocateDoubleStub
    // 0x8d383c: RestoreReg d0
    //     0x8d383c: ldr             q0, [SP], #0x10
    // 0x8d3840: b               #0x8d3820
  }
  bO -(bO, bO) {
    // ** addr: 0x8d385c, size: 0x84
    // 0x8d385c: EnterFrame
    //     0x8d385c: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3860: mov             fp, SP
    // 0x8d3864: CheckStackOverflow
    //     0x8d3864: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d3868: cmp             SP, x16
    //     0x8d386c: b.ls            #0x8d38c0
    // 0x8d3870: ldr             x0, [fp, #0x10]
    // 0x8d3874: r2 = Null
    //     0x8d3874: mov             x2, NULL
    // 0x8d3878: r1 = Null
    //     0x8d3878: mov             x1, NULL
    // 0x8d387c: r4 = 59
    //     0x8d387c: movz            x4, #0x3b
    // 0x8d3880: branchIfSmi(r0, 0x8d388c)
    //     0x8d3880: tbz             w0, #0, #0x8d388c
    // 0x8d3884: r4 = LoadClassIdInstr(r0)
    //     0x8d3884: ldur            x4, [x0, #-1]
    //     0x8d3888: ubfx            x4, x4, #0xc, #0x14
    // 0x8d388c: cmp             x4, #0x1b0
    // 0x8d3890: b.eq            #0x8d38a8
    // 0x8d3894: r8 = bO
    //     0x8d3894: add             x8, PP, #0x14, lsl #12  ; [pp+0x143e0] Type: bO
    //     0x8d3898: ldr             x8, [x8, #0x3e0]
    // 0x8d389c: r3 = Null
    //     0x8d389c: add             x3, PP, #0x14, lsl #12  ; [pp+0x143f8] Null
    //     0x8d38a0: ldr             x3, [x3, #0x3f8]
    // 0x8d38a4: r0 = DefaultTypeTest()
    //     0x8d38a4: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8d38a8: ldr             x1, [fp, #0x18]
    // 0x8d38ac: ldr             x2, [fp, #0x10]
    // 0x8d38b0: r0 = call 0x372bfc
    //     0x8d38b0: bl              #0x372bfc
    // 0x8d38b4: LeaveFrame
    //     0x8d38b4: mov             SP, fp
    //     0x8d38b8: ldp             fp, lr, [SP], #0x10
    // 0x8d38bc: ret
    //     0x8d38bc: ret             
    // 0x8d38c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d38c0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d38c4: b               #0x8d3870
  }
  bO *(bO, double) {
    // ** addr: 0x8d38e0, size: 0x50
    // 0x8d38e0: EnterFrame
    //     0x8d38e0: stp             fp, lr, [SP, #-0x10]!
    //     0x8d38e4: mov             fp, SP
    // 0x8d38e8: CheckStackOverflow
    //     0x8d38e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d38ec: cmp             SP, x16
    //     0x8d38f0: b.ls            #0x8d3910
    // 0x8d38f4: ldr             x0, [fp, #0x10]
    // 0x8d38f8: LoadField: d0 = r0->field_7
    //     0x8d38f8: ldur            d0, [x0, #7]
    // 0x8d38fc: ldr             x1, [fp, #0x18]
    // 0x8d3900: r0 = call 0x372b74
    //     0x8d3900: bl              #0x372b74
    // 0x8d3904: LeaveFrame
    //     0x8d3904: mov             SP, fp
    //     0x8d3908: ldp             fp, lr, [SP], #0x10
    // 0x8d390c: ret
    //     0x8d390c: ret             
    // 0x8d3910: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3910: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3914: b               #0x8d38f4
  }
  void []=(bO, int, double) {
    // ** addr: 0x8d3930, size: 0x94
    // 0x8d3930: EnterFrame
    //     0x8d3930: stp             fp, lr, [SP, #-0x10]!
    //     0x8d3934: mov             fp, SP
    // 0x8d3938: ldr             x0, [fp, #0x18]
    // 0x8d393c: r2 = Null
    //     0x8d393c: mov             x2, NULL
    // 0x8d3940: r1 = Null
    //     0x8d3940: mov             x1, NULL
    // 0x8d3944: branchIfSmi(r0, 0x8d396c)
    //     0x8d3944: tbz             w0, #0, #0x8d396c
    // 0x8d3948: r4 = LoadClassIdInstr(r0)
    //     0x8d3948: ldur            x4, [x0, #-1]
    //     0x8d394c: ubfx            x4, x4, #0xc, #0x14
    // 0x8d3950: sub             x4, x4, #0x3b
    // 0x8d3954: cmp             x4, #1
    // 0x8d3958: b.ls            #0x8d396c
    // 0x8d395c: r8 = int
    //     0x8d395c: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8d3960: r3 = Null
    //     0x8d3960: add             x3, PP, #0x28, lsl #12  ; [pp+0x28278] Null
    //     0x8d3964: ldr             x3, [x3, #0x278]
    // 0x8d3968: r0 = int()
    //     0x8d3968: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8d396c: ldr             x0, [fp, #0x10]
    // 0x8d3970: r2 = Null
    //     0x8d3970: mov             x2, NULL
    // 0x8d3974: r1 = Null
    //     0x8d3974: mov             x1, NULL
    // 0x8d3978: r4 = 59
    //     0x8d3978: movz            x4, #0x3b
    // 0x8d397c: branchIfSmi(r0, 0x8d3988)
    //     0x8d397c: tbz             w0, #0, #0x8d3988
    // 0x8d3980: r4 = LoadClassIdInstr(r0)
    //     0x8d3980: ldur            x4, [x0, #-1]
    //     0x8d3984: ubfx            x4, x4, #0xc, #0x14
    // 0x8d3988: cmp             x4, #0x3d
    // 0x8d398c: b.eq            #0x8d39a0
    // 0x8d3990: r8 = double
    //     0x8d3990: ldr             x8, [PP, #0xd88]  ; [pp+0xd88] Type: double
    // 0x8d3994: r3 = Null
    //     0x8d3994: add             x3, PP, #0x28, lsl #12  ; [pp+0x28288] Null
    //     0x8d3998: ldr             x3, [x3, #0x288]
    // 0x8d399c: r0 = double()
    //     0x8d399c: bl              #0x9589c8  ; IsType_double_Stub
    // 0x8d39a0: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x8d39a0: ldr             x0, [PP, #0x828]  ; [pp+0x828] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x8d39a4: r0 = Throw()
    //     0x8d39a4: bl              #0x94dd08  ; ThrowStub
    // 0x8d39a8: brk             #0
  }
  double dyn:get:length(bO) {
    // ** addr: 0x8d39c4, size: 0x80
    // 0x8d39c4: EnterFrame
    //     0x8d39c4: stp             fp, lr, [SP, #-0x10]!
    //     0x8d39c8: mov             fp, SP
    // 0x8d39cc: CheckStackOverflow
    //     0x8d39cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d39d0: cmp             SP, x16
    //     0x8d39d4: b.ls            #0x8d3a14
    // 0x8d39d8: ldr             x1, [fp, #0x10]
    // 0x8d39dc: r0 = call 0x373018
    //     0x8d39dc: bl              #0x373018
    // 0x8d39e0: r0 = inline_Allocate_Double()
    //     0x8d39e0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8d39e4: add             x0, x0, #0x10
    //     0x8d39e8: cmp             x1, x0
    //     0x8d39ec: b.ls            #0x8d3a1c
    //     0x8d39f0: str             x0, [THR, #0x50]  ; THR::top
    //     0x8d39f4: sub             x0, x0, #0xf
    //     0x8d39f8: movz            x1, #0xd15c
    //     0x8d39fc: movk            x1, #0x3, lsl #16
    //     0x8d3a00: stur            x1, [x0, #-1]
    // 0x8d3a04: StoreField: r0->field_7 = d0
    //     0x8d3a04: stur            d0, [x0, #7]
    // 0x8d3a08: LeaveFrame
    //     0x8d3a08: mov             SP, fp
    //     0x8d3a0c: ldp             fp, lr, [SP], #0x10
    // 0x8d3a10: ret
    //     0x8d3a10: ret             
    // 0x8d3a14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d3a14: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d3a18: b               #0x8d39d8
    // 0x8d3a1c: SaveReg d0
    //     0x8d3a1c: str             q0, [SP, #-0x10]!
    // 0x8d3a20: r0 = AllocateDouble()
    //     0x8d3a20: bl              #0x94f97c  ; AllocateDoubleStub
    // 0x8d3a24: RestoreReg d0
    //     0x8d3a24: ldr             q0, [SP], #0x10
    // 0x8d3a28: b               #0x8d3a04
  }
}

// class id: 433, size: 0xc, field offset: 0x8
class ydb extends Object
    implements xdb {

  ydb +(ydb, ydb) {
    // ** addr: 0x938cfc, size: 0x84
    // 0x938cfc: EnterFrame
    //     0x938cfc: stp             fp, lr, [SP, #-0x10]!
    //     0x938d00: mov             fp, SP
    // 0x938d04: CheckStackOverflow
    //     0x938d04: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x938d08: cmp             SP, x16
    //     0x938d0c: b.ls            #0x938d60
    // 0x938d10: ldr             x0, [fp, #0x10]
    // 0x938d14: r2 = Null
    //     0x938d14: mov             x2, NULL
    // 0x938d18: r1 = Null
    //     0x938d18: mov             x1, NULL
    // 0x938d1c: r4 = 59
    //     0x938d1c: movz            x4, #0x3b
    // 0x938d20: branchIfSmi(r0, 0x938d2c)
    //     0x938d20: tbz             w0, #0, #0x938d2c
    // 0x938d24: r4 = LoadClassIdInstr(r0)
    //     0x938d24: ldur            x4, [x0, #-1]
    //     0x938d28: ubfx            x4, x4, #0xc, #0x14
    // 0x938d2c: cmp             x4, #0x1b1
    // 0x938d30: b.eq            #0x938d48
    // 0x938d34: r8 = ydb
    //     0x938d34: add             x8, PP, #0x49, lsl #12  ; [pp+0x49920] Type: ydb
    //     0x938d38: ldr             x8, [x8, #0x920]
    // 0x938d3c: r3 = Null
    //     0x938d3c: add             x3, PP, #0x49, lsl #12  ; [pp+0x49928] Null
    //     0x938d40: ldr             x3, [x3, #0x928]
    // 0x938d44: r0 = ydb()
    //     0x938d44: bl              #0x939034  ; IsType_ydb_Stub
    // 0x938d48: ldr             x1, [fp, #0x18]
    // 0x938d4c: ldr             x2, [fp, #0x10]
    // 0x938d50: r0 = call 0x7914a0
    //     0x938d50: bl              #0x7914a0
    // 0x938d54: LeaveFrame
    //     0x938d54: mov             SP, fp
    //     0x938d58: ldp             fp, lr, [SP], #0x10
    // 0x938d5c: ret
    //     0x938d5c: ret             
    // 0x938d60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x938d60: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x938d64: b               #0x938d10
  }
  double [](ydb, int) {
    // ** addr: 0x938d8c, size: 0xd8
    // 0x938d8c: EnterFrame
    //     0x938d8c: stp             fp, lr, [SP, #-0x10]!
    //     0x938d90: mov             fp, SP
    // 0x938d94: ldr             x0, [fp, #0x10]
    // 0x938d98: r2 = Null
    //     0x938d98: mov             x2, NULL
    // 0x938d9c: r1 = Null
    //     0x938d9c: mov             x1, NULL
    // 0x938da0: branchIfSmi(r0, 0x938dc8)
    //     0x938da0: tbz             w0, #0, #0x938dc8
    // 0x938da4: r4 = LoadClassIdInstr(r0)
    //     0x938da4: ldur            x4, [x0, #-1]
    //     0x938da8: ubfx            x4, x4, #0xc, #0x14
    // 0x938dac: sub             x4, x4, #0x3b
    // 0x938db0: cmp             x4, #1
    // 0x938db4: b.ls            #0x938dc8
    // 0x938db8: r8 = int
    //     0x938db8: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x938dbc: r3 = Null
    //     0x938dbc: add             x3, PP, #0x49, lsl #12  ; [pp+0x49910] Null
    //     0x938dc0: ldr             x3, [x3, #0x910]
    // 0x938dc4: r0 = int()
    //     0x938dc4: bl              #0x9595b0  ; IsType_int_Stub
    // 0x938dc8: ldr             x2, [fp, #0x18]
    // 0x938dcc: LoadField: r3 = r2->field_7
    //     0x938dcc: ldur            w3, [x2, #7]
    // 0x938dd0: DecompressPointer r3
    //     0x938dd0: add             x3, x3, HEAP, lsl #32
    // 0x938dd4: LoadField: r2 = r3->field_13
    //     0x938dd4: ldur            w2, [x3, #0x13]
    // 0x938dd8: DecompressPointer r2
    //     0x938dd8: add             x2, x2, HEAP, lsl #32
    // 0x938ddc: ldr             x4, [fp, #0x10]
    // 0x938de0: r5 = LoadInt32Instr(r4)
    //     0x938de0: sbfx            x5, x4, #1, #0x1f
    //     0x938de4: tbz             w4, #0, #0x938dec
    //     0x938de8: ldur            x5, [x4, #7]
    // 0x938dec: r0 = LoadInt32Instr(r2)
    //     0x938dec: sbfx            x0, x2, #1, #0x1f
    // 0x938df0: mov             x1, x5
    // 0x938df4: cmp             x1, x0
    // 0x938df8: b.hs            #0x938e38
    // 0x938dfc: ArrayLoad: d0 = r3[r5]  ; List_8
    //     0x938dfc: add             x16, x3, x5, lsl #3
    //     0x938e00: ldur            d0, [x16, #0x17]
    // 0x938e04: r0 = inline_Allocate_Double()
    //     0x938e04: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x938e08: add             x0, x0, #0x10
    //     0x938e0c: cmp             x1, x0
    //     0x938e10: b.ls            #0x938e3c
    //     0x938e14: str             x0, [THR, #0x50]  ; THR::top
    //     0x938e18: sub             x0, x0, #0xf
    //     0x938e1c: movz            x1, #0xd15c
    //     0x938e20: movk            x1, #0x3, lsl #16
    //     0x938e24: stur            x1, [x0, #-1]
    // 0x938e28: StoreField: r0->field_7 = d0
    //     0x938e28: stur            d0, [x0, #7]
    // 0x938e2c: LeaveFrame
    //     0x938e2c: mov             SP, fp
    //     0x938e30: ldp             fp, lr, [SP], #0x10
    // 0x938e34: ret
    //     0x938e34: ret             
    // 0x938e38: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x938e38: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
    // 0x938e3c: SaveReg d0
    //     0x938e3c: str             q0, [SP, #-0x10]!
    // 0x938e40: r0 = AllocateDouble()
    //     0x938e40: bl              #0x94f97c  ; AllocateDoubleStub
    // 0x938e44: RestoreReg d0
    //     0x938e44: ldr             q0, [SP], #0x10
    // 0x938e48: b               #0x938e28
  }
  ydb -(ydb, ydb) {
    // ** addr: 0x938e64, size: 0x84
    // 0x938e64: EnterFrame
    //     0x938e64: stp             fp, lr, [SP, #-0x10]!
    //     0x938e68: mov             fp, SP
    // 0x938e6c: CheckStackOverflow
    //     0x938e6c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x938e70: cmp             SP, x16
    //     0x938e74: b.ls            #0x938ec8
    // 0x938e78: ldr             x0, [fp, #0x10]
    // 0x938e7c: r2 = Null
    //     0x938e7c: mov             x2, NULL
    // 0x938e80: r1 = Null
    //     0x938e80: mov             x1, NULL
    // 0x938e84: r4 = 59
    //     0x938e84: movz            x4, #0x3b
    // 0x938e88: branchIfSmi(r0, 0x938e94)
    //     0x938e88: tbz             w0, #0, #0x938e94
    // 0x938e8c: r4 = LoadClassIdInstr(r0)
    //     0x938e8c: ldur            x4, [x0, #-1]
    //     0x938e90: ubfx            x4, x4, #0xc, #0x14
    // 0x938e94: cmp             x4, #0x1b1
    // 0x938e98: b.eq            #0x938eb0
    // 0x938e9c: r8 = ydb
    //     0x938e9c: add             x8, PP, #0x49, lsl #12  ; [pp+0x49920] Type: ydb
    //     0x938ea0: ldr             x8, [x8, #0x920]
    // 0x938ea4: r3 = Null
    //     0x938ea4: add             x3, PP, #0x49, lsl #12  ; [pp+0x49938] Null
    //     0x938ea8: ldr             x3, [x3, #0x938]
    // 0x938eac: r0 = ydb()
    //     0x938eac: bl              #0x939034  ; IsType_ydb_Stub
    // 0x938eb0: ldr             x1, [fp, #0x18]
    // 0x938eb4: ldr             x2, [fp, #0x10]
    // 0x938eb8: r0 = call 0x791620
    //     0x938eb8: bl              #0x791620
    // 0x938ebc: LeaveFrame
    //     0x938ebc: mov             SP, fp
    //     0x938ec0: ldp             fp, lr, [SP], #0x10
    // 0x938ec4: ret
    //     0x938ec4: ret             
    // 0x938ec8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x938ec8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x938ecc: b               #0x938e78
  }
  ydb *(ydb, double) {
    // ** addr: 0x938ee8, size: 0x50
    // 0x938ee8: EnterFrame
    //     0x938ee8: stp             fp, lr, [SP, #-0x10]!
    //     0x938eec: mov             fp, SP
    // 0x938ef0: CheckStackOverflow
    //     0x938ef0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x938ef4: cmp             SP, x16
    //     0x938ef8: b.ls            #0x938f18
    // 0x938efc: ldr             x0, [fp, #0x10]
    // 0x938f00: LoadField: d0 = r0->field_7
    //     0x938f00: ldur            d0, [x0, #7]
    // 0x938f04: ldr             x1, [fp, #0x18]
    // 0x938f08: r0 = call 0x7916f8
    //     0x938f08: bl              #0x7916f8
    // 0x938f0c: LeaveFrame
    //     0x938f0c: mov             SP, fp
    //     0x938f10: ldp             fp, lr, [SP], #0x10
    // 0x938f14: ret
    //     0x938f14: ret             
    // 0x938f18: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x938f18: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x938f1c: b               #0x938efc
  }
  void []=(ydb, int, double) {
    // ** addr: 0x938f38, size: 0x94
    // 0x938f38: EnterFrame
    //     0x938f38: stp             fp, lr, [SP, #-0x10]!
    //     0x938f3c: mov             fp, SP
    // 0x938f40: ldr             x0, [fp, #0x18]
    // 0x938f44: r2 = Null
    //     0x938f44: mov             x2, NULL
    // 0x938f48: r1 = Null
    //     0x938f48: mov             x1, NULL
    // 0x938f4c: branchIfSmi(r0, 0x938f74)
    //     0x938f4c: tbz             w0, #0, #0x938f74
    // 0x938f50: r4 = LoadClassIdInstr(r0)
    //     0x938f50: ldur            x4, [x0, #-1]
    //     0x938f54: ubfx            x4, x4, #0xc, #0x14
    // 0x938f58: sub             x4, x4, #0x3b
    // 0x938f5c: cmp             x4, #1
    // 0x938f60: b.ls            #0x938f74
    // 0x938f64: r8 = int
    //     0x938f64: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x938f68: r3 = Null
    //     0x938f68: add             x3, PP, #0x49, lsl #12  ; [pp+0x498f0] Null
    //     0x938f6c: ldr             x3, [x3, #0x8f0]
    // 0x938f70: r0 = int()
    //     0x938f70: bl              #0x9595b0  ; IsType_int_Stub
    // 0x938f74: ldr             x0, [fp, #0x10]
    // 0x938f78: r2 = Null
    //     0x938f78: mov             x2, NULL
    // 0x938f7c: r1 = Null
    //     0x938f7c: mov             x1, NULL
    // 0x938f80: r4 = 59
    //     0x938f80: movz            x4, #0x3b
    // 0x938f84: branchIfSmi(r0, 0x938f90)
    //     0x938f84: tbz             w0, #0, #0x938f90
    // 0x938f88: r4 = LoadClassIdInstr(r0)
    //     0x938f88: ldur            x4, [x0, #-1]
    //     0x938f8c: ubfx            x4, x4, #0xc, #0x14
    // 0x938f90: cmp             x4, #0x3d
    // 0x938f94: b.eq            #0x938fa8
    // 0x938f98: r8 = double
    //     0x938f98: ldr             x8, [PP, #0xd88]  ; [pp+0xd88] Type: double
    // 0x938f9c: r3 = Null
    //     0x938f9c: add             x3, PP, #0x49, lsl #12  ; [pp+0x49900] Null
    //     0x938fa0: ldr             x3, [x3, #0x900]
    // 0x938fa4: r0 = double()
    //     0x938fa4: bl              #0x9589c8  ; IsType_double_Stub
    // 0x938fa8: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x938fa8: ldr             x0, [PP, #0x828]  ; [pp+0x828] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x938fac: r0 = Throw()
    //     0x938fac: bl              #0x94dd08  ; ThrowStub
    // 0x938fb0: brk             #0
  }
  double dyn:get:length(ydb) {
    // ** addr: 0x938fcc, size: 0x80
    // 0x938fcc: EnterFrame
    //     0x938fcc: stp             fp, lr, [SP, #-0x10]!
    //     0x938fd0: mov             fp, SP
    // 0x938fd4: CheckStackOverflow
    //     0x938fd4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x938fd8: cmp             SP, x16
    //     0x938fdc: b.ls            #0x93901c
    // 0x938fe0: ldr             x1, [fp, #0x10]
    // 0x938fe4: r0 = call 0x791774
    //     0x938fe4: bl              #0x791774
    // 0x938fe8: r0 = inline_Allocate_Double()
    //     0x938fe8: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x938fec: add             x0, x0, #0x10
    //     0x938ff0: cmp             x1, x0
    //     0x938ff4: b.ls            #0x939024
    //     0x938ff8: str             x0, [THR, #0x50]  ; THR::top
    //     0x938ffc: sub             x0, x0, #0xf
    //     0x939000: movz            x1, #0xd15c
    //     0x939004: movk            x1, #0x3, lsl #16
    //     0x939008: stur            x1, [x0, #-1]
    // 0x93900c: StoreField: r0->field_7 = d0
    //     0x93900c: stur            d0, [x0, #7]
    // 0x939010: LeaveFrame
    //     0x939010: mov             SP, fp
    //     0x939014: ldp             fp, lr, [SP], #0x10
    // 0x939018: ret
    //     0x939018: ret             
    // 0x93901c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x93901c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x939020: b               #0x938fe0
    // 0x939024: SaveReg d0
    //     0x939024: str             q0, [SP, #-0x10]!
    // 0x939028: r0 = AllocateDouble()
    //     0x939028: bl              #0x94f97c  ; AllocateDoubleStub
    // 0x93902c: RestoreReg d0
    //     0x93902c: ldr             q0, [SP], #0x10
    // 0x939030: b               #0x93900c
  }
}

// class id: 434, size: 0x8, field offset: 0x8
abstract class xdb extends Object {
}

// class id: 435, size: 0xc, field offset: 0x8
class wdb extends Object {

  wdb +(wdb, wdb) {
    // ** addr: 0x934804, size: 0x84
    // 0x934804: EnterFrame
    //     0x934804: stp             fp, lr, [SP, #-0x10]!
    //     0x934808: mov             fp, SP
    // 0x93480c: CheckStackOverflow
    //     0x93480c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x934810: cmp             SP, x16
    //     0x934814: b.ls            #0x934868
    // 0x934818: ldr             x0, [fp, #0x10]
    // 0x93481c: r2 = Null
    //     0x93481c: mov             x2, NULL
    // 0x934820: r1 = Null
    //     0x934820: mov             x1, NULL
    // 0x934824: r4 = 59
    //     0x934824: movz            x4, #0x3b
    // 0x934828: branchIfSmi(r0, 0x934834)
    //     0x934828: tbz             w0, #0, #0x934834
    // 0x93482c: r4 = LoadClassIdInstr(r0)
    //     0x93482c: ldur            x4, [x0, #-1]
    //     0x934830: ubfx            x4, x4, #0xc, #0x14
    // 0x934834: cmp             x4, #0x1b3
    // 0x934838: b.eq            #0x934850
    // 0x93483c: r8 = wdb
    //     0x93483c: add             x8, PP, #0x4a, lsl #12  ; [pp+0x4a388] Type: wdb
    //     0x934840: ldr             x8, [x8, #0x388]
    // 0x934844: r3 = Null
    //     0x934844: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a3a0] Null
    //     0x934848: ldr             x3, [x3, #0x3a0]
    // 0x93484c: r0 = DefaultTypeTest()
    //     0x93484c: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x934850: ldr             x1, [fp, #0x18]
    // 0x934854: ldr             x2, [fp, #0x10]
    // 0x934858: r0 = call 0x7270ec
    //     0x934858: bl              #0x7270ec
    // 0x93485c: LeaveFrame
    //     0x93485c: mov             SP, fp
    //     0x934860: ldp             fp, lr, [SP], #0x10
    // 0x934864: ret
    //     0x934864: ret             
    // 0x934868: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x934868: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x93486c: b               #0x934818
  }
  double [](wdb, int) {
    // ** addr: 0x934888, size: 0xd8
    // 0x934888: EnterFrame
    //     0x934888: stp             fp, lr, [SP, #-0x10]!
    //     0x93488c: mov             fp, SP
    // 0x934890: ldr             x0, [fp, #0x10]
    // 0x934894: r2 = Null
    //     0x934894: mov             x2, NULL
    // 0x934898: r1 = Null
    //     0x934898: mov             x1, NULL
    // 0x93489c: branchIfSmi(r0, 0x9348c4)
    //     0x93489c: tbz             w0, #0, #0x9348c4
    // 0x9348a0: r4 = LoadClassIdInstr(r0)
    //     0x9348a0: ldur            x4, [x0, #-1]
    //     0x9348a4: ubfx            x4, x4, #0xc, #0x14
    // 0x9348a8: sub             x4, x4, #0x3b
    // 0x9348ac: cmp             x4, #1
    // 0x9348b0: b.ls            #0x9348c4
    // 0x9348b4: r8 = int
    //     0x9348b4: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x9348b8: r3 = Null
    //     0x9348b8: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a378] Null
    //     0x9348bc: ldr             x3, [x3, #0x378]
    // 0x9348c0: r0 = int()
    //     0x9348c0: bl              #0x9595b0  ; IsType_int_Stub
    // 0x9348c4: ldr             x2, [fp, #0x18]
    // 0x9348c8: LoadField: r3 = r2->field_7
    //     0x9348c8: ldur            w3, [x2, #7]
    // 0x9348cc: DecompressPointer r3
    //     0x9348cc: add             x3, x3, HEAP, lsl #32
    // 0x9348d0: LoadField: r2 = r3->field_13
    //     0x9348d0: ldur            w2, [x3, #0x13]
    // 0x9348d4: DecompressPointer r2
    //     0x9348d4: add             x2, x2, HEAP, lsl #32
    // 0x9348d8: ldr             x4, [fp, #0x10]
    // 0x9348dc: r5 = LoadInt32Instr(r4)
    //     0x9348dc: sbfx            x5, x4, #1, #0x1f
    //     0x9348e0: tbz             w4, #0, #0x9348e8
    //     0x9348e4: ldur            x5, [x4, #7]
    // 0x9348e8: r0 = LoadInt32Instr(r2)
    //     0x9348e8: sbfx            x0, x2, #1, #0x1f
    // 0x9348ec: mov             x1, x5
    // 0x9348f0: cmp             x1, x0
    // 0x9348f4: b.hs            #0x934934
    // 0x9348f8: ArrayLoad: d0 = r3[r5]  ; List_8
    //     0x9348f8: add             x16, x3, x5, lsl #3
    //     0x9348fc: ldur            d0, [x16, #0x17]
    // 0x934900: r0 = inline_Allocate_Double()
    //     0x934900: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x934904: add             x0, x0, #0x10
    //     0x934908: cmp             x1, x0
    //     0x93490c: b.ls            #0x934938
    //     0x934910: str             x0, [THR, #0x50]  ; THR::top
    //     0x934914: sub             x0, x0, #0xf
    //     0x934918: movz            x1, #0xd15c
    //     0x93491c: movk            x1, #0x3, lsl #16
    //     0x934920: stur            x1, [x0, #-1]
    // 0x934924: StoreField: r0->field_7 = d0
    //     0x934924: stur            d0, [x0, #7]
    // 0x934928: LeaveFrame
    //     0x934928: mov             SP, fp
    //     0x93492c: ldp             fp, lr, [SP], #0x10
    // 0x934930: ret
    //     0x934930: ret             
    // 0x934934: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x934934: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
    // 0x934938: SaveReg d0
    //     0x934938: str             q0, [SP, #-0x10]!
    // 0x93493c: r0 = AllocateDouble()
    //     0x93493c: bl              #0x94f97c  ; AllocateDoubleStub
    // 0x934940: RestoreReg d0
    //     0x934940: ldr             q0, [SP], #0x10
    // 0x934944: b               #0x934924
  }
  wdb -(wdb, wdb) {
    // ** addr: 0x934960, size: 0x84
    // 0x934960: EnterFrame
    //     0x934960: stp             fp, lr, [SP, #-0x10]!
    //     0x934964: mov             fp, SP
    // 0x934968: CheckStackOverflow
    //     0x934968: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x93496c: cmp             SP, x16
    //     0x934970: b.ls            #0x9349c4
    // 0x934974: ldr             x0, [fp, #0x10]
    // 0x934978: r2 = Null
    //     0x934978: mov             x2, NULL
    // 0x93497c: r1 = Null
    //     0x93497c: mov             x1, NULL
    // 0x934980: r4 = 59
    //     0x934980: movz            x4, #0x3b
    // 0x934984: branchIfSmi(r0, 0x934990)
    //     0x934984: tbz             w0, #0, #0x934990
    // 0x934988: r4 = LoadClassIdInstr(r0)
    //     0x934988: ldur            x4, [x0, #-1]
    //     0x93498c: ubfx            x4, x4, #0xc, #0x14
    // 0x934990: cmp             x4, #0x1b3
    // 0x934994: b.eq            #0x9349ac
    // 0x934998: r8 = wdb
    //     0x934998: add             x8, PP, #0x4a, lsl #12  ; [pp+0x4a388] Type: wdb
    //     0x93499c: ldr             x8, [x8, #0x388]
    // 0x9349a0: r3 = Null
    //     0x9349a0: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a390] Null
    //     0x9349a4: ldr             x3, [x3, #0x390]
    // 0x9349a8: r0 = DefaultTypeTest()
    //     0x9349a8: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x9349ac: ldr             x1, [fp, #0x18]
    // 0x9349b0: ldr             x2, [fp, #0x10]
    // 0x9349b4: r0 = call 0x726fa4
    //     0x9349b4: bl              #0x726fa4
    // 0x9349b8: LeaveFrame
    //     0x9349b8: mov             SP, fp
    //     0x9349bc: ldp             fp, lr, [SP], #0x10
    // 0x9349c0: ret
    //     0x9349c0: ret             
    // 0x9349c4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9349c4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9349c8: b               #0x934974
  }
  wdb *(wdb, wdb) {
    // ** addr: 0x9349e4, size: 0x88
    // 0x9349e4: EnterFrame
    //     0x9349e4: stp             fp, lr, [SP, #-0x10]!
    //     0x9349e8: mov             fp, SP
    // 0x9349ec: ldr             x0, [fp, #0x10]
    // 0x9349f0: r2 = Null
    //     0x9349f0: mov             x2, NULL
    // 0x9349f4: r1 = Null
    //     0x9349f4: mov             x1, NULL
    // 0x9349f8: r4 = LoadClassIdInstr(r0)
    //     0x9349f8: ldur            x4, [x0, #-1]
    //     0x9349fc: ubfx            x4, x4, #0xc, #0x14
    // 0x934a00: cmp             x4, #0x1b3
    // 0x934a04: b.eq            #0x934a1c
    // 0x934a08: r8 = wdb
    //     0x934a08: add             x8, PP, #0x4a, lsl #12  ; [pp+0x4a388] Type: wdb
    //     0x934a0c: ldr             x8, [x8, #0x388]
    // 0x934a10: r3 = Null
    //     0x934a10: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a3b0] Null
    //     0x934a14: ldr             x3, [x3, #0x3b0]
    // 0x934a18: r0 = DefaultTypeTest()
    //     0x934a18: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x934a1c: ldr             x0, [fp, #0x18]
    // 0x934a20: LoadField: r1 = r0->field_7
    //     0x934a20: ldur            w1, [x0, #7]
    // 0x934a24: DecompressPointer r1
    //     0x934a24: add             x1, x1, HEAP, lsl #32
    // 0x934a28: LoadField: r0 = r1->field_13
    //     0x934a28: ldur            w0, [x1, #0x13]
    // 0x934a2c: DecompressPointer r0
    //     0x934a2c: add             x0, x0, HEAP, lsl #32
    // 0x934a30: r1 = LoadInt32Instr(r0)
    //     0x934a30: sbfx            x1, x0, #1, #0x1f
    // 0x934a34: mov             x0, x1
    // 0x934a38: r1 = 3
    //     0x934a38: movz            x1, #0x3
    // 0x934a3c: cmp             x1, x0
    // 0x934a40: b.hs            #0x934a50
    // 0x934a44: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x934a44: ldr             x0, [PP, #0x828]  ; [pp+0x828] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x934a48: r0 = Throw()
    //     0x934a48: bl              #0x94dd08  ; ThrowStub
    // 0x934a4c: brk             #0
    // 0x934a50: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x934a50: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
  }
  void []=(wdb, int, double) {
    // ** addr: 0x934a6c, size: 0x94
    // 0x934a6c: EnterFrame
    //     0x934a6c: stp             fp, lr, [SP, #-0x10]!
    //     0x934a70: mov             fp, SP
    // 0x934a74: ldr             x0, [fp, #0x18]
    // 0x934a78: r2 = Null
    //     0x934a78: mov             x2, NULL
    // 0x934a7c: r1 = Null
    //     0x934a7c: mov             x1, NULL
    // 0x934a80: branchIfSmi(r0, 0x934aa8)
    //     0x934a80: tbz             w0, #0, #0x934aa8
    // 0x934a84: r4 = LoadClassIdInstr(r0)
    //     0x934a84: ldur            x4, [x0, #-1]
    //     0x934a88: ubfx            x4, x4, #0xc, #0x14
    // 0x934a8c: sub             x4, x4, #0x3b
    // 0x934a90: cmp             x4, #1
    // 0x934a94: b.ls            #0x934aa8
    // 0x934a98: r8 = int
    //     0x934a98: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x934a9c: r3 = Null
    //     0x934a9c: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a358] Null
    //     0x934aa0: ldr             x3, [x3, #0x358]
    // 0x934aa4: r0 = int()
    //     0x934aa4: bl              #0x9595b0  ; IsType_int_Stub
    // 0x934aa8: ldr             x0, [fp, #0x10]
    // 0x934aac: r2 = Null
    //     0x934aac: mov             x2, NULL
    // 0x934ab0: r1 = Null
    //     0x934ab0: mov             x1, NULL
    // 0x934ab4: r4 = 59
    //     0x934ab4: movz            x4, #0x3b
    // 0x934ab8: branchIfSmi(r0, 0x934ac4)
    //     0x934ab8: tbz             w0, #0, #0x934ac4
    // 0x934abc: r4 = LoadClassIdInstr(r0)
    //     0x934abc: ldur            x4, [x0, #-1]
    //     0x934ac0: ubfx            x4, x4, #0xc, #0x14
    // 0x934ac4: cmp             x4, #0x3d
    // 0x934ac8: b.eq            #0x934adc
    // 0x934acc: r8 = double
    //     0x934acc: ldr             x8, [PP, #0xd88]  ; [pp+0xd88] Type: double
    // 0x934ad0: r3 = Null
    //     0x934ad0: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a368] Null
    //     0x934ad4: ldr             x3, [x3, #0x368]
    // 0x934ad8: r0 = double()
    //     0x934ad8: bl              #0x9589c8  ; IsType_double_Stub
    // 0x934adc: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x934adc: ldr             x0, [PP, #0x828]  ; [pp+0x828] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x934ae0: r0 = Throw()
    //     0x934ae0: bl              #0x94dd08  ; ThrowStub
    // 0x934ae4: brk             #0
  }
  double dyn:get:length(wdb) {
    // ** addr: 0x934b00, size: 0x80
    // 0x934b00: EnterFrame
    //     0x934b00: stp             fp, lr, [SP, #-0x10]!
    //     0x934b04: mov             fp, SP
    // 0x934b08: CheckStackOverflow
    //     0x934b08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x934b0c: cmp             SP, x16
    //     0x934b10: b.ls            #0x934b50
    // 0x934b14: ldr             x1, [fp, #0x10]
    // 0x934b18: r0 = call 0x726e04
    //     0x934b18: bl              #0x726e04
    // 0x934b1c: r0 = inline_Allocate_Double()
    //     0x934b1c: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x934b20: add             x0, x0, #0x10
    //     0x934b24: cmp             x1, x0
    //     0x934b28: b.ls            #0x934b58
    //     0x934b2c: str             x0, [THR, #0x50]  ; THR::top
    //     0x934b30: sub             x0, x0, #0xf
    //     0x934b34: movz            x1, #0xd15c
    //     0x934b38: movk            x1, #0x3, lsl #16
    //     0x934b3c: stur            x1, [x0, #-1]
    // 0x934b40: StoreField: r0->field_7 = d0
    //     0x934b40: stur            d0, [x0, #7]
    // 0x934b44: LeaveFrame
    //     0x934b44: mov             SP, fp
    //     0x934b48: ldp             fp, lr, [SP], #0x10
    // 0x934b4c: ret
    //     0x934b4c: ret             
    // 0x934b50: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x934b50: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x934b54: b               #0x934b14
    // 0x934b58: SaveReg d0
    //     0x934b58: str             q0, [SP, #-0x10]!
    // 0x934b5c: r0 = AllocateDouble()
    //     0x934b5c: bl              #0x94f97c  ; AllocateDoubleStub
    // 0x934b60: RestoreReg d0
    //     0x934b60: ldr             q0, [SP], #0x10
    // 0x934b64: b               #0x934b40
  }
}

// class id: 436, size: 0xc, field offset: 0x8
class vdb extends Object {

  vdb +(vdb, vdb) {
    // ** addr: 0x934b8c, size: 0x84
    // 0x934b8c: EnterFrame
    //     0x934b8c: stp             fp, lr, [SP, #-0x10]!
    //     0x934b90: mov             fp, SP
    // 0x934b94: CheckStackOverflow
    //     0x934b94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x934b98: cmp             SP, x16
    //     0x934b9c: b.ls            #0x934bf0
    // 0x934ba0: ldr             x0, [fp, #0x10]
    // 0x934ba4: r2 = Null
    //     0x934ba4: mov             x2, NULL
    // 0x934ba8: r1 = Null
    //     0x934ba8: mov             x1, NULL
    // 0x934bac: r4 = 59
    //     0x934bac: movz            x4, #0x3b
    // 0x934bb0: branchIfSmi(r0, 0x934bbc)
    //     0x934bb0: tbz             w0, #0, #0x934bbc
    // 0x934bb4: r4 = LoadClassIdInstr(r0)
    //     0x934bb4: ldur            x4, [x0, #-1]
    //     0x934bb8: ubfx            x4, x4, #0xc, #0x14
    // 0x934bbc: cmp             x4, #0x1b4
    // 0x934bc0: b.eq            #0x934bd8
    // 0x934bc4: r8 = vdb
    //     0x934bc4: add             x8, PP, #0x4a, lsl #12  ; [pp+0x4a2f8] Type: vdb
    //     0x934bc8: ldr             x8, [x8, #0x2f8]
    // 0x934bcc: r3 = Null
    //     0x934bcc: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a310] Null
    //     0x934bd0: ldr             x3, [x3, #0x310]
    // 0x934bd4: r0 = DefaultTypeTest()
    //     0x934bd4: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x934bd8: ldr             x1, [fp, #0x18]
    // 0x934bdc: ldr             x2, [fp, #0x10]
    // 0x934be0: r0 = call 0x727d7c
    //     0x934be0: bl              #0x727d7c
    // 0x934be4: LeaveFrame
    //     0x934be4: mov             SP, fp
    //     0x934be8: ldp             fp, lr, [SP], #0x10
    // 0x934bec: ret
    //     0x934bec: ret             
    // 0x934bf0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x934bf0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x934bf4: b               #0x934ba0
  }
  dynamic *(vdb, dynamic) {
    // ** addr: 0x934c10, size: 0x50
    // 0x934c10: EnterFrame
    //     0x934c10: stp             fp, lr, [SP, #-0x10]!
    //     0x934c14: mov             fp, SP
    // 0x934c18: CheckStackOverflow
    //     0x934c18: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x934c1c: cmp             SP, x16
    //     0x934c20: b.ls            #0x934c40
    // 0x934c24: ldr             x0, [fp, #0x10]
    // 0x934c28: LoadField: d0 = r0->field_7
    //     0x934c28: ldur            d0, [x0, #7]
    // 0x934c2c: ldr             x1, [fp, #0x18]
    // 0x934c30: r0 = call 0x7281d8
    //     0x934c30: bl              #0x7281d8
    // 0x934c34: LeaveFrame
    //     0x934c34: mov             SP, fp
    //     0x934c38: ldp             fp, lr, [SP], #0x10
    // 0x934c3c: ret
    //     0x934c3c: ret             
    // 0x934c40: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x934c40: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x934c44: b               #0x934c24
  }
  vdb -(vdb, vdb) {
    // ** addr: 0x934c60, size: 0x84
    // 0x934c60: EnterFrame
    //     0x934c60: stp             fp, lr, [SP, #-0x10]!
    //     0x934c64: mov             fp, SP
    // 0x934c68: CheckStackOverflow
    //     0x934c68: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x934c6c: cmp             SP, x16
    //     0x934c70: b.ls            #0x934cc4
    // 0x934c74: ldr             x0, [fp, #0x10]
    // 0x934c78: r2 = Null
    //     0x934c78: mov             x2, NULL
    // 0x934c7c: r1 = Null
    //     0x934c7c: mov             x1, NULL
    // 0x934c80: r4 = 59
    //     0x934c80: movz            x4, #0x3b
    // 0x934c84: branchIfSmi(r0, 0x934c90)
    //     0x934c84: tbz             w0, #0, #0x934c90
    // 0x934c88: r4 = LoadClassIdInstr(r0)
    //     0x934c88: ldur            x4, [x0, #-1]
    //     0x934c8c: ubfx            x4, x4, #0xc, #0x14
    // 0x934c90: cmp             x4, #0x1b4
    // 0x934c94: b.eq            #0x934cac
    // 0x934c98: r8 = vdb
    //     0x934c98: add             x8, PP, #0x4a, lsl #12  ; [pp+0x4a2f8] Type: vdb
    //     0x934c9c: ldr             x8, [x8, #0x2f8]
    // 0x934ca0: r3 = Null
    //     0x934ca0: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a300] Null
    //     0x934ca4: ldr             x3, [x3, #0x300]
    // 0x934ca8: r0 = DefaultTypeTest()
    //     0x934ca8: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x934cac: ldr             x1, [fp, #0x18]
    // 0x934cb0: ldr             x2, [fp, #0x10]
    // 0x934cb4: r0 = call 0x72836c
    //     0x934cb4: bl              #0x72836c
    // 0x934cb8: LeaveFrame
    //     0x934cb8: mov             SP, fp
    //     0x934cbc: ldp             fp, lr, [SP], #0x10
    // 0x934cc0: ret
    //     0x934cc0: ret             
    // 0x934cc4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x934cc4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x934cc8: b               #0x934c74
  }
  void []=(vdb, int, double) {
    // ** addr: 0x934ce4, size: 0x94
    // 0x934ce4: EnterFrame
    //     0x934ce4: stp             fp, lr, [SP, #-0x10]!
    //     0x934ce8: mov             fp, SP
    // 0x934cec: ldr             x0, [fp, #0x18]
    // 0x934cf0: r2 = Null
    //     0x934cf0: mov             x2, NULL
    // 0x934cf4: r1 = Null
    //     0x934cf4: mov             x1, NULL
    // 0x934cf8: branchIfSmi(r0, 0x934d20)
    //     0x934cf8: tbz             w0, #0, #0x934d20
    // 0x934cfc: r4 = LoadClassIdInstr(r0)
    //     0x934cfc: ldur            x4, [x0, #-1]
    //     0x934d00: ubfx            x4, x4, #0xc, #0x14
    // 0x934d04: sub             x4, x4, #0x3b
    // 0x934d08: cmp             x4, #1
    // 0x934d0c: b.ls            #0x934d20
    // 0x934d10: r8 = int
    //     0x934d10: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x934d14: r3 = Null
    //     0x934d14: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a320] Null
    //     0x934d18: ldr             x3, [x3, #0x320]
    // 0x934d1c: r0 = int()
    //     0x934d1c: bl              #0x9595b0  ; IsType_int_Stub
    // 0x934d20: ldr             x0, [fp, #0x10]
    // 0x934d24: r2 = Null
    //     0x934d24: mov             x2, NULL
    // 0x934d28: r1 = Null
    //     0x934d28: mov             x1, NULL
    // 0x934d2c: r4 = 59
    //     0x934d2c: movz            x4, #0x3b
    // 0x934d30: branchIfSmi(r0, 0x934d3c)
    //     0x934d30: tbz             w0, #0, #0x934d3c
    // 0x934d34: r4 = LoadClassIdInstr(r0)
    //     0x934d34: ldur            x4, [x0, #-1]
    //     0x934d38: ubfx            x4, x4, #0xc, #0x14
    // 0x934d3c: cmp             x4, #0x3d
    // 0x934d40: b.eq            #0x934d54
    // 0x934d44: r8 = double
    //     0x934d44: ldr             x8, [PP, #0xd88]  ; [pp+0xd88] Type: double
    // 0x934d48: r3 = Null
    //     0x934d48: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a330] Null
    //     0x934d4c: ldr             x3, [x3, #0x330]
    // 0x934d50: r0 = double()
    //     0x934d50: bl              #0x9589c8  ; IsType_double_Stub
    // 0x934d54: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x934d54: ldr             x0, [PP, #0x828]  ; [pp+0x828] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x934d58: r0 = Throw()
    //     0x934d58: bl              #0x94dd08  ; ThrowStub
    // 0x934d5c: brk             #0
  }
  double [](vdb, int) {
    // ** addr: 0x934d78, size: 0xd8
    // 0x934d78: EnterFrame
    //     0x934d78: stp             fp, lr, [SP, #-0x10]!
    //     0x934d7c: mov             fp, SP
    // 0x934d80: ldr             x0, [fp, #0x10]
    // 0x934d84: r2 = Null
    //     0x934d84: mov             x2, NULL
    // 0x934d88: r1 = Null
    //     0x934d88: mov             x1, NULL
    // 0x934d8c: branchIfSmi(r0, 0x934db4)
    //     0x934d8c: tbz             w0, #0, #0x934db4
    // 0x934d90: r4 = LoadClassIdInstr(r0)
    //     0x934d90: ldur            x4, [x0, #-1]
    //     0x934d94: ubfx            x4, x4, #0xc, #0x14
    // 0x934d98: sub             x4, x4, #0x3b
    // 0x934d9c: cmp             x4, #1
    // 0x934da0: b.ls            #0x934db4
    // 0x934da4: r8 = int
    //     0x934da4: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x934da8: r3 = Null
    //     0x934da8: add             x3, PP, #0x4a, lsl #12  ; [pp+0x4a340] Null
    //     0x934dac: ldr             x3, [x3, #0x340]
    // 0x934db0: r0 = int()
    //     0x934db0: bl              #0x9595b0  ; IsType_int_Stub
    // 0x934db4: ldr             x2, [fp, #0x18]
    // 0x934db8: LoadField: r3 = r2->field_7
    //     0x934db8: ldur            w3, [x2, #7]
    // 0x934dbc: DecompressPointer r3
    //     0x934dbc: add             x3, x3, HEAP, lsl #32
    // 0x934dc0: LoadField: r2 = r3->field_13
    //     0x934dc0: ldur            w2, [x3, #0x13]
    // 0x934dc4: DecompressPointer r2
    //     0x934dc4: add             x2, x2, HEAP, lsl #32
    // 0x934dc8: ldr             x4, [fp, #0x10]
    // 0x934dcc: r5 = LoadInt32Instr(r4)
    //     0x934dcc: sbfx            x5, x4, #1, #0x1f
    //     0x934dd0: tbz             w4, #0, #0x934dd8
    //     0x934dd4: ldur            x5, [x4, #7]
    // 0x934dd8: r0 = LoadInt32Instr(r2)
    //     0x934dd8: sbfx            x0, x2, #1, #0x1f
    // 0x934ddc: mov             x1, x5
    // 0x934de0: cmp             x1, x0
    // 0x934de4: b.hs            #0x934e24
    // 0x934de8: ArrayLoad: d0 = r3[r5]  ; List_8
    //     0x934de8: add             x16, x3, x5, lsl #3
    //     0x934dec: ldur            d0, [x16, #0x17]
    // 0x934df0: r0 = inline_Allocate_Double()
    //     0x934df0: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x934df4: add             x0, x0, #0x10
    //     0x934df8: cmp             x1, x0
    //     0x934dfc: b.ls            #0x934e28
    //     0x934e00: str             x0, [THR, #0x50]  ; THR::top
    //     0x934e04: sub             x0, x0, #0xf
    //     0x934e08: movz            x1, #0xd15c
    //     0x934e0c: movk            x1, #0x3, lsl #16
    //     0x934e10: stur            x1, [x0, #-1]
    // 0x934e14: StoreField: r0->field_7 = d0
    //     0x934e14: stur            d0, [x0, #7]
    // 0x934e18: LeaveFrame
    //     0x934e18: mov             SP, fp
    //     0x934e1c: ldp             fp, lr, [SP], #0x10
    // 0x934e20: ret
    //     0x934e20: ret             
    // 0x934e24: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x934e24: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
    // 0x934e28: SaveReg d0
    //     0x934e28: str             q0, [SP, #-0x10]!
    // 0x934e2c: r0 = AllocateDouble()
    //     0x934e2c: bl              #0x94f97c  ; AllocateDoubleStub
    // 0x934e30: RestoreReg d0
    //     0x934e30: ldr             q0, [SP], #0x10
    // 0x934e34: b               #0x934e14
  }
}

// class id: 2260, size: 0xc, field offset: 0x8
class rK extends Object {

  rK +(rK, rK) {
    // ** addr: 0x8c9924, size: 0x84
    // 0x8c9924: EnterFrame
    //     0x8c9924: stp             fp, lr, [SP, #-0x10]!
    //     0x8c9928: mov             fp, SP
    // 0x8c992c: CheckStackOverflow
    //     0x8c992c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c9930: cmp             SP, x16
    //     0x8c9934: b.ls            #0x8c9988
    // 0x8c9938: ldr             x0, [fp, #0x10]
    // 0x8c993c: r2 = Null
    //     0x8c993c: mov             x2, NULL
    // 0x8c9940: r1 = Null
    //     0x8c9940: mov             x1, NULL
    // 0x8c9944: r4 = 59
    //     0x8c9944: movz            x4, #0x3b
    // 0x8c9948: branchIfSmi(r0, 0x8c9954)
    //     0x8c9948: tbz             w0, #0, #0x8c9954
    // 0x8c994c: r4 = LoadClassIdInstr(r0)
    //     0x8c994c: ldur            x4, [x0, #-1]
    //     0x8c9950: ubfx            x4, x4, #0xc, #0x14
    // 0x8c9954: cmp             x4, #0x8d4
    // 0x8c9958: b.eq            #0x8c9970
    // 0x8c995c: r8 = rK
    //     0x8c995c: add             x8, PP, #0x14, lsl #12  ; [pp+0x143b8] Type: rK
    //     0x8c9960: ldr             x8, [x8, #0x3b8]
    // 0x8c9964: r3 = Null
    //     0x8c9964: add             x3, PP, #0x14, lsl #12  ; [pp+0x143d0] Null
    //     0x8c9968: ldr             x3, [x3, #0x3d0]
    // 0x8c996c: r0 = rK()
    //     0x8c996c: bl              #0x8c9bd0  ; IsType_rK_Stub
    // 0x8c9970: ldr             x1, [fp, #0x18]
    // 0x8c9974: ldr             x2, [fp, #0x10]
    // 0x8c9978: r0 = call 0x31644c
    //     0x8c9978: bl              #0x31644c
    // 0x8c997c: LeaveFrame
    //     0x8c997c: mov             SP, fp
    //     0x8c9980: ldp             fp, lr, [SP], #0x10
    // 0x8c9984: ret
    //     0x8c9984: ret             
    // 0x8c9988: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c9988: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c998c: b               #0x8c9938
  }
  dynamic *(rK, dynamic) {
    // ** addr: 0x8c99a8, size: 0x50
    // 0x8c99a8: EnterFrame
    //     0x8c99a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c99ac: mov             fp, SP
    // 0x8c99b0: CheckStackOverflow
    //     0x8c99b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c99b4: cmp             SP, x16
    //     0x8c99b8: b.ls            #0x8c99d8
    // 0x8c99bc: ldr             x0, [fp, #0x10]
    // 0x8c99c0: LoadField: d0 = r0->field_7
    //     0x8c99c0: ldur            d0, [x0, #7]
    // 0x8c99c4: ldr             x1, [fp, #0x18]
    // 0x8c99c8: r0 = call 0x316ac4
    //     0x8c99c8: bl              #0x316ac4
    // 0x8c99cc: LeaveFrame
    //     0x8c99cc: mov             SP, fp
    //     0x8c99d0: ldp             fp, lr, [SP], #0x10
    // 0x8c99d4: ret
    //     0x8c99d4: ret             
    // 0x8c99d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c99d8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c99dc: b               #0x8c99bc
  }
  rK -(rK, rK) {
    // ** addr: 0x8c99f8, size: 0x84
    // 0x8c99f8: EnterFrame
    //     0x8c99f8: stp             fp, lr, [SP, #-0x10]!
    //     0x8c99fc: mov             fp, SP
    // 0x8c9a00: CheckStackOverflow
    //     0x8c9a00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c9a04: cmp             SP, x16
    //     0x8c9a08: b.ls            #0x8c9a5c
    // 0x8c9a0c: ldr             x0, [fp, #0x10]
    // 0x8c9a10: r2 = Null
    //     0x8c9a10: mov             x2, NULL
    // 0x8c9a14: r1 = Null
    //     0x8c9a14: mov             x1, NULL
    // 0x8c9a18: r4 = 59
    //     0x8c9a18: movz            x4, #0x3b
    // 0x8c9a1c: branchIfSmi(r0, 0x8c9a28)
    //     0x8c9a1c: tbz             w0, #0, #0x8c9a28
    // 0x8c9a20: r4 = LoadClassIdInstr(r0)
    //     0x8c9a20: ldur            x4, [x0, #-1]
    //     0x8c9a24: ubfx            x4, x4, #0xc, #0x14
    // 0x8c9a28: cmp             x4, #0x8d4
    // 0x8c9a2c: b.eq            #0x8c9a44
    // 0x8c9a30: r8 = rK
    //     0x8c9a30: add             x8, PP, #0x14, lsl #12  ; [pp+0x143b8] Type: rK
    //     0x8c9a34: ldr             x8, [x8, #0x3b8]
    // 0x8c9a38: r3 = Null
    //     0x8c9a38: add             x3, PP, #0x14, lsl #12  ; [pp+0x143c0] Null
    //     0x8c9a3c: ldr             x3, [x3, #0x3c0]
    // 0x8c9a40: r0 = rK()
    //     0x8c9a40: bl              #0x8c9bd0  ; IsType_rK_Stub
    // 0x8c9a44: ldr             x1, [fp, #0x18]
    // 0x8c9a48: ldr             x2, [fp, #0x10]
    // 0x8c9a4c: r0 = call 0x316e6c
    //     0x8c9a4c: bl              #0x316e6c
    // 0x8c9a50: LeaveFrame
    //     0x8c9a50: mov             SP, fp
    //     0x8c9a54: ldp             fp, lr, [SP], #0x10
    // 0x8c9a58: ret
    //     0x8c9a58: ret             
    // 0x8c9a5c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c9a5c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c9a60: b               #0x8c9a0c
  }
  void []=(rK, int, double) {
    // ** addr: 0x8c9a7c, size: 0x94
    // 0x8c9a7c: EnterFrame
    //     0x8c9a7c: stp             fp, lr, [SP, #-0x10]!
    //     0x8c9a80: mov             fp, SP
    // 0x8c9a84: ldr             x0, [fp, #0x18]
    // 0x8c9a88: r2 = Null
    //     0x8c9a88: mov             x2, NULL
    // 0x8c9a8c: r1 = Null
    //     0x8c9a8c: mov             x1, NULL
    // 0x8c9a90: branchIfSmi(r0, 0x8c9ab8)
    //     0x8c9a90: tbz             w0, #0, #0x8c9ab8
    // 0x8c9a94: r4 = LoadClassIdInstr(r0)
    //     0x8c9a94: ldur            x4, [x0, #-1]
    //     0x8c9a98: ubfx            x4, x4, #0xc, #0x14
    // 0x8c9a9c: sub             x4, x4, #0x3b
    // 0x8c9aa0: cmp             x4, #1
    // 0x8c9aa4: b.ls            #0x8c9ab8
    // 0x8c9aa8: r8 = int
    //     0x8c9aa8: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8c9aac: r3 = Null
    //     0x8c9aac: add             x3, PP, #0x28, lsl #12  ; [pp+0x28258] Null
    //     0x8c9ab0: ldr             x3, [x3, #0x258]
    // 0x8c9ab4: r0 = int()
    //     0x8c9ab4: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8c9ab8: ldr             x0, [fp, #0x10]
    // 0x8c9abc: r2 = Null
    //     0x8c9abc: mov             x2, NULL
    // 0x8c9ac0: r1 = Null
    //     0x8c9ac0: mov             x1, NULL
    // 0x8c9ac4: r4 = 59
    //     0x8c9ac4: movz            x4, #0x3b
    // 0x8c9ac8: branchIfSmi(r0, 0x8c9ad4)
    //     0x8c9ac8: tbz             w0, #0, #0x8c9ad4
    // 0x8c9acc: r4 = LoadClassIdInstr(r0)
    //     0x8c9acc: ldur            x4, [x0, #-1]
    //     0x8c9ad0: ubfx            x4, x4, #0xc, #0x14
    // 0x8c9ad4: cmp             x4, #0x3d
    // 0x8c9ad8: b.eq            #0x8c9aec
    // 0x8c9adc: r8 = double
    //     0x8c9adc: ldr             x8, [PP, #0xd88]  ; [pp+0xd88] Type: double
    // 0x8c9ae0: r3 = Null
    //     0x8c9ae0: add             x3, PP, #0x28, lsl #12  ; [pp+0x28268] Null
    //     0x8c9ae4: ldr             x3, [x3, #0x268]
    // 0x8c9ae8: r0 = double()
    //     0x8c9ae8: bl              #0x9589c8  ; IsType_double_Stub
    // 0x8c9aec: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x8c9aec: ldr             x0, [PP, #0x828]  ; [pp+0x828] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x8c9af0: r0 = Throw()
    //     0x8c9af0: bl              #0x94dd08  ; ThrowStub
    // 0x8c9af4: brk             #0
  }
  double [](rK, int) {
    // ** addr: 0x8c9b10, size: 0xd8
    // 0x8c9b10: EnterFrame
    //     0x8c9b10: stp             fp, lr, [SP, #-0x10]!
    //     0x8c9b14: mov             fp, SP
    // 0x8c9b18: ldr             x0, [fp, #0x10]
    // 0x8c9b1c: r2 = Null
    //     0x8c9b1c: mov             x2, NULL
    // 0x8c9b20: r1 = Null
    //     0x8c9b20: mov             x1, NULL
    // 0x8c9b24: branchIfSmi(r0, 0x8c9b4c)
    //     0x8c9b24: tbz             w0, #0, #0x8c9b4c
    // 0x8c9b28: r4 = LoadClassIdInstr(r0)
    //     0x8c9b28: ldur            x4, [x0, #-1]
    //     0x8c9b2c: ubfx            x4, x4, #0xc, #0x14
    // 0x8c9b30: sub             x4, x4, #0x3b
    // 0x8c9b34: cmp             x4, #1
    // 0x8c9b38: b.ls            #0x8c9b4c
    // 0x8c9b3c: r8 = int
    //     0x8c9b3c: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8c9b40: r3 = Null
    //     0x8c9b40: add             x3, PP, #0xf, lsl #12  ; [pp+0xf118] Null
    //     0x8c9b44: ldr             x3, [x3, #0x118]
    // 0x8c9b48: r0 = int()
    //     0x8c9b48: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8c9b4c: ldr             x2, [fp, #0x18]
    // 0x8c9b50: LoadField: r3 = r2->field_7
    //     0x8c9b50: ldur            w3, [x2, #7]
    // 0x8c9b54: DecompressPointer r3
    //     0x8c9b54: add             x3, x3, HEAP, lsl #32
    // 0x8c9b58: LoadField: r2 = r3->field_13
    //     0x8c9b58: ldur            w2, [x3, #0x13]
    // 0x8c9b5c: DecompressPointer r2
    //     0x8c9b5c: add             x2, x2, HEAP, lsl #32
    // 0x8c9b60: ldr             x4, [fp, #0x10]
    // 0x8c9b64: r5 = LoadInt32Instr(r4)
    //     0x8c9b64: sbfx            x5, x4, #1, #0x1f
    //     0x8c9b68: tbz             w4, #0, #0x8c9b70
    //     0x8c9b6c: ldur            x5, [x4, #7]
    // 0x8c9b70: r0 = LoadInt32Instr(r2)
    //     0x8c9b70: sbfx            x0, x2, #1, #0x1f
    // 0x8c9b74: mov             x1, x5
    // 0x8c9b78: cmp             x1, x0
    // 0x8c9b7c: b.hs            #0x8c9bbc
    // 0x8c9b80: ArrayLoad: d0 = r3[r5]  ; List_8
    //     0x8c9b80: add             x16, x3, x5, lsl #3
    //     0x8c9b84: ldur            d0, [x16, #0x17]
    // 0x8c9b88: r0 = inline_Allocate_Double()
    //     0x8c9b88: ldp             x0, x1, [THR, #0x50]  ; THR::top
    //     0x8c9b8c: add             x0, x0, #0x10
    //     0x8c9b90: cmp             x1, x0
    //     0x8c9b94: b.ls            #0x8c9bc0
    //     0x8c9b98: str             x0, [THR, #0x50]  ; THR::top
    //     0x8c9b9c: sub             x0, x0, #0xf
    //     0x8c9ba0: movz            x1, #0xd15c
    //     0x8c9ba4: movk            x1, #0x3, lsl #16
    //     0x8c9ba8: stur            x1, [x0, #-1]
    // 0x8c9bac: StoreField: r0->field_7 = d0
    //     0x8c9bac: stur            d0, [x0, #7]
    // 0x8c9bb0: LeaveFrame
    //     0x8c9bb0: mov             SP, fp
    //     0x8c9bb4: ldp             fp, lr, [SP], #0x10
    // 0x8c9bb8: ret
    //     0x8c9bb8: ret             
    // 0x8c9bbc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8c9bbc: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8c9bc0: SaveReg d0
    //     0x8c9bc0: str             q0, [SP, #-0x10]!
    // 0x8c9bc4: r0 = AllocateDouble()
    //     0x8c9bc4: bl              #0x94f97c  ; AllocateDoubleStub
    // 0x8c9bc8: RestoreReg d0
    //     0x8c9bc8: ldr             q0, [SP], #0x10
    // 0x8c9bcc: b               #0x8c9bac
  }
}
