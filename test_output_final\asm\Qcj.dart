// lib: , url: Qcj

// class id: 1049345, size: 0x8
class :: {
}

// class id: 1189, size: 0x40, field offset: 0x8
class yJa extends tJa {

  late Zp uEg; // offset: 0x20
  late final jJa Yfe; // offset: 0x34
  late Zp vEg; // offset: 0x24
  late Zp wEg; // offset: 0x28

  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x8ea3d0, size: 0x8dc
    // 0x8ea3d0: EnterFrame
    //     0x8ea3d0: stp             fp, lr, [SP, #-0x10]!
    //     0x8ea3d4: mov             fp, SP
    // 0x8ea3d8: AllocStack(0x118)
    //     0x8ea3d8: sub             SP, SP, #0x118
    // 0x8ea3dc: SetupParameters(yJa this /* r1, fp-0x98 */)
    //     0x8ea3dc: stur            NULL, [fp, #-8]
    //     0x8ea3e0: movz            x2, #0
    //     0x8ea3e4: add             x1, fp, w2, sxtw #2
    //     0x8ea3e8: ldr             x1, [x1, #0x10]
    //     0x8ea3ec: stur            x1, [fp, #-0x98]
    //     0x8ea3f0: ldur            w3, [x1, #0x17]
    //     0x8ea3f4: add             x3, x3, HEAP, lsl #32
    //     0x8ea3f8: stur            x3, [fp, #-0x90]
    // 0x8ea3fc: CheckStackOverflow
    //     0x8ea3fc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ea400: cmp             SP, x16
    //     0x8ea404: b.ls            #0x8eac54
    // 0x8ea408: InitAsync() -> Future<void?>
    //     0x8ea408: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x8ea40c: bl              #0x8c1de0  ; InitAsyncStub
    // 0x8ea410: ldur            x0, [fp, #-0x90]
    // 0x8ea414: LoadField: r1 = r0->field_f
    //     0x8ea414: ldur            w1, [x0, #0xf]
    // 0x8ea418: DecompressPointer r1
    //     0x8ea418: add             x1, x1, HEAP, lsl #32
    // 0x8ea41c: LoadField: r2 = r1->field_1f
    //     0x8ea41c: ldur            w2, [x1, #0x1f]
    // 0x8ea420: DecompressPointer r2
    //     0x8ea420: add             x2, x2, HEAP, lsl #32
    // 0x8ea424: r16 = Sentinel
    //     0x8ea424: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8ea428: cmp             w2, w16
    // 0x8ea42c: b.eq            #0x8eac5c
    // 0x8ea430: mov             x1, x2
    // 0x8ea434: r2 = 0
    //     0x8ea434: movz            x2, #0
    // 0x8ea438: r0 = call 0x448794
    //     0x8ea438: bl              #0x448794
    // 0x8ea43c: mov             x1, x0
    // 0x8ea440: stur            x1, [fp, #-0xa0]
    // 0x8ea444: r0 = Await()
    //     0x8ea444: bl              #0x8c1bb8  ; AwaitStub
    // 0x8ea448: ldur            x0, [fp, #-0x90]
    // 0x8ea44c: LoadField: r1 = r0->field_f
    //     0x8ea44c: ldur            w1, [x0, #0xf]
    // 0x8ea450: DecompressPointer r1
    //     0x8ea450: add             x1, x1, HEAP, lsl #32
    // 0x8ea454: stur            x1, [fp, #-0xa8]
    // 0x8ea458: LoadField: r2 = r1->field_1f
    //     0x8ea458: ldur            w2, [x1, #0x1f]
    // 0x8ea45c: DecompressPointer r2
    //     0x8ea45c: add             x2, x2, HEAP, lsl #32
    // 0x8ea460: r16 = Sentinel
    //     0x8ea460: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8ea464: cmp             w2, w16
    // 0x8ea468: b.eq            #0x8eac64
    // 0x8ea46c: stur            x2, [fp, #-0xa0]
    // 0x8ea470: r0 = MJa()
    //     0x8ea470: bl              #0x8eb224  ; AllocateMJaStub -> MJa (size=0x28)
    // 0x8ea474: mov             x1, x0
    // 0x8ea478: r0 = 0
    //     0x8ea478: movz            x0, #0
    // 0x8ea47c: stur            x1, [fp, #-0xb0]
    // 0x8ea480: StoreField: r1->field_f = r0
    //     0x8ea480: stur            x0, [x1, #0xf]
    // 0x8ea484: ArrayStore: r1[0] = r0  ; List_8
    //     0x8ea484: stur            x0, [x1, #0x17]
    // 0x8ea488: StoreField: r1->field_1f = r0
    //     0x8ea488: stur            x0, [x1, #0x1f]
    // 0x8ea48c: ldur            x2, [fp, #-0xa0]
    // 0x8ea490: StoreField: r1->field_7 = r2
    //     0x8ea490: stur            w2, [x1, #7]
    // 0x8ea494: r4 = 128000
    //     0x8ea494: movz            x4, #0xf400
    //     0x8ea498: movk            x4, #0x1, lsl #16
    // 0x8ea49c: r0 = AllocateUint8Array()
    //     0x8ea49c: bl              #0x94f700  ; AllocateUint8ArrayStub
    // 0x8ea4a0: mov             x1, x0
    // 0x8ea4a4: ldur            x0, [fp, #-0xb0]
    // 0x8ea4a8: StoreField: r0->field_b = r1
    //     0x8ea4a8: stur            w1, [x0, #0xb]
    // 0x8ea4ac: ldur            x1, [fp, #-0xa8]
    // 0x8ea4b0: LoadField: r2 = r1->field_7
    //     0x8ea4b0: ldur            w2, [x1, #7]
    // 0x8ea4b4: DecompressPointer r2
    //     0x8ea4b4: add             x2, x2, HEAP, lsl #32
    // 0x8ea4b8: LoadField: r1 = r2->field_7
    //     0x8ea4b8: ldur            w1, [x2, #7]
    // 0x8ea4bc: DecompressPointer r1
    //     0x8ea4bc: add             x1, x1, HEAP, lsl #32
    // 0x8ea4c0: LoadField: r2 = r1->field_7
    //     0x8ea4c0: ldur            w2, [x1, #7]
    // 0x8ea4c4: DecompressPointer r2
    //     0x8ea4c4: add             x2, x2, HEAP, lsl #32
    // 0x8ea4c8: r3 = LoadInt32Instr(r2)
    //     0x8ea4c8: sbfx            x3, x2, #1, #0x1f
    // 0x8ea4cc: sub             x2, x3, #5
    // 0x8ea4d0: lsl             x3, x2, #1
    // 0x8ea4d4: str             x3, [SP]
    // 0x8ea4d8: r2 = 0
    //     0x8ea4d8: movz            x2, #0
    // 0x8ea4dc: r4 = const [0, 0x3, 0x1, 0x3, null]
    //     0x8ea4dc: ldr             x4, [PP, #0x2b0]  ; [pp+0x2b0] List(5) [0, 0x3, 0x1, 0x3, Null]
    // 0x8ea4e0: r0 = call 0x2e5154
    //     0x8ea4e0: bl              #0x2e5154
    // 0x8ea4e4: r1 = Null
    //     0x8ea4e4: mov             x1, NULL
    // 0x8ea4e8: r2 = 4
    //     0x8ea4e8: movz            x2, #0x4
    // 0x8ea4ec: stur            x0, [fp, #-0xa0]
    // 0x8ea4f0: r0 = AllocateArray()
    //     0x8ea4f0: bl              #0x94fa24  ; AllocateArrayStub
    // 0x8ea4f4: mov             x1, x0
    // 0x8ea4f8: ldur            x0, [fp, #-0xa0]
    // 0x8ea4fc: StoreField: r1->field_f = r0
    //     0x8ea4fc: stur            w0, [x1, #0xf]
    // 0x8ea500: r17 = ".hivec"
    //     0x8ea500: add             x17, PP, #0xa, lsl #12  ; [pp+0xa598] ".hivec"
    //     0x8ea504: ldr             x17, [x17, #0x598]
    // 0x8ea508: StoreField: r1->field_13 = r17
    //     0x8ea508: stur            w17, [x1, #0x13]
    // 0x8ea50c: str             x1, [SP]
    // 0x8ea510: r0 = _interpolate()
    //     0x8ea510: bl              #0x2dd54c  ; [dart:core] _StringBase::_interpolate
    // 0x8ea514: stur            x0, [fp, #-0xa8]
    // 0x8ea518: r0 = call 0x2ed398
    //     0x8ea518: bl              #0x2ed398
    // 0x8ea51c: r0 = _eq()
    //     0x8ea51c: bl              #0x8c1460  ; Allocate_eqStub -> _eq (size=0x10)
    // 0x8ea520: mov             x1, x0
    // 0x8ea524: ldur            x2, [fp, #-0xa8]
    // 0x8ea528: stur            x0, [fp, #-0xa8]
    // 0x8ea52c: r0 = call 0x2ed25c
    //     0x8ea52c: bl              #0x2ed25c
    // 0x8ea530: r16 = Instance_Xp
    //     0x8ea530: add             x16, PP, #0xa, lsl #12  ; [pp+0xa5a0] Obj!Xp@69eff1
    //     0x8ea534: ldr             x16, [x16, #0x5a0]
    // 0x8ea538: str             x16, [SP]
    // 0x8ea53c: ldur            x1, [fp, #-0xa8]
    // 0x8ea540: r4 = const [0, 0x2, 0x1, 0x1, UUb, 0x1, null]
    //     0x8ea540: add             x4, PP, #0xa, lsl #12  ; [pp+0xa5a8] List(7) [0, 0x2, 0x1, 0x1, "UUb", 0x1, Null]
    //     0x8ea544: ldr             x4, [x4, #0x5a8]
    // 0x8ea548: r0 = call 0x44850c
    //     0x8ea548: bl              #0x44850c
    // 0x8ea54c: mov             x1, x0
    // 0x8ea550: stur            x1, [fp, #-0xb8]
    // 0x8ea554: r0 = Await()
    //     0x8ea554: bl              #0x8c1bb8  ; AwaitStub
    // 0x8ea558: stur            x0, [fp, #-0x98]
    // 0x8ea55c: r0 = _qg()
    //     0x8ea55c: bl              #0x8eb20c  ; Allocate_qgStub -> _qg (size=0x14)
    // 0x8ea560: mov             x1, x0
    // 0x8ea564: r0 = 0
    //     0x8ea564: movz            x0, #0
    // 0x8ea568: stur            x1, [fp, #-0xa0]
    // 0x8ea56c: StoreField: r1->field_7 = r0
    //     0x8ea56c: stur            x0, [x1, #7]
    // 0x8ea570: r0 = InitLateStaticField(0x5b0) // [dart:_internal] _qg::_pm
    //     0x8ea570: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ea574: ldr             x0, [x0, #0xb60]
    //     0x8ea578: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ea57c: cmp             w0, w16
    //     0x8ea580: b.ne            #0x8ea590
    //     0x8ea584: add             x2, PP, #0xa, lsl #12  ; [pp+0xa5b0] Field <_qg@9040228._pm@9040228>: static late final (offset: 0x5b0)
    //     0x8ea588: ldr             x2, [x2, #0x5b0]
    //     0x8ea58c: bl              #0x94dbac  ; InitLateFinalStaticFieldStub
    // 0x8ea590: ldur            x1, [fp, #-0xa0]
    // 0x8ea594: StoreField: r1->field_f = r0
    //     0x8ea594: stur            w0, [x1, #0xf]
    // 0x8ea598: r0 = NJa()
    //     0x8ea598: bl              #0x8eb200  ; AllocateNJaStub -> NJa (size=0x18)
    // 0x8ea59c: mov             x3, x0
    // 0x8ea5a0: ldur            x0, [fp, #-0xa0]
    // 0x8ea5a4: stur            x3, [fp, #-0xb8]
    // 0x8ea5a8: StoreField: r3->field_13 = r0
    //     0x8ea5a8: stur            w0, [x3, #0x13]
    // 0x8ea5ac: ldur            x4, [fp, #-0x98]
    // 0x8ea5b0: StoreField: r3->field_7 = r4
    //     0x8ea5b0: stur            w4, [x3, #7]
    // 0x8ea5b4: r1 = 64000
    //     0x8ea5b4: movz            x1, #0xfa00
    // 0x8ea5b8: StoreField: r3->field_b = r1
    //     0x8ea5b8: stur            x1, [x3, #0xb]
    // 0x8ea5bc: ldur            x5, [fp, #-0x90]
    // 0x8ea5c0: LoadField: r2 = r5->field_13
    //     0x8ea5c0: ldur            w2, [x5, #0x13]
    // 0x8ea5c4: DecompressPointer r2
    //     0x8ea5c4: add             x2, x2, HEAP, lsl #32
    // 0x8ea5c8: LoadField: r1 = r2->field_7
    //     0x8ea5c8: ldur            w1, [x2, #7]
    // 0x8ea5cc: DecompressPointer r1
    //     0x8ea5cc: add             x1, x1, HEAP, lsl #32
    // 0x8ea5d0: r0 = call 0x2d79a4
    //     0x8ea5d0: bl              #0x2d79a4
    // 0x8ea5d4: r1 = Function '<anonymous closure>':.
    //     0x8ea5d4: add             x1, PP, #0xa, lsl #12  ; [pp+0xa5b8] AnonymousClosure: (0x4488d0), in [Qcj] yJa::<anonymous closure> (0x8ea3d0)
    //     0x8ea5d8: ldr             x1, [x1, #0x5b8]
    // 0x8ea5dc: r2 = Null
    //     0x8ea5dc: mov             x2, NULL
    // 0x8ea5e0: stur            x0, [fp, #-0xc0]
    // 0x8ea5e4: r0 = AllocateClosure()
    //     0x8ea5e4: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x8ea5e8: str             x0, [SP]
    // 0x8ea5ec: ldur            x1, [fp, #-0xc0]
    // 0x8ea5f0: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x8ea5f0: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x8ea5f4: r0 = call 0x32ccb4
    //     0x8ea5f4: bl              #0x32ccb4
    // 0x8ea5f8: ldur            x0, [fp, #-0xc0]
    // 0x8ea5fc: LoadField: r2 = r0->field_7
    //     0x8ea5fc: ldur            w2, [x0, #7]
    // 0x8ea600: DecompressPointer r2
    //     0x8ea600: add             x2, x2, HEAP, lsl #32
    // 0x8ea604: mov             x1, x2
    // 0x8ea608: stur            x2, [fp, #-0xc8]
    // 0x8ea60c: r0 = h()
    //     0x8ea60c: bl              #0x8c6b30  ; AllocatehStub -> h<X0> (size=0x24)
    // 0x8ea610: mov             x4, x0
    // 0x8ea614: ldur            x3, [fp, #-0xc0]
    // 0x8ea618: stur            x4, [fp, #-0xe8]
    // 0x8ea61c: StoreField: r4->field_b = r3
    //     0x8ea61c: stur            w3, [x4, #0xb]
    // 0x8ea620: LoadField: r0 = r3->field_b
    //     0x8ea620: ldur            w0, [x3, #0xb]
    // 0x8ea624: DecompressPointer r0
    //     0x8ea624: add             x0, x0, HEAP, lsl #32
    // 0x8ea628: r5 = LoadInt32Instr(r0)
    //     0x8ea628: sbfx            x5, x0, #1, #0x1f
    // 0x8ea62c: stur            x5, [fp, #-0xe0]
    // 0x8ea630: StoreField: r4->field_f = r5
    //     0x8ea630: stur            x5, [x4, #0xf]
    // 0x8ea634: r6 = 0
    //     0x8ea634: movz            x6, #0
    // 0x8ea638: ArrayStore: r4[0] = r6  ; List_8
    //     0x8ea638: stur            x6, [x4, #0x17]
    // 0x8ea63c: r2 = 0
    //     0x8ea63c: movz            x2, #0
    // 0x8ea640: ldur            x8, [fp, #-0xb0]
    // 0x8ea644: ldur            x7, [fp, #-0xa0]
    // 0x8ea648: CheckStackOverflow
    //     0x8ea648: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ea64c: cmp             SP, x16
    //     0x8ea650: b.ls            #0x8eac6c
    // 0x8ea654: LoadField: r9 = r3->field_b
    //     0x8ea654: ldur            w9, [x3, #0xb]
    // 0x8ea658: DecompressPointer r9
    //     0x8ea658: add             x9, x9, HEAP, lsl #32
    // 0x8ea65c: r17 = -264
    //     0x8ea65c: movn            x17, #0x107
    // 0x8ea660: str             x9, [fp, x17]
    // 0x8ea664: r0 = LoadInt32Instr(r9)
    //     0x8ea664: sbfx            x0, x9, #1, #0x1f
    // 0x8ea668: cmp             x5, x0
    // 0x8ea66c: b.ne            #0x8eabf4
    // 0x8ea670: cmp             x2, x0
    // 0x8ea674: b.ge            #0x8ea980
    // 0x8ea678: mov             x1, x2
    // 0x8ea67c: cmp             x1, x0
    // 0x8ea680: b.hs            #0x8eac74
    // 0x8ea684: LoadField: r0 = r3->field_f
    //     0x8ea684: ldur            w0, [x3, #0xf]
    // 0x8ea688: DecompressPointer r0
    //     0x8ea688: add             x0, x0, HEAP, lsl #32
    // 0x8ea68c: ArrayLoad: r9 = r0[r2]  ; Unknown_4
    //     0x8ea68c: add             x16, x0, x2, lsl #2
    //     0x8ea690: ldur            w9, [x16, #0xf]
    // 0x8ea694: DecompressPointer r9
    //     0x8ea694: add             x9, x9, HEAP, lsl #32
    // 0x8ea698: mov             x0, x9
    // 0x8ea69c: stur            x9, [fp, #-0xd8]
    // 0x8ea6a0: StoreField: r4->field_1f = r0
    //     0x8ea6a0: stur            w0, [x4, #0x1f]
    //     0x8ea6a4: tbz             w0, #0, #0x8ea6c0
    //     0x8ea6a8: ldurb           w16, [x4, #-1]
    //     0x8ea6ac: ldurb           w17, [x0, #-1]
    //     0x8ea6b0: and             x16, x17, x16, lsr #2
    //     0x8ea6b4: tst             x16, HEAP, lsr #32
    //     0x8ea6b8: b.eq            #0x8ea6c0
    //     0x8ea6bc: bl              #0x94e1a8  ; WriteBarrierWrappersStub
    // 0x8ea6c0: add             x10, x2, #1
    // 0x8ea6c4: stur            x10, [fp, #-0xd0]
    // 0x8ea6c8: ArrayStore: r4[0] = r10  ; List_8
    //     0x8ea6c8: stur            x10, [x4, #0x17]
    // 0x8ea6cc: cmp             w9, NULL
    // 0x8ea6d0: b.ne            #0x8ea704
    // 0x8ea6d4: mov             x0, x9
    // 0x8ea6d8: ldur            x2, [fp, #-0xc8]
    // 0x8ea6dc: r1 = Null
    //     0x8ea6dc: mov             x1, NULL
    // 0x8ea6e0: cmp             w2, NULL
    // 0x8ea6e4: b.eq            #0x8ea704
    // 0x8ea6e8: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8ea6e8: ldur            w4, [x2, #0x17]
    // 0x8ea6ec: DecompressPointer r4
    //     0x8ea6ec: add             x4, x4, HEAP, lsl #32
    // 0x8ea6f0: r8 = X0
    //     0x8ea6f0: ldr             x8, [PP, #0x190]  ; [pp+0x190] TypeParameter: X0
    // 0x8ea6f4: LoadField: r9 = r4->field_7
    //     0x8ea6f4: ldur            x9, [x4, #7]
    // 0x8ea6f8: r3 = Null
    //     0x8ea6f8: add             x3, PP, #0xa, lsl #12  ; [pp+0xa5c0] Null
    //     0x8ea6fc: ldr             x3, [x3, #0x5c0]
    // 0x8ea700: blr             x9
    // 0x8ea704: ldur            x0, [fp, #-0xd8]
    // 0x8ea708: LoadField: r1 = r0->field_1b
    //     0x8ea708: ldur            x1, [x0, #0x1b]
    // 0x8ea70c: cmn             x1, #1
    // 0x8ea710: b.eq            #0x8ea968
    // 0x8ea714: ldur            x3, [fp, #-0xb0]
    // 0x8ea718: LoadField: r2 = r3->field_1f
    //     0x8ea718: ldur            x2, [x3, #0x1f]
    // 0x8ea71c: LoadField: r4 = r3->field_f
    //     0x8ea71c: ldur            x4, [x3, #0xf]
    // 0x8ea720: ArrayLoad: r5 = r3[0]  ; List_8
    //     0x8ea720: ldur            x5, [x3, #0x17]
    // 0x8ea724: sub             x6, x4, x5
    // 0x8ea728: sub             x4, x2, x6
    // 0x8ea72c: cmp             x1, x4
    // 0x8ea730: b.eq            #0x8ea79c
    // 0x8ea734: sub             x5, x1, x4
    // 0x8ea738: stur            x5, [fp, #-0xf0]
    // 0x8ea73c: cmp             x6, x5
    // 0x8ea740: b.ge            #0x8ea780
    // 0x8ea744: mov             x1, x3
    // 0x8ea748: mov             x2, x5
    // 0x8ea74c: r0 = __unknown_function__()
    //     0x8ea74c: bl              #0x8eafe4  ; [] ::__unknown_function__
    // 0x8ea750: mov             x1, x0
    // 0x8ea754: stur            x1, [fp, #-0xf8]
    // 0x8ea758: r0 = Await()
    //     0x8ea758: bl              #0x8c1bb8  ; AwaitStub
    // 0x8ea75c: cmp             w0, NULL
    // 0x8ea760: b.eq            #0x8eac78
    // 0x8ea764: r1 = LoadInt32Instr(r0)
    //     0x8ea764: sbfx            x1, x0, #1, #0x1f
    //     0x8ea768: tbz             w0, #0, #0x8ea770
    //     0x8ea76c: ldur            x1, [x0, #7]
    // 0x8ea770: ldur            x0, [fp, #-0xf0]
    // 0x8ea774: cmp             x1, x0
    // 0x8ea778: b.ge            #0x8ea784
    // 0x8ea77c: b               #0x8eaba4
    // 0x8ea780: mov             x0, x5
    // 0x8ea784: ldur            x3, [fp, #-0xb0]
    // 0x8ea788: ArrayLoad: r1 = r3[0]  ; List_8
    //     0x8ea788: ldur            x1, [x3, #0x17]
    // 0x8ea78c: add             x4, x1, x0
    // 0x8ea790: ArrayStore: r3[0] = r4  ; List_8
    //     0x8ea790: stur            x4, [x3, #0x17]
    // 0x8ea794: mov             x1, x4
    // 0x8ea798: b               #0x8ea7a0
    // 0x8ea79c: mov             x1, x5
    // 0x8ea7a0: ldur            x0, [fp, #-0xd8]
    // 0x8ea7a4: LoadField: r2 = r3->field_f
    //     0x8ea7a4: ldur            x2, [x3, #0xf]
    // 0x8ea7a8: sub             x4, x2, x1
    // 0x8ea7ac: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x8ea7ac: ldur            w1, [x0, #0x17]
    // 0x8ea7b0: DecompressPointer r1
    //     0x8ea7b0: add             x1, x1, HEAP, lsl #32
    // 0x8ea7b4: cmp             w1, NULL
    // 0x8ea7b8: b.eq            #0x8eac7c
    // 0x8ea7bc: r2 = LoadInt32Instr(r1)
    //     0x8ea7bc: sbfx            x2, x1, #1, #0x1f
    //     0x8ea7c0: tbz             w1, #0, #0x8ea7c8
    //     0x8ea7c4: ldur            x2, [x1, #7]
    // 0x8ea7c8: cmp             x4, x2
    // 0x8ea7cc: b.ge            #0x8ea838
    // 0x8ea7d0: mov             x1, x3
    // 0x8ea7d4: r0 = __unknown_function__()
    //     0x8ea7d4: bl              #0x8eafe4  ; [] ::__unknown_function__
    // 0x8ea7d8: mov             x1, x0
    // 0x8ea7dc: stur            x1, [fp, #-0xf8]
    // 0x8ea7e0: r0 = Await()
    //     0x8ea7e0: bl              #0x8c1bb8  ; AwaitStub
    // 0x8ea7e4: mov             x1, x0
    // 0x8ea7e8: ldur            x0, [fp, #-0xd8]
    // 0x8ea7ec: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x8ea7ec: ldur            w2, [x0, #0x17]
    // 0x8ea7f0: DecompressPointer r2
    //     0x8ea7f0: add             x2, x2, HEAP, lsl #32
    // 0x8ea7f4: cmp             w2, NULL
    // 0x8ea7f8: b.eq            #0x8eac80
    // 0x8ea7fc: cmp             w1, NULL
    // 0x8ea800: b.eq            #0x8eac84
    // 0x8ea804: r0 = LoadInt32Instr(r2)
    //     0x8ea804: sbfx            x0, x2, #1, #0x1f
    //     0x8ea808: tbz             w2, #0, #0x8ea810
    //     0x8ea80c: ldur            x0, [x2, #7]
    // 0x8ea810: r3 = LoadInt32Instr(r1)
    //     0x8ea810: sbfx            x3, x1, #1, #0x1f
    //     0x8ea814: tbz             w1, #0, #0x8ea81c
    //     0x8ea818: ldur            x3, [x1, #7]
    // 0x8ea81c: cmp             x3, x0
    // 0x8ea820: b.lt            #0x8eabc8
    // 0x8ea824: r0 = LoadInt32Instr(r2)
    //     0x8ea824: sbfx            x0, x2, #1, #0x1f
    //     0x8ea828: tbz             w2, #0, #0x8ea830
    //     0x8ea82c: ldur            x0, [x2, #7]
    // 0x8ea830: mov             x2, x0
    // 0x8ea834: b               #0x8ea848
    // 0x8ea838: r0 = LoadInt32Instr(r1)
    //     0x8ea838: sbfx            x0, x1, #1, #0x1f
    //     0x8ea83c: tbz             w1, #0, #0x8ea844
    //     0x8ea840: ldur            x0, [x1, #7]
    // 0x8ea844: mov             x2, x0
    // 0x8ea848: ldur            x0, [fp, #-0xb0]
    // 0x8ea84c: ldur            x1, [fp, #-0xa0]
    // 0x8ea850: stur            x2, [fp, #-0xf0]
    // 0x8ea854: LoadField: r3 = r0->field_b
    //     0x8ea854: ldur            w3, [x0, #0xb]
    // 0x8ea858: DecompressPointer r3
    //     0x8ea858: add             x3, x3, HEAP, lsl #32
    // 0x8ea85c: stur            x3, [fp, #-0xd8]
    // 0x8ea860: r0 = _ByteBuffer()
    //     0x8ea860: bl              #0x8c929c  ; Allocate_ByteBufferStub -> _ByteBuffer (size=0xc)
    // 0x8ea864: mov             x2, x0
    // 0x8ea868: ldur            x0, [fp, #-0xd8]
    // 0x8ea86c: stur            x2, [fp, #-0xf8]
    // 0x8ea870: StoreField: r2->field_7 = r0
    //     0x8ea870: stur            w0, [x2, #7]
    // 0x8ea874: ldur            x3, [fp, #-0xb0]
    // 0x8ea878: ArrayLoad: r4 = r3[0]  ; List_8
    //     0x8ea878: ldur            x4, [x3, #0x17]
    // 0x8ea87c: stur            x4, [fp, #-0x100]
    // 0x8ea880: r0 = BoxInt64Instr(r4)
    //     0x8ea880: sbfiz           x0, x4, #1, #0x1f
    //     0x8ea884: cmp             x4, x0, asr #1
    //     0x8ea888: b.eq            #0x8ea894
    //     0x8ea88c: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8ea890: stur            x4, [x0, #7]
    // 0x8ea894: mov             x6, x0
    // 0x8ea898: ldur            x5, [fp, #-0xf0]
    // 0x8ea89c: r0 = BoxInt64Instr(r5)
    //     0x8ea89c: sbfiz           x0, x5, #1, #0x1f
    //     0x8ea8a0: cmp             x5, x0, asr #1
    //     0x8ea8a4: b.eq            #0x8ea8b0
    //     0x8ea8a8: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8ea8ac: stur            x5, [x0, #7]
    // 0x8ea8b0: stp             x0, x6, [SP]
    // 0x8ea8b4: mov             x1, x2
    // 0x8ea8b8: r4 = const [0, 0x3, 0x2, 0x3, null]
    //     0x8ea8b8: ldr             x4, [PP, #0x780]  ; [pp+0x780] List(5) [0, 0x3, 0x2, 0x3, Null]
    // 0x8ea8bc: r0 = call 0x8b0f78
    //     0x8ea8bc: bl              #0x8b0f78
    // 0x8ea8c0: mov             x3, x0
    // 0x8ea8c4: ldur            x0, [fp, #-0xb0]
    // 0x8ea8c8: stur            x3, [fp, #-0xd8]
    // 0x8ea8cc: ArrayLoad: r1 = r0[0]  ; List_8
    //     0x8ea8cc: ldur            x1, [x0, #0x17]
    // 0x8ea8d0: ldur            x2, [fp, #-0xf0]
    // 0x8ea8d4: add             x4, x1, x2
    // 0x8ea8d8: ArrayStore: r0[0] = r4  ; List_8
    //     0x8ea8d8: stur            x4, [x0, #0x17]
    // 0x8ea8dc: ldur            x1, [fp, #-0xa0]
    // 0x8ea8e0: mov             x2, x3
    // 0x8ea8e4: r0 = call 0x4479f4
    //     0x8ea8e4: bl              #0x4479f4
    // 0x8ea8e8: ldur            x0, [fp, #-0xa0]
    // 0x8ea8ec: LoadField: r1 = r0->field_7
    //     0x8ea8ec: ldur            x1, [x0, #7]
    // 0x8ea8f0: r17 = 64000
    //     0x8ea8f0: movz            x17, #0xfa00
    // 0x8ea8f4: cmp             x1, x17
    // 0x8ea8f8: b.lt            #0x8ea90c
    // 0x8ea8fc: ldur            x1, [fp, #-0xb8]
    // 0x8ea900: r0 = call 0x447240
    //     0x8ea900: bl              #0x447240
    // 0x8ea904: mov             x1, x0
    // 0x8ea908: b               #0x8ea95c
    // 0x8ea90c: r1 = <void?>
    //     0x8ea90c: ldr             x1, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    // 0x8ea910: r0 = _Future()
    //     0x8ea910: bl              #0x8c0e10  ; Allocate_FutureStub -> _Future<X0> (size=0x1c)
    // 0x8ea914: mov             x1, x0
    // 0x8ea918: r0 = 0
    //     0x8ea918: movz            x0, #0
    // 0x8ea91c: stur            x1, [fp, #-0xf8]
    // 0x8ea920: StoreField: r1->field_b = r0
    //     0x8ea920: stur            x0, [x1, #0xb]
    // 0x8ea924: r0 = InitLateStaticField(0x590) // [dart:async] s::_current
    //     0x8ea924: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x8ea928: ldr             x0, [x0, #0xb20]
    //     0x8ea92c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x8ea930: cmp             w0, w16
    //     0x8ea934: b.ne            #0x8ea940
    //     0x8ea938: ldr             x2, [PP, #0x158]  ; [pp+0x158] Field <s._current@4048458>: static late (offset: 0x590)
    //     0x8ea93c: bl              #0x94dc14  ; InitLateStaticFieldStub
    // 0x8ea940: mov             x1, x0
    // 0x8ea944: ldur            x0, [fp, #-0xf8]
    // 0x8ea948: StoreField: r0->field_13 = r1
    //     0x8ea948: stur            w1, [x0, #0x13]
    // 0x8ea94c: mov             x1, x0
    // 0x8ea950: r2 = Null
    //     0x8ea950: mov             x2, NULL
    // 0x8ea954: r0 = call 0x2ee0cc
    //     0x8ea954: bl              #0x2ee0cc
    // 0x8ea958: ldur            x1, [fp, #-0xf8]
    // 0x8ea95c: mov             x0, x1
    // 0x8ea960: stur            x1, [fp, #-0xd8]
    // 0x8ea964: r0 = Await()
    //     0x8ea964: bl              #0x8c1bb8  ; AwaitStub
    // 0x8ea968: ldur            x2, [fp, #-0xd0]
    // 0x8ea96c: ldur            x4, [fp, #-0xe8]
    // 0x8ea970: ldur            x3, [fp, #-0xc0]
    // 0x8ea974: ldur            x5, [fp, #-0xe0]
    // 0x8ea978: r6 = 0
    //     0x8ea978: movz            x6, #0
    // 0x8ea97c: b               #0x8ea640
    // 0x8ea980: mov             x0, x4
    // 0x8ea984: StoreField: r0->field_1f = rNULL
    //     0x8ea984: stur            NULL, [x0, #0x1f]
    // 0x8ea988: ldur            x1, [fp, #-0xb8]
    // 0x8ea98c: r0 = call 0x447240
    //     0x8ea98c: bl              #0x447240
    // 0x8ea990: mov             x1, x0
    // 0x8ea994: stur            x1, [fp, #-0xa0]
    // 0x8ea998: r0 = Await()
    //     0x8ea998: bl              #0x8c1bb8  ; AwaitStub
    // 0x8ea99c: ldur            x2, [fp, #-0x90]
    // 0x8ea9a0: ldur            x0, [fp, #-0xc0]
    // 0x8ea9a4: ldur            x1, [fp, #-0x98]
    // 0x8ea9a8: r0 = call 0x446f88
    //     0x8ea9a8: bl              #0x446f88
    // 0x8ea9ac: mov             x1, x0
    // 0x8ea9b0: stur            x1, [fp, #-0xa0]
    // 0x8ea9b4: r0 = Await()
    //     0x8ea9b4: bl              #0x8c1bb8  ; AwaitStub
    // 0x8ea9b8: ldur            x0, [fp, #-0x90]
    // 0x8ea9bc: LoadField: r1 = r0->field_f
    //     0x8ea9bc: ldur            w1, [x0, #0xf]
    // 0x8ea9c0: DecompressPointer r1
    //     0x8ea9c0: add             x1, x1, HEAP, lsl #32
    // 0x8ea9c4: LoadField: r2 = r1->field_1f
    //     0x8ea9c4: ldur            w2, [x1, #0x1f]
    // 0x8ea9c8: DecompressPointer r2
    //     0x8ea9c8: add             x2, x2, HEAP, lsl #32
    // 0x8ea9cc: r16 = Sentinel
    //     0x8ea9cc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8ea9d0: cmp             w2, w16
    // 0x8ea9d4: b.eq            #0x8eac88
    // 0x8ea9d8: mov             x1, x2
    // 0x8ea9dc: r0 = call 0x446f88
    //     0x8ea9dc: bl              #0x446f88
    // 0x8ea9e0: mov             x1, x0
    // 0x8ea9e4: stur            x1, [fp, #-0xa0]
    // 0x8ea9e8: r0 = Await()
    //     0x8ea9e8: bl              #0x8c1bb8  ; AwaitStub
    // 0x8ea9ec: ldur            x0, [fp, #-0x90]
    // 0x8ea9f0: LoadField: r1 = r0->field_f
    //     0x8ea9f0: ldur            w1, [x0, #0xf]
    // 0x8ea9f4: DecompressPointer r1
    //     0x8ea9f4: add             x1, x1, HEAP, lsl #32
    // 0x8ea9f8: LoadField: r2 = r1->field_23
    //     0x8ea9f8: ldur            w2, [x1, #0x23]
    // 0x8ea9fc: DecompressPointer r2
    //     0x8ea9fc: add             x2, x2, HEAP, lsl #32
    // 0x8eaa00: r16 = Sentinel
    //     0x8eaa00: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8eaa04: cmp             w2, w16
    // 0x8eaa08: b.eq            #0x8eac90
    // 0x8eaa0c: mov             x1, x2
    // 0x8eaa10: r0 = call 0x446f88
    //     0x8eaa10: bl              #0x446f88
    // 0x8eaa14: mov             x1, x0
    // 0x8eaa18: stur            x1, [fp, #-0xa0]
    // 0x8eaa1c: r0 = Await()
    //     0x8eaa1c: bl              #0x8c1bb8  ; AwaitStub
    // 0x8eaa20: ldur            x0, [fp, #-0x90]
    // 0x8eaa24: LoadField: r1 = r0->field_f
    //     0x8eaa24: ldur            w1, [x0, #0xf]
    // 0x8eaa28: DecompressPointer r1
    //     0x8eaa28: add             x1, x1, HEAP, lsl #32
    // 0x8eaa2c: r0 = call 0x8410bc
    //     0x8eaa2c: bl              #0x8410bc
    // 0x8eaa30: ldur            x1, [fp, #-0xa8]
    // 0x8eaa34: mov             x2, x0
    // 0x8eaa38: r0 = call 0x446de8
    //     0x8eaa38: bl              #0x446de8
    // 0x8eaa3c: mov             x1, x0
    // 0x8eaa40: stur            x1, [fp, #-0xa0]
    // 0x8eaa44: r0 = Await()
    //     0x8eaa44: bl              #0x8c1bb8  ; AwaitStub
    // 0x8eaa48: ldur            x0, [fp, #-0x90]
    // 0x8eaa4c: LoadField: r1 = r0->field_f
    //     0x8eaa4c: ldur            w1, [x0, #0xf]
    // 0x8eaa50: DecompressPointer r1
    //     0x8eaa50: add             x1, x1, HEAP, lsl #32
    // 0x8eaa54: r0 = __unknown_function__()
    //     0x8eaa54: bl              #0x8eacac  ; [] ::__unknown_function__
    // 0x8eaa58: mov             x1, x0
    // 0x8eaa5c: stur            x1, [fp, #-0xa0]
    // 0x8eaa60: r0 = Await()
    //     0x8eaa60: bl              #0x8c1bb8  ; AwaitStub
    // 0x8eaa64: ldur            x1, [fp, #-0xc8]
    // 0x8eaa68: r0 = h()
    //     0x8eaa68: bl              #0x8c6b30  ; AllocatehStub -> h<X0> (size=0x24)
    // 0x8eaa6c: mov             x3, x0
    // 0x8eaa70: ldur            x1, [fp, #-0xc0]
    // 0x8eaa74: stur            x3, [fp, #-0xb0]
    // 0x8eaa78: StoreField: r3->field_b = r1
    //     0x8eaa78: stur            w1, [x3, #0xb]
    // 0x8eaa7c: LoadField: r0 = r1->field_b
    //     0x8eaa7c: ldur            w0, [x1, #0xb]
    // 0x8eaa80: DecompressPointer r0
    //     0x8eaa80: add             x0, x0, HEAP, lsl #32
    // 0x8eaa84: r4 = LoadInt32Instr(r0)
    //     0x8eaa84: sbfx            x4, x0, #1, #0x1f
    // 0x8eaa88: stur            x4, [fp, #-0xf0]
    // 0x8eaa8c: StoreField: r3->field_f = r4
    //     0x8eaa8c: stur            x4, [x3, #0xf]
    // 0x8eaa90: r0 = 0
    //     0x8eaa90: movz            x0, #0
    // 0x8eaa94: ArrayStore: r3[0] = r0  ; List_8
    //     0x8eaa94: stur            x0, [x3, #0x17]
    // 0x8eaa98: LoadField: r5 = r1->field_f
    //     0x8eaa98: ldur            w5, [x1, #0xf]
    // 0x8eaa9c: DecompressPointer r5
    //     0x8eaa9c: add             x5, x5, HEAP, lsl #32
    // 0x8eaaa0: stur            x5, [fp, #-0xa8]
    // 0x8eaaa4: r6 = 0
    //     0x8eaaa4: movz            x6, #0
    // 0x8eaaa8: r2 = 0
    //     0x8eaaa8: movz            x2, #0
    // 0x8eaaac: stur            x6, [fp, #-0xe0]
    // 0x8eaab0: CheckStackOverflow
    //     0x8eaab0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8eaab4: cmp             SP, x16
    //     0x8eaab8: b.ls            #0x8eac9c
    // 0x8eaabc: cmp             x2, x4
    // 0x8eaac0: b.ge            #0x8eab80
    // 0x8eaac4: mov             x0, x4
    // 0x8eaac8: mov             x1, x2
    // 0x8eaacc: cmp             x1, x0
    // 0x8eaad0: b.hs            #0x8eaca4
    // 0x8eaad4: ArrayLoad: r7 = r5[r2]  ; Unknown_4
    //     0x8eaad4: add             x16, x5, x2, lsl #2
    //     0x8eaad8: ldur            w7, [x16, #0xf]
    // 0x8eaadc: DecompressPointer r7
    //     0x8eaadc: add             x7, x7, HEAP, lsl #32
    // 0x8eaae0: stur            x7, [fp, #-0xa0]
    // 0x8eaae4: add             x8, x2, #1
    // 0x8eaae8: stur            x8, [fp, #-0xd0]
    // 0x8eaaec: ArrayStore: r3[0] = r8  ; List_8
    //     0x8eaaec: stur            x8, [x3, #0x17]
    // 0x8eaaf0: cmp             w7, NULL
    // 0x8eaaf4: b.ne            #0x8eab28
    // 0x8eaaf8: mov             x0, x7
    // 0x8eaafc: ldur            x2, [fp, #-0xc8]
    // 0x8eab00: r1 = Null
    //     0x8eab00: mov             x1, NULL
    // 0x8eab04: cmp             w2, NULL
    // 0x8eab08: b.eq            #0x8eab28
    // 0x8eab0c: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8eab0c: ldur            w4, [x2, #0x17]
    // 0x8eab10: DecompressPointer r4
    //     0x8eab10: add             x4, x4, HEAP, lsl #32
    // 0x8eab14: r8 = X0
    //     0x8eab14: ldr             x8, [PP, #0x190]  ; [pp+0x190] TypeParameter: X0
    // 0x8eab18: LoadField: r9 = r4->field_7
    //     0x8eab18: ldur            x9, [x4, #7]
    // 0x8eab1c: r3 = Null
    //     0x8eab1c: add             x3, PP, #0xa, lsl #12  ; [pp+0xa5d0] Null
    //     0x8eab20: ldr             x3, [x3, #0x5d0]
    // 0x8eab24: blr             x9
    // 0x8eab28: ldur            x0, [fp, #-0xa0]
    // 0x8eab2c: LoadField: r1 = r0->field_1b
    //     0x8eab2c: ldur            x1, [x0, #0x1b]
    // 0x8eab30: cmn             x1, #1
    // 0x8eab34: b.ne            #0x8eab40
    // 0x8eab38: ldur            x6, [fp, #-0xe0]
    // 0x8eab3c: b               #0x8eab6c
    // 0x8eab40: ldur            x1, [fp, #-0xe0]
    // 0x8eab44: StoreField: r0->field_1b = r1
    //     0x8eab44: stur            x1, [x0, #0x1b]
    // 0x8eab48: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x8eab48: ldur            w2, [x0, #0x17]
    // 0x8eab4c: DecompressPointer r2
    //     0x8eab4c: add             x2, x2, HEAP, lsl #32
    // 0x8eab50: cmp             w2, NULL
    // 0x8eab54: b.eq            #0x8eaca8
    // 0x8eab58: r0 = LoadInt32Instr(r2)
    //     0x8eab58: sbfx            x0, x2, #1, #0x1f
    //     0x8eab5c: tbz             w2, #0, #0x8eab64
    //     0x8eab60: ldur            x0, [x2, #7]
    // 0x8eab64: add             x3, x1, x0
    // 0x8eab68: mov             x6, x3
    // 0x8eab6c: ldur            x2, [fp, #-0xd0]
    // 0x8eab70: ldur            x3, [fp, #-0xb0]
    // 0x8eab74: ldur            x5, [fp, #-0xa8]
    // 0x8eab78: ldur            x4, [fp, #-0xf0]
    // 0x8eab7c: b               #0x8eaaac
    // 0x8eab80: ldur            x1, [fp, #-0x90]
    // 0x8eab84: mov             x0, x3
    // 0x8eab88: r2 = false
    //     0x8eab88: add             x2, NULL, #0x30  ; false
    // 0x8eab8c: StoreField: r0->field_1f = rNULL
    //     0x8eab8c: stur            NULL, [x0, #0x1f]
    // 0x8eab90: LoadField: r0 = r1->field_f
    //     0x8eab90: ldur            w0, [x1, #0xf]
    // 0x8eab94: DecompressPointer r0
    //     0x8eab94: add             x0, x0, HEAP, lsl #32
    // 0x8eab98: StoreField: r0->field_37 = r2
    //     0x8eab98: stur            w2, [x0, #0x37]
    // 0x8eab9c: r0 = Null
    //     0x8eab9c: mov             x0, NULL
    // 0x8eaba0: r0 = ReturnAsyncNotFuture()
    //     0x8eaba0: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x8eaba4: r0 = kJa()
    //     0x8eaba4: bl              #0x8cafa8  ; AllocatekJaStub -> kJa (size=0x10)
    // 0x8eaba8: mov             x1, x0
    // 0x8eabac: r0 = "Could not compact box: Unexpected EOF."
    //     0x8eabac: add             x0, PP, #0xa, lsl #12  ; [pp+0xa5e0] "Could not compact box: Unexpected EOF."
    //     0x8eabb0: ldr             x0, [x0, #0x5e0]
    // 0x8eabb4: stur            x1, [fp, #-0x90]
    // 0x8eabb8: StoreField: r1->field_b = r0
    //     0x8eabb8: stur            w0, [x1, #0xb]
    // 0x8eabbc: mov             x0, x1
    // 0x8eabc0: r0 = Throw()
    //     0x8eabc0: bl              #0x94dd08  ; ThrowStub
    // 0x8eabc4: brk             #0
    // 0x8eabc8: r0 = "Could not compact box: Unexpected EOF."
    //     0x8eabc8: add             x0, PP, #0xa, lsl #12  ; [pp+0xa5e0] "Could not compact box: Unexpected EOF."
    //     0x8eabcc: ldr             x0, [x0, #0x5e0]
    // 0x8eabd0: r0 = kJa()
    //     0x8eabd0: bl              #0x8cafa8  ; AllocatekJaStub -> kJa (size=0x10)
    // 0x8eabd4: mov             x1, x0
    // 0x8eabd8: r0 = "Could not compact box: Unexpected EOF."
    //     0x8eabd8: add             x0, PP, #0xa, lsl #12  ; [pp+0xa5e0] "Could not compact box: Unexpected EOF."
    //     0x8eabdc: ldr             x0, [x0, #0x5e0]
    // 0x8eabe0: stur            x1, [fp, #-0x90]
    // 0x8eabe4: StoreField: r1->field_b = r0
    //     0x8eabe4: stur            w0, [x1, #0xb]
    // 0x8eabe8: mov             x0, x1
    // 0x8eabec: r0 = Throw()
    //     0x8eabec: bl              #0x94dd08  ; ThrowStub
    // 0x8eabf0: brk             #0
    // 0x8eabf4: mov             x0, x4
    // 0x8eabf8: mov             x1, x3
    // 0x8eabfc: r0 = Ia()
    //     0x8eabfc: bl              #0x8bab1c  ; AllocateIaStub -> Ia (size=0x10)
    // 0x8eac00: mov             x1, x0
    // 0x8eac04: ldur            x0, [fp, #-0xc0]
    // 0x8eac08: stur            x1, [fp, #-0x90]
    // 0x8eac0c: StoreField: r1->field_b = r0
    //     0x8eac0c: stur            w0, [x1, #0xb]
    // 0x8eac10: mov             x0, x1
    // 0x8eac14: r0 = Throw()
    //     0x8eac14: bl              #0x94dd08  ; ThrowStub
    // 0x8eac18: brk             #0
    // 0x8eac1c: sub             SP, fp, #0x118
    // 0x8eac20: mov             x2, x0
    // 0x8eac24: stur            x0, [fp, #-0x90]
    // 0x8eac28: mov             x0, x1
    // 0x8eac2c: stur            x1, [fp, #-0x98]
    // 0x8eac30: ldur            x1, [fp, #-0x58]
    // 0x8eac34: r0 = call 0x446f88
    //     0x8eac34: bl              #0x446f88
    // 0x8eac38: mov             x1, x0
    // 0x8eac3c: stur            x1, [fp, #-0xa0]
    // 0x8eac40: r0 = Await()
    //     0x8eac40: bl              #0x8c1bb8  ; AwaitStub
    // 0x8eac44: ldur            x0, [fp, #-0x90]
    // 0x8eac48: ldur            x1, [fp, #-0x98]
    // 0x8eac4c: r0 = ReThrow()
    //     0x8eac4c: bl              #0x94dce4  ; ReThrowStub
    // 0x8eac50: brk             #0
    // 0x8eac54: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eac54: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eac58: b               #0x8ea408
    // 0x8eac5c: r9 = uEg
    //     0x8eac5c: ldr             x9, [PP, #0x73a8]  ; [pp+0x73a8] Field <yJa.uEg>: late (offset: 0x20)
    // 0x8eac60: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8eac60: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8eac64: r9 = uEg
    //     0x8eac64: ldr             x9, [PP, #0x73a8]  ; [pp+0x73a8] Field <yJa.uEg>: late (offset: 0x20)
    // 0x8eac68: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8eac68: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8eac6c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eac6c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eac70: b               #0x8ea654
    // 0x8eac74: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8eac74: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8eac78: r0 = NullErrorSharedWithoutFPURegs()
    //     0x8eac78: bl              #0x950284  ; NullErrorSharedWithoutFPURegsStub
    // 0x8eac7c: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8eac7c: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8eac80: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8eac80: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8eac84: r0 = NullErrorSharedWithoutFPURegs()
    //     0x8eac84: bl              #0x950284  ; NullErrorSharedWithoutFPURegsStub
    // 0x8eac88: r9 = uEg
    //     0x8eac88: ldr             x9, [PP, #0x73a8]  ; [pp+0x73a8] Field <yJa.uEg>: late (offset: 0x20)
    // 0x8eac8c: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8eac8c: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8eac90: r9 = vEg
    //     0x8eac90: add             x9, PP, #0xa, lsl #12  ; [pp+0xa368] Field <yJa.vEg>: late (offset: 0x24)
    //     0x8eac94: ldr             x9, [x9, #0x368]
    // 0x8eac98: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8eac98: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8eac9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eac9c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eaca0: b               #0x8eaabc
    // 0x8eaca4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8eaca4: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8eaca8: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8eaca8: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x8eb294, size: 0x3a0
    // 0x8eb294: EnterFrame
    //     0x8eb294: stp             fp, lr, [SP, #-0x10]!
    //     0x8eb298: mov             fp, SP
    // 0x8eb29c: AllocStack(0xa0)
    //     0x8eb29c: sub             SP, SP, #0xa0
    // 0x8eb2a0: SetupParameters(yJa this /* r1, fp-0x78 */)
    //     0x8eb2a0: stur            NULL, [fp, #-8]
    //     0x8eb2a4: movz            x0, #0
    //     0x8eb2a8: add             x1, fp, w0, sxtw #2
    //     0x8eb2ac: ldr             x1, [x1, #0x10]
    //     0x8eb2b0: stur            x1, [fp, #-0x78]
    //     0x8eb2b4: ldur            w2, [x1, #0x17]
    //     0x8eb2b8: add             x2, x2, HEAP, lsl #32
    //     0x8eb2bc: stur            x2, [fp, #-0x70]
    // 0x8eb2c0: CheckStackOverflow
    //     0x8eb2c0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8eb2c4: cmp             SP, x16
    //     0x8eb2c8: b.ls            #0x8eb5f0
    // 0x8eb2cc: InitAsync() -> Future<void?>
    //     0x8eb2cc: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x8eb2d0: bl              #0x8c1de0  ; InitAsyncStub
    // 0x8eb2d4: ldur            x0, [fp, #-0x70]
    // 0x8eb2d8: LoadField: r1 = r0->field_f
    //     0x8eb2d8: ldur            w1, [x0, #0xf]
    // 0x8eb2dc: DecompressPointer r1
    //     0x8eb2dc: add             x1, x1, HEAP, lsl #32
    // 0x8eb2e0: LoadField: r2 = r1->field_33
    //     0x8eb2e0: ldur            w2, [x1, #0x33]
    // 0x8eb2e4: DecompressPointer r2
    //     0x8eb2e4: add             x2, x2, HEAP, lsl #32
    // 0x8eb2e8: r16 = Sentinel
    //     0x8eb2e8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8eb2ec: cmp             w2, w16
    // 0x8eb2f0: b.eq            #0x8eb5f8
    // 0x8eb2f4: stur            x2, [fp, #-0x78]
    // 0x8eb2f8: r0 = AJa()
    //     0x8eb2f8: bl              #0x8eb634  ; AllocateAJaStub -> AJa (size=0x1c)
    // 0x8eb2fc: mov             x1, x0
    // 0x8eb300: r0 = 0
    //     0x8eb300: movz            x0, #0
    // 0x8eb304: stur            x1, [fp, #-0x80]
    // 0x8eb308: StoreField: r1->field_13 = r0
    //     0x8eb308: stur            x0, [x1, #0x13]
    // 0x8eb30c: r4 = 8192
    //     0x8eb30c: movz            x4, #0x2000
    // 0x8eb310: r0 = AllocateUint8Array()
    //     0x8eb310: bl              #0x94f700  ; AllocateUint8ArrayStub
    // 0x8eb314: mov             x1, x0
    // 0x8eb318: ldur            x0, [fp, #-0x80]
    // 0x8eb31c: StoreField: r0->field_b = r1
    //     0x8eb31c: stur            w1, [x0, #0xb]
    // 0x8eb320: ldur            x1, [fp, #-0x78]
    // 0x8eb324: StoreField: r0->field_7 = r1
    //     0x8eb324: stur            w1, [x0, #7]
    // 0x8eb328: ldur            x2, [fp, #-0x70]
    // 0x8eb32c: LoadField: r3 = r2->field_13
    //     0x8eb32c: ldur            w3, [x2, #0x13]
    // 0x8eb330: DecompressPointer r3
    //     0x8eb330: add             x3, x3, HEAP, lsl #32
    // 0x8eb334: stur            x3, [fp, #-0x78]
    // 0x8eb338: LoadField: r1 = r3->field_7
    //     0x8eb338: ldur            w1, [x3, #7]
    // 0x8eb33c: DecompressPointer r1
    //     0x8eb33c: add             x1, x1, HEAP, lsl #32
    // 0x8eb340: r0 = h()
    //     0x8eb340: bl              #0x8c6b30  ; AllocatehStub -> h<X0> (size=0x24)
    // 0x8eb344: mov             x4, x0
    // 0x8eb348: ldur            x3, [fp, #-0x78]
    // 0x8eb34c: stur            x4, [fp, #-0xa0]
    // 0x8eb350: StoreField: r4->field_b = r3
    //     0x8eb350: stur            w3, [x4, #0xb]
    // 0x8eb354: LoadField: r0 = r3->field_b
    //     0x8eb354: ldur            w0, [x3, #0xb]
    // 0x8eb358: DecompressPointer r0
    //     0x8eb358: add             x0, x0, HEAP, lsl #32
    // 0x8eb35c: r5 = LoadInt32Instr(r0)
    //     0x8eb35c: sbfx            x5, x0, #1, #0x1f
    // 0x8eb360: stur            x5, [fp, #-0x98]
    // 0x8eb364: StoreField: r4->field_f = r5
    //     0x8eb364: stur            x5, [x4, #0xf]
    // 0x8eb368: r6 = 0
    //     0x8eb368: movz            x6, #0
    // 0x8eb36c: ArrayStore: r4[0] = r6  ; List_8
    //     0x8eb36c: stur            x6, [x4, #0x17]
    // 0x8eb370: r2 = 0
    //     0x8eb370: movz            x2, #0
    // 0x8eb374: CheckStackOverflow
    //     0x8eb374: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8eb378: cmp             SP, x16
    //     0x8eb37c: b.ls            #0x8eb600
    // 0x8eb380: LoadField: r0 = r3->field_b
    //     0x8eb380: ldur            w0, [x3, #0xb]
    // 0x8eb384: DecompressPointer r0
    //     0x8eb384: add             x0, x0, HEAP, lsl #32
    // 0x8eb388: r1 = LoadInt32Instr(r0)
    //     0x8eb388: sbfx            x1, x0, #1, #0x1f
    // 0x8eb38c: cmp             x5, x1
    // 0x8eb390: b.ne            #0x8eb570
    // 0x8eb394: cmp             x2, x1
    // 0x8eb398: b.ge            #0x8eb450
    // 0x8eb39c: mov             x0, x1
    // 0x8eb3a0: mov             x1, x2
    // 0x8eb3a4: cmp             x1, x0
    // 0x8eb3a8: b.hs            #0x8eb608
    // 0x8eb3ac: LoadField: r0 = r3->field_f
    //     0x8eb3ac: ldur            w0, [x3, #0xf]
    // 0x8eb3b0: DecompressPointer r0
    //     0x8eb3b0: add             x0, x0, HEAP, lsl #32
    // 0x8eb3b4: ArrayLoad: r7 = r0[r2]  ; Unknown_4
    //     0x8eb3b4: add             x16, x0, x2, lsl #2
    //     0x8eb3b8: ldur            w7, [x16, #0xf]
    // 0x8eb3bc: DecompressPointer r7
    //     0x8eb3bc: add             x7, x7, HEAP, lsl #32
    // 0x8eb3c0: mov             x0, x7
    // 0x8eb3c4: stur            x7, [fp, #-0x90]
    // 0x8eb3c8: StoreField: r4->field_1f = r0
    //     0x8eb3c8: stur            w0, [x4, #0x1f]
    //     0x8eb3cc: ldurb           w16, [x4, #-1]
    //     0x8eb3d0: ldurb           w17, [x0, #-1]
    //     0x8eb3d4: and             x16, x17, x16, lsr #2
    //     0x8eb3d8: tst             x16, HEAP, lsr #32
    //     0x8eb3dc: b.eq            #0x8eb3e4
    //     0x8eb3e0: bl              #0x94e1a8  ; WriteBarrierWrappersStub
    // 0x8eb3e4: add             x0, x2, #1
    // 0x8eb3e8: stur            x0, [fp, #-0x88]
    // 0x8eb3ec: ArrayStore: r4[0] = r0  ; List_8
    //     0x8eb3ec: stur            x0, [x4, #0x17]
    // 0x8eb3f0: ldur            x1, [fp, #-0x80]
    // 0x8eb3f4: mov             x2, x7
    // 0x8eb3f8: r0 = call 0x449c6c
    //     0x8eb3f8: bl              #0x449c6c
    // 0x8eb3fc: mov             x2, x0
    // 0x8eb400: r0 = BoxInt64Instr(r2)
    //     0x8eb400: sbfiz           x0, x2, #1, #0x1f
    //     0x8eb404: cmp             x2, x0, asr #1
    //     0x8eb408: b.eq            #0x8eb414
    //     0x8eb40c: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8eb410: stur            x2, [x0, #7]
    // 0x8eb414: ldur            x1, [fp, #-0x90]
    // 0x8eb418: ArrayStore: r1[0] = r0  ; List_4
    //     0x8eb418: stur            w0, [x1, #0x17]
    //     0x8eb41c: tbz             w0, #0, #0x8eb438
    //     0x8eb420: ldurb           w16, [x1, #-1]
    //     0x8eb424: ldurb           w17, [x0, #-1]
    //     0x8eb428: and             x16, x17, x16, lsr #2
    //     0x8eb42c: tst             x16, HEAP, lsr #32
    //     0x8eb430: b.eq            #0x8eb438
    //     0x8eb434: bl              #0x94e148  ; WriteBarrierWrappersStub
    // 0x8eb438: ldur            x2, [fp, #-0x88]
    // 0x8eb43c: ldur            x3, [fp, #-0x78]
    // 0x8eb440: ldur            x4, [fp, #-0xa0]
    // 0x8eb444: ldur            x5, [fp, #-0x98]
    // 0x8eb448: r6 = 0
    //     0x8eb448: movz            x6, #0
    // 0x8eb44c: b               #0x8eb374
    // 0x8eb450: mov             x0, x4
    // 0x8eb454: StoreField: r0->field_1f = rNULL
    //     0x8eb454: stur            NULL, [x0, #0x1f]
    // 0x8eb458: ldur            x0, [fp, #-0x70]
    // 0x8eb45c: LoadField: r1 = r0->field_f
    //     0x8eb45c: ldur            w1, [x0, #0xf]
    // 0x8eb460: DecompressPointer r1
    //     0x8eb460: add             x1, x1, HEAP, lsl #32
    // 0x8eb464: LoadField: r2 = r1->field_23
    //     0x8eb464: ldur            w2, [x1, #0x23]
    // 0x8eb468: DecompressPointer r2
    //     0x8eb468: add             x2, x2, HEAP, lsl #32
    // 0x8eb46c: r16 = Sentinel
    //     0x8eb46c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8eb470: cmp             w2, w16
    // 0x8eb474: b.eq            #0x8eb60c
    // 0x8eb478: ldur            x1, [fp, #-0x80]
    // 0x8eb47c: stur            x2, [fp, #-0x90]
    // 0x8eb480: r0 = call 0x449bf4
    //     0x8eb480: bl              #0x449bf4
    // 0x8eb484: ldur            x1, [fp, #-0x90]
    // 0x8eb488: mov             x2, x0
    // 0x8eb48c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x8eb48c: ldr             x4, [PP, #0x2a8]  ; [pp+0x2a8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x8eb490: r0 = __unknown_function__()
    //     0x8eb490: bl              #0x8ead9c  ; [] ::__unknown_function__
    // 0x8eb494: mov             x1, x0
    // 0x8eb498: stur            x1, [fp, #-0x80]
    // 0x8eb49c: r0 = Await()
    //     0x8eb49c: bl              #0x8c1bb8  ; AwaitStub
    // 0x8eb4a0: ldur            x0, [fp, #-0x70]
    // 0x8eb4a4: LoadField: r2 = r0->field_13
    //     0x8eb4a4: ldur            w2, [x0, #0x13]
    // 0x8eb4a8: DecompressPointer r2
    //     0x8eb4a8: add             x2, x2, HEAP, lsl #32
    // 0x8eb4ac: stur            x2, [fp, #-0x80]
    // 0x8eb4b0: LoadField: r1 = r2->field_7
    //     0x8eb4b0: ldur            w1, [x2, #7]
    // 0x8eb4b4: DecompressPointer r1
    //     0x8eb4b4: add             x1, x1, HEAP, lsl #32
    // 0x8eb4b8: r0 = h()
    //     0x8eb4b8: bl              #0x8c6b30  ; AllocatehStub -> h<X0> (size=0x24)
    // 0x8eb4bc: mov             x2, x0
    // 0x8eb4c0: ldur            x0, [fp, #-0x80]
    // 0x8eb4c4: StoreField: r2->field_b = r0
    //     0x8eb4c4: stur            w0, [x2, #0xb]
    // 0x8eb4c8: LoadField: r1 = r0->field_b
    //     0x8eb4c8: ldur            w1, [x0, #0xb]
    // 0x8eb4cc: DecompressPointer r1
    //     0x8eb4cc: add             x1, x1, HEAP, lsl #32
    // 0x8eb4d0: r3 = LoadInt32Instr(r1)
    //     0x8eb4d0: sbfx            x3, x1, #1, #0x1f
    // 0x8eb4d4: StoreField: r2->field_f = r3
    //     0x8eb4d4: stur            x3, [x2, #0xf]
    // 0x8eb4d8: r1 = 0
    //     0x8eb4d8: movz            x1, #0
    // 0x8eb4dc: ArrayStore: r2[0] = r1  ; List_8
    //     0x8eb4dc: stur            x1, [x2, #0x17]
    // 0x8eb4e0: LoadField: r4 = r0->field_f
    //     0x8eb4e0: ldur            w4, [x0, #0xf]
    // 0x8eb4e4: DecompressPointer r4
    //     0x8eb4e4: add             x4, x4, HEAP, lsl #32
    // 0x8eb4e8: ldur            x0, [fp, #-0x70]
    // 0x8eb4ec: LoadField: r5 = r0->field_f
    //     0x8eb4ec: ldur            w5, [x0, #0xf]
    // 0x8eb4f0: DecompressPointer r5
    //     0x8eb4f0: add             x5, x5, HEAP, lsl #32
    // 0x8eb4f4: r6 = 0
    //     0x8eb4f4: movz            x6, #0
    // 0x8eb4f8: CheckStackOverflow
    //     0x8eb4f8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8eb4fc: cmp             SP, x16
    //     0x8eb500: b.ls            #0x8eb618
    // 0x8eb504: cmp             x6, x3
    // 0x8eb508: b.ge            #0x8eb564
    // 0x8eb50c: mov             x0, x3
    // 0x8eb510: mov             x1, x6
    // 0x8eb514: cmp             x1, x0
    // 0x8eb518: b.hs            #0x8eb620
    // 0x8eb51c: ArrayLoad: r0 = r4[r6]  ; Unknown_4
    //     0x8eb51c: add             x16, x4, x6, lsl #2
    //     0x8eb520: ldur            w0, [x16, #0xf]
    // 0x8eb524: DecompressPointer r0
    //     0x8eb524: add             x0, x0, HEAP, lsl #32
    // 0x8eb528: add             x1, x6, #1
    // 0x8eb52c: ArrayStore: r2[0] = r1  ; List_8
    //     0x8eb52c: stur            x1, [x2, #0x17]
    // 0x8eb530: LoadField: r6 = r5->field_2b
    //     0x8eb530: ldur            x6, [x5, #0x2b]
    // 0x8eb534: StoreField: r0->field_1b = r6
    //     0x8eb534: stur            x6, [x0, #0x1b]
    // 0x8eb538: ArrayLoad: r7 = r0[0]  ; List_4
    //     0x8eb538: ldur            w7, [x0, #0x17]
    // 0x8eb53c: DecompressPointer r7
    //     0x8eb53c: add             x7, x7, HEAP, lsl #32
    // 0x8eb540: cmp             w7, NULL
    // 0x8eb544: b.eq            #0x8eb624
    // 0x8eb548: r0 = LoadInt32Instr(r7)
    //     0x8eb548: sbfx            x0, x7, #1, #0x1f
    //     0x8eb54c: tbz             w7, #0, #0x8eb554
    //     0x8eb550: ldur            x0, [x7, #7]
    // 0x8eb554: add             x7, x6, x0
    // 0x8eb558: StoreField: r5->field_2b = r7
    //     0x8eb558: stur            x7, [x5, #0x2b]
    // 0x8eb55c: mov             x6, x1
    // 0x8eb560: b               #0x8eb4f8
    // 0x8eb564: StoreField: r2->field_1f = rNULL
    //     0x8eb564: stur            NULL, [x2, #0x1f]
    // 0x8eb568: r0 = Null
    //     0x8eb568: mov             x0, NULL
    // 0x8eb56c: r0 = ReturnAsyncNotFuture()
    //     0x8eb56c: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x8eb570: mov             x0, x3
    // 0x8eb574: r0 = Ia()
    //     0x8eb574: bl              #0x8bab1c  ; AllocateIaStub -> Ia (size=0x10)
    // 0x8eb578: mov             x1, x0
    // 0x8eb57c: ldur            x0, [fp, #-0x78]
    // 0x8eb580: StoreField: r1->field_b = r0
    //     0x8eb580: stur            w0, [x1, #0xb]
    // 0x8eb584: mov             x0, x1
    // 0x8eb588: r0 = Throw()
    //     0x8eb588: bl              #0x94dd08  ; ThrowStub
    // 0x8eb58c: brk             #0
    // 0x8eb590: sub             SP, fp, #0xa0
    // 0x8eb594: ldur            x4, [fp, #-0x20]
    // 0x8eb598: mov             x3, x0
    // 0x8eb59c: stur            x0, [fp, #-0x70]
    // 0x8eb5a0: mov             x0, x1
    // 0x8eb5a4: stur            x1, [fp, #-0x78]
    // 0x8eb5a8: LoadField: r1 = r4->field_f
    //     0x8eb5a8: ldur            w1, [x4, #0xf]
    // 0x8eb5ac: DecompressPointer r1
    //     0x8eb5ac: add             x1, x1, HEAP, lsl #32
    // 0x8eb5b0: LoadField: r2 = r1->field_23
    //     0x8eb5b0: ldur            w2, [x1, #0x23]
    // 0x8eb5b4: DecompressPointer r2
    //     0x8eb5b4: add             x2, x2, HEAP, lsl #32
    // 0x8eb5b8: r16 = Sentinel
    //     0x8eb5b8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8eb5bc: cmp             w2, w16
    // 0x8eb5c0: b.eq            #0x8eb628
    // 0x8eb5c4: LoadField: r5 = r1->field_2b
    //     0x8eb5c4: ldur            x5, [x1, #0x2b]
    // 0x8eb5c8: mov             x1, x2
    // 0x8eb5cc: mov             x2, x5
    // 0x8eb5d0: r0 = call 0x448794
    //     0x8eb5d0: bl              #0x448794
    // 0x8eb5d4: mov             x1, x0
    // 0x8eb5d8: stur            x1, [fp, #-0x80]
    // 0x8eb5dc: r0 = Await()
    //     0x8eb5dc: bl              #0x8c1bb8  ; AwaitStub
    // 0x8eb5e0: ldur            x0, [fp, #-0x70]
    // 0x8eb5e4: ldur            x1, [fp, #-0x78]
    // 0x8eb5e8: r0 = ReThrow()
    //     0x8eb5e8: bl              #0x94dce4  ; ReThrowStub
    // 0x8eb5ec: brk             #0
    // 0x8eb5f0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eb5f0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eb5f4: b               #0x8eb2cc
    // 0x8eb5f8: r9 = Yfe
    //     0x8eb5f8: ldr             x9, [PP, #0x73b0]  ; [pp+0x73b0] Field <yJa.Yfe>: late final (offset: 0x34)
    // 0x8eb5fc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8eb5fc: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8eb600: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eb600: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eb604: b               #0x8eb380
    // 0x8eb608: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8eb608: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8eb60c: r9 = vEg
    //     0x8eb60c: add             x9, PP, #0xa, lsl #12  ; [pp+0xa368] Field <yJa.vEg>: late (offset: 0x24)
    //     0x8eb610: ldr             x9, [x9, #0x368]
    // 0x8eb614: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8eb614: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8eb618: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eb618: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eb61c: b               #0x8eb504
    // 0x8eb620: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8eb620: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8eb624: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x8eb624: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x8eb628: r9 = vEg
    //     0x8eb628: add             x9, PP, #0xa, lsl #12  ; [pp+0xa368] Field <yJa.vEg>: late (offset: 0x24)
    //     0x8eb62c: ldr             x9, [x9, #0x368]
    // 0x8eb630: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8eb630: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x8f964c, size: 0xe8
    // 0x8f964c: EnterFrame
    //     0x8f964c: stp             fp, lr, [SP, #-0x10]!
    //     0x8f9650: mov             fp, SP
    // 0x8f9654: AllocStack(0x18)
    //     0x8f9654: sub             SP, SP, #0x18
    // 0x8f9658: SetupParameters(yJa this /* r1 */)
    //     0x8f9658: stur            NULL, [fp, #-8]
    //     0x8f965c: movz            x0, #0
    //     0x8f9660: add             x1, fp, w0, sxtw #2
    //     0x8f9664: ldr             x1, [x1, #0x10]
    //     0x8f9668: ldur            w2, [x1, #0x17]
    //     0x8f966c: add             x2, x2, HEAP, lsl #32
    //     0x8f9670: stur            x2, [fp, #-0x10]
    // 0x8f9674: CheckStackOverflow
    //     0x8f9674: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f9678: cmp             SP, x16
    //     0x8f967c: b.ls            #0x8f9714
    // 0x8f9680: InitAsync() -> Future<void?>
    //     0x8f9680: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x8f9684: bl              #0x8c1de0  ; InitAsyncStub
    // 0x8f9688: ldur            x0, [fp, #-0x10]
    // 0x8f968c: LoadField: r1 = r0->field_f
    //     0x8f968c: ldur            w1, [x0, #0xf]
    // 0x8f9690: DecompressPointer r1
    //     0x8f9690: add             x1, x1, HEAP, lsl #32
    // 0x8f9694: LoadField: r2 = r1->field_23
    //     0x8f9694: ldur            w2, [x1, #0x23]
    // 0x8f9698: DecompressPointer r2
    //     0x8f9698: add             x2, x2, HEAP, lsl #32
    // 0x8f969c: r16 = Sentinel
    //     0x8f969c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8f96a0: cmp             w2, w16
    // 0x8f96a4: b.eq            #0x8f971c
    // 0x8f96a8: mov             x1, x2
    // 0x8f96ac: r2 = 0
    //     0x8f96ac: movz            x2, #0
    // 0x8f96b0: r0 = call 0x4f1cb8
    //     0x8f96b0: bl              #0x4f1cb8
    // 0x8f96b4: mov             x1, x0
    // 0x8f96b8: stur            x1, [fp, #-0x18]
    // 0x8f96bc: r0 = Await()
    //     0x8f96bc: bl              #0x8c1bb8  ; AwaitStub
    // 0x8f96c0: ldur            x0, [fp, #-0x10]
    // 0x8f96c4: LoadField: r1 = r0->field_f
    //     0x8f96c4: ldur            w1, [x0, #0xf]
    // 0x8f96c8: DecompressPointer r1
    //     0x8f96c8: add             x1, x1, HEAP, lsl #32
    // 0x8f96cc: LoadField: r2 = r1->field_23
    //     0x8f96cc: ldur            w2, [x1, #0x23]
    // 0x8f96d0: DecompressPointer r2
    //     0x8f96d0: add             x2, x2, HEAP, lsl #32
    // 0x8f96d4: r16 = Sentinel
    //     0x8f96d4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8f96d8: cmp             w2, w16
    // 0x8f96dc: b.eq            #0x8f9728
    // 0x8f96e0: mov             x1, x2
    // 0x8f96e4: r2 = 0
    //     0x8f96e4: movz            x2, #0
    // 0x8f96e8: r0 = call 0x448794
    //     0x8f96e8: bl              #0x448794
    // 0x8f96ec: mov             x1, x0
    // 0x8f96f0: stur            x1, [fp, #-0x18]
    // 0x8f96f4: r0 = Await()
    //     0x8f96f4: bl              #0x8c1bb8  ; AwaitStub
    // 0x8f96f8: ldur            x1, [fp, #-0x10]
    // 0x8f96fc: LoadField: r2 = r1->field_f
    //     0x8f96fc: ldur            w2, [x1, #0xf]
    // 0x8f9700: DecompressPointer r2
    //     0x8f9700: add             x2, x2, HEAP, lsl #32
    // 0x8f9704: r1 = 0
    //     0x8f9704: movz            x1, #0
    // 0x8f9708: StoreField: r2->field_2b = r1
    //     0x8f9708: stur            x1, [x2, #0x2b]
    // 0x8f970c: r0 = Null
    //     0x8f970c: mov             x0, NULL
    // 0x8f9710: r0 = ReturnAsyncNotFuture()
    //     0x8f9710: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x8f9714: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f9714: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f9718: b               #0x8f9680
    // 0x8f971c: r9 = vEg
    //     0x8f971c: add             x9, PP, #0xa, lsl #12  ; [pp+0xa368] Field <yJa.vEg>: late (offset: 0x24)
    //     0x8f9720: ldr             x9, [x9, #0x368]
    // 0x8f9724: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8f9724: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x8f9728: r9 = vEg
    //     0x8f9728: add             x9, PP, #0xa, lsl #12  ; [pp+0xa368] Field <yJa.vEg>: late (offset: 0x24)
    //     0x8f972c: ldr             x9, [x9, #0x368]
    // 0x8f9730: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8f9730: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Future<dynamic> <anonymous closure>(dynamic) async {
    // ** addr: 0x907760, size: 0x18c
    // 0x907760: EnterFrame
    //     0x907760: stp             fp, lr, [SP, #-0x10]!
    //     0x907764: mov             fp, SP
    // 0x907768: AllocStack(0x18)
    //     0x907768: sub             SP, SP, #0x18
    // 0x90776c: SetupParameters(yJa this /* r1 */)
    //     0x90776c: stur            NULL, [fp, #-8]
    //     0x907770: movz            x0, #0
    //     0x907774: add             x1, fp, w0, sxtw #2
    //     0x907778: ldr             x1, [x1, #0x10]
    //     0x90777c: ldur            w2, [x1, #0x17]
    //     0x907780: add             x2, x2, HEAP, lsl #32
    //     0x907784: stur            x2, [fp, #-0x10]
    // 0x907788: CheckStackOverflow
    //     0x907788: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90778c: cmp             SP, x16
    //     0x907790: b.ls            #0x9078c8
    // 0x907794: InitAsync() -> Future
    //     0x907794: mov             x0, NULL
    //     0x907798: bl              #0x8c1de0  ; InitAsyncStub
    // 0x90779c: ldur            x0, [fp, #-0x10]
    // 0x9077a0: LoadField: r1 = r0->field_f
    //     0x9077a0: ldur            w1, [x0, #0xf]
    // 0x9077a4: DecompressPointer r1
    //     0x9077a4: add             x1, x1, HEAP, lsl #32
    // 0x9077a8: LoadField: r2 = r1->field_1f
    //     0x9077a8: ldur            w2, [x1, #0x1f]
    // 0x9077ac: DecompressPointer r2
    //     0x9077ac: add             x2, x2, HEAP, lsl #32
    // 0x9077b0: r16 = Sentinel
    //     0x9077b0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9077b4: cmp             w2, w16
    // 0x9077b8: b.eq            #0x9078d0
    // 0x9077bc: LoadField: r1 = r0->field_13
    //     0x9077bc: ldur            w1, [x0, #0x13]
    // 0x9077c0: DecompressPointer r1
    //     0x9077c0: add             x1, x1, HEAP, lsl #32
    // 0x9077c4: LoadField: r3 = r1->field_1b
    //     0x9077c4: ldur            x3, [x1, #0x1b]
    // 0x9077c8: mov             x1, x2
    // 0x9077cc: mov             x2, x3
    // 0x9077d0: r0 = call 0x448794
    //     0x9077d0: bl              #0x448794
    // 0x9077d4: mov             x1, x0
    // 0x9077d8: stur            x1, [fp, #-0x18]
    // 0x9077dc: r0 = Await()
    //     0x9077dc: bl              #0x8c1bb8  ; AwaitStub
    // 0x9077e0: ldur            x0, [fp, #-0x10]
    // 0x9077e4: LoadField: r1 = r0->field_f
    //     0x9077e4: ldur            w1, [x0, #0xf]
    // 0x9077e8: DecompressPointer r1
    //     0x9077e8: add             x1, x1, HEAP, lsl #32
    // 0x9077ec: LoadField: r2 = r1->field_1f
    //     0x9077ec: ldur            w2, [x1, #0x1f]
    // 0x9077f0: DecompressPointer r2
    //     0x9077f0: add             x2, x2, HEAP, lsl #32
    // 0x9077f4: r16 = Sentinel
    //     0x9077f4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x9077f8: cmp             w2, w16
    // 0x9077fc: b.eq            #0x9078d8
    // 0x907800: LoadField: r1 = r0->field_13
    //     0x907800: ldur            w1, [x0, #0x13]
    // 0x907804: DecompressPointer r1
    //     0x907804: add             x1, x1, HEAP, lsl #32
    // 0x907808: ArrayLoad: r3 = r1[0]  ; List_4
    //     0x907808: ldur            w3, [x1, #0x17]
    // 0x90780c: DecompressPointer r3
    //     0x90780c: add             x3, x3, HEAP, lsl #32
    // 0x907810: cmp             w3, NULL
    // 0x907814: b.eq            #0x9078e0
    // 0x907818: r1 = LoadInt32Instr(r3)
    //     0x907818: sbfx            x1, x3, #1, #0x1f
    //     0x90781c: tbz             w3, #0, #0x907824
    //     0x907820: ldur            x1, [x3, #7]
    // 0x907824: mov             x16, x1
    // 0x907828: mov             x1, x2
    // 0x90782c: mov             x2, x16
    // 0x907830: r0 = call 0x4b3888
    //     0x907830: bl              #0x4b3888
    // 0x907834: mov             x1, x0
    // 0x907838: stur            x1, [fp, #-0x18]
    // 0x90783c: r0 = Await()
    //     0x90783c: bl              #0x8c1bb8  ; AwaitStub
    // 0x907840: mov             x1, x0
    // 0x907844: ldur            x0, [fp, #-0x10]
    // 0x907848: stur            x1, [fp, #-0x18]
    // 0x90784c: LoadField: r2 = r0->field_f
    //     0x90784c: ldur            w2, [x0, #0xf]
    // 0x907850: DecompressPointer r2
    //     0x907850: add             x2, x2, HEAP, lsl #32
    // 0x907854: LoadField: r3 = r2->field_33
    //     0x907854: ldur            w3, [x2, #0x33]
    // 0x907858: DecompressPointer r3
    //     0x907858: add             x3, x3, HEAP, lsl #32
    // 0x90785c: r16 = Sentinel
    //     0x90785c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x907860: cmp             w3, w16
    // 0x907864: b.eq            #0x9078e4
    // 0x907868: stur            x3, [fp, #-0x10]
    // 0x90786c: r0 = zJa()
    //     0x90786c: bl              #0x907a28  ; AllocatezJaStub -> zJa (size=0x2c)
    // 0x907870: mov             x1, x0
    // 0x907874: ldur            x2, [fp, #-0x18]
    // 0x907878: ldur            x3, [fp, #-0x10]
    // 0x90787c: stur            x0, [fp, #-0x10]
    // 0x907880: r0 = call 0x571d20
    //     0x907880: bl              #0x571d20
    // 0x907884: ldur            x1, [fp, #-0x10]
    // 0x907888: r2 = false
    //     0x907888: add             x2, NULL, #0x30  ; false
    // 0x90788c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x90788c: ldr             x4, [PP, #0x2a8]  ; [pp+0x2a8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x907890: r0 = call 0x56f044
    //     0x907890: bl              #0x56f044
    // 0x907894: cmp             w0, NULL
    // 0x907898: b.eq            #0x9078ac
    // 0x90789c: LoadField: r1 = r0->field_b
    //     0x90789c: ldur            w1, [x0, #0xb]
    // 0x9078a0: DecompressPointer r1
    //     0x9078a0: add             x1, x1, HEAP, lsl #32
    // 0x9078a4: mov             x0, x1
    // 0x9078a8: r0 = ReturnAsync()
    //     0x9078a8: b               #0x8c3a30  ; ReturnAsyncStub
    // 0x9078ac: r0 = kJa()
    //     0x9078ac: bl              #0x8cafa8  ; AllocatekJaStub -> kJa (size=0x10)
    // 0x9078b0: mov             x1, x0
    // 0x9078b4: r0 = "Could not read value from box. Maybe your box is corrupted."
    //     0x9078b4: ldr             x0, [PP, #0x73a0]  ; [pp+0x73a0] "Could not read value from box. Maybe your box is corrupted."
    // 0x9078b8: StoreField: r1->field_b = r0
    //     0x9078b8: stur            w0, [x1, #0xb]
    // 0x9078bc: mov             x0, x1
    // 0x9078c0: r0 = Throw()
    //     0x9078c0: bl              #0x94dd08  ; ThrowStub
    // 0x9078c4: brk             #0
    // 0x9078c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9078c8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9078cc: b               #0x907794
    // 0x9078d0: r9 = uEg
    //     0x9078d0: ldr             x9, [PP, #0x73a8]  ; [pp+0x73a8] Field <yJa.uEg>: late (offset: 0x20)
    // 0x9078d4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9078d4: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9078d8: r9 = uEg
    //     0x9078d8: ldr             x9, [PP, #0x73a8]  ; [pp+0x73a8] Field <yJa.uEg>: late (offset: 0x20)
    // 0x9078dc: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9078dc: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x9078e0: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x9078e0: bl              #0x9500d4  ; NullCastErrorSharedWithoutFPURegsStub
    // 0x9078e4: r9 = Yfe
    //     0x9078e4: ldr             x9, [PP, #0x73b0]  ; [pp+0x73b0] Field <yJa.Yfe>: late final (offset: 0x34)
    // 0x9078e8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x9078e8: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] int <anonymous closure>(dynamic, BJa, BJa) {
    // ** addr: 0x4488d0, size: -0x1
  }
  [closure] Future<dynamic> _Wrb(dynamic) {
    // ** addr: 0x7ec1f4, size: -0x1
  }
}
