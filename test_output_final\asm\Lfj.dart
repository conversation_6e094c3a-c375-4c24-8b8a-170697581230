// lib: Aqj, url: lfj

// class id: 1049656, size: 0x8
class :: {
}

// class id: 777, size: 0xc, field offset: 0xc
class AUa extends OTa {

  static late final vWa iog; // offset: 0x1230

  [closure] static AUa <anonymous closure>(dynamic) {
    // ** addr: 0x471c64, size: -0x1
  }
  [closure] static AUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x471d7c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x471c0c, size: -0x1
  }
}
