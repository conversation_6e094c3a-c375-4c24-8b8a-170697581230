// lib: , url: mTi

// class id: 1048865, size: 0x8
class :: {
}

// class id: 2596, size: 0x3c, field offset: 0x8
//   const constructor, 
class KI extends Object {

  _OneByteString field_8;
  _Mint field_c;
  _OneByteString field_14;
  _OneByteString field_18;
  _OneByteString field_1c;
  _Mint field_20;
  _Mint field_28;
  _OneByteString field_30;
  _OneByteString field_34;
  bool field_38;
  static late final RegExp _xxc; // offset: 0x81c

  [closure] static bool <anonymous closure>(dynamic, String) {
    // ** addr: 0x304cbc, size: -0x1
  }
  [closure] static KI? Fxc(dynamic, String) {
    // ** addr: 0x3041cc, size: -0x1
  }
}
