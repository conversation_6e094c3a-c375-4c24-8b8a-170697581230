// lib: Oqj, url: FVi

// class id: 1049670, size: 0x8
class :: {
}

// class id: 763, size: 0xc, field offset: 0xc
class cVa extends OTa {

  static late final vWa iog; // offset: 0x1268

  [closure] static cVa <anonymous closure>(dynamic) {
    // ** addr: 0x4706b4, size: -0x1
  }
  [closure] static cVa _Psg(dynamic, String, LTa, KTa, za, za, List<int>?) {
    // ** addr: 0x4707b4, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x47065c, size: -0x1
  }
}
