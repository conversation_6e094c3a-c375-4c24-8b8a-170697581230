// lib: , url: MPi

// class id: 1048698, size: 0x8
class :: {
}

// class id: 4506, size: 0x60, field offset: 0x8
class Vy extends Object {

  Map<String, dynamic> WNb(Vy) {
    // ** addr: 0x903220, size: 0x48
    // 0x903220: EnterFrame
    //     0x903220: stp             fp, lr, [SP, #-0x10]!
    //     0x903224: mov             fp, SP
    // 0x903228: CheckStackOverflow
    //     0x903228: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90322c: cmp             SP, x16
    //     0x903230: b.ls            #0x903248
    // 0x903234: ldr             x1, [fp, #0x10]
    // 0x903238: r0 = call 0x55955c
    //     0x903238: bl              #0x55955c
    // 0x90323c: LeaveFrame
    //     0x90323c: mov             SP, fp
    //     0x903240: ldp             fp, lr, [SP], #0x10
    // 0x903244: ret
    //     0x903244: ret             
    // 0x903248: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x903248: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90324c: b               #0x903234
  }
  [closure] static Wy <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x55d810, size: -0x1
  }
  [closure] static Ty <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x55d7c0, size: -0x1
  }
  [closure] Map<String, dynamic> <anonymous closure>(dynamic, Wy) {
    // ** addr: 0x55a9e4, size: -0x1
  }
  [closure] Map<String, dynamic> <anonymous closure>(dynamic, Ty) {
    // ** addr: 0x55a9b4, size: -0x1
  }
}
