// lib: , url: Gkj

// class id: 1049743, size: 0x8
class :: {
}

// class id: 4466, size: 0x14, field offset: 0x8
class WXa extends _tA {

  int length(WXa) {
    // ** addr: 0x8fdf34, size: 0x48
    // 0x8fdf34: EnterFrame
    //     0x8fdf34: stp             fp, lr, [SP, #-0x10]!
    //     0x8fdf38: mov             fp, SP
    // 0x8fdf3c: ldr             x2, [fp, #0x10]
    // 0x8fdf40: LoadField: r3 = r2->field_b
    //     0x8fdf40: ldur            x3, [x2, #0xb]
    // 0x8fdf44: r0 = BoxInt64Instr(r3)
    //     0x8fdf44: sbfiz           x0, x3, #1, #0x1f
    //     0x8fdf48: cmp             x3, x0, asr #1
    //     0x8fdf4c: b.eq            #0x8fdf58
    //     0x8fdf50: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8fdf54: stur            x3, [x0, #7]
    // 0x8fdf58: LeaveFrame
    //     0x8fdf58: mov             SP, fp
    //     0x8fdf5c: ldp             fp, lr, [SP], #0x10
    // 0x8fdf60: ret
    //     0x8fdf60: ret             
  }
  void []=(WXa, int, bool) {
    // ** addr: 0x8fdf7c, size: 0x94
    // 0x8fdf7c: EnterFrame
    //     0x8fdf7c: stp             fp, lr, [SP, #-0x10]!
    //     0x8fdf80: mov             fp, SP
    // 0x8fdf84: AllocStack(0x18)
    //     0x8fdf84: sub             SP, SP, #0x18
    // 0x8fdf88: CheckStackOverflow
    //     0x8fdf88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fdf8c: cmp             SP, x16
    //     0x8fdf90: b.ls            #0x8fdff0
    // 0x8fdf94: ldr             x0, [fp, #0x18]
    // 0x8fdf98: r2 = Null
    //     0x8fdf98: mov             x2, NULL
    // 0x8fdf9c: r1 = Null
    //     0x8fdf9c: mov             x1, NULL
    // 0x8fdfa0: branchIfSmi(r0, 0x8fdfc8)
    //     0x8fdfa0: tbz             w0, #0, #0x8fdfc8
    // 0x8fdfa4: r4 = LoadClassIdInstr(r0)
    //     0x8fdfa4: ldur            x4, [x0, #-1]
    //     0x8fdfa8: ubfx            x4, x4, #0xc, #0x14
    // 0x8fdfac: sub             x4, x4, #0x3b
    // 0x8fdfb0: cmp             x4, #1
    // 0x8fdfb4: b.ls            #0x8fdfc8
    // 0x8fdfb8: r8 = int
    //     0x8fdfb8: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8fdfbc: r3 = Null
    //     0x8fdfbc: add             x3, PP, #0x33, lsl #12  ; [pp+0x33e78] Null
    //     0x8fdfc0: ldr             x3, [x3, #0xe78]
    // 0x8fdfc4: r0 = int()
    //     0x8fdfc4: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8fdfc8: ldr             x16, [fp, #0x20]
    // 0x8fdfcc: ldr             lr, [fp, #0x18]
    // 0x8fdfd0: stp             lr, x16, [SP, #8]
    // 0x8fdfd4: ldr             x16, [fp, #0x10]
    // 0x8fdfd8: str             x16, [SP]
    // 0x8fdfdc: r0 = call 0x31c21c
    //     0x8fdfdc: bl              #0x31c21c
    // 0x8fdfe0: r0 = Null
    //     0x8fdfe0: mov             x0, NULL
    // 0x8fdfe4: LeaveFrame
    //     0x8fdfe4: mov             SP, fp
    //     0x8fdfe8: ldp             fp, lr, [SP], #0x10
    // 0x8fdfec: ret
    //     0x8fdfec: ret             
    // 0x8fdff0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fdff0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fdff4: b               #0x8fdf94
  }
  bool [](WXa, int) {
    // ** addr: 0x8fe010, size: 0xfc
    // 0x8fe010: EnterFrame
    //     0x8fe010: stp             fp, lr, [SP, #-0x10]!
    //     0x8fe014: mov             fp, SP
    // 0x8fe018: ldr             x0, [fp, #0x10]
    // 0x8fe01c: r2 = Null
    //     0x8fe01c: mov             x2, NULL
    // 0x8fe020: r1 = Null
    //     0x8fe020: mov             x1, NULL
    // 0x8fe024: branchIfSmi(r0, 0x8fe04c)
    //     0x8fe024: tbz             w0, #0, #0x8fe04c
    // 0x8fe028: r4 = LoadClassIdInstr(r0)
    //     0x8fe028: ldur            x4, [x0, #-1]
    //     0x8fe02c: ubfx            x4, x4, #0xc, #0x14
    // 0x8fe030: sub             x4, x4, #0x3b
    // 0x8fe034: cmp             x4, #1
    // 0x8fe038: b.ls            #0x8fe04c
    // 0x8fe03c: r8 = int
    //     0x8fe03c: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8fe040: r3 = Null
    //     0x8fe040: add             x3, PP, #0x33, lsl #12  ; [pp+0x33e68] Null
    //     0x8fe044: ldr             x3, [x3, #0xe68]
    // 0x8fe048: r0 = int()
    //     0x8fe048: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8fe04c: ldr             x2, [fp, #0x10]
    // 0x8fe050: r3 = LoadInt32Instr(r2)
    //     0x8fe050: sbfx            x3, x2, #1, #0x1f
    //     0x8fe054: tbz             w2, #0, #0x8fe05c
    //     0x8fe058: ldur            x3, [x2, #7]
    // 0x8fe05c: r2 = 8
    //     0x8fe05c: movz            x2, #0x8
    // 0x8fe060: sdiv            x4, x3, x2
    // 0x8fe064: ldr             x2, [fp, #0x18]
    // 0x8fe068: LoadField: r5 = r2->field_7
    //     0x8fe068: ldur            w5, [x2, #7]
    // 0x8fe06c: DecompressPointer r5
    //     0x8fe06c: add             x5, x5, HEAP, lsl #32
    // 0x8fe070: LoadField: r2 = r5->field_b
    //     0x8fe070: ldur            w2, [x5, #0xb]
    // 0x8fe074: DecompressPointer r2
    //     0x8fe074: add             x2, x2, HEAP, lsl #32
    // 0x8fe078: r0 = LoadInt32Instr(r2)
    //     0x8fe078: sbfx            x0, x2, #1, #0x1f
    // 0x8fe07c: mov             x1, x4
    // 0x8fe080: cmp             x1, x0
    // 0x8fe084: b.hs            #0x8fe0f0
    // 0x8fe088: LoadField: r1 = r5->field_f
    //     0x8fe088: ldur            w1, [x5, #0xf]
    // 0x8fe08c: DecompressPointer r1
    //     0x8fe08c: add             x1, x1, HEAP, lsl #32
    // 0x8fe090: ArrayLoad: r2 = r1[r4]  ; Unknown_4
    //     0x8fe090: add             x16, x1, x4, lsl #2
    //     0x8fe094: ldur            w2, [x16, #0xf]
    // 0x8fe098: DecompressPointer r2
    //     0x8fe098: add             x2, x2, HEAP, lsl #32
    // 0x8fe09c: ubfx            x3, x3, #0, #0x20
    // 0x8fe0a0: r1 = 7
    //     0x8fe0a0: movz            x1, #0x7
    // 0x8fe0a4: and             x4, x3, x1
    // 0x8fe0a8: ubfx            x4, x4, #0, #0x20
    // 0x8fe0ac: r1 = 7
    //     0x8fe0ac: movz            x1, #0x7
    // 0x8fe0b0: sub             x3, x1, x4
    // 0x8fe0b4: r1 = LoadInt32Instr(r2)
    //     0x8fe0b4: sbfx            x1, x2, #1, #0x1f
    //     0x8fe0b8: tbz             w2, #0, #0x8fe0c0
    //     0x8fe0bc: ldur            x1, [x2, #7]
    // 0x8fe0c0: asr             x2, x1, x3
    // 0x8fe0c4: ubfx            x2, x2, #0, #0x20
    // 0x8fe0c8: r1 = 1
    //     0x8fe0c8: movz            x1, #0x1
    // 0x8fe0cc: and             x3, x2, x1
    // 0x8fe0d0: ubfx            x3, x3, #0, #0x20
    // 0x8fe0d4: cmp             x3, #1
    // 0x8fe0d8: r16 = true
    //     0x8fe0d8: add             x16, NULL, #0x20  ; true
    // 0x8fe0dc: r17 = false
    //     0x8fe0dc: add             x17, NULL, #0x30  ; false
    // 0x8fe0e0: csel            x0, x16, x17, eq
    // 0x8fe0e4: LeaveFrame
    //     0x8fe0e4: mov             SP, fp
    //     0x8fe0e8: ldp             fp, lr, [SP], #0x10
    // 0x8fe0ec: ret
    //     0x8fe0ec: ret             
    // 0x8fe0f0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8fe0f0: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
  }
}
