// lib: , url: EOi

// class id: 1048640, size: 0x8
class :: {
}

// class id: 3399, size: 0x74, field offset: 0x30
class _ew extends Wu<dynamic> {

  dynamic dyn:get:data(_ew) {
    // ** addr: 0x8ebb74, size: 0x28
    // 0x8ebb74: ldr             x1, [SP]
    // 0x8ebb78: LoadField: r0 = r1->field_57
    //     0x8ebb78: ldur            w0, [x1, #0x57]
    // 0x8ebb7c: DecompressPointer r0
    //     0x8ebb7c: add             x0, x0, HEAP, lsl #32
    // 0x8ebb80: ret
    //     0x8ebb80: ret             
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x4891f0, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x48910c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x459bdc, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x48909c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x488acc, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x4887d4, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x48875c, size: -0x1
  }
  [closure] qoa <anonymous closure>(dynamic, int) {
    // ** addr: 0x4882ec, size: -0x1
  }
  [closure] qoa <anonymous closure>(dynamic, int) {
    // ** addr: 0x487e1c, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Function) {
    // ** addr: 0x487d5c, size: -0x1
  }
  [closure] yya <anonymous closure>(dynamic, aoa) {
    // ** addr: 0x45a9e8, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Function) {
    // ** addr: 0x45a928, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x45a844, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x459d08, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Function) {
    // ** addr: 0x459c48, size: -0x1
  }
  [closure] Zv <anonymous closure>(dynamic, aoa, int) {
    // ** addr: 0x4599cc, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x458e44, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x456e98, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x456b48, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x456a54, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x456d78, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x456f94, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x45723c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x457ad4, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x458218, size: -0x1
  }
  [closure] FutureOr<bool> <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x458f24, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x459890, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x45981c, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, String, String) {
    // ** addr: 0x459ac4, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x45a094, size: -0x1
  }
  [closure] qoa <anonymous closure>(dynamic, Bya, (dynamic) => List<QZ>) {
    // ** addr: 0x45d2ac, size: -0x1
  }
  [closure] Kt <anonymous closure>(dynamic, Bya, (dynamic) => List<QZ>) {
    // ** addr: 0x45c770, size: -0x1
  }
  [closure] Kt <anonymous closure>(dynamic, Bya, (dynamic) => List<QZ>) {
    // ** addr: 0x45c144, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x45c53c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x45d120, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x45d4cc, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x48815c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x488558, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x4888f8, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x488bf0, size: -0x1
  }
}

// class id: 4131, size: 0x14, field offset: 0x10
class dw extends Tu {
}
