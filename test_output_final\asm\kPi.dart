// lib: , url: KPi

// class id: 1048696, size: 0x8
class :: {
}

// class id: 4508, size: 0x54, field offset: 0x8
class Ty extends Object {

  Map<String, dynamic> WNb(Ty) {
    // ** addr: 0x8ebf00, size: 0x48
    // 0x8ebf00: EnterFrame
    //     0x8ebf00: stp             fp, lr, [SP, #-0x10]!
    //     0x8ebf04: mov             fp, SP
    // 0x8ebf08: CheckStackOverflow
    //     0x8ebf08: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ebf0c: cmp             SP, x16
    //     0x8ebf10: b.ls            #0x8ebf28
    // 0x8ebf14: ldr             x1, [fp, #0x10]
    // 0x8ebf18: r0 = call 0x458a9c
    //     0x8ebf18: bl              #0x458a9c
    // 0x8ebf1c: LeaveFrame
    //     0x8ebf1c: mov             SP, fp
    //     0x8ebf20: ldp             fp, lr, [SP], #0x10
    // 0x8ebf24: ret
    //     0x8ebf24: ret             
    // 0x8ebf28: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ebf28: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ebf2c: b               #0x8ebf14
  }
}
