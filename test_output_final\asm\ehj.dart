// lib: <PERSON>j, url: Ehj

// class id: 1049578, size: 0x8
class :: {

  [closure] static fRa wSf(dynamic, fRa) {
    // ** addr: 0x6abf9c, size: -0x1
  }
}

// class id: 3060, size: 0x18, field offset: 0x14
//   transformed mixin,
abstract class _RQa extends Mt<dynamic>
     with Vz<X0 bound It> {
}

// class id: 3061, size: 0x28, field offset: 0x18
class _SQa extends _RQa {

  late cRa<dRa> _eCb; // offset: 0x1c
  late gRa _uSf; // offset: 0x24
  late bool _sSf; // offset: 0x18
  late bool _tSf; // offset: 0x20

  [closure] pI <anonymous closure>(dynamic, aoa, GV) {
    // ** addr: 0x607920, size: -0x1
  }
  [closure] void vSf(dynamic, fRa) {
    // ** addr: 0x6494f8, size: -0x1
  }
}

// class id: 3856, size: 0x74, field offset: 0xc
class QQa extends It {
}
