// lib: cpj, url: Aij

// class id: 1049607, size: 0x8
class :: {
}

// class id: 869, size: 0x14, field offset: 0x14
class zSa extends HRa {

  static late final vWa iog; // offset: 0x10d4

  [closure] static (dynamic) => zSa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x48446c, size: -0x1
  }
  [closure] static zSa <anonymous closure>(dynamic) {
    // ** addr: 0x4844c0, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x484408, size: -0x1
  }
}
