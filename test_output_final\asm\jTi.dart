// lib: , url: JTi

// class id: 1048887, size: 0x8
class :: {
}

// class id: 2576, size: 0x4c, field offset: 0x24
//   transformed mixin,
abstract class _eN extends bM
     with _dN {
}

// class id: 2577, size: 0xa8, field offset: 0x4c
abstract class fN extends _eN {

  late NM _dSd; // offset: 0x90
  late double _Cze; // offset: 0x94
  late double _QBe; // offset: 0x98

  [closure] void jCc(dynamic, YJ) {
    // ** addr: 0x78b5e8, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x6d3c4c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x6d38bc, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x6d3210, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x6d3324, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x6d1160, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x6d0ebc, size: -0x1
  }
}

// class id: 2578, size: 0xa8, field offset: 0xa8
class gN extends fN {
}

// class id: 2579, size: 0x24, field offset: 0x24
abstract class _dN extends bM {
}

// class id: 2968, size: 0x10, field offset: 0x8
class cN extends _DF {
}

// class id: 2969, size: 0x1c, field offset: 0x8
class bN extends _DF {
}

// class id: 2970, size: 0x18, field offset: 0x8
class aN extends _DF {
}

// class id: 2971, size: 0x14, field offset: 0x8
class ZM extends _DF {
}

// class id: 2972, size: 0x18, field offset: 0x8
class YM extends _DF {
}

// class id: 5579, size: 0x14, field offset: 0x14
enum _vM extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
