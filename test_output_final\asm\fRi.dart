// lib: , url: FRi

// class id: 1048783, size: 0x8
class :: {
}

// class id: 4263, size: 0x1c, field offset: 0x8
//   transformed mixin,
abstract class _VD extends Object
     with ID {

  late fE options; // offset: 0x8
  late BD CIf; // offset: 0x10

  [closure] Future<dynamic> <anonymous closure>(dynamic, dynamic) async {
    // ** addr: 0x8ded60, size: 0x144
    // 0x8ded60: EnterFrame
    //     0x8ded60: stp             fp, lr, [SP, #-0x10]!
    //     0x8ded64: mov             fp, SP
    // 0x8ded68: AllocStack(0x40)
    //     0x8ded68: sub             SP, SP, #0x40
    // 0x8ded6c: SetupParameters(_VD this /* r1 */, dynamic _ /* r2, fp-0x20 */)
    //     0x8ded6c: stur            NULL, [fp, #-8]
    //     0x8ded70: movz            x0, #0
    //     0x8ded74: add             x1, fp, w0, sxtw #2
    //     0x8ded78: ldr             x1, [x1, #0x18]
    //     0x8ded7c: add             x2, fp, w0, sxtw #2
    //     0x8ded80: ldr             x2, [x2, #0x10]
    //     0x8ded84: stur            x2, [fp, #-0x20]
    //     0x8ded88: ldur            w3, [x1, #0x17]
    //     0x8ded8c: add             x3, x3, HEAP, lsl #32
    //     0x8ded90: stur            x3, [fp, #-0x18]
    // 0x8ded94: CheckStackOverflow
    //     0x8ded94: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ded98: cmp             SP, x16
    //     0x8ded9c: b.ls            #0x8dee9c
    // 0x8deda0: LoadField: r4 = r1->field_b
    //     0x8deda0: ldur            w4, [x1, #0xb]
    // 0x8deda4: DecompressPointer r4
    //     0x8deda4: add             x4, x4, HEAP, lsl #32
    // 0x8deda8: stur            x4, [fp, #-0x10]
    // 0x8dedac: InitAsync() -> Future
    //     0x8dedac: mov             x0, NULL
    //     0x8dedb0: bl              #0x8c1de0  ; InitAsyncStub
    // 0x8dedb4: r1 = 1
    //     0x8dedb4: movz            x1, #0x1
    // 0x8dedb8: r0 = AllocateContext()
    //     0x8dedb8: bl              #0x94e988  ; AllocateContextStub
    // 0x8dedbc: mov             x4, x0
    // 0x8dedc0: ldur            x3, [fp, #-0x18]
    // 0x8dedc4: stur            x4, [fp, #-0x28]
    // 0x8dedc8: StoreField: r4->field_b = r3
    //     0x8dedc8: stur            w3, [x4, #0xb]
    // 0x8dedcc: ldur            x0, [fp, #-0x20]
    // 0x8dedd0: r2 = Null
    //     0x8dedd0: mov             x2, NULL
    // 0x8dedd4: r1 = Null
    //     0x8dedd4: mov             x1, NULL
    // 0x8dedd8: r4 = 59
    //     0x8dedd8: movz            x4, #0x3b
    // 0x8deddc: branchIfSmi(r0, 0x8dede8)
    //     0x8deddc: tbz             w0, #0, #0x8dede8
    // 0x8dede0: r4 = LoadClassIdInstr(r0)
    //     0x8dede0: ldur            x4, [x0, #-1]
    //     0x8dede4: ubfx            x4, x4, #0xc, #0x14
    // 0x8dede8: r17 = 4273
    //     0x8dede8: movz            x17, #0x10b1
    // 0x8dedec: cmp             x4, x17
    // 0x8dedf0: b.eq            #0x8dee08
    // 0x8dedf4: r8 = LD
    //     0x8dedf4: add             x8, PP, #8, lsl #12  ; [pp+0x8468] Type: LD
    //     0x8dedf8: ldr             x8, [x8, #0x468]
    // 0x8dedfc: r3 = Null
    //     0x8dedfc: add             x3, PP, #8, lsl #12  ; [pp+0x8470] Null
    //     0x8dee00: ldr             x3, [x3, #0x470]
    // 0x8dee04: r0 = LD()
    //     0x8dee04: bl              #0x8ded30  ; IsType_LD_Stub
    // 0x8dee08: ldur            x0, [fp, #-0x20]
    // 0x8dee0c: ldur            x2, [fp, #-0x28]
    // 0x8dee10: StoreField: r2->field_f = r0
    //     0x8dee10: stur            w0, [x2, #0xf]
    // 0x8dee14: LoadField: r1 = r0->field_f
    //     0x8dee14: ldur            w1, [x0, #0xf]
    // 0x8dee18: DecompressPointer r1
    //     0x8dee18: add             x1, x1, HEAP, lsl #32
    // 0x8dee1c: r16 = Instance_KD
    //     0x8dee1c: ldr             x16, [PP, #0x7750]  ; [pp+0x7750] Obj!KD@6a4b11
    // 0x8dee20: cmp             w1, w16
    // 0x8dee24: b.eq            #0x8dee38
    // 0x8dee28: r16 = Instance_KD
    //     0x8dee28: add             x16, PP, #8, lsl #12  ; [pp+0x8480] Obj!KD@6a4b31
    //     0x8dee2c: ldr             x16, [x16, #0x480]
    // 0x8dee30: cmp             w1, w16
    // 0x8dee34: b.ne            #0x8dee98
    // 0x8dee38: ldur            x0, [fp, #-0x18]
    // 0x8dee3c: ldur            x3, [fp, #-0x10]
    // 0x8dee40: LoadField: r1 = r0->field_b
    //     0x8dee40: ldur            w1, [x0, #0xb]
    // 0x8dee44: DecompressPointer r1
    //     0x8dee44: add             x1, x1, HEAP, lsl #32
    // 0x8dee48: LoadField: r0 = r1->field_13
    //     0x8dee48: ldur            w0, [x1, #0x13]
    // 0x8dee4c: DecompressPointer r0
    //     0x8dee4c: add             x0, x0, HEAP, lsl #32
    // 0x8dee50: LoadField: r4 = r0->field_53
    //     0x8dee50: ldur            w4, [x0, #0x53]
    // 0x8dee54: DecompressPointer r4
    //     0x8dee54: add             x4, x4, HEAP, lsl #32
    // 0x8dee58: stur            x4, [fp, #-0x18]
    // 0x8dee5c: r1 = Function '<anonymous closure>':.
    //     0x8dee5c: add             x1, PP, #8, lsl #12  ; [pp+0x8488] AnonymousClosure: (0x40b578), in [FRi] _VD::_rxh (0x42900c)
    //     0x8dee60: ldr             x1, [x1, #0x488]
    // 0x8dee64: r0 = AllocateClosure()
    //     0x8dee64: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x8dee68: mov             x1, x0
    // 0x8dee6c: ldur            x0, [fp, #-0x10]
    // 0x8dee70: StoreField: r1->field_b = r0
    //     0x8dee70: stur            w0, [x1, #0xb]
    // 0x8dee74: mov             x2, x1
    // 0x8dee78: r1 = Null
    //     0x8dee78: mov             x1, NULL
    // 0x8dee7c: r0 = call 0x2f44a0
    //     0x8dee7c: bl              #0x2f44a0
    // 0x8dee80: ldur            x16, [fp, #-0x18]
    // 0x8dee84: stp             x16, NULL, [SP, #8]
    // 0x8dee88: str             x0, [SP]
    // 0x8dee8c: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8dee8c: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8dee90: r0 = call 0x40ab48
    //     0x8dee90: bl              #0x40ab48
    // 0x8dee94: r0 = ReturnAsync()
    //     0x8dee94: b               #0x8c3a30  ; ReturnAsyncStub
    // 0x8dee98: r0 = ReturnAsyncNotFuture()
    //     0x8dee98: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x8dee9c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8dee9c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8deea0: b               #0x8deda0
  }
  [closure] Future<dynamic> <anonymous closure>(dynamic, dynamic) async {
    // ** addr: 0x8deeb0, size: 0x134
    // 0x8deeb0: EnterFrame
    //     0x8deeb0: stp             fp, lr, [SP, #-0x10]!
    //     0x8deeb4: mov             fp, SP
    // 0x8deeb8: AllocStack(0x40)
    //     0x8deeb8: sub             SP, SP, #0x40
    // 0x8deebc: SetupParameters(_VD this /* r1 */, dynamic _ /* r2, fp-0x20 */)
    //     0x8deebc: stur            NULL, [fp, #-8]
    //     0x8deec0: movz            x0, #0
    //     0x8deec4: add             x1, fp, w0, sxtw #2
    //     0x8deec8: ldr             x1, [x1, #0x18]
    //     0x8deecc: add             x2, fp, w0, sxtw #2
    //     0x8deed0: ldr             x2, [x2, #0x10]
    //     0x8deed4: stur            x2, [fp, #-0x20]
    //     0x8deed8: ldur            w3, [x1, #0x17]
    //     0x8deedc: add             x3, x3, HEAP, lsl #32
    //     0x8deee0: stur            x3, [fp, #-0x18]
    // 0x8deee4: CheckStackOverflow
    //     0x8deee4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8deee8: cmp             SP, x16
    //     0x8deeec: b.ls            #0x8defdc
    // 0x8deef0: LoadField: r4 = r1->field_b
    //     0x8deef0: ldur            w4, [x1, #0xb]
    // 0x8deef4: DecompressPointer r4
    //     0x8deef4: add             x4, x4, HEAP, lsl #32
    // 0x8deef8: stur            x4, [fp, #-0x10]
    // 0x8deefc: InitAsync() -> Future
    //     0x8deefc: mov             x0, NULL
    //     0x8def00: bl              #0x8c1de0  ; InitAsyncStub
    // 0x8def04: r1 = 1
    //     0x8def04: movz            x1, #0x1
    // 0x8def08: r0 = AllocateContext()
    //     0x8def08: bl              #0x94e988  ; AllocateContextStub
    // 0x8def0c: mov             x4, x0
    // 0x8def10: ldur            x3, [fp, #-0x18]
    // 0x8def14: stur            x4, [fp, #-0x28]
    // 0x8def18: StoreField: r4->field_b = r3
    //     0x8def18: stur            w3, [x4, #0xb]
    // 0x8def1c: ldur            x0, [fp, #-0x20]
    // 0x8def20: r2 = Null
    //     0x8def20: mov             x2, NULL
    // 0x8def24: r1 = Null
    //     0x8def24: mov             x1, NULL
    // 0x8def28: r4 = 59
    //     0x8def28: movz            x4, #0x3b
    // 0x8def2c: branchIfSmi(r0, 0x8def38)
    //     0x8def2c: tbz             w0, #0, #0x8def38
    // 0x8def30: r4 = LoadClassIdInstr(r0)
    //     0x8def30: ldur            x4, [x0, #-1]
    //     0x8def34: ubfx            x4, x4, #0xc, #0x14
    // 0x8def38: r17 = 4273
    //     0x8def38: movz            x17, #0x10b1
    // 0x8def3c: cmp             x4, x17
    // 0x8def40: b.eq            #0x8def58
    // 0x8def44: r8 = LD
    //     0x8def44: add             x8, PP, #8, lsl #12  ; [pp+0x8468] Type: LD
    //     0x8def48: ldr             x8, [x8, #0x468]
    // 0x8def4c: r3 = Null
    //     0x8def4c: add             x3, PP, #8, lsl #12  ; [pp+0x84a8] Null
    //     0x8def50: ldr             x3, [x3, #0x4a8]
    // 0x8def54: r0 = LD()
    //     0x8def54: bl              #0x8ded30  ; IsType_LD_Stub
    // 0x8def58: ldur            x0, [fp, #-0x20]
    // 0x8def5c: ldur            x2, [fp, #-0x28]
    // 0x8def60: StoreField: r2->field_f = r0
    //     0x8def60: stur            w0, [x2, #0xf]
    // 0x8def64: LoadField: r1 = r0->field_f
    //     0x8def64: ldur            w1, [x0, #0xf]
    // 0x8def68: DecompressPointer r1
    //     0x8def68: add             x1, x1, HEAP, lsl #32
    // 0x8def6c: r16 = Instance_KD
    //     0x8def6c: ldr             x16, [PP, #0x7750]  ; [pp+0x7750] Obj!KD@6a4b11
    // 0x8def70: cmp             w1, w16
    // 0x8def74: b.ne            #0x8defd8
    // 0x8def78: ldur            x0, [fp, #-0x18]
    // 0x8def7c: ldur            x3, [fp, #-0x10]
    // 0x8def80: LoadField: r1 = r0->field_b
    //     0x8def80: ldur            w1, [x0, #0xb]
    // 0x8def84: DecompressPointer r1
    //     0x8def84: add             x1, x1, HEAP, lsl #32
    // 0x8def88: LoadField: r0 = r1->field_13
    //     0x8def88: ldur            w0, [x1, #0x13]
    // 0x8def8c: DecompressPointer r0
    //     0x8def8c: add             x0, x0, HEAP, lsl #32
    // 0x8def90: LoadField: r4 = r0->field_53
    //     0x8def90: ldur            w4, [x0, #0x53]
    // 0x8def94: DecompressPointer r4
    //     0x8def94: add             x4, x4, HEAP, lsl #32
    // 0x8def98: stur            x4, [fp, #-0x18]
    // 0x8def9c: r1 = Function '<anonymous closure>':.
    //     0x8def9c: add             x1, PP, #8, lsl #12  ; [pp+0x84b8] AnonymousClosure: (0x40b788), in [FRi] _VD::_oxh (0x429078)
    //     0x8defa0: ldr             x1, [x1, #0x4b8]
    // 0x8defa4: r0 = AllocateClosure()
    //     0x8defa4: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x8defa8: mov             x1, x0
    // 0x8defac: ldur            x0, [fp, #-0x10]
    // 0x8defb0: StoreField: r1->field_b = r0
    //     0x8defb0: stur            w0, [x1, #0xb]
    // 0x8defb4: mov             x2, x1
    // 0x8defb8: r1 = Null
    //     0x8defb8: mov             x1, NULL
    // 0x8defbc: r0 = call 0x2f44a0
    //     0x8defbc: bl              #0x2f44a0
    // 0x8defc0: ldur            x16, [fp, #-0x18]
    // 0x8defc4: stp             x16, NULL, [SP, #8]
    // 0x8defc8: str             x0, [SP]
    // 0x8defcc: r4 = const [0x1, 0x2, 0x2, 0x2, null]
    //     0x8defcc: ldr             x4, [PP, #0x58]  ; [pp+0x58] List(5) [0x1, 0x2, 0x2, 0x2, Null]
    // 0x8defd0: r0 = call 0x40ab48
    //     0x8defd0: bl              #0x40ab48
    // 0x8defd4: r0 = ReturnAsync()
    //     0x8defd4: b               #0x8c3a30  ; ReturnAsyncStub
    // 0x8defd8: r0 = ReturnAsyncNotFuture()
    //     0x8defd8: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x8defdc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8defdc: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8defe0: b               #0x8deef0
  }
  [closure] (dynamic, dynamic) => dynamic _oxh(dynamic, (dynamic, hE, ND) => void) {
    // ** addr: 0x429078, size: -0x1
  }
  [closure] (dynamic, dynamic) => dynamic _rxh(dynamic, (dynamic, jE<dynamic>, OD) => void) {
    // ** addr: 0x42900c, size: -0x1
  }
  [closure] (dynamic, dynamic, ua) => dynamic _txh(dynamic, (dynamic, HD, PD) => void) {
    // ** addr: 0x428fa0, size: -0x1
  }
  [closure] LD<hE> <anonymous closure>(dynamic) {
    // ** addr: 0x428f54, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, QD) {
    // ** addr: 0x428dd8, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, hE, ND) {
    // ** addr: 0x40b910, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, QD) {
    // ** addr: 0x40b488, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, QD) {
    // ** addr: 0x40a8e8, size: -0x1
  }
  [closure] jE<Y0> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x40a864, size: -0x1
  }
  [closure] jE<Y0> <anonymous closure>(dynamic, dynamic, dynamic) {
    // ** addr: 0x409898, size: -0x1
  }
  [closure] Future<dynamic> <anonymous closure>(dynamic, dynamic, ua) {
    // ** addr: 0x40a9d4, size: -0x1
  }
  [closure] dynamic <anonymous closure>(dynamic) {
    // ** addr: 0x40b13c, size: -0x1
  }
  [closure] Future<LD<dynamic>> <anonymous closure>(dynamic) {
    // ** addr: 0x40b21c, size: -0x1
  }
  [closure] dynamic <anonymous closure>(dynamic) {
    // ** addr: 0x40b578, size: -0x1
  }
  [closure] Future<LD<dynamic>> <anonymous closure>(dynamic) {
    // ** addr: 0x40b614, size: -0x1
  }
  [closure] dynamic <anonymous closure>(dynamic) {
    // ** addr: 0x40b788, size: -0x1
  }
  [closure] Future<LD<dynamic>> <anonymous closure>(dynamic) {
    // ** addr: 0x40b824, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, jE<dynamic>) {
    // ** addr: 0x428d18, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x428c0c, size: -0x1
  }
  [closure] jE<Y0> <anonymous closure>(dynamic) {
    // ** addr: 0x428bf4, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, String) {
    // ** addr: 0x428aa8, size: -0x1
  }
}

// class id: 4264, size: 0x1c, field offset: 0x1c
class WD extends _VD
    implements FD {

  [closure] Future<Never> <anonymous closure>(dynamic, Object) async {
    // ** addr: 0x91d150, size: 0x194
    // 0x91d150: EnterFrame
    //     0x91d150: stp             fp, lr, [SP, #-0x10]!
    //     0x91d154: mov             fp, SP
    // 0x91d158: AllocStack(0x28)
    //     0x91d158: sub             SP, SP, #0x28
    // 0x91d15c: SetupParameters(WD this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x91d15c: stur            NULL, [fp, #-8]
    //     0x91d160: movz            x0, #0
    //     0x91d164: add             x1, fp, w0, sxtw #2
    //     0x91d168: ldr             x1, [x1, #0x18]
    //     0x91d16c: add             x2, fp, w0, sxtw #2
    //     0x91d170: ldr             x2, [x2, #0x10]
    //     0x91d174: stur            x2, [fp, #-0x18]
    //     0x91d178: ldur            w3, [x1, #0x17]
    //     0x91d17c: add             x3, x3, HEAP, lsl #32
    //     0x91d180: stur            x3, [fp, #-0x10]
    // 0x91d184: CheckStackOverflow
    //     0x91d184: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d188: cmp             SP, x16
    //     0x91d18c: b.ls            #0x91d2c8
    // 0x91d190: InitAsync() -> Future<Never>
    //     0x91d190: ldr             x0, [PP, #0x188]  ; [pp+0x188] TypeArguments: <Never>
    //     0x91d194: bl              #0x8c1de0  ; InitAsyncStub
    // 0x91d198: ldur            x0, [fp, #-0x10]
    // 0x91d19c: LoadField: r1 = r0->field_2b
    //     0x91d19c: ldur            w1, [x0, #0x2b]
    // 0x91d1a0: DecompressPointer r1
    //     0x91d1a0: add             x1, x1, HEAP, lsl #32
    // 0x91d1a4: r16 = Sentinel
    //     0x91d1a4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91d1a8: cmp             w1, w16
    // 0x91d1ac: b.ne            #0x91d1c0
    // 0x91d1b0: r16 = "Rqb"
    //     0x91d1b0: add             x16, PP, #9, lsl #12  ; [pp+0x93b0] "Rqb"
    //     0x91d1b4: ldr             x16, [x16, #0x3b0]
    // 0x91d1b8: str             x16, [SP]
    // 0x91d1bc: r0 = _throwLocalNotInitialized()
    //     0x91d1bc: bl              #0x301b2c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x91d1c0: ldur            x2, [fp, #-0x18]
    // 0x91d1c4: ldur            x0, [fp, #-0x10]
    // 0x91d1c8: LoadField: r1 = r0->field_2b
    //     0x91d1c8: ldur            w1, [x0, #0x2b]
    // 0x91d1cc: DecompressPointer r1
    //     0x91d1cc: add             x1, x1, HEAP, lsl #32
    // 0x91d1d0: r0 = call 0x7db904
    //     0x91d1d0: bl              #0x7db904
    // 0x91d1d4: mov             x1, x0
    // 0x91d1d8: stur            x1, [fp, #-0x20]
    // 0x91d1dc: r0 = Await()
    //     0x91d1dc: bl              #0x8c1bb8  ; AwaitStub
    // 0x91d1e0: ldur            x1, [fp, #-0x10]
    // 0x91d1e4: LoadField: r0 = r1->field_37
    //     0x91d1e4: ldur            w0, [x1, #0x37]
    // 0x91d1e8: DecompressPointer r0
    //     0x91d1e8: add             x0, x0, HEAP, lsl #32
    // 0x91d1ec: str             x0, [SP]
    // 0x91d1f0: ClosureCall
    //     0x91d1f0: ldr             x4, [PP, #0x490]  ; [pp+0x490] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x91d1f4: ldur            x2, [x0, #0x1f]
    //     0x91d1f8: blr             x2
    // 0x91d1fc: mov             x1, x0
    // 0x91d200: stur            x1, [fp, #-0x20]
    // 0x91d204: r0 = Await()
    //     0x91d204: bl              #0x8c1bb8  ; AwaitStub
    // 0x91d208: ldur            x0, [fp, #-0x18]
    // 0x91d20c: r1 = 59
    //     0x91d20c: movz            x1, #0x3b
    // 0x91d210: branchIfSmi(r0, 0x91d21c)
    //     0x91d210: tbz             w0, #0, #0x91d21c
    // 0x91d214: r1 = LoadClassIdInstr(r0)
    //     0x91d214: ldur            x1, [x0, #-1]
    //     0x91d218: ubfx            x1, x1, #0xc, #0x14
    // 0x91d21c: r17 = 5322
    //     0x91d21c: movz            x17, #0x14ca
    // 0x91d220: cmp             x1, x17
    // 0x91d224: b.ne            #0x91d2c0
    // 0x91d228: ldur            x0, [fp, #-0x10]
    // 0x91d22c: LoadField: r1 = r0->field_13
    //     0x91d22c: ldur            w1, [x0, #0x13]
    // 0x91d230: DecompressPointer r1
    //     0x91d230: add             x1, x1, HEAP, lsl #32
    // 0x91d234: LoadField: r0 = r1->field_13
    //     0x91d234: ldur            w0, [x1, #0x13]
    // 0x91d238: DecompressPointer r0
    //     0x91d238: add             x0, x0, HEAP, lsl #32
    // 0x91d23c: r16 = Sentinel
    //     0x91d23c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91d240: cmp             w0, w16
    // 0x91d244: b.eq            #0x91d2d0
    // 0x91d248: stur            x0, [fp, #-0x10]
    // 0x91d24c: r1 = Null
    //     0x91d24c: mov             x1, NULL
    // 0x91d250: r2 = 6
    //     0x91d250: movz            x2, #0x6
    // 0x91d254: r0 = AllocateArray()
    //     0x91d254: bl              #0x94fa24  ; AllocateArrayStub
    // 0x91d258: r17 = "Receiving data timeout["
    //     0x91d258: add             x17, PP, #8, lsl #12  ; [pp+0x8980] "Receiving data timeout["
    //     0x91d25c: ldr             x17, [x17, #0x980]
    // 0x91d260: StoreField: r0->field_f = r17
    //     0x91d260: stur            w17, [x0, #0xf]
    // 0x91d264: ldur            x1, [fp, #-0x10]
    // 0x91d268: LoadField: r2 = r1->field_13
    //     0x91d268: ldur            w2, [x1, #0x13]
    // 0x91d26c: DecompressPointer r2
    //     0x91d26c: add             x2, x2, HEAP, lsl #32
    // 0x91d270: r16 = Sentinel
    //     0x91d270: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91d274: cmp             w2, w16
    // 0x91d278: b.eq            #0x91d2d8
    // 0x91d27c: StoreField: r0->field_13 = r2
    //     0x91d27c: stur            w2, [x0, #0x13]
    // 0x91d280: r17 = "ms]"
    //     0x91d280: add             x17, PP, #8, lsl #12  ; [pp+0x8960] "ms]"
    //     0x91d284: ldr             x17, [x17, #0x960]
    // 0x91d288: ArrayStore: r0[0] = r17  ; List_4
    //     0x91d288: stur            w17, [x0, #0x17]
    // 0x91d28c: str             x0, [SP]
    // 0x91d290: r0 = _interpolate()
    //     0x91d290: bl              #0x2dd54c  ; [dart:core] _StringBase::_interpolate
    // 0x91d294: stur            x0, [fp, #-0x10]
    // 0x91d298: r0 = HD()
    //     0x91d298: bl              #0x8de65c  ; AllocateHDStub -> HD (size=0x18)
    // 0x91d29c: mov             x1, x0
    // 0x91d2a0: r0 = Instance_GD
    //     0x91d2a0: add             x0, PP, #8, lsl #12  ; [pp+0x8988] Obj!GD@6a4b71
    //     0x91d2a4: ldr             x0, [x0, #0x988]
    // 0x91d2a8: StoreField: r1->field_b = r0
    //     0x91d2a8: stur            w0, [x1, #0xb]
    // 0x91d2ac: ldur            x0, [fp, #-0x10]
    // 0x91d2b0: StoreField: r1->field_f = r0
    //     0x91d2b0: stur            w0, [x1, #0xf]
    // 0x91d2b4: mov             x0, x1
    // 0x91d2b8: r0 = Throw()
    //     0x91d2b8: bl              #0x94dd08  ; ThrowStub
    // 0x91d2bc: brk             #0
    // 0x91d2c0: r0 = Throw()
    //     0x91d2c0: bl              #0x94dd08  ; ThrowStub
    // 0x91d2c4: brk             #0
    // 0x91d2c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d2c8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d2cc: b               #0x91d190
    // 0x91d2d0: r9 = lIf
    //     0x91d2d0: ldr             x9, [PP, #0x77d8]  ; [pp+0x77d8] Field <jE.lIf>: late (offset: 0x14)
    // 0x91d2d4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91d2d4: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x91d2d8: r9 = jJf
    //     0x91d2d8: add             x9, PP, #8, lsl #12  ; [pp+0x89c0] Field <<EMAIL>>: late (offset: 0x14)
    //     0x91d2dc: ldr             x9, [x9, #0x9c0]
    // 0x91d2e0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91d2e0: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Future<Null> <anonymous closure>(dynamic, dynamic) async {
    // ** addr: 0x91d2e4, size: 0x150
    // 0x91d2e4: EnterFrame
    //     0x91d2e4: stp             fp, lr, [SP, #-0x10]!
    //     0x91d2e8: mov             fp, SP
    // 0x91d2ec: AllocStack(0x70)
    //     0x91d2ec: sub             SP, SP, #0x70
    // 0x91d2f0: SetupParameters(WD this /* r1, fp-0x68 */, dynamic _ /* r2, fp-0x60 */)
    //     0x91d2f0: stur            NULL, [fp, #-8]
    //     0x91d2f4: movz            x0, #0
    //     0x91d2f8: add             x1, fp, w0, sxtw #2
    //     0x91d2fc: ldr             x1, [x1, #0x18]
    //     0x91d300: stur            x1, [fp, #-0x68]
    //     0x91d304: add             x2, fp, w0, sxtw #2
    //     0x91d308: ldr             x2, [x2, #0x10]
    //     0x91d30c: stur            x2, [fp, #-0x60]
    //     0x91d310: ldur            w3, [x1, #0x17]
    //     0x91d314: add             x3, x3, HEAP, lsl #32
    //     0x91d318: stur            x3, [fp, #-0x58]
    // 0x91d31c: CheckStackOverflow
    //     0x91d31c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d320: cmp             SP, x16
    //     0x91d324: b.ls            #0x91d41c
    // 0x91d328: InitAsync() -> Future<Null?>
    //     0x91d328: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x91d32c: bl              #0x8c1de0  ; InitAsyncStub
    // 0x91d330: ldur            x1, [fp, #-0x58]
    // 0x91d334: LoadField: r2 = r1->field_37
    //     0x91d334: ldur            w2, [x1, #0x37]
    // 0x91d338: DecompressPointer r2
    //     0x91d338: add             x2, x2, HEAP, lsl #32
    // 0x91d33c: stur            x2, [fp, #-0x68]
    // 0x91d340: str             x2, [SP]
    // 0x91d344: mov             x0, x2
    // 0x91d348: ClosureCall
    //     0x91d348: ldr             x4, [PP, #0x490]  ; [pp+0x490] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x91d34c: ldur            x2, [x0, #0x1f]
    //     0x91d350: blr             x2
    // 0x91d354: mov             x1, x0
    // 0x91d358: stur            x1, [fp, #-0x68]
    // 0x91d35c: r0 = Await()
    //     0x91d35c: bl              #0x8c1bb8  ; AwaitStub
    // 0x91d360: ldur            x0, [fp, #-0x58]
    // 0x91d364: LoadField: r2 = r0->field_1f
    //     0x91d364: ldur            w2, [x0, #0x1f]
    // 0x91d368: DecompressPointer r2
    //     0x91d368: add             x2, x2, HEAP, lsl #32
    // 0x91d36c: stur            x2, [fp, #-0x68]
    // 0x91d370: LoadField: r1 = r0->field_13
    //     0x91d370: ldur            w1, [x0, #0x13]
    // 0x91d374: DecompressPointer r1
    //     0x91d374: add             x1, x1, HEAP, lsl #32
    // 0x91d378: LoadField: r0 = r1->field_13
    //     0x91d378: ldur            w0, [x1, #0x13]
    // 0x91d37c: DecompressPointer r0
    //     0x91d37c: add             x0, x0, HEAP, lsl #32
    // 0x91d380: r16 = Sentinel
    //     0x91d380: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91d384: cmp             w0, w16
    // 0x91d388: b.eq            #0x91d424
    // 0x91d38c: ldur            x1, [fp, #-0x60]
    // 0x91d390: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91d390: ldr             x4, [PP, #0x5a8]  ; [pp+0x5a8] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91d394: r0 = call 0x40996c
    //     0x91d394: bl              #0x40996c
    // 0x91d398: ldur            x1, [fp, #-0x68]
    // 0x91d39c: mov             x2, x0
    // 0x91d3a0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x91d3a0: ldr             x4, [PP, #0x2a8]  ; [pp+0x2a8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x91d3a4: r0 = call 0x2f52a0
    //     0x91d3a4: bl              #0x2f52a0
    // 0x91d3a8: r0 = Null
    //     0x91d3a8: mov             x0, NULL
    // 0x91d3ac: r0 = ReturnAsyncNotFuture()
    //     0x91d3ac: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x91d3b0: sub             SP, fp, #0x70
    // 0x91d3b4: mov             x2, x0
    // 0x91d3b8: stur            x0, [fp, #-0x60]
    // 0x91d3bc: mov             x0, x1
    // 0x91d3c0: stur            x1, [fp, #-0x68]
    // 0x91d3c4: ldur            x1, [fp, #-0x28]
    // 0x91d3c8: LoadField: r3 = r1->field_1f
    //     0x91d3c8: ldur            w3, [x1, #0x1f]
    // 0x91d3cc: DecompressPointer r3
    //     0x91d3cc: add             x3, x3, HEAP, lsl #32
    // 0x91d3d0: stur            x3, [fp, #-0x58]
    // 0x91d3d4: LoadField: r4 = r1->field_13
    //     0x91d3d4: ldur            w4, [x1, #0x13]
    // 0x91d3d8: DecompressPointer r4
    //     0x91d3d8: add             x4, x4, HEAP, lsl #32
    // 0x91d3dc: LoadField: r1 = r4->field_13
    //     0x91d3dc: ldur            w1, [x4, #0x13]
    // 0x91d3e0: DecompressPointer r1
    //     0x91d3e0: add             x1, x1, HEAP, lsl #32
    // 0x91d3e4: r16 = Sentinel
    //     0x91d3e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91d3e8: cmp             w1, w16
    // 0x91d3ec: b.eq            #0x91d42c
    // 0x91d3f0: ldur            x1, [fp, #-0x18]
    // 0x91d3f4: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91d3f4: ldr             x4, [PP, #0x5a8]  ; [pp+0x5a8] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91d3f8: r0 = call 0x40996c
    //     0x91d3f8: bl              #0x40996c
    // 0x91d3fc: ldur            x1, [fp, #-0x58]
    // 0x91d400: mov             x2, x0
    // 0x91d404: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x91d404: ldr             x4, [PP, #0x2a8]  ; [pp+0x2a8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x91d408: r0 = call 0x2f52a0
    //     0x91d408: bl              #0x2f52a0
    // 0x91d40c: ldur            x0, [fp, #-0x60]
    // 0x91d410: ldur            x1, [fp, #-0x68]
    // 0x91d414: r0 = ReThrow()
    //     0x91d414: bl              #0x94dce4  ; ReThrowStub
    // 0x91d418: brk             #0
    // 0x91d41c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d41c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d420: b               #0x91d328
    // 0x91d424: r9 = lIf
    //     0x91d424: ldr             x9, [PP, #0x77d8]  ; [pp+0x77d8] Field <jE.lIf>: late (offset: 0x14)
    // 0x91d428: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91d428: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x91d42c: r9 = lIf
    //     0x91d42c: ldr             x9, [PP, #0x77d8]  ; [pp+0x77d8] Field <jE.lIf>: late (offset: 0x14)
    // 0x91d430: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91d430: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic) async {
    // ** addr: 0x91d434, size: 0x104
    // 0x91d434: EnterFrame
    //     0x91d434: stp             fp, lr, [SP, #-0x10]!
    //     0x91d438: mov             fp, SP
    // 0x91d43c: AllocStack(0x60)
    //     0x91d43c: sub             SP, SP, #0x60
    // 0x91d440: SetupParameters(WD this /* r1, fp-0x58 */)
    //     0x91d440: stur            NULL, [fp, #-8]
    //     0x91d444: movz            x0, #0
    //     0x91d448: add             x1, fp, w0, sxtw #2
    //     0x91d44c: ldr             x1, [x1, #0x10]
    //     0x91d450: stur            x1, [fp, #-0x58]
    //     0x91d454: ldur            w2, [x1, #0x17]
    //     0x91d458: add             x2, x2, HEAP, lsl #32
    //     0x91d45c: stur            x2, [fp, #-0x50]
    // 0x91d460: CheckStackOverflow
    //     0x91d460: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d464: cmp             SP, x16
    //     0x91d468: b.ls            #0x91d528
    // 0x91d46c: InitAsync() -> Future<void?>
    //     0x91d46c: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x91d470: bl              #0x8c1de0  ; InitAsyncStub
    // 0x91d474: ldur            x1, [fp, #-0x50]
    // 0x91d478: LoadField: r2 = r1->field_2f
    //     0x91d478: ldur            w2, [x1, #0x2f]
    // 0x91d47c: DecompressPointer r2
    //     0x91d47c: add             x2, x2, HEAP, lsl #32
    // 0x91d480: mov             x0, x2
    // 0x91d484: stur            x2, [fp, #-0x58]
    // 0x91d488: r0 = Await()
    //     0x91d488: bl              #0x8c1bb8  ; AwaitStub
    // 0x91d48c: ldur            x0, [fp, #-0x50]
    // 0x91d490: r1 = true
    //     0x91d490: add             x1, NULL, #0x20  ; true
    // 0x91d494: StoreField: r0->field_33 = r1
    //     0x91d494: stur            w1, [x0, #0x33]
    // 0x91d498: LoadField: r1 = r0->field_1b
    //     0x91d498: ldur            w1, [x0, #0x1b]
    // 0x91d49c: DecompressPointer r1
    //     0x91d49c: add             x1, x1, HEAP, lsl #32
    // 0x91d4a0: r0 = call 0x446f88
    //     0x91d4a0: bl              #0x446f88
    // 0x91d4a4: mov             x1, x0
    // 0x91d4a8: stur            x1, [fp, #-0x58]
    // 0x91d4ac: r0 = Await()
    //     0x91d4ac: bl              #0x8c1bb8  ; AwaitStub
    // 0x91d4b0: ldur            x0, [fp, #-0x50]
    // 0x91d4b4: LoadField: r1 = r0->field_1f
    //     0x91d4b4: ldur            w1, [x0, #0x1f]
    // 0x91d4b8: DecompressPointer r1
    //     0x91d4b8: add             x1, x1, HEAP, lsl #32
    // 0x91d4bc: LoadField: r2 = r0->field_13
    //     0x91d4bc: ldur            w2, [x0, #0x13]
    // 0x91d4c0: DecompressPointer r2
    //     0x91d4c0: add             x2, x2, HEAP, lsl #32
    // 0x91d4c4: str             x2, [SP]
    // 0x91d4c8: r4 = const [0, 0x2, 0x1, 0x2, null]
    //     0x91d4c8: ldr             x4, [PP, #0x5b8]  ; [pp+0x5b8] List(5) [0, 0x2, 0x1, 0x2, Null]
    // 0x91d4cc: r0 = call 0x7e4510
    //     0x91d4cc: bl              #0x7e4510
    // 0x91d4d0: b               #0x91d520
    // 0x91d4d4: sub             SP, fp, #0x60
    // 0x91d4d8: mov             x1, x0
    // 0x91d4dc: ldur            x0, [fp, #-0x20]
    // 0x91d4e0: LoadField: r2 = r0->field_1f
    //     0x91d4e0: ldur            w2, [x0, #0x1f]
    // 0x91d4e4: DecompressPointer r2
    //     0x91d4e4: add             x2, x2, HEAP, lsl #32
    // 0x91d4e8: stur            x2, [fp, #-0x50]
    // 0x91d4ec: LoadField: r3 = r0->field_13
    //     0x91d4ec: ldur            w3, [x0, #0x13]
    // 0x91d4f0: DecompressPointer r3
    //     0x91d4f0: add             x3, x3, HEAP, lsl #32
    // 0x91d4f4: LoadField: r0 = r3->field_13
    //     0x91d4f4: ldur            w0, [x3, #0x13]
    // 0x91d4f8: DecompressPointer r0
    //     0x91d4f8: add             x0, x0, HEAP, lsl #32
    // 0x91d4fc: r16 = Sentinel
    //     0x91d4fc: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91d500: cmp             w0, w16
    // 0x91d504: b.eq            #0x91d530
    // 0x91d508: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91d508: ldr             x4, [PP, #0x5a8]  ; [pp+0x5a8] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91d50c: r0 = call 0x40996c
    //     0x91d50c: bl              #0x40996c
    // 0x91d510: ldur            x1, [fp, #-0x50]
    // 0x91d514: mov             x2, x0
    // 0x91d518: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x91d518: ldr             x4, [PP, #0x2a8]  ; [pp+0x2a8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x91d51c: r0 = call 0x2f52a0
    //     0x91d51c: bl              #0x2f52a0
    // 0x91d520: r0 = Null
    //     0x91d520: mov             x0, NULL
    // 0x91d524: r0 = ReturnAsyncNotFuture()
    //     0x91d524: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x91d528: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d528: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d52c: b               #0x91d46c
    // 0x91d530: r9 = lIf
    //     0x91d530: ldr             x9, [PP, #0x77d8]  ; [pp+0x77d8] Field <jE.lIf>: late (offset: 0x14)
    // 0x91d534: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91d534: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Future<Null> <anonymous closure>(dynamic, dynamic, ua) async {
    // ** addr: 0x91d538, size: 0x184
    // 0x91d538: EnterFrame
    //     0x91d538: stp             fp, lr, [SP, #-0x10]!
    //     0x91d53c: mov             fp, SP
    // 0x91d540: AllocStack(0x80)
    //     0x91d540: sub             SP, SP, #0x80
    // 0x91d544: SetupParameters(WD this /* r1, fp-0x78 */, dynamic _ /* r2, fp-0x70 */, dynamic _ /* r3, fp-0x68 */)
    //     0x91d544: stur            NULL, [fp, #-8]
    //     0x91d548: movz            x0, #0
    //     0x91d54c: add             x1, fp, w0, sxtw #2
    //     0x91d550: ldr             x1, [x1, #0x20]
    //     0x91d554: stur            x1, [fp, #-0x78]
    //     0x91d558: add             x2, fp, w0, sxtw #2
    //     0x91d55c: ldr             x2, [x2, #0x18]
    //     0x91d560: stur            x2, [fp, #-0x70]
    //     0x91d564: add             x3, fp, w0, sxtw #2
    //     0x91d568: ldr             x3, [x3, #0x10]
    //     0x91d56c: stur            x3, [fp, #-0x68]
    //     0x91d570: ldur            w4, [x1, #0x17]
    //     0x91d574: add             x4, x4, HEAP, lsl #32
    //     0x91d578: stur            x4, [fp, #-0x60]
    // 0x91d57c: CheckStackOverflow
    //     0x91d57c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d580: cmp             SP, x16
    //     0x91d584: b.ls            #0x91d6a4
    // 0x91d588: InitAsync() -> Future<Null?>
    //     0x91d588: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x91d58c: bl              #0x8c1de0  ; InitAsyncStub
    // 0x91d590: ldur            x0, [fp, #-0x60]
    // 0x91d594: LoadField: r1 = r0->field_b
    //     0x91d594: ldur            w1, [x0, #0xb]
    // 0x91d598: DecompressPointer r1
    //     0x91d598: add             x1, x1, HEAP, lsl #32
    // 0x91d59c: stur            x1, [fp, #-0x68]
    // 0x91d5a0: LoadField: r2 = r1->field_2b
    //     0x91d5a0: ldur            w2, [x1, #0x2b]
    // 0x91d5a4: DecompressPointer r2
    //     0x91d5a4: add             x2, x2, HEAP, lsl #32
    // 0x91d5a8: r16 = Sentinel
    //     0x91d5a8: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91d5ac: cmp             w2, w16
    // 0x91d5b0: b.ne            #0x91d5c4
    // 0x91d5b4: r16 = "Rqb"
    //     0x91d5b4: add             x16, PP, #9, lsl #12  ; [pp+0x93b0] "Rqb"
    //     0x91d5b8: ldr             x16, [x16, #0x3b0]
    // 0x91d5bc: str             x16, [SP]
    // 0x91d5c0: r0 = _throwLocalNotInitialized()
    //     0x91d5c0: bl              #0x301b2c  ; [dart:_internal] LateError::_throwLocalNotInitialized
    // 0x91d5c4: ldur            x0, [fp, #-0x68]
    // 0x91d5c8: LoadField: r1 = r0->field_2b
    //     0x91d5c8: ldur            w1, [x0, #0x2b]
    // 0x91d5cc: DecompressPointer r1
    //     0x91d5cc: add             x1, x1, HEAP, lsl #32
    // 0x91d5d0: r0 = call 0x7db904
    //     0x91d5d0: bl              #0x7db904
    // 0x91d5d4: mov             x1, x0
    // 0x91d5d8: stur            x1, [fp, #-0x78]
    // 0x91d5dc: r0 = Await()
    //     0x91d5dc: bl              #0x8c1bb8  ; AwaitStub
    // 0x91d5e0: ldur            x0, [fp, #-0x68]
    // 0x91d5e4: LoadField: r2 = r0->field_1f
    //     0x91d5e4: ldur            w2, [x0, #0x1f]
    // 0x91d5e8: DecompressPointer r2
    //     0x91d5e8: add             x2, x2, HEAP, lsl #32
    // 0x91d5ec: stur            x2, [fp, #-0x60]
    // 0x91d5f0: LoadField: r1 = r0->field_13
    //     0x91d5f0: ldur            w1, [x0, #0x13]
    // 0x91d5f4: DecompressPointer r1
    //     0x91d5f4: add             x1, x1, HEAP, lsl #32
    // 0x91d5f8: LoadField: r0 = r1->field_13
    //     0x91d5f8: ldur            w0, [x1, #0x13]
    // 0x91d5fc: DecompressPointer r0
    //     0x91d5fc: add             x0, x0, HEAP, lsl #32
    // 0x91d600: r16 = Sentinel
    //     0x91d600: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91d604: cmp             w0, w16
    // 0x91d608: b.eq            #0x91d6ac
    // 0x91d60c: ldur            x1, [fp, #-0x70]
    // 0x91d610: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91d610: ldr             x4, [PP, #0x5a8]  ; [pp+0x5a8] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91d614: r0 = call 0x40996c
    //     0x91d614: bl              #0x40996c
    // 0x91d618: ldur            x1, [fp, #-0x60]
    // 0x91d61c: mov             x2, x0
    // 0x91d620: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x91d620: ldr             x4, [PP, #0x2a8]  ; [pp+0x2a8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x91d624: r0 = call 0x2f52a0
    //     0x91d624: bl              #0x2f52a0
    // 0x91d628: r0 = Null
    //     0x91d628: mov             x0, NULL
    // 0x91d62c: r0 = ReturnAsyncNotFuture()
    //     0x91d62c: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x91d630: sub             SP, fp, #0x80
    // 0x91d634: mov             x2, x0
    // 0x91d638: stur            x0, [fp, #-0x68]
    // 0x91d63c: mov             x0, x1
    // 0x91d640: stur            x1, [fp, #-0x70]
    // 0x91d644: ldur            x1, [fp, #-0x30]
    // 0x91d648: LoadField: r3 = r1->field_b
    //     0x91d648: ldur            w3, [x1, #0xb]
    // 0x91d64c: DecompressPointer r3
    //     0x91d64c: add             x3, x3, HEAP, lsl #32
    // 0x91d650: LoadField: r4 = r3->field_1f
    //     0x91d650: ldur            w4, [x3, #0x1f]
    // 0x91d654: DecompressPointer r4
    //     0x91d654: add             x4, x4, HEAP, lsl #32
    // 0x91d658: stur            x4, [fp, #-0x60]
    // 0x91d65c: LoadField: r1 = r3->field_13
    //     0x91d65c: ldur            w1, [x3, #0x13]
    // 0x91d660: DecompressPointer r1
    //     0x91d660: add             x1, x1, HEAP, lsl #32
    // 0x91d664: LoadField: r3 = r1->field_13
    //     0x91d664: ldur            w3, [x1, #0x13]
    // 0x91d668: DecompressPointer r3
    //     0x91d668: add             x3, x3, HEAP, lsl #32
    // 0x91d66c: r16 = Sentinel
    //     0x91d66c: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x91d670: cmp             w3, w16
    // 0x91d674: b.eq            #0x91d6b4
    // 0x91d678: ldur            x1, [fp, #-0x18]
    // 0x91d67c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x91d67c: ldr             x4, [PP, #0x5a8]  ; [pp+0x5a8] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x91d680: r0 = call 0x40996c
    //     0x91d680: bl              #0x40996c
    // 0x91d684: ldur            x1, [fp, #-0x60]
    // 0x91d688: mov             x2, x0
    // 0x91d68c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x91d68c: ldr             x4, [PP, #0x2a8]  ; [pp+0x2a8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x91d690: r0 = call 0x2f52a0
    //     0x91d690: bl              #0x2f52a0
    // 0x91d694: ldur            x0, [fp, #-0x68]
    // 0x91d698: ldur            x1, [fp, #-0x70]
    // 0x91d69c: r0 = ReThrow()
    //     0x91d69c: bl              #0x94dce4  ; ReThrowStub
    // 0x91d6a0: brk             #0
    // 0x91d6a4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d6a4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d6a8: b               #0x91d588
    // 0x91d6ac: r9 = lIf
    //     0x91d6ac: ldr             x9, [PP, #0x77d8]  ; [pp+0x77d8] Field <jE.lIf>: late (offset: 0x14)
    // 0x91d6b0: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91d6b0: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x91d6b4: r9 = lIf
    //     0x91d6b4: ldr             x9, [PP, #0x77d8]  ; [pp+0x77d8] Field <jE.lIf>: late (offset: 0x14)
    // 0x91d6b8: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x91d6b8: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  [closure] Future<dynamic> _Ghi(dynamic) async {
    // ** addr: 0x91d6bc, size: 0xc4
    // 0x91d6bc: EnterFrame
    //     0x91d6bc: stp             fp, lr, [SP, #-0x10]!
    //     0x91d6c0: mov             fp, SP
    // 0x91d6c4: AllocStack(0x18)
    //     0x91d6c4: sub             SP, SP, #0x18
    // 0x91d6c8: SetupParameters(WD this /* r1 */)
    //     0x91d6c8: stur            NULL, [fp, #-8]
    //     0x91d6cc: movz            x0, #0
    //     0x91d6d0: add             x1, fp, w0, sxtw #2
    //     0x91d6d4: ldr             x1, [x1, #0x10]
    //     0x91d6d8: ldur            w2, [x1, #0x17]
    //     0x91d6dc: add             x2, x2, HEAP, lsl #32
    //     0x91d6e0: stur            x2, [fp, #-0x10]
    // 0x91d6e4: CheckStackOverflow
    //     0x91d6e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91d6e8: cmp             SP, x16
    //     0x91d6ec: b.ls            #0x91d778
    // 0x91d6f0: InitAsync() -> Future
    //     0x91d6f0: mov             x0, NULL
    //     0x91d6f4: bl              #0x8c1de0  ; InitAsyncStub
    // 0x91d6f8: ldur            x1, [fp, #-0x10]
    // 0x91d6fc: LoadField: r0 = r1->field_33
    //     0x91d6fc: ldur            w0, [x1, #0x33]
    // 0x91d700: DecompressPointer r0
    //     0x91d700: add             x0, x0, HEAP, lsl #32
    // 0x91d704: tbz             w0, #4, #0x91d770
    // 0x91d708: r0 = true
    //     0x91d708: add             x0, NULL, #0x20  ; true
    // 0x91d70c: StoreField: r1->field_33 = r0
    //     0x91d70c: stur            w0, [x1, #0x33]
    // 0x91d710: LoadField: r2 = r1->field_2f
    //     0x91d710: ldur            w2, [x1, #0x2f]
    // 0x91d714: DecompressPointer r2
    //     0x91d714: add             x2, x2, HEAP, lsl #32
    // 0x91d718: mov             x0, x2
    // 0x91d71c: stur            x2, [fp, #-0x18]
    // 0x91d720: r0 = Await()
    //     0x91d720: bl              #0x8c1bb8  ; AwaitStub
    // 0x91d724: ldur            x0, [fp, #-0x10]
    // 0x91d728: LoadField: r1 = r0->field_1b
    //     0x91d728: ldur            w1, [x0, #0x1b]
    // 0x91d72c: DecompressPointer r1
    //     0x91d72c: add             x1, x1, HEAP, lsl #32
    // 0x91d730: r0 = call 0x446f88
    //     0x91d730: bl              #0x446f88
    // 0x91d734: mov             x1, x0
    // 0x91d738: stur            x1, [fp, #-0x18]
    // 0x91d73c: r0 = Await()
    //     0x91d73c: bl              #0x8c1bb8  ; AwaitStub
    // 0x91d740: ldur            x0, [fp, #-0x10]
    // 0x91d744: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x91d744: ldur            w1, [x0, #0x17]
    // 0x91d748: DecompressPointer r1
    //     0x91d748: add             x1, x1, HEAP, lsl #32
    // 0x91d74c: r0 = call 0x561d38
    //     0x91d74c: bl              #0x561d38
    // 0x91d750: tbnz            w0, #4, #0x91d770
    // 0x91d754: ldur            x0, [fp, #-0x10]
    // 0x91d758: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x91d758: ldur            w1, [x0, #0x17]
    // 0x91d75c: DecompressPointer r1
    //     0x91d75c: add             x1, x1, HEAP, lsl #32
    // 0x91d760: r0 = call 0x634e88
    //     0x91d760: bl              #0x634e88
    // 0x91d764: mov             x1, x0
    // 0x91d768: stur            x1, [fp, #-0x18]
    // 0x91d76c: r0 = Await()
    //     0x91d76c: bl              #0x8c1bb8  ; AwaitStub
    // 0x91d770: r0 = Null
    //     0x91d770: mov             x0, NULL
    // 0x91d774: r0 = ReturnAsyncNotFuture()
    //     0x91d774: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x91d778: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91d778: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91d77c: b               #0x91d6f0
  }
  [closure] void <anonymous closure>(dynamic, Uint8List) {
    // ** addr: 0x634c34, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Zp) {
    // ** addr: 0x634d60, size: -0x1
  }
}
