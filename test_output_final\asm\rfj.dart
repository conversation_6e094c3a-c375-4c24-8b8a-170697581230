// lib: , url: Rfj

// class id: 1049493, size: 0x8
class :: {
}

// class id: 958, size: 0x28, field offset: 0x8
class vOa extends Object {

  int dyn:get:length(vOa) {
    // ** addr: 0x8f1314, size: 0x50
    // 0x8f1314: EnterFrame
    //     0x8f1314: stp             fp, lr, [SP, #-0x10]!
    //     0x8f1318: mov             fp, SP
    // 0x8f131c: ldr             x2, [fp, #0x10]
    // 0x8f1320: LoadField: r3 = r2->field_13
    //     0x8f1320: ldur            x3, [x2, #0x13]
    // 0x8f1324: LoadField: r4 = r2->field_1b
    //     0x8f1324: ldur            x4, [x2, #0x1b]
    // 0x8f1328: sub             x2, x3, x4
    // 0x8f132c: r0 = BoxInt64Instr(r2)
    //     0x8f132c: sbfiz           x0, x2, #1, #0x1f
    //     0x8f1330: cmp             x2, x0, asr #1
    //     0x8f1334: b.eq            #0x8f1340
    //     0x8f1338: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f133c: stur            x2, [x0, #7]
    // 0x8f1340: LeaveFrame
    //     0x8f1340: mov             SP, fp
    //     0x8f1344: ldp             fp, lr, [SP], #0x10
    // 0x8f1348: ret
    //     0x8f1348: ret             
  }
  void []=(vOa, int, int) {
    // ** addr: 0x8f1364, size: 0xbc
    // 0x8f1364: EnterFrame
    //     0x8f1364: stp             fp, lr, [SP, #-0x10]!
    //     0x8f1368: mov             fp, SP
    // 0x8f136c: CheckStackOverflow
    //     0x8f136c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f1370: cmp             SP, x16
    //     0x8f1374: b.ls            #0x8f1400
    // 0x8f1378: ldr             x0, [fp, #0x18]
    // 0x8f137c: r2 = Null
    //     0x8f137c: mov             x2, NULL
    // 0x8f1380: r1 = Null
    //     0x8f1380: mov             x1, NULL
    // 0x8f1384: branchIfSmi(r0, 0x8f13ac)
    //     0x8f1384: tbz             w0, #0, #0x8f13ac
    // 0x8f1388: r4 = LoadClassIdInstr(r0)
    //     0x8f1388: ldur            x4, [x0, #-1]
    //     0x8f138c: ubfx            x4, x4, #0xc, #0x14
    // 0x8f1390: sub             x4, x4, #0x3b
    // 0x8f1394: cmp             x4, #1
    // 0x8f1398: b.ls            #0x8f13ac
    // 0x8f139c: r8 = int
    //     0x8f139c: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8f13a0: r3 = Null
    //     0x8f13a0: add             x3, PP, #0x23, lsl #12  ; [pp+0x23960] Null
    //     0x8f13a4: ldr             x3, [x3, #0x960]
    // 0x8f13a8: r0 = int()
    //     0x8f13a8: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8f13ac: ldr             x0, [fp, #0x10]
    // 0x8f13b0: r2 = Null
    //     0x8f13b0: mov             x2, NULL
    // 0x8f13b4: r1 = Null
    //     0x8f13b4: mov             x1, NULL
    // 0x8f13b8: branchIfSmi(r0, 0x8f13e0)
    //     0x8f13b8: tbz             w0, #0, #0x8f13e0
    // 0x8f13bc: r4 = LoadClassIdInstr(r0)
    //     0x8f13bc: ldur            x4, [x0, #-1]
    //     0x8f13c0: ubfx            x4, x4, #0xc, #0x14
    // 0x8f13c4: sub             x4, x4, #0x3b
    // 0x8f13c8: cmp             x4, #1
    // 0x8f13cc: b.ls            #0x8f13e0
    // 0x8f13d0: r8 = int
    //     0x8f13d0: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8f13d4: r3 = Null
    //     0x8f13d4: add             x3, PP, #0x23, lsl #12  ; [pp+0x23970] Null
    //     0x8f13d8: ldr             x3, [x3, #0x970]
    // 0x8f13dc: r0 = int()
    //     0x8f13dc: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8f13e0: ldr             x1, [fp, #0x20]
    // 0x8f13e4: ldr             x2, [fp, #0x18]
    // 0x8f13e8: ldr             x3, [fp, #0x10]
    // 0x8f13ec: r0 = call 0x4afd38
    //     0x8f13ec: bl              #0x4afd38
    // 0x8f13f0: r0 = Null
    //     0x8f13f0: mov             x0, NULL
    // 0x8f13f4: LeaveFrame
    //     0x8f13f4: mov             SP, fp
    //     0x8f13f8: ldp             fp, lr, [SP], #0x10
    // 0x8f13fc: ret
    //     0x8f13fc: ret             
    // 0x8f1400: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f1400: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f1404: b               #0x8f1378
  }
  int [](vOa, int) {
    // ** addr: 0x8f1420, size: 0x98
    // 0x8f1420: EnterFrame
    //     0x8f1420: stp             fp, lr, [SP, #-0x10]!
    //     0x8f1424: mov             fp, SP
    // 0x8f1428: CheckStackOverflow
    //     0x8f1428: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f142c: cmp             SP, x16
    //     0x8f1430: b.ls            #0x8f1498
    // 0x8f1434: ldr             x0, [fp, #0x10]
    // 0x8f1438: r2 = Null
    //     0x8f1438: mov             x2, NULL
    // 0x8f143c: r1 = Null
    //     0x8f143c: mov             x1, NULL
    // 0x8f1440: branchIfSmi(r0, 0x8f1468)
    //     0x8f1440: tbz             w0, #0, #0x8f1468
    // 0x8f1444: r4 = LoadClassIdInstr(r0)
    //     0x8f1444: ldur            x4, [x0, #-1]
    //     0x8f1448: ubfx            x4, x4, #0xc, #0x14
    // 0x8f144c: sub             x4, x4, #0x3b
    // 0x8f1450: cmp             x4, #1
    // 0x8f1454: b.ls            #0x8f1468
    // 0x8f1458: r8 = int
    //     0x8f1458: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8f145c: r3 = Null
    //     0x8f145c: add             x3, PP, #0x23, lsl #12  ; [pp+0x23980] Null
    //     0x8f1460: ldr             x3, [x3, #0x980]
    // 0x8f1464: r0 = int()
    //     0x8f1464: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8f1468: ldr             x1, [fp, #0x18]
    // 0x8f146c: ldr             x2, [fp, #0x10]
    // 0x8f1470: r0 = call 0x4afdd0
    //     0x8f1470: bl              #0x4afdd0
    // 0x8f1474: mov             x2, x0
    // 0x8f1478: r0 = BoxInt64Instr(r2)
    //     0x8f1478: sbfiz           x0, x2, #1, #0x1f
    //     0x8f147c: cmp             x2, x0, asr #1
    //     0x8f1480: b.eq            #0x8f148c
    //     0x8f1484: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8f1488: stur            x2, [x0, #7]
    // 0x8f148c: LeaveFrame
    //     0x8f148c: mov             SP, fp
    //     0x8f1490: ldp             fp, lr, [SP], #0x10
    // 0x8f1494: ret
    //     0x8f1494: ret             
    // 0x8f1498: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f1498: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f149c: b               #0x8f1434
  }
}
