// lib: , url: GRi

// class id: 1048784, size: 0x8
class :: {
}

// class id: 4262, size: 0x1c, field offset: 0x8
class XD extends Object {

  late String _TIf; // offset: 0x8

  int dyn:get:length(XD) {
    // ** addr: 0x8e7898, size: 0x60
    // 0x8e7898: EnterFrame
    //     0x8e7898: stp             fp, lr, [SP, #-0x10]!
    //     0x8e789c: mov             fp, SP
    // 0x8e78a0: CheckStackOverflow
    //     0x8e78a0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e78a4: cmp             SP, x16
    //     0x8e78a8: b.ls            #0x8e78d8
    // 0x8e78ac: ldr             x1, [fp, #0x10]
    // 0x8e78b0: r0 = call 0x427104
    //     0x8e78b0: bl              #0x427104
    // 0x8e78b4: mov             x2, x0
    // 0x8e78b8: r0 = BoxInt64Instr(r2)
    //     0x8e78b8: sbfiz           x0, x2, #1, #0x1f
    //     0x8e78bc: cmp             x2, x0, asr #1
    //     0x8e78c0: b.eq            #0x8e78cc
    //     0x8e78c4: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8e78c8: stur            x2, [x0, #7]
    // 0x8e78cc: LeaveFrame
    //     0x8e78cc: mov             SP, fp
    //     0x8e78d0: ldp             fp, lr, [SP], #0x10
    // 0x8e78d4: ret
    //     0x8e78d4: ret             
    // 0x8e78d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e78d8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e78dc: b               #0x8e78ac
  }
  [closure] void <anonymous closure>(dynamic, Ra<String, String>) {
    // ** addr: 0x4277f8, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, String, List<String>) {
    // ** addr: 0x4276a0, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0x427734, size: -0x1
  }
  [closure] void Ezh(dynamic, String) {
    // ** addr: 0x428a44, size: -0x1
  }
  [closure] void Gzh(dynamic) {
    // ** addr: 0x4289b0, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, Ra<String, String>) {
    // ** addr: 0x4287c8, size: -0x1
  }
  [closure] Future<dynamic> <anonymous closure>(dynamic, Ra<String, ZD>) {
    // ** addr: 0x4283e8, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, void) {
    // ** addr: 0x4282f8, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x428724, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, String, Object?) {
    // ** addr: 0x4b4bf4, size: -0x1
  }
}
