// lib: , url: Bbj

// class id: 1049284, size: 0x8
class :: {
}

// class id: 3089, size: 0x34, field offset: 0x14
class _gGa extends Mt<dynamic> {

  late final iJ<bool> _PVf; // offset: 0x30
  late double _NVf; // offset: 0x28
  late double _OVf; // offset: 0x2c

  [closure] void _SVf(dynamic, mK) {
    // ** addr: 0x605064, size: -0x1
  }
  [closure] pI <anonymous closure>(dynamic, aoa, bool, pI?) {
    // ** addr: 0x604e6c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, DM) {
    // ** addr: 0x604cac, size: -0x1
  }
  [closure] void _TVf(dynamic, qK) {
    // ** addr: 0x604c18, size: -0x1
  }
  [closure] void _UVf(dynamic, lK) {
    // ** addr: 0x604af8, size: -0x1
  }
}

// class id: 3716, size: 0x20, field offset: 0xc
class _jGa extends Kt {
}

// class id: 3717, size: 0x24, field offset: 0xc
class _hGa extends Kt {
}

// class id: 3878, size: 0x6c, field offset: 0xc
//   const constructor, 
class fGa extends It {
}

// class id: 4227, size: 0x14, field offset: 0x10
class _iGa extends wx<dynamic> {
}
