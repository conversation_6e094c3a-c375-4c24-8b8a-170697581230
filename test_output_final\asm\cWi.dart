// lib: , url: CWi

// class id: 1049019, size: 0x8
class :: {
}

// class id: 2354, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class PS extends Object {

  PS +(PS, PS) {
    // ** addr: 0x8e8658, size: 0x88
    // 0x8e8658: EnterFrame
    //     0x8e8658: stp             fp, lr, [SP, #-0x10]!
    //     0x8e865c: mov             fp, SP
    // 0x8e8660: CheckStackOverflow
    //     0x8e8660: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8e8664: cmp             SP, x16
    //     0x8e8668: b.ls            #0x8e86c0
    // 0x8e866c: ldr             x0, [fp, #0x10]
    // 0x8e8670: r2 = Null
    //     0x8e8670: mov             x2, NULL
    // 0x8e8674: r1 = Null
    //     0x8e8674: mov             x1, NULL
    // 0x8e8678: r4 = 59
    //     0x8e8678: movz            x4, #0x3b
    // 0x8e867c: branchIfSmi(r0, 0x8e8688)
    //     0x8e867c: tbz             w0, #0, #0x8e8688
    // 0x8e8680: r4 = LoadClassIdInstr(r0)
    //     0x8e8680: ldur            x4, [x0, #-1]
    //     0x8e8684: ubfx            x4, x4, #0xc, #0x14
    // 0x8e8688: sub             x4, x4, #0x934
    // 0x8e868c: cmp             x4, #0xd
    // 0x8e8690: b.ls            #0x8e86a8
    // 0x8e8694: r8 = PS
    //     0x8e8694: add             x8, PP, #0x13, lsl #12  ; [pp+0x13db8] Type: PS
    //     0x8e8698: ldr             x8, [x8, #0xdb8]
    // 0x8e869c: r3 = Null
    //     0x8e869c: add             x3, PP, #0x13, lsl #12  ; [pp+0x13dc0] Null
    //     0x8e86a0: ldr             x3, [x3, #0xdc0]
    // 0x8e86a4: r0 = PS()
    //     0x8e86a4: bl              #0x8e86d4  ; IsType_PS_Stub
    // 0x8e86a8: ldr             x1, [fp, #0x18]
    // 0x8e86ac: ldr             x2, [fp, #0x10]
    // 0x8e86b0: r0 = call 0x437b60
    //     0x8e86b0: bl              #0x437b60
    // 0x8e86b4: LeaveFrame
    //     0x8e86b4: mov             SP, fp
    //     0x8e86b8: ldp             fp, lr, [SP], #0x10
    // 0x8e86bc: ret
    //     0x8e86bc: ret             
    // 0x8e86c0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8e86c0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8e86c4: b               #0x8e866c
  }
}

// class id: 2358, size: 0xc, field offset: 0x8
class _WY extends PS {

  [closure] bool <anonymous closure>(dynamic, PS) {
    // ** addr: 0x8044f4, size: -0x1
  }
  [closure] qZ <anonymous closure>(dynamic, qZ, PS) {
    // ** addr: 0x808a84, size: -0x1
  }
  [closure] PS <anonymous closure>(dynamic, PS) {
    // ** addr: 0x811168, size: -0x1
  }
}

// class id: 2359, size: 0xc, field offset: 0x8
//   const constructor, 
abstract class VY extends PS {

  [closure] static VY? Jdc(dynamic, VY?, VY?, double) {
    // ** addr: 0x71d5c8, size: -0x1
  }
}

// class id: 2842, size: 0x20, field offset: 0x8
//   const constructor, 
class BO extends _DF {

  mr field_8;
  _Mint field_c;
  UY field_14;
  _Double field_18;
}

// class id: 5536, size: 0x14, field offset: 0x14
enum UY extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
