// lib: , url: NPi

// class id: 1048699, size: 0x8
class :: {
}

// class id: 4504, size: 0x38, field offset: 0x8
class Xy extends Object {

  Map<String, dynamic> WNb(Xy) {
    // ** addr: 0x8f7fb0, size: 0x48
    // 0x8f7fb0: EnterFrame
    //     0x8f7fb0: stp             fp, lr, [SP, #-0x10]!
    //     0x8f7fb4: mov             fp, SP
    // 0x8f7fb8: CheckStackOverflow
    //     0x8f7fb8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f7fbc: cmp             SP, x16
    //     0x8f7fc0: b.ls            #0x8f7fd8
    // 0x8f7fc4: ldr             x1, [fp, #0x10]
    // 0x8f7fc8: r0 = call 0x4e1604
    //     0x8f7fc8: bl              #0x4e1604
    // 0x8f7fcc: LeaveFrame
    //     0x8f7fcc: mov             SP, fp
    //     0x8f7fd0: ldp             fp, lr, [SP], #0x10
    // 0x8f7fd4: ret
    //     0x8f7fd4: ret             
    // 0x8f7fd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f7fd8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f7fdc: b               #0x8f7fc4
  }
}

// class id: 4505, size: 0x18, field offset: 0x8
class Wy extends Object {

  Map<String, dynamic> WNb(Wy) {
    // ** addr: 0x9032b0, size: 0x48
    // 0x9032b0: EnterFrame
    //     0x9032b0: stp             fp, lr, [SP, #-0x10]!
    //     0x9032b4: mov             fp, SP
    // 0x9032b8: CheckStackOverflow
    //     0x9032b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9032bc: cmp             SP, x16
    //     0x9032c0: b.ls            #0x9032d8
    // 0x9032c4: ldr             x1, [fp, #0x10]
    // 0x9032c8: r0 = call 0x55aa14
    //     0x9032c8: bl              #0x55aa14
    // 0x9032cc: LeaveFrame
    //     0x9032cc: mov             SP, fp
    //     0x9032d0: ldp             fp, lr, [SP], #0x10
    // 0x9032d4: ret
    //     0x9032d4: ret             
    // 0x9032d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9032d8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9032dc: b               #0x9032c4
  }
  [closure] static Xy <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x55da70, size: -0x1
  }
  [closure] Map<String, dynamic> <anonymous closure>(dynamic, Xy) {
    // ** addr: 0x55ab58, size: -0x1
  }
}
