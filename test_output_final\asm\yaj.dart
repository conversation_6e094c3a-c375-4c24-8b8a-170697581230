// lib: , url: Yaj

// class id: 1049255, size: 0x8
class :: {
}

// class id: 2105, size: 0x6c, field offset: 0x5c
class _Wxa extends _ada {

  [closure] void <anonymous closure>(dynamic, sca, er) {
    // ** addr: 0x38823c, size: -0x1
  }
  [closure] double ABc(dynamic, double) {
    // ** addr: 0x352e4c, size: -0x1
  }
  [closure] double CBc(dynamic, double) {
    // ** addr: 0x3497c4, size: -0x1
  }
  [closure] double zBc(dynamic, double) {
    // ** addr: 0x35568c, size: -0x1
  }
  [closure] double BBc(dynamic, double) {
    // ** addr: 0x34f658, size: -0x1
  }
}

// class id: 3420, size: 0x18, field offset: 0x14
//   const constructor, 
class Nma extends UZ {

  ct field_c;
  Jka field_14;

  KY rn(Nma, QZ) {
    // ** addr: 0x8c3220, size: 0x88
    // 0x8c3220: EnterFrame
    //     0x8c3220: stp             fp, lr, [SP, #-0x10]!
    //     0x8c3224: mov             fp, SP
    // 0x8c3228: CheckStackOverflow
    //     0x8c3228: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c322c: cmp             SP, x16
    //     0x8c3230: b.ls            #0x8c3288
    // 0x8c3234: ldr             x0, [fp, #0x10]
    // 0x8c3238: r2 = Null
    //     0x8c3238: mov             x2, NULL
    // 0x8c323c: r1 = Null
    //     0x8c323c: mov             x1, NULL
    // 0x8c3240: r4 = 59
    //     0x8c3240: movz            x4, #0x3b
    // 0x8c3244: branchIfSmi(r0, 0x8c3250)
    //     0x8c3244: tbz             w0, #0, #0x8c3250
    // 0x8c3248: r4 = LoadClassIdInstr(r0)
    //     0x8c3248: ldur            x4, [x0, #-1]
    //     0x8c324c: ubfx            x4, x4, #0xc, #0x14
    // 0x8c3250: sub             x4, x4, #0xd5a
    // 0x8c3254: cmp             x4, #3
    // 0x8c3258: b.ls            #0x8c3270
    // 0x8c325c: r8 = QZ
    //     0x8c325c: add             x8, PP, #0x39, lsl #12  ; [pp+0x392d0] Type: QZ
    //     0x8c3260: ldr             x8, [x8, #0x2d0]
    // 0x8c3264: r3 = Null
    //     0x8c3264: add             x3, PP, #0x39, lsl #12  ; [pp+0x39fb0] Null
    //     0x8c3268: ldr             x3, [x3, #0xfb0]
    // 0x8c326c: r0 = QZ()
    //     0x8c326c: bl              #0x8c3290  ; IsType_QZ_Stub
    // 0x8c3270: ldr             x1, [fp, #0x18]
    // 0x8c3274: ldr             x2, [fp, #0x10]
    // 0x8c3278: r0 = call 0x7fb6bc
    //     0x8c3278: bl              #0x7fb6bc
    // 0x8c327c: LeaveFrame
    //     0x8c327c: mov             SP, fp
    //     0x8c3280: ldp             fp, lr, [SP], #0x10
    // 0x8c3284: ret
    //     0x8c3284: ret             
    // 0x8c3288: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c3288: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c328c: b               #0x8c3234
  }
  [closure] static bool zch(dynamic, QZ) {
    // ** addr: 0x439e58, size: -0x1
  }
}

// class id: 3477, size: 0x18, field offset: 0x14
//   const constructor, 
class _Uxa extends Hka<dynamic> {
}

// class id: 3609, size: 0x1c, field offset: 0x10
//   const constructor, 
class _Vxa extends Fz {
}
