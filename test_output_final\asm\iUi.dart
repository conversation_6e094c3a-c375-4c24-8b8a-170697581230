// lib: , url: IUi

// class id: 1049093, size: 0x8
class :: {
}

// class id: 1928, size: 0xc, field offset: 0x8
//   const constructor, 
class Ofa extends Object
    implements Ja {
}

// class id: 1929, size: 0x14, field offset: 0x8
class Nfa extends Object
    implements Future<X0> {
}

// class id: 1930, size: 0x1c, field offset: 0x8
class Mfa extends Object {

  [closure] void _JAb(dynamic, Ea) {
    // ** addr: 0x310958, size: -0x1
  }
}

// class id: 4632, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class Bt extends Object {
}
