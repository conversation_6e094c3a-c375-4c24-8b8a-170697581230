// lib: , url: MYi

// class id: 1049143, size: 0x8
class :: {
}

// class id: 3165, size: 0x14, field offset: 0x14
//   transformed mixin,
abstract class _Rja extends Mt<dynamic>
     with Ft {
}

// class id: 3166, size: 0x24, field offset: 0x14
class _Sja extends _Rja {

  [closure] pI <anonymous closure>(dynamic, aoa) {
    // ** addr: 0x5ee3f8, size: -0x1
  }
  [closure] bool _Swd(dynamic, Cqa) {
    // ** addr: 0x5edf80, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x30cf14, size: -0x1
  }
}

// class id: 3932, size: 0x68, field offset: 0xc
class Qja extends It {

  static late Map<Type, fja<eja>> Nwd; // offset: 0x974
}
