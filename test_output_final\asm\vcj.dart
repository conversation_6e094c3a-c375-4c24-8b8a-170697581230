// lib: , url: Vcj

// class id: 1049350, size: 0x8
class :: {
}

// class id: 1179, size: 0x24, field offset: 0x8
abstract class DJa<X0> extends Object
    implements dJa<X0> {

  late GJa<X0> eEg; // offset: 0x1c

  int dyn:get:length(DJa<X0>) {
    // ** addr: 0x8cb01c, size: 0x88
    // 0x8cb01c: EnterFrame
    //     0x8cb01c: stp             fp, lr, [SP, #-0x10]!
    //     0x8cb020: mov             fp, SP
    // 0x8cb024: CheckStackOverflow
    //     0x8cb024: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cb028: cmp             SP, x16
    //     0x8cb02c: b.ls            #0x8cb07c
    // 0x8cb030: ldr             x1, [fp, #0x10]
    // 0x8cb034: r0 = call 0x31d8b4
    //     0x8cb034: bl              #0x31d8b4
    // 0x8cb038: ldr             x2, [fp, #0x10]
    // 0x8cb03c: LoadField: r3 = r2->field_1b
    //     0x8cb03c: ldur            w3, [x2, #0x1b]
    // 0x8cb040: DecompressPointer r3
    //     0x8cb040: add             x3, x3, HEAP, lsl #32
    // 0x8cb044: r16 = Sentinel
    //     0x8cb044: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x8cb048: cmp             w3, w16
    // 0x8cb04c: b.eq            #0x8cb084
    // 0x8cb050: LoadField: r2 = r3->field_f
    //     0x8cb050: ldur            w2, [x3, #0xf]
    // 0x8cb054: DecompressPointer r2
    //     0x8cb054: add             x2, x2, HEAP, lsl #32
    // 0x8cb058: LoadField: r3 = r2->field_1f
    //     0x8cb058: ldur            x3, [x2, #0x1f]
    // 0x8cb05c: r0 = BoxInt64Instr(r3)
    //     0x8cb05c: sbfiz           x0, x3, #1, #0x1f
    //     0x8cb060: cmp             x3, x0, asr #1
    //     0x8cb064: b.eq            #0x8cb070
    //     0x8cb068: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8cb06c: stur            x3, [x0, #7]
    // 0x8cb070: LeaveFrame
    //     0x8cb070: mov             SP, fp
    //     0x8cb074: ldp             fp, lr, [SP], #0x10
    // 0x8cb078: ret
    //     0x8cb078: ret             
    // 0x8cb07c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cb07c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cb080: b               #0x8cb030
    // 0x8cb084: r9 = eEg
    //     0x8cb084: ldr             x9, [PP, #0x7398]  ; [pp+0x7398] Field <DJa.eEg>: late (offset: 0x1c)
    // 0x8cb088: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x8cb088: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  bool dyn:get:dk(DJa<X0>) {
    // ** addr: 0x8cb0a4, size: 0x48
    // 0x8cb0a4: EnterFrame
    //     0x8cb0a4: stp             fp, lr, [SP, #-0x10]!
    //     0x8cb0a8: mov             fp, SP
    // 0x8cb0ac: CheckStackOverflow
    //     0x8cb0ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cb0b0: cmp             SP, x16
    //     0x8cb0b4: b.ls            #0x8cb0cc
    // 0x8cb0b8: ldr             x1, [fp, #0x10]
    // 0x8cb0bc: r0 = call 0x31dab8
    //     0x8cb0bc: bl              #0x31dab8
    // 0x8cb0c0: LeaveFrame
    //     0x8cb0c0: mov             SP, fp
    //     0x8cb0c4: ldp             fp, lr, [SP], #0x10
    // 0x8cb0c8: ret
    //     0x8cb0c8: ret             
    // 0x8cb0cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cb0cc: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cb0d0: b               #0x8cb0b8
  }
  bool dyn:get:isEmpty(DJa<X0>) {
    // ** addr: 0x8cb0ec, size: 0x48
    // 0x8cb0ec: EnterFrame
    //     0x8cb0ec: stp             fp, lr, [SP, #-0x10]!
    //     0x8cb0f0: mov             fp, SP
    // 0x8cb0f4: CheckStackOverflow
    //     0x8cb0f4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cb0f8: cmp             SP, x16
    //     0x8cb0fc: b.ls            #0x8cb114
    // 0x8cb100: ldr             x1, [fp, #0x10]
    // 0x8cb104: r0 = call 0x31db30
    //     0x8cb104: bl              #0x31db30
    // 0x8cb108: LeaveFrame
    //     0x8cb108: mov             SP, fp
    //     0x8cb10c: ldp             fp, lr, [SP], #0x10
    // 0x8cb110: ret
    //     0x8cb110: ret             
    // 0x8cb114: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cb114: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cb118: b               #0x8cb100
  }
}
