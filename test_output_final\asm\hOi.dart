// lib: , url: HOi

// class id: 1048643, size: 0x8
class :: {
}

// class id: 3322, size: 0x2c, field offset: 0x14
class _lw extends Mt<dynamic> {

  [closure] Future<Null> <anonymous closure>(dynamic, int) async {
    // ** addr: 0x909294, size: 0x144
    // 0x909294: EnterFrame
    //     0x909294: stp             fp, lr, [SP, #-0x10]!
    //     0x909298: mov             fp, SP
    // 0x90929c: AllocStack(0x30)
    //     0x90929c: sub             SP, SP, #0x30
    // 0x9092a0: SetupParameters(_lw this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x9092a0: stur            NULL, [fp, #-8]
    //     0x9092a4: movz            x0, #0
    //     0x9092a8: add             x1, fp, w0, sxtw #2
    //     0x9092ac: ldr             x1, [x1, #0x18]
    //     0x9092b0: add             x2, fp, w0, sxtw #2
    //     0x9092b4: ldr             x2, [x2, #0x10]
    //     0x9092b8: stur            x2, [fp, #-0x18]
    //     0x9092bc: ldur            w3, [x1, #0x17]
    //     0x9092c0: add             x3, x3, HEAP, lsl #32
    //     0x9092c4: stur            x3, [fp, #-0x10]
    // 0x9092c8: CheckStackOverflow
    //     0x9092c8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9092cc: cmp             SP, x16
    //     0x9092d0: b.ls            #0x9093cc
    // 0x9092d4: InitAsync() -> Future<Null?>
    //     0x9092d4: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x9092d8: bl              #0x8c1de0  ; InitAsyncStub
    // 0x9092dc: r0 = InitLateStaticField(0xcc8) // [aQi] Bz::qkc
    //     0x9092dc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9092e0: ldr             x0, [x0, #0x1990]
    //     0x9092e4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9092e8: cmp             w0, w16
    //     0x9092ec: b.ne            #0x9092f8
    //     0x9092f0: ldr             x2, [PP, #0x6b28]  ; [pp+0x6b28] Field <Bz.qkc>: static late final (offset: 0xcc8)
    //     0x9092f4: bl              #0x94dbac  ; InitLateFinalStaticFieldStub
    // 0x9092f8: mov             x3, x0
    // 0x9092fc: ldur            x2, [fp, #-0x10]
    // 0x909300: stur            x3, [fp, #-0x20]
    // 0x909304: LoadField: r0 = r2->field_f
    //     0x909304: ldur            w0, [x2, #0xf]
    // 0x909308: DecompressPointer r0
    //     0x909308: add             x0, x0, HEAP, lsl #32
    // 0x90930c: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x90930c: ldur            w4, [x0, #0x17]
    // 0x909310: DecompressPointer r4
    //     0x909310: add             x4, x4, HEAP, lsl #32
    // 0x909314: LoadField: r0 = r4->field_b
    //     0x909314: ldur            w0, [x4, #0xb]
    // 0x909318: DecompressPointer r0
    //     0x909318: add             x0, x0, HEAP, lsl #32
    // 0x90931c: ldur            x1, [fp, #-0x18]
    // 0x909320: r5 = LoadInt32Instr(r1)
    //     0x909320: sbfx            x5, x1, #1, #0x1f
    //     0x909324: tbz             w1, #0, #0x90932c
    //     0x909328: ldur            x5, [x1, #7]
    // 0x90932c: r1 = LoadInt32Instr(r0)
    //     0x90932c: sbfx            x1, x0, #1, #0x1f
    // 0x909330: mov             x0, x1
    // 0x909334: mov             x1, x5
    // 0x909338: cmp             x1, x0
    // 0x90933c: b.hs            #0x9093d4
    // 0x909340: LoadField: r0 = r4->field_f
    //     0x909340: ldur            w0, [x4, #0xf]
    // 0x909344: DecompressPointer r0
    //     0x909344: add             x0, x0, HEAP, lsl #32
    // 0x909348: ArrayLoad: r1 = r0[r5]  ; Unknown_4
    //     0x909348: add             x16, x0, x5, lsl #2
    //     0x90934c: ldur            w1, [x16, #0xf]
    // 0x909350: DecompressPointer r1
    //     0x909350: add             x1, x1, HEAP, lsl #32
    // 0x909354: r16 = "mid"
    //     0x909354: ldr             x16, [PP, #0x6f50]  ; [pp+0x6f50] "mid"
    // 0x909358: stp             x16, x1, [SP]
    // 0x90935c: r4 = 0
    //     0x90935c: movz            x4, #0
    // 0x909360: ldr             x0, [SP, #8]
    // 0x909364: r16 = UnlinkedCall_0x2d3c80
    //     0x909364: add             x16, PP, #0x34, lsl #12  ; [pp+0x34c20] UnlinkedCall: 0x2d3c80 - SwitchableCallMissStub
    //     0x909368: add             x16, x16, #0xc20
    // 0x90936c: ldp             x5, lr, [x16]
    // 0x909370: blr             lr
    // 0x909374: mov             x3, x0
    // 0x909378: r2 = Null
    //     0x909378: mov             x2, NULL
    // 0x90937c: r1 = Null
    //     0x90937c: mov             x1, NULL
    // 0x909380: stur            x3, [fp, #-0x18]
    // 0x909384: branchIfSmi(r0, 0x9093ac)
    //     0x909384: tbz             w0, #0, #0x9093ac
    // 0x909388: r4 = LoadClassIdInstr(r0)
    //     0x909388: ldur            x4, [x0, #-1]
    //     0x90938c: ubfx            x4, x4, #0xc, #0x14
    // 0x909390: sub             x4, x4, #0x3b
    // 0x909394: cmp             x4, #1
    // 0x909398: b.ls            #0x9093ac
    // 0x90939c: r8 = int?
    //     0x90939c: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x9093a0: r3 = Null
    //     0x9093a0: add             x3, PP, #0x34, lsl #12  ; [pp+0x34c30] Null
    //     0x9093a4: ldr             x3, [x3, #0xc30]
    // 0x9093a8: r0 = int?()
    //     0x9093a8: bl              #0x959574  ; IsType_int?_Stub
    // 0x9093ac: ldur            x1, [fp, #-0x20]
    // 0x9093b0: ldur            x2, [fp, #-0x18]
    // 0x9093b4: r0 = __unknown_function__()
    //     0x9093b4: bl              #0x908cb0  ; [] ::__unknown_function__
    // 0x9093b8: mov             x1, x0
    // 0x9093bc: stur            x1, [fp, #-0x18]
    // 0x9093c0: r0 = Await()
    //     0x9093c0: bl              #0x8c1bb8  ; AwaitStub
    // 0x9093c4: r0 = Null
    //     0x9093c4: mov             x0, NULL
    // 0x9093c8: r0 = ReturnAsyncNotFuture()
    //     0x9093c8: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x9093cc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x9093cc: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9093d0: b               #0x9092d4
    // 0x9093d4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x9093d4: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Future<Null> <anonymous closure>(dynamic, Zy<dynamic>?) async {
    // ** addr: 0x909504, size: 0x314
    // 0x909504: EnterFrame
    //     0x909504: stp             fp, lr, [SP, #-0x10]!
    //     0x909508: mov             fp, SP
    // 0x90950c: AllocStack(0x30)
    //     0x90950c: sub             SP, SP, #0x30
    // 0x909510: SetupParameters(_lw this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x909510: stur            NULL, [fp, #-8]
    //     0x909514: movz            x0, #0
    //     0x909518: add             x1, fp, w0, sxtw #2
    //     0x90951c: ldr             x1, [x1, #0x18]
    //     0x909520: add             x2, fp, w0, sxtw #2
    //     0x909524: ldr             x2, [x2, #0x10]
    //     0x909528: stur            x2, [fp, #-0x18]
    //     0x90952c: ldur            w3, [x1, #0x17]
    //     0x909530: add             x3, x3, HEAP, lsl #32
    //     0x909534: stur            x3, [fp, #-0x10]
    // 0x909538: CheckStackOverflow
    //     0x909538: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90953c: cmp             SP, x16
    //     0x909540: b.ls            #0x909804
    // 0x909544: InitAsync() -> Future<Null?>
    //     0x909544: ldr             x0, [PP, #0xed0]  ; [pp+0xed0] TypeArguments: <Null?>
    //     0x909548: bl              #0x8c1de0  ; InitAsyncStub
    // 0x90954c: ldur            x0, [fp, #-0x18]
    // 0x909550: cmp             w0, NULL
    // 0x909554: b.eq            #0x909574
    // 0x909558: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x909558: ldur            w1, [x0, #0x17]
    // 0x90955c: DecompressPointer r1
    //     0x90955c: add             x1, x1, HEAP, lsl #32
    // 0x909560: r16 = Sentinel
    //     0x909560: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x909564: cmp             w1, w16
    // 0x909568: b.eq            #0x90980c
    // 0x90956c: cmp             w1, NULL
    // 0x909570: b.ne            #0x9095c0
    // 0x909574: ldur            x0, [fp, #-0x10]
    // 0x909578: r1 = true
    //     0x909578: add             x1, NULL, #0x20  ; true
    // 0x90957c: LoadField: r3 = r0->field_f
    //     0x90957c: ldur            w3, [x0, #0xf]
    // 0x909580: DecompressPointer r3
    //     0x909580: add             x3, x3, HEAP, lsl #32
    // 0x909584: stur            x3, [fp, #-0x18]
    // 0x909588: StoreField: r3->field_13 = r1
    //     0x909588: stur            w1, [x3, #0x13]
    // 0x90958c: LoadField: r0 = r3->field_f
    //     0x90958c: ldur            w0, [x3, #0xf]
    // 0x909590: DecompressPointer r0
    //     0x909590: add             x0, x0, HEAP, lsl #32
    // 0x909594: cmp             w0, NULL
    // 0x909598: b.eq            #0x9095b8
    // 0x90959c: r1 = Function '<anonymous closure>':.
    //     0x90959c: add             x1, PP, #0x34, lsl #12  ; [pp+0x34ca0] Function: [dart:ui] Shader::Shader._ (0x8aec68)
    //     0x9095a0: ldr             x1, [x1, #0xca0]
    // 0x9095a4: r2 = Null
    //     0x9095a4: mov             x2, NULL
    // 0x9095a8: r0 = AllocateClosure()
    //     0x9095a8: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x9095ac: ldur            x1, [fp, #-0x18]
    // 0x9095b0: mov             x2, x0
    // 0x9095b4: r0 = call 0x30b424
    //     0x9095b4: bl              #0x30b424
    // 0x9095b8: r0 = Null
    //     0x9095b8: mov             x0, NULL
    // 0x9095bc: r0 = ReturnAsyncNotFuture()
    //     0x9095bc: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x9095c0: ldur            x0, [fp, #-0x10]
    // 0x9095c4: LoadField: r2 = r0->field_f
    //     0x9095c4: ldur            w2, [x0, #0xf]
    // 0x9095c8: DecompressPointer r2
    //     0x9095c8: add             x2, x2, HEAP, lsl #32
    // 0x9095cc: stur            x2, [fp, #-0x18]
    // 0x9095d0: r16 = "list"
    //     0x9095d0: add             x16, PP, #0x15, lsl #12  ; [pp+0x15620] "list"
    //     0x9095d4: ldr             x16, [x16, #0x620]
    // 0x9095d8: stp             x16, x1, [SP]
    // 0x9095dc: r4 = 0
    //     0x9095dc: movz            x4, #0
    // 0x9095e0: ldr             x0, [SP, #8]
    // 0x9095e4: r16 = UnlinkedCall_0x2d3c80
    //     0x9095e4: add             x16, PP, #0x34, lsl #12  ; [pp+0x34ca8] UnlinkedCall: 0x2d3c80 - SwitchableCallMissStub
    //     0x9095e8: add             x16, x16, #0xca8
    // 0x9095ec: ldp             x5, lr, [x16]
    // 0x9095f0: blr             lr
    // 0x9095f4: cmp             w0, NULL
    // 0x9095f8: b.ne            #0x909610
    // 0x9095fc: r1 = Null
    //     0x9095fc: mov             x1, NULL
    // 0x909600: r2 = 0
    //     0x909600: movz            x2, #0
    // 0x909604: r0 = call 0x2d76d0
    //     0x909604: bl              #0x2d76d0
    // 0x909608: mov             x5, x0
    // 0x90960c: b               #0x909614
    // 0x909610: mov             x5, x0
    // 0x909614: ldur            x3, [fp, #-0x10]
    // 0x909618: ldur            x4, [fp, #-0x18]
    // 0x90961c: mov             x0, x5
    // 0x909620: stur            x5, [fp, #-0x20]
    // 0x909624: r2 = Null
    //     0x909624: mov             x2, NULL
    // 0x909628: r1 = Null
    //     0x909628: mov             x1, NULL
    // 0x90962c: r8 = Iterable
    //     0x90962c: ldr             x8, [PP, #0x1150]  ; [pp+0x1150] Type: Iterable
    // 0x909630: r3 = Null
    //     0x909630: add             x3, PP, #0x34, lsl #12  ; [pp+0x34cb8] Null
    //     0x909634: ldr             x3, [x3, #0xcb8]
    // 0x909638: r0 = Iterable()
    //     0x909638: bl              #0x8be6a8  ; IsType_Iterable_Stub
    // 0x90963c: ldur            x2, [fp, #-0x20]
    // 0x909640: r1 = Null
    //     0x909640: mov             x1, NULL
    // 0x909644: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x909644: ldr             x4, [PP, #0x2a8]  ; [pp+0x2a8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x909648: r0 = call 0x307884
    //     0x909648: bl              #0x307884
    // 0x90964c: ldur            x1, [fp, #-0x18]
    // 0x909650: ArrayStore: r1[0] = r0  ; List_4
    //     0x909650: stur            w0, [x1, #0x17]
    //     0x909654: ldurb           w16, [x1, #-1]
    //     0x909658: ldurb           w17, [x0, #-1]
    //     0x90965c: and             x16, x17, x16, lsr #2
    //     0x909660: tst             x16, HEAP, lsr #32
    //     0x909664: b.eq            #0x90966c
    //     0x909668: bl              #0x94e148  ; WriteBarrierWrappersStub
    // 0x90966c: ldur            x0, [fp, #-0x10]
    // 0x909670: LoadField: r3 = r0->field_f
    //     0x909670: ldur            w3, [x0, #0xf]
    // 0x909674: DecompressPointer r3
    //     0x909674: add             x3, x3, HEAP, lsl #32
    // 0x909678: stur            x3, [fp, #-0x20]
    // 0x90967c: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x90967c: ldur            w4, [x3, #0x17]
    // 0x909680: DecompressPointer r4
    //     0x909680: add             x4, x4, HEAP, lsl #32
    // 0x909684: stur            x4, [fp, #-0x18]
    // 0x909688: r1 = Function '<anonymous closure>':.
    //     0x909688: add             x1, PP, #0x34, lsl #12  ; [pp+0x34cc8] AnonymousClosure: (0x587148), in [HOi] _lw::<anonymous closure> (0x909504)
    //     0x90968c: ldr             x1, [x1, #0xcc8]
    // 0x909690: r2 = Null
    //     0x909690: mov             x2, NULL
    // 0x909694: r0 = AllocateClosure()
    //     0x909694: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x909698: ldur            x1, [fp, #-0x18]
    // 0x90969c: mov             x2, x0
    // 0x9096a0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x9096a0: ldr             x4, [PP, #0x2a8]  ; [pp+0x2a8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x9096a4: r0 = call 0x2d77f0
    //     0x9096a4: bl              #0x2d77f0
    // 0x9096a8: r1 = LoadInt32Instr(r0)
    //     0x9096a8: sbfx            x1, x0, #1, #0x1f
    //     0x9096ac: tbz             w0, #0, #0x9096b4
    //     0x9096b0: ldur            x1, [x0, #7]
    // 0x9096b4: ldur            x0, [fp, #-0x20]
    // 0x9096b8: StoreField: r0->field_1b = r1
    //     0x9096b8: stur            x1, [x0, #0x1b]
    // 0x9096bc: r0 = InitLateStaticField(0xcc8) // [aQi] Bz::qkc
    //     0x9096bc: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x9096c0: ldr             x0, [x0, #0x1990]
    //     0x9096c4: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x9096c8: cmp             w0, w16
    //     0x9096cc: b.ne            #0x9096d8
    //     0x9096d0: ldr             x2, [PP, #0x6b28]  ; [pp+0x6b28] Field <Bz.qkc>: static late final (offset: 0xcc8)
    //     0x9096d4: bl              #0x94dbac  ; InitLateFinalStaticFieldStub
    // 0x9096d8: mov             x3, x0
    // 0x9096dc: ldur            x0, [fp, #-0x10]
    // 0x9096e0: stur            x3, [fp, #-0x18]
    // 0x9096e4: LoadField: r1 = r0->field_f
    //     0x9096e4: ldur            w1, [x0, #0xf]
    // 0x9096e8: DecompressPointer r1
    //     0x9096e8: add             x1, x1, HEAP, lsl #32
    // 0x9096ec: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x9096ec: ldur            w2, [x1, #0x17]
    // 0x9096f0: DecompressPointer r2
    //     0x9096f0: add             x2, x2, HEAP, lsl #32
    // 0x9096f4: mov             x1, x3
    // 0x9096f8: r0 = __unknown_function__()
    //     0x9096f8: bl              #0x909818  ; [] ::__unknown_function__
    // 0x9096fc: mov             x1, x0
    // 0x909700: stur            x1, [fp, #-0x20]
    // 0x909704: r0 = Await()
    //     0x909704: bl              #0x8c1bb8  ; AwaitStub
    // 0x909708: ldur            x2, [fp, #-0x10]
    // 0x90970c: LoadField: r0 = r2->field_f
    //     0x90970c: ldur            w0, [x2, #0xf]
    // 0x909710: DecompressPointer r0
    //     0x909710: add             x0, x0, HEAP, lsl #32
    // 0x909714: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x909714: ldur            w3, [x0, #0x17]
    // 0x909718: DecompressPointer r3
    //     0x909718: add             x3, x3, HEAP, lsl #32
    // 0x90971c: LoadField: r4 = r0->field_1b
    //     0x90971c: ldur            x4, [x0, #0x1b]
    // 0x909720: LoadField: r0 = r3->field_b
    //     0x909720: ldur            w0, [x3, #0xb]
    // 0x909724: DecompressPointer r0
    //     0x909724: add             x0, x0, HEAP, lsl #32
    // 0x909728: r1 = LoadInt32Instr(r0)
    //     0x909728: sbfx            x1, x0, #1, #0x1f
    // 0x90972c: mov             x0, x1
    // 0x909730: mov             x1, x4
    // 0x909734: cmp             x1, x0
    // 0x909738: b.hs            #0x909814
    // 0x90973c: LoadField: r0 = r3->field_f
    //     0x90973c: ldur            w0, [x3, #0xf]
    // 0x909740: DecompressPointer r0
    //     0x909740: add             x0, x0, HEAP, lsl #32
    // 0x909744: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x909744: add             x16, x0, x4, lsl #2
    //     0x909748: ldur            w1, [x16, #0xf]
    // 0x90974c: DecompressPointer r1
    //     0x90974c: add             x1, x1, HEAP, lsl #32
    // 0x909750: r16 = "mid"
    //     0x909750: ldr             x16, [PP, #0x6f50]  ; [pp+0x6f50] "mid"
    // 0x909754: stp             x16, x1, [SP]
    // 0x909758: r4 = 0
    //     0x909758: movz            x4, #0
    // 0x90975c: ldr             x0, [SP, #8]
    // 0x909760: r16 = UnlinkedCall_0x2d3c80
    //     0x909760: add             x16, PP, #0x34, lsl #12  ; [pp+0x34cd0] UnlinkedCall: 0x2d3c80 - SwitchableCallMissStub
    //     0x909764: add             x16, x16, #0xcd0
    // 0x909768: ldp             x5, lr, [x16]
    // 0x90976c: blr             lr
    // 0x909770: mov             x3, x0
    // 0x909774: r2 = Null
    //     0x909774: mov             x2, NULL
    // 0x909778: r1 = Null
    //     0x909778: mov             x1, NULL
    // 0x90977c: stur            x3, [fp, #-0x20]
    // 0x909780: branchIfSmi(r0, 0x9097a8)
    //     0x909780: tbz             w0, #0, #0x9097a8
    // 0x909784: r4 = LoadClassIdInstr(r0)
    //     0x909784: ldur            x4, [x0, #-1]
    //     0x909788: ubfx            x4, x4, #0xc, #0x14
    // 0x90978c: sub             x4, x4, #0x3b
    // 0x909790: cmp             x4, #1
    // 0x909794: b.ls            #0x9097a8
    // 0x909798: r8 = int?
    //     0x909798: ldr             x8, [PP, #0xbc8]  ; [pp+0xbc8] Type: int?
    // 0x90979c: r3 = Null
    //     0x90979c: add             x3, PP, #0x34, lsl #12  ; [pp+0x34ce0] Null
    //     0x9097a0: ldr             x3, [x3, #0xce0]
    // 0x9097a4: r0 = int?()
    //     0x9097a4: bl              #0x959574  ; IsType_int?_Stub
    // 0x9097a8: ldur            x1, [fp, #-0x18]
    // 0x9097ac: ldur            x2, [fp, #-0x20]
    // 0x9097b0: r0 = __unknown_function__()
    //     0x9097b0: bl              #0x908cb0  ; [] ::__unknown_function__
    // 0x9097b4: mov             x1, x0
    // 0x9097b8: stur            x1, [fp, #-0x18]
    // 0x9097bc: r0 = Await()
    //     0x9097bc: bl              #0x8c1bb8  ; AwaitStub
    // 0x9097c0: ldur            x0, [fp, #-0x10]
    // 0x9097c4: LoadField: r3 = r0->field_f
    //     0x9097c4: ldur            w3, [x0, #0xf]
    // 0x9097c8: DecompressPointer r3
    //     0x9097c8: add             x3, x3, HEAP, lsl #32
    // 0x9097cc: stur            x3, [fp, #-0x18]
    // 0x9097d0: LoadField: r0 = r3->field_f
    //     0x9097d0: ldur            w0, [x3, #0xf]
    // 0x9097d4: DecompressPointer r0
    //     0x9097d4: add             x0, x0, HEAP, lsl #32
    // 0x9097d8: cmp             w0, NULL
    // 0x9097dc: b.eq            #0x9097fc
    // 0x9097e0: r1 = Function '<anonymous closure>':.
    //     0x9097e0: add             x1, PP, #0x34, lsl #12  ; [pp+0x34cf0] Function: [dart:ui] Shader::Shader._ (0x8aec68)
    //     0x9097e4: ldr             x1, [x1, #0xcf0]
    // 0x9097e8: r2 = Null
    //     0x9097e8: mov             x2, NULL
    // 0x9097ec: r0 = AllocateClosure()
    //     0x9097ec: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x9097f0: ldur            x1, [fp, #-0x18]
    // 0x9097f4: mov             x2, x0
    // 0x9097f8: r0 = call 0x30b424
    //     0x9097f8: bl              #0x30b424
    // 0x9097fc: r0 = Null
    //     0x9097fc: mov             x0, NULL
    // 0x909800: r0 = ReturnAsyncNotFuture()
    //     0x909800: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x909804: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x909804: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x909808: b               #0x909544
    // 0x90980c: r9 = data
    //     0x90980c: ldr             x9, [PP, #0x78c8]  ; [pp+0x78c8] Field <Zy.data>: late final (offset: 0x18)
    // 0x909810: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x909810: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
    // 0x909814: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x909814: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] Future<void> <anonymous closure>(dynamic, Lz) async {
    // ** addr: 0x91fbc4, size: 0x1cc
    // 0x91fbc4: EnterFrame
    //     0x91fbc4: stp             fp, lr, [SP, #-0x10]!
    //     0x91fbc8: mov             fp, SP
    // 0x91fbcc: AllocStack(0x38)
    //     0x91fbcc: sub             SP, SP, #0x38
    // 0x91fbd0: SetupParameters(_lw this /* r1 */, dynamic _ /* r2, fp-0x18 */)
    //     0x91fbd0: stur            NULL, [fp, #-8]
    //     0x91fbd4: movz            x0, #0
    //     0x91fbd8: add             x1, fp, w0, sxtw #2
    //     0x91fbdc: ldr             x1, [x1, #0x18]
    //     0x91fbe0: add             x2, fp, w0, sxtw #2
    //     0x91fbe4: ldr             x2, [x2, #0x10]
    //     0x91fbe8: stur            x2, [fp, #-0x18]
    //     0x91fbec: ldur            w3, [x1, #0x17]
    //     0x91fbf0: add             x3, x3, HEAP, lsl #32
    //     0x91fbf4: stur            x3, [fp, #-0x10]
    // 0x91fbf8: CheckStackOverflow
    //     0x91fbf8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91fbfc: cmp             SP, x16
    //     0x91fc00: b.ls            #0x91fd88
    // 0x91fc04: InitAsync() -> Future<void?>
    //     0x91fc04: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x91fc08: bl              #0x8c1de0  ; InitAsyncStub
    // 0x91fc0c: ldur            x0, [fp, #-0x18]
    // 0x91fc10: LoadField: r3 = r0->field_7
    //     0x91fc10: ldur            w3, [x0, #7]
    // 0x91fc14: DecompressPointer r3
    //     0x91fc14: add             x3, x3, HEAP, lsl #32
    // 0x91fc18: mov             x1, x3
    // 0x91fc1c: stur            x3, [fp, #-0x20]
    // 0x91fc20: r2 = "name"
    //     0x91fc20: ldr             x2, [PP, #0x6a18]  ; [pp+0x6a18] "name"
    // 0x91fc24: r0 = call 0x8b0540
    //     0x91fc24: bl              #0x8b0540
    // 0x91fc28: mov             x1, x0
    // 0x91fc2c: ldur            x0, [fp, #-0x20]
    // 0x91fc30: LoadField: r2 = r0->field_f
    //     0x91fc30: ldur            w2, [x0, #0xf]
    // 0x91fc34: DecompressPointer r2
    //     0x91fc34: add             x2, x2, HEAP, lsl #32
    // 0x91fc38: cmp             w2, w1
    // 0x91fc3c: b.ne            #0x91fc48
    // 0x91fc40: r0 = Null
    //     0x91fc40: mov             x0, NULL
    // 0x91fc44: b               #0x91fc4c
    // 0x91fc48: mov             x0, x1
    // 0x91fc4c: r1 = 59
    //     0x91fc4c: movz            x1, #0x3b
    // 0x91fc50: branchIfSmi(r0, 0x91fc5c)
    //     0x91fc50: tbz             w0, #0, #0x91fc5c
    // 0x91fc54: r1 = LoadClassIdInstr(r0)
    //     0x91fc54: ldur            x1, [x0, #-1]
    //     0x91fc58: ubfx            x1, x1, #0xc, #0x14
    // 0x91fc5c: r16 = "category"
    //     0x91fc5c: add             x16, PP, #0x1b, lsl #12  ; [pp+0x1bc60] "category"
    //     0x91fc60: ldr             x16, [x16, #0xc60]
    // 0x91fc64: stp             x16, x0, [SP]
    // 0x91fc68: mov             x0, x1
    // 0x91fc6c: mov             lr, x0
    // 0x91fc70: ldr             lr, [x21, lr, lsl #3]
    // 0x91fc74: blr             lr
    // 0x91fc78: tbnz            w0, #4, #0x91fd80
    // 0x91fc7c: ldur            x0, [fp, #-0x10]
    // 0x91fc80: r1 = 1
    //     0x91fc80: movz            x1, #0x1
    // 0x91fc84: r0 = AllocateContext()
    //     0x91fc84: bl              #0x94e988  ; AllocateContextStub
    // 0x91fc88: mov             x1, x0
    // 0x91fc8c: ldur            x0, [fp, #-0x10]
    // 0x91fc90: stur            x1, [fp, #-0x18]
    // 0x91fc94: StoreField: r1->field_b = r0
    //     0x91fc94: stur            w0, [x1, #0xb]
    // 0x91fc98: r0 = InitLateStaticField(0xcc8) // [aQi] Bz::qkc
    //     0x91fc98: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x91fc9c: ldr             x0, [x0, #0x1990]
    //     0x91fca0: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    //     0x91fca4: cmp             w0, w16
    //     0x91fca8: b.ne            #0x91fcb4
    //     0x91fcac: ldr             x2, [PP, #0x6b28]  ; [pp+0x6b28] Field <Bz.qkc>: static late final (offset: 0xcc8)
    //     0x91fcb0: bl              #0x94dbac  ; InitLateFinalStaticFieldStub
    // 0x91fcb4: mov             x1, x0
    // 0x91fcb8: r0 = __unknown_function__()
    //     0x91fcb8: bl              #0x91fb18  ; [] ::__unknown_function__
    // 0x91fcbc: mov             x1, x0
    // 0x91fcc0: stur            x1, [fp, #-0x20]
    // 0x91fcc4: r0 = Await()
    //     0x91fcc4: bl              #0x8c1bb8  ; AwaitStub
    // 0x91fcc8: ldur            x2, [fp, #-0x18]
    // 0x91fccc: StoreField: r2->field_f = r0
    //     0x91fccc: stur            w0, [x2, #0xf]
    //     0x91fcd0: tbz             w0, #0, #0x91fcec
    //     0x91fcd4: ldurb           w16, [x2, #-1]
    //     0x91fcd8: ldurb           w17, [x0, #-1]
    //     0x91fcdc: and             x16, x17, x16, lsr #2
    //     0x91fce0: tst             x16, HEAP, lsr #32
    //     0x91fce4: b.eq            #0x91fcec
    //     0x91fce8: bl              #0x94e168  ; WriteBarrierWrappersStub
    // 0x91fcec: ldur            x0, [fp, #-0x10]
    // 0x91fcf0: LoadField: r3 = r0->field_f
    //     0x91fcf0: ldur            w3, [x0, #0xf]
    // 0x91fcf4: DecompressPointer r3
    //     0x91fcf4: add             x3, x3, HEAP, lsl #32
    // 0x91fcf8: stur            x3, [fp, #-0x28]
    // 0x91fcfc: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x91fcfc: ldur            w4, [x3, #0x17]
    // 0x91fd00: DecompressPointer r4
    //     0x91fd00: add             x4, x4, HEAP, lsl #32
    // 0x91fd04: stur            x4, [fp, #-0x20]
    // 0x91fd08: r1 = Function '<anonymous closure>':.
    //     0x91fd08: add             x1, PP, #0x34, lsl #12  ; [pp+0x34d58] AnonymousClosure: (0x637720), in [HOi] _lw::<anonymous closure> (0x91fbc4)
    //     0x91fd0c: ldr             x1, [x1, #0xd58]
    // 0x91fd10: r0 = AllocateClosure()
    //     0x91fd10: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x91fd14: ldur            x1, [fp, #-0x20]
    // 0x91fd18: mov             x2, x0
    // 0x91fd1c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x91fd1c: ldr             x4, [PP, #0x2a8]  ; [pp+0x2a8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x91fd20: r0 = call 0x2d77f0
    //     0x91fd20: bl              #0x2d77f0
    // 0x91fd24: r1 = LoadInt32Instr(r0)
    //     0x91fd24: sbfx            x1, x0, #1, #0x1f
    //     0x91fd28: tbz             w0, #0, #0x91fd30
    //     0x91fd2c: ldur            x1, [x0, #7]
    // 0x91fd30: ldur            x0, [fp, #-0x28]
    // 0x91fd34: StoreField: r0->field_1b = r1
    //     0x91fd34: stur            x1, [x0, #0x1b]
    // 0x91fd38: ldur            x0, [fp, #-0x10]
    // 0x91fd3c: LoadField: r1 = r0->field_f
    //     0x91fd3c: ldur            w1, [x0, #0xf]
    // 0x91fd40: DecompressPointer r1
    //     0x91fd40: add             x1, x1, HEAP, lsl #32
    // 0x91fd44: LoadField: r2 = r1->field_23
    //     0x91fd44: ldur            w2, [x1, #0x23]
    // 0x91fd48: DecompressPointer r2
    //     0x91fd48: add             x2, x2, HEAP, lsl #32
    // 0x91fd4c: cmp             w2, NULL
    // 0x91fd50: b.eq            #0x91fd80
    // 0x91fd54: LoadField: r3 = r1->field_1b
    //     0x91fd54: ldur            x3, [x1, #0x1b]
    // 0x91fd58: r0 = BoxInt64Instr(r3)
    //     0x91fd58: sbfiz           x0, x3, #1, #0x1f
    //     0x91fd5c: cmp             x3, x0, asr #1
    //     0x91fd60: b.eq            #0x91fd6c
    //     0x91fd64: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x91fd68: stur            x3, [x0, #7]
    // 0x91fd6c: stp             x0, x2, [SP]
    // 0x91fd70: mov             x0, x2
    // 0x91fd74: ClosureCall
    //     0x91fd74: ldr             x4, [PP, #0x170]  ; [pp+0x170] List(5) [0, 0x2, 0x2, 0x2, Null]
    //     0x91fd78: ldur            x2, [x0, #0x1f]
    //     0x91fd7c: blr             x2
    // 0x91fd80: r0 = Null
    //     0x91fd80: mov             x0, NULL
    // 0x91fd84: r0 = ReturnAsyncNotFuture()
    //     0x91fd84: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x91fd88: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91fd88: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91fd8c: b               #0x91fc04
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x587080, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x586ff8, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x586fc0, size: -0x1
  }
  [closure] String <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x586f44, size: -0x1
  }
  [closure] fw <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x586e34, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x586db0, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, (dynamic, int) => dynamic) {
    // ** addr: 0x586d6c, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x587148, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x637720, size: -0x1
  }
}

// class id: 3836, size: 0xc, field offset: 0xc
//   const constructor, 
class jw extends Kt {
}

// class id: 4063, size: 0xc, field offset: 0xc
//   const constructor, 
class _kw extends It {
}
