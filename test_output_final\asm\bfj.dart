// lib: , url: Bfj

// class id: 1049477, size: 0x8
class :: {
}

// class id: 971, size: 0xc, field offset: 0x8
class _cOa extends Object {
}

// class id: 972, size: 0x2c, field offset: 0x8
class bOa extends Object {

  static late final List<(dynamic, Uint32List, int, int) => int> wFf; // offset: 0xeb4

  [closure] static int _eGf(dynamic, Uint32List, int, int) {
    // ** addr: 0x877984, size: -0x1
  }
  [closure] static int _fGf(dynamic, Uint32List, int, int) {
    // ** addr: 0x87797c, size: -0x1
  }
  [closure] static int _gGf(dynamic, Uint32List, int, int) {
    // ** addr: 0x8778d8, size: -0x1
  }
  [closure] static int _hGf(dynamic, Uint32List, int, int) {
    // ** addr: 0x877830, size: -0x1
  }
  [closure] static int _iGf(dynamic, Uint32List, int, int) {
    // ** addr: 0x877788, size: -0x1
  }
  [closure] static int _jGf(dynamic, Uint32List, int, int) {
    // ** addr: 0x877620, size: -0x1
  }
  [closure] static int _kGf(dynamic, Uint32List, int, int) {
    // ** addr: 0x877550, size: -0x1
  }
  [closure] static int _lGf(dynamic, Uint32List, int, int) {
    // ** addr: 0x877484, size: -0x1
  }
  [closure] static int _mGf(dynamic, Uint32List, int, int) {
    // ** addr: 0x87739c, size: -0x1
  }
  [closure] static int _nGf(dynamic, Uint32List, int, int) {
    // ** addr: 0x8772b4, size: -0x1
  }
  [closure] static int _oGf(dynamic, Uint32List, int, int) {
    // ** addr: 0x8770fc, size: -0x1
  }
  [closure] static int _pGf(dynamic, Uint32List, int, int) {
    // ** addr: 0x876e58, size: -0x1
  }
  [closure] static int _qGf(dynamic, Uint32List, int, int) {
    // ** addr: 0x876c04, size: -0x1
  }
  [closure] static int _rGf(dynamic, Uint32List, int, int) {
    // ** addr: 0x876978, size: -0x1
  }
}
