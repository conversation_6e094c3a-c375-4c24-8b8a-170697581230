// lib: Tqj, url: pMi

// class id: 1049675, size: 0x8
class :: {
}

// class id: 758, size: 0xc, field offset: 0xc
class mVa extends OTa {

  static late final vWa iog; // offset: 0x127c

  [closure] static mVa <anonymous closure>(dynamic) {
    // ** addr: 0x46ff14, size: -0x1
  }
  [closure] static mVa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x47002c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x46febc, size: -0x1
  }
}
