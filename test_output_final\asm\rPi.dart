// lib: , url: RPi

// class id: 1048703, size: 0x8
class :: {
}

// class id: 4497, size: 0x14, field offset: 0x8
class ez extends Object {

  Map<String, dynamic> WNb(ez) {
    // ** addr: 0x8f0254, size: 0x48
    // 0x8f0254: EnterFrame
    //     0x8f0254: stp             fp, lr, [SP, #-0x10]!
    //     0x8f0258: mov             fp, SP
    // 0x8f025c: CheckStackOverflow
    //     0x8f025c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0260: cmp             SP, x16
    //     0x8f0264: b.ls            #0x8f027c
    // 0x8f0268: ldr             x1, [fp, #0x10]
    // 0x8f026c: r0 = call 0x4a52a8
    //     0x8f026c: bl              #0x4a52a8
    // 0x8f0270: LeaveFrame
    //     0x8f0270: mov             SP, fp
    //     0x8f0274: ldp             fp, lr, [SP], #0x10
    // 0x8f0278: ret
    //     0x8f0278: ret             
    // 0x8f027c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f027c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f0280: b               #0x8f0268
  }
}

// class id: 4498, size: 0x14, field offset: 0x8
class dz extends Object {

  Map<String, dynamic> WNb(dz) {
    // ** addr: 0x8f02a8, size: 0x48
    // 0x8f02a8: EnterFrame
    //     0x8f02a8: stp             fp, lr, [SP, #-0x10]!
    //     0x8f02ac: mov             fp, SP
    // 0x8f02b0: CheckStackOverflow
    //     0x8f02b0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f02b4: cmp             SP, x16
    //     0x8f02b8: b.ls            #0x8f02d0
    // 0x8f02bc: ldr             x1, [fp, #0x10]
    // 0x8f02c0: r0 = call 0x400690
    //     0x8f02c0: bl              #0x400690
    // 0x8f02c4: LeaveFrame
    //     0x8f02c4: mov             SP, fp
    //     0x8f02c8: ldp             fp, lr, [SP], #0x10
    // 0x8f02cc: ret
    //     0x8f02cc: ret             
    // 0x8f02d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f02d0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f02d4: b               #0x8f02bc
  }
}

// class id: 4499, size: 0x180, field offset: 0x8
class cz extends Object {

  Map<String, dynamic> WNb(cz) {
    // ** addr: 0x8f0188, size: 0x48
    // 0x8f0188: EnterFrame
    //     0x8f0188: stp             fp, lr, [SP, #-0x10]!
    //     0x8f018c: mov             fp, SP
    // 0x8f0190: CheckStackOverflow
    //     0x8f0190: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8f0194: cmp             SP, x16
    //     0x8f0198: b.ls            #0x8f01b0
    // 0x8f019c: ldr             x1, [fp, #0x10]
    // 0x8f01a0: r0 = call 0x3ff17c
    //     0x8f01a0: bl              #0x3ff17c
    // 0x8f01a4: LeaveFrame
    //     0x8f01a4: mov             SP, fp
    //     0x8f01a8: ldp             fp, lr, [SP], #0x10
    // 0x8f01ac: ret
    //     0x8f01ac: ret             
    // 0x8f01b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8f01b0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8f01b4: b               #0x8f019c
  }
}
