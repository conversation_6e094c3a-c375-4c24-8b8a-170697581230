// lib: , url: IZi

// class id: 1049294, size: 0x8
class :: {
}

// class id: 3082, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class _UGa extends Mt<dynamic>
     with Eu<X0 bound It> {

  [closure] void _wJc(dynamic) {
    // ** addr: 0x3e9ea4, size: -0x1
  }
}

// class id: 3083, size: 0x28, field offset: 0x1c
class _VGa extends _UGa {

  late lF<double> hCg; // offset: 0x24
  late yF WEc; // offset: 0x20

  [closure] void iCg(dynamic) {
    // ** addr: 0x553a00, size: -0x1
  }
}

// class id: 3873, size: 0x18, field offset: 0xc
//   const constructor, 
class TGa extends It {
}

// class id: 4010, size: 0x18, field offset: 0x10
//   const constructor, 
class _WGa extends cH {
}
