// lib: , url: Boj

// class id: 1049909, size: 0x8
class :: {
}

// class id: 179, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class Pjb extends Object {
}

// class id: 180, size: 0x8, field offset: 0x8
//   const constructor, 
class Sjb extends Pjb {
}

// class id: 181, size: 0x8, field offset: 0x8
//   const constructor, 
class Rjb extends Pjb {
}

// class id: 182, size: 0x8, field offset: 0x8
//   const constructor, 
class Qjb extends Pjb {
}
