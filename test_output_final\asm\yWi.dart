// lib: , url: yWi

// class id: 1049015, size: 0x8
class :: {
}

// class id: 2301, size: 0x10, field offset: 0x8
//   const constructor, 
class EY extends Object {

  _Mint field_8;
}

// class id: 2302, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class BY extends Object {
}

// class id: 2303, size: 0x20, field offset: 0x8
//   const constructor, 
class _DY extends BY {
}

// class id: 2304, size: 0x18, field offset: 0x8
//   const constructor, 
class CY extends BY {

  _Double field_8;
  _Double field_10;

  CY *(CY, double) {
    // ** addr: 0x8d6904, size: 0x50
    // 0x8d6904: EnterFrame
    //     0x8d6904: stp             fp, lr, [SP, #-0x10]!
    //     0x8d6908: mov             fp, SP
    // 0x8d690c: CheckStackOverflow
    //     0x8d690c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d6910: cmp             SP, x16
    //     0x8d6914: b.ls            #0x8d6934
    // 0x8d6918: ldr             x0, [fp, #0x10]
    // 0x8d691c: LoadField: d0 = r0->field_7
    //     0x8d691c: ldur            d0, [x0, #7]
    // 0x8d6920: ldr             x1, [fp, #0x18]
    // 0x8d6924: r0 = call 0x80fa28
    //     0x8d6924: bl              #0x80fa28
    // 0x8d6928: LeaveFrame
    //     0x8d6928: mov             SP, fp
    //     0x8d692c: ldp             fp, lr, [SP], #0x10
    // 0x8d6930: ret
    //     0x8d6930: ret             
    // 0x8d6934: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d6934: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d6938: b               #0x8d6918
  }
  CY +(CY, CY) {
    // ** addr: 0x8d6954, size: 0x84
    // 0x8d6954: EnterFrame
    //     0x8d6954: stp             fp, lr, [SP, #-0x10]!
    //     0x8d6958: mov             fp, SP
    // 0x8d695c: CheckStackOverflow
    //     0x8d695c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d6960: cmp             SP, x16
    //     0x8d6964: b.ls            #0x8d69b8
    // 0x8d6968: ldr             x0, [fp, #0x10]
    // 0x8d696c: r2 = Null
    //     0x8d696c: mov             x2, NULL
    // 0x8d6970: r1 = Null
    //     0x8d6970: mov             x1, NULL
    // 0x8d6974: r4 = 59
    //     0x8d6974: movz            x4, #0x3b
    // 0x8d6978: branchIfSmi(r0, 0x8d6984)
    //     0x8d6978: tbz             w0, #0, #0x8d6984
    // 0x8d697c: r4 = LoadClassIdInstr(r0)
    //     0x8d697c: ldur            x4, [x0, #-1]
    //     0x8d6980: ubfx            x4, x4, #0xc, #0x14
    // 0x8d6984: cmp             x4, #0x900
    // 0x8d6988: b.eq            #0x8d69a0
    // 0x8d698c: r8 = CY
    //     0x8d698c: add             x8, PP, #0x13, lsl #12  ; [pp+0x13e30] Type: CY
    //     0x8d6990: ldr             x8, [x8, #0xe30]
    // 0x8d6994: r3 = Null
    //     0x8d6994: add             x3, PP, #0x13, lsl #12  ; [pp+0x13e38] Null
    //     0x8d6998: ldr             x3, [x3, #0xe38]
    // 0x8d699c: r0 = DefaultTypeTest()
    //     0x8d699c: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8d69a0: ldr             x1, [fp, #0x18]
    // 0x8d69a4: ldr             x2, [fp, #0x10]
    // 0x8d69a8: r0 = call 0x398218
    //     0x8d69a8: bl              #0x398218
    // 0x8d69ac: LeaveFrame
    //     0x8d69ac: mov             SP, fp
    //     0x8d69b0: ldp             fp, lr, [SP], #0x10
    // 0x8d69b4: ret
    //     0x8d69b4: ret             
    // 0x8d69b8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d69b8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d69bc: b               #0x8d6968
  }
  CY -(CY, CY) {
    // ** addr: 0x8d69e4, size: 0x84
    // 0x8d69e4: EnterFrame
    //     0x8d69e4: stp             fp, lr, [SP, #-0x10]!
    //     0x8d69e8: mov             fp, SP
    // 0x8d69ec: CheckStackOverflow
    //     0x8d69ec: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8d69f0: cmp             SP, x16
    //     0x8d69f4: b.ls            #0x8d6a48
    // 0x8d69f8: ldr             x0, [fp, #0x10]
    // 0x8d69fc: r2 = Null
    //     0x8d69fc: mov             x2, NULL
    // 0x8d6a00: r1 = Null
    //     0x8d6a00: mov             x1, NULL
    // 0x8d6a04: r4 = 59
    //     0x8d6a04: movz            x4, #0x3b
    // 0x8d6a08: branchIfSmi(r0, 0x8d6a14)
    //     0x8d6a08: tbz             w0, #0, #0x8d6a14
    // 0x8d6a0c: r4 = LoadClassIdInstr(r0)
    //     0x8d6a0c: ldur            x4, [x0, #-1]
    //     0x8d6a10: ubfx            x4, x4, #0xc, #0x14
    // 0x8d6a14: cmp             x4, #0x900
    // 0x8d6a18: b.eq            #0x8d6a30
    // 0x8d6a1c: r8 = CY
    //     0x8d6a1c: add             x8, PP, #0x13, lsl #12  ; [pp+0x13e30] Type: CY
    //     0x8d6a20: ldr             x8, [x8, #0xe30]
    // 0x8d6a24: r3 = Null
    //     0x8d6a24: add             x3, PP, #0x13, lsl #12  ; [pp+0x13e48] Null
    //     0x8d6a28: ldr             x3, [x3, #0xe48]
    // 0x8d6a2c: r0 = DefaultTypeTest()
    //     0x8d6a2c: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8d6a30: ldr             x1, [fp, #0x18]
    // 0x8d6a34: ldr             x2, [fp, #0x10]
    // 0x8d6a38: r0 = call 0x398264
    //     0x8d6a38: bl              #0x398264
    // 0x8d6a3c: LeaveFrame
    //     0x8d6a3c: mov             SP, fp
    //     0x8d6a40: ldp             fp, lr, [SP], #0x10
    // 0x8d6a44: ret
    //     0x8d6a44: ret             
    // 0x8d6a48: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8d6a48: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8d6a4c: b               #0x8d69f8
  }
}

// class id: 2305, size: 0x18, field offset: 0x8
//   const constructor, 
class fu extends BY {

  _Mint field_8;
  _Mint field_10;

  fu -(fu, fu) {
    // ** addr: 0x8cef70, size: 0x88
    // 0x8cef70: EnterFrame
    //     0x8cef70: stp             fp, lr, [SP, #-0x10]!
    //     0x8cef74: mov             fp, SP
    // 0x8cef78: CheckStackOverflow
    //     0x8cef78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cef7c: cmp             SP, x16
    //     0x8cef80: b.ls            #0x8cefd8
    // 0x8cef84: ldr             x0, [fp, #0x10]
    // 0x8cef88: r2 = Null
    //     0x8cef88: mov             x2, NULL
    // 0x8cef8c: r1 = Null
    //     0x8cef8c: mov             x1, NULL
    // 0x8cef90: r4 = 59
    //     0x8cef90: movz            x4, #0x3b
    // 0x8cef94: branchIfSmi(r0, 0x8cefa0)
    //     0x8cef94: tbz             w0, #0, #0x8cefa0
    // 0x8cef98: r4 = LoadClassIdInstr(r0)
    //     0x8cef98: ldur            x4, [x0, #-1]
    //     0x8cef9c: ubfx            x4, x4, #0xc, #0x14
    // 0x8cefa0: sub             x4, x4, #0x901
    // 0x8cefa4: cmp             x4, #2
    // 0x8cefa8: b.ls            #0x8cefc0
    // 0x8cefac: r8 = fu
    //     0x8cefac: add             x8, PP, #0x13, lsl #12  ; [pp+0x13e08] Type: fu
    //     0x8cefb0: ldr             x8, [x8, #0xe08]
    // 0x8cefb4: r3 = Null
    //     0x8cefb4: add             x3, PP, #0x13, lsl #12  ; [pp+0x13e20] Null
    //     0x8cefb8: ldr             x3, [x3, #0xe20]
    // 0x8cefbc: r0 = DefaultTypeTest()
    //     0x8cefbc: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8cefc0: ldr             x1, [fp, #0x18]
    // 0x8cefc4: ldr             x2, [fp, #0x10]
    // 0x8cefc8: r0 = call 0x340d28
    //     0x8cefc8: bl              #0x340d28
    // 0x8cefcc: LeaveFrame
    //     0x8cefcc: mov             SP, fp
    //     0x8cefd0: ldp             fp, lr, [SP], #0x10
    // 0x8cefd4: ret
    //     0x8cefd4: ret             
    // 0x8cefd8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cefd8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cefdc: b               #0x8cef84
  }
  fu *(fu, double) {
    // ** addr: 0x8cf004, size: 0x50
    // 0x8cf004: EnterFrame
    //     0x8cf004: stp             fp, lr, [SP, #-0x10]!
    //     0x8cf008: mov             fp, SP
    // 0x8cf00c: CheckStackOverflow
    //     0x8cf00c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cf010: cmp             SP, x16
    //     0x8cf014: b.ls            #0x8cf034
    // 0x8cf018: ldr             x0, [fp, #0x10]
    // 0x8cf01c: LoadField: d0 = r0->field_7
    //     0x8cf01c: ldur            d0, [x0, #7]
    // 0x8cf020: ldr             x1, [fp, #0x18]
    // 0x8cf024: r0 = call 0x80f9e4
    //     0x8cf024: bl              #0x80f9e4
    // 0x8cf028: LeaveFrame
    //     0x8cf028: mov             SP, fp
    //     0x8cf02c: ldp             fp, lr, [SP], #0x10
    // 0x8cf030: ret
    //     0x8cf030: ret             
    // 0x8cf034: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cf034: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cf038: b               #0x8cf018
  }
  fu +(fu, fu) {
    // ** addr: 0x8cf054, size: 0x88
    // 0x8cf054: EnterFrame
    //     0x8cf054: stp             fp, lr, [SP, #-0x10]!
    //     0x8cf058: mov             fp, SP
    // 0x8cf05c: CheckStackOverflow
    //     0x8cf05c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8cf060: cmp             SP, x16
    //     0x8cf064: b.ls            #0x8cf0bc
    // 0x8cf068: ldr             x0, [fp, #0x10]
    // 0x8cf06c: r2 = Null
    //     0x8cf06c: mov             x2, NULL
    // 0x8cf070: r1 = Null
    //     0x8cf070: mov             x1, NULL
    // 0x8cf074: r4 = 59
    //     0x8cf074: movz            x4, #0x3b
    // 0x8cf078: branchIfSmi(r0, 0x8cf084)
    //     0x8cf078: tbz             w0, #0, #0x8cf084
    // 0x8cf07c: r4 = LoadClassIdInstr(r0)
    //     0x8cf07c: ldur            x4, [x0, #-1]
    //     0x8cf080: ubfx            x4, x4, #0xc, #0x14
    // 0x8cf084: sub             x4, x4, #0x901
    // 0x8cf088: cmp             x4, #2
    // 0x8cf08c: b.ls            #0x8cf0a4
    // 0x8cf090: r8 = fu
    //     0x8cf090: add             x8, PP, #0x13, lsl #12  ; [pp+0x13e08] Type: fu
    //     0x8cf094: ldr             x8, [x8, #0xe08]
    // 0x8cf098: r3 = Null
    //     0x8cf098: add             x3, PP, #0x13, lsl #12  ; [pp+0x13e10] Null
    //     0x8cf09c: ldr             x3, [x3, #0xe10]
    // 0x8cf0a0: r0 = DefaultTypeTest()
    //     0x8cf0a0: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8cf0a4: ldr             x1, [fp, #0x18]
    // 0x8cf0a8: ldr             x2, [fp, #0x10]
    // 0x8cf0ac: r0 = call 0x340d74
    //     0x8cf0ac: bl              #0x340d74
    // 0x8cf0b0: LeaveFrame
    //     0x8cf0b0: mov             SP, fp
    //     0x8cf0b4: ldp             fp, lr, [SP], #0x10
    // 0x8cf0b8: ret
    //     0x8cf0b8: ret             
    // 0x8cf0bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8cf0bc: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8cf0c0: b               #0x8cf068
  }
}
