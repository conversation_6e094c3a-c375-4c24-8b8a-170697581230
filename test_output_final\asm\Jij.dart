// lib: opj, url: Jij

// class id: 1049616, size: 0x8
class :: {
}

// class id: 824, size: 0x18, field offset: 0x18
class RSa extends SSa
    implements fSa {

  static late final vWa iog; // offset: 0x1118
  static late final RegExp _Aqg; // offset: 0x1114

  [closure] static (dynamic) => RSa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x474524, size: -0x1
  }
  [closure] static RSa <anonymous closure>(dynamic) {
    // ** addr: 0x474578, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x474498, size: -0x1
  }
}
