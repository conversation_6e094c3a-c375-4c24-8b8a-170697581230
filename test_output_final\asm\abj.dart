// lib: , url: Abj

// class id: 1049283, size: 0x8
class :: {
}

// class id: 1250, size: 0xc, field offset: 0x8
//   const constructor, 
abstract class SDa extends Object
    implements tpa {
}

// class id: 2690, size: 0xc, field offset: 0xc
//   const constructor, 
class _upa extends YG<dynamic> {

  static late final Map<os, Future<tpa>> _oWf; // offset: 0xf68

  [closure] RJ<tpa> <anonymous closure>(dynamic) {
    // ** addr: 0x7717a4, size: -0x1
  }
}
