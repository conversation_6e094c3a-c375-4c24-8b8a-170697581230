// lib: epj, url: Dij

// class id: 1049610, size: 0x8
class :: {
}

// class id: 862, size: 0x24, field offset: 0x8
class FSa extends IRa {

  static late final vWa iog; // offset: 0x10dc
  late Uint8List _hpg; // offset: 0xc
  late int _rpg; // offset: 0x1c
  late int _spg; // offset: 0x20

  [closure] static (dynamic) => FSa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x483fa0, size: -0x1
  }
  [closure] static FSa <anonymous closure>(dynamic) {
    // ** addr: 0x483ff4, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x483f3c, size: -0x1
  }
}
