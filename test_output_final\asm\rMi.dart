// lib: Vqj, url: rMi

// class id: 1049677, size: 0x8
class :: {
}

// class id: 756, size: 0xc, field offset: 0xc
class qVa extends OTa {

  static late final vWa iog; // offset: 0x1284

  [closure] static qVa <anonymous closure>(dynamic) {
    // ** addr: 0x463d88, size: -0x1
  }
  [closure] static qVa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x46fd0c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x463d30, size: -0x1
  }
}
