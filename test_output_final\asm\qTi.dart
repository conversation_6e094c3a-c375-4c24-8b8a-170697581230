// lib: , url: QTi

// class id: 1048894, size: 0x8
class :: {
}

// class id: 2965, size: 0x54, field offset: 0x44
class _QN extends PN {

  late final iP _IAc; // offset: 0x4c
  late final UX _mBc; // offset: 0x50
  late final VX _HAc; // offset: 0x48
}

// class id: 2966, size: 0x50, field offset: 0x44
class _ON extends PN {

  late final iP _IAc; // offset: 0x4c
  late final VX _HAc; // offset: 0x48
}
