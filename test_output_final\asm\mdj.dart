// lib: , url: Mdj

// class id: 1049393, size: 0x8
class :: {
}

// class id: 1076, size: 0x14, field offset: 0x10
class _ULa extends RLa
    implements Bq {
}

// class id: 1082, size: 0x2c, field offset: 0x2c
class _VLa extends <PERSON><PERSON>a
    implements LLa {
}

// class id: 1088, size: 0xc, field offset: 0x8
class XLa extends PLa {

  [closure] void <anonymous closure>(dynamic, String, String) {
    // ** addr: 0x599b48, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, String, List<String>) {
    // ** addr: 0x599a48, size: -0x1
  }
  [closure] Never <anonymous closure>(dynamic, Object) {
    // ** addr: 0x599984, size: -0x1
  }
  [closure] String <anonymous closure>(dynamic, String) {
    // ** addr: 0x599b18, size: -0x1
  }
}
