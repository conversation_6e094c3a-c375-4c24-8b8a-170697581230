// lib: , url: dcj

// class id: 1049320, size: 0x8
class :: {
}

// class id: 4158, size: 0xc, field offset: 0xc
//   transformed mixin,
abstract class _zIa extends dta<dynamic>
     with ita<X0> {
}

// class id: 4159, size: 0x28, field offset: 0xc
//   transformed mixin,
abstract class _BIa extends _zIa
     with Pu {
}

// class id: 4160, size: 0x3c, field offset: 0x28
class CIa extends _BIa {

  [closure] pI Rcc(dynamic, aoa) {
    // ** addr: 0x5f7d9c, size: -0x1
  }
  [closure] void Ecc(dynamic) {
    // ** addr: 0x5f8d1c, size: -0x1
  }
  [closure] Future<void> Vae(dynamic, Object?) {
    // ** addr: 0x53c7bc, size: -0x1
  }
}
