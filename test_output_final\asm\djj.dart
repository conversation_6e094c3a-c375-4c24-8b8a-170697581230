// lib: Npj, url: Djj

// class id: 1049691, size: 0x8
class :: {
}

// class id: 734, size: 0x14, field offset: 0x8
class SVa extends QVa {

  static late final vWa iog; // offset: 0x1190

  [closure] static (dynamic) => SVa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x461d9c, size: -0x1
  }
  [closure] static SVa <anonymous closure>(dynamic) {
    // ** addr: 0x461df0, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x461d38, size: -0x1
  }
}
