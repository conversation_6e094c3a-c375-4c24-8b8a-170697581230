// lib: sqj, url: pjj

// class id: 1049648, size: 0x8
class :: {
}

// class id: 785, size: 0xc, field offset: 0xc
class kUa extends OTa {

  static late final vWa iog; // offset: 0x1210

  [closure] static kUa <anonymous closure>(dynamic) {
    // ** addr: 0x472858, size: -0x1
  }
  [closure] static kUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>?) {
    // ** addr: 0x47295c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x472800, size: -0x1
  }
}
