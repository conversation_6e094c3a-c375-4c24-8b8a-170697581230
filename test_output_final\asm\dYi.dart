// lib: , url: DYi

// class id: 1049134, size: 0x8
class :: {
}

// class id: 1816, size: 0x18, field offset: 0x8
class _Iia extends Object {
}

// class id: 1817, size: 0x18, field offset: 0x8
class _Hia extends Object {
}

// class id: 1818, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class Gia extends Object {
}

// class id: 1819, size: 0x14, field offset: 0x8
class Kia extends Gia {
}

// class id: 1820, size: 0x14, field offset: 0x8
class Jia extends Gia {

  static late final Gia uIe; // offset: 0xb84
  static late final Gia tIe; // offset: 0xb80
}

// class id: 5492, size: 0x14, field offset: 0x14
enum Fia extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
