// lib: mpj, url: Vij

// class id: 1049628, size: 0x8
class :: {
}

// class id: 821, size: 0x18, field offset: 0x18
class sTa extends VSa {

  static late final vWa iog; // offset: 0x1108
  static late final RegExp _yqg; // offset: 0x1104

  [closure] static (dynamic) => sTa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x481740, size: -0x1
  }
  [closure] static sTa <anonymous closure>(dynamic) {
    // ** addr: 0x481794, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x4816b4, size: -0x1
  }
}
