// lib: Zpj, url: nkj

// class id: 1049727, size: 0x8
class :: {
}

// class id: 714, size: 0x1c, field offset: 0x1c
class IWa extends JWa {

  static late final vWa iog; // offset: 0x11c4

  [closure] static (dynamic) => IWa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x45fe60, size: -0x1
  }
  [closure] static IWa <anonymous closure>(dynamic) {
    // ** addr: 0x45feb4, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x45fdfc, size: -0x1
  }
}
