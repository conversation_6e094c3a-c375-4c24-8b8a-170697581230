// lib: , url: vgj

// class id: 1049520, size: 0x8
class :: {
}

// class id: 930, size: 0x14, field offset: 0x8
//   const constructor, 
class oPa extends Object
    implements Ca<X0> {

  _OneByteString field_8;
  _Mint field_c;

  int rn(oPa, oPa) {
    // ** addr: 0x8cc290, size: 0x8c
    // 0x8cc290: EnterFrame
    //     0x8cc290: stp             fp, lr, [SP, #-0x10]!
    //     0x8cc294: mov             fp, SP
    // 0x8cc298: ldr             x0, [fp, #0x10]
    // 0x8cc29c: r2 = Null
    //     0x8cc29c: mov             x2, NULL
    // 0x8cc2a0: r1 = Null
    //     0x8cc2a0: mov             x1, NULL
    // 0x8cc2a4: r4 = 59
    //     0x8cc2a4: movz            x4, #0x3b
    // 0x8cc2a8: branchIfSmi(r0, 0x8cc2b4)
    //     0x8cc2a8: tbz             w0, #0, #0x8cc2b4
    // 0x8cc2ac: r4 = LoadClassIdInstr(r0)
    //     0x8cc2ac: ldur            x4, [x0, #-1]
    //     0x8cc2b0: ubfx            x4, x4, #0xc, #0x14
    // 0x8cc2b4: cmp             x4, #0x3a2
    // 0x8cc2b8: b.eq            #0x8cc2d0
    // 0x8cc2bc: r8 = oPa
    //     0x8cc2bc: add             x8, PP, #0xd, lsl #12  ; [pp+0xdf18] Type: oPa
    //     0x8cc2c0: ldr             x8, [x8, #0xf18]
    // 0x8cc2c4: r3 = Null
    //     0x8cc2c4: add             x3, PP, #0x37, lsl #12  ; [pp+0x37af8] Null
    //     0x8cc2c8: ldr             x3, [x3, #0xaf8]
    // 0x8cc2cc: r0 = DefaultTypeTest()
    //     0x8cc2cc: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8cc2d0: ldr             x2, [fp, #0x18]
    // 0x8cc2d4: LoadField: r3 = r2->field_b
    //     0x8cc2d4: ldur            x3, [x2, #0xb]
    // 0x8cc2d8: ldr             x2, [fp, #0x10]
    // 0x8cc2dc: LoadField: r4 = r2->field_b
    //     0x8cc2dc: ldur            x4, [x2, #0xb]
    // 0x8cc2e0: sub             x2, x3, x4
    // 0x8cc2e4: r0 = BoxInt64Instr(r2)
    //     0x8cc2e4: sbfiz           x0, x2, #1, #0x1f
    //     0x8cc2e8: cmp             x2, x0, asr #1
    //     0x8cc2ec: b.eq            #0x8cc2f8
    //     0x8cc2f0: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8cc2f4: stur            x2, [x0, #7]
    // 0x8cc2f8: LeaveFrame
    //     0x8cc2f8: mov             SP, fp
    //     0x8cc2fc: ldp             fp, lr, [SP], #0x10
    // 0x8cc300: ret
    //     0x8cc300: ret             
  }
  bool <(oPa, oPa) {
    // ** addr: 0x8cc31c, size: 0x64
    // 0x8cc31c: EnterFrame
    //     0x8cc31c: stp             fp, lr, [SP, #-0x10]!
    //     0x8cc320: mov             fp, SP
    // 0x8cc324: ldr             x0, [fp, #0x10]
    // 0x8cc328: r2 = Null
    //     0x8cc328: mov             x2, NULL
    // 0x8cc32c: r1 = Null
    //     0x8cc32c: mov             x1, NULL
    // 0x8cc330: r4 = 59
    //     0x8cc330: movz            x4, #0x3b
    // 0x8cc334: branchIfSmi(r0, 0x8cc340)
    //     0x8cc334: tbz             w0, #0, #0x8cc340
    // 0x8cc338: r4 = LoadClassIdInstr(r0)
    //     0x8cc338: ldur            x4, [x0, #-1]
    //     0x8cc33c: ubfx            x4, x4, #0xc, #0x14
    // 0x8cc340: cmp             x4, #0x3a2
    // 0x8cc344: b.eq            #0x8cc35c
    // 0x8cc348: r8 = oPa
    //     0x8cc348: add             x8, PP, #0xd, lsl #12  ; [pp+0xdf18] Type: oPa
    //     0x8cc34c: ldr             x8, [x8, #0xf18]
    // 0x8cc350: r3 = Null
    //     0x8cc350: add             x3, PP, #0x23, lsl #12  ; [pp+0x23530] Null
    //     0x8cc354: ldr             x3, [x3, #0x530]
    // 0x8cc358: r0 = DefaultTypeTest()
    //     0x8cc358: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8cc35c: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x8cc35c: ldr             x0, [PP, #0x828]  ; [pp+0x828] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x8cc360: r0 = Throw()
    //     0x8cc360: bl              #0x94dd08  ; ThrowStub
    // 0x8cc364: brk             #0
  }
  bool >(oPa, oPa) {
    // ** addr: 0x8cc380, size: 0x64
    // 0x8cc380: EnterFrame
    //     0x8cc380: stp             fp, lr, [SP, #-0x10]!
    //     0x8cc384: mov             fp, SP
    // 0x8cc388: ldr             x0, [fp, #0x10]
    // 0x8cc38c: r2 = Null
    //     0x8cc38c: mov             x2, NULL
    // 0x8cc390: r1 = Null
    //     0x8cc390: mov             x1, NULL
    // 0x8cc394: r4 = 59
    //     0x8cc394: movz            x4, #0x3b
    // 0x8cc398: branchIfSmi(r0, 0x8cc3a4)
    //     0x8cc398: tbz             w0, #0, #0x8cc3a4
    // 0x8cc39c: r4 = LoadClassIdInstr(r0)
    //     0x8cc39c: ldur            x4, [x0, #-1]
    //     0x8cc3a0: ubfx            x4, x4, #0xc, #0x14
    // 0x8cc3a4: cmp             x4, #0x3a2
    // 0x8cc3a8: b.eq            #0x8cc3c0
    // 0x8cc3ac: r8 = oPa
    //     0x8cc3ac: add             x8, PP, #0xd, lsl #12  ; [pp+0xdf18] Type: oPa
    //     0x8cc3b0: ldr             x8, [x8, #0xf18]
    // 0x8cc3b4: r3 = Null
    //     0x8cc3b4: add             x3, PP, #0x16, lsl #12  ; [pp+0x16a18] Null
    //     0x8cc3b8: ldr             x3, [x3, #0xa18]
    // 0x8cc3bc: r0 = DefaultTypeTest()
    //     0x8cc3bc: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8cc3c0: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x8cc3c0: ldr             x0, [PP, #0x828]  ; [pp+0x828] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x8cc3c4: r0 = Throw()
    //     0x8cc3c4: bl              #0x94dd08  ; ThrowStub
    // 0x8cc3c8: brk             #0
  }
}
