// lib: , url: oQi

// class id: 1048725, size: 0x8
class :: {
}

// class id: 4475, size: 0x8, field offset: 0x8
abstract class Xz extends Object {

  [closure] static Future<Map<String, dynamic>> <anonymous closure>(dynamic) async {
    // ** addr: 0x91e470, size: 0x44c
    // 0x91e470: EnterFrame
    //     0x91e470: stp             fp, lr, [SP, #-0x10]!
    //     0x91e474: mov             fp, SP
    // 0x91e478: AllocStack(0x70)
    //     0x91e478: sub             SP, SP, #0x70
    // 0x91e47c: SetupParameters(dynamic _ /* r1 */)
    //     0x91e47c: stur            NULL, [fp, #-8]
    //     0x91e480: movz            x0, #0
    //     0x91e484: add             x1, fp, w0, sxtw #2
    //     0x91e488: ldr             x1, [x1, #0x10]
    //     0x91e48c: ldur            w2, [x1, #0x17]
    //     0x91e490: add             x2, x2, HEAP, lsl #32
    //     0x91e494: stur            x2, [fp, #-0x10]
    // 0x91e498: CheckStackOverflow
    //     0x91e498: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91e49c: cmp             SP, x16
    //     0x91e4a0: b.ls            #0x91e8ac
    // 0x91e4a4: InitAsync() -> Future<Map<String, dynamic>>
    //     0x91e4a4: ldr             x0, [PP, #0x4208]  ; [pp+0x4208] TypeArguments: <Map<String, dynamic>>
    //     0x91e4a8: bl              #0x8c1de0  ; InitAsyncStub
    // 0x91e4ac: r1 = 2
    //     0x91e4ac: movz            x1, #0x2
    // 0x91e4b0: r0 = AllocateContext()
    //     0x91e4b0: bl              #0x94e988  ; AllocateContextStub
    // 0x91e4b4: mov             x5, x0
    // 0x91e4b8: ldur            x4, [fp, #-0x10]
    // 0x91e4bc: stur            x5, [fp, #-0x28]
    // 0x91e4c0: StoreField: r5->field_b = r4
    //     0x91e4c0: stur            w4, [x5, #0xb]
    // 0x91e4c4: LoadField: r0 = r4->field_f
    //     0x91e4c4: ldur            w0, [x4, #0xf]
    // 0x91e4c8: DecompressPointer r0
    //     0x91e4c8: add             x0, x0, HEAP, lsl #32
    // 0x91e4cc: r1 = LoadInt32Instr(r0)
    //     0x91e4cc: sbfx            x1, x0, #1, #0x1f
    //     0x91e4d0: tbz             w0, #0, #0x91e4d8
    //     0x91e4d4: ldur            x1, [x0, #7]
    // 0x91e4d8: r16 = 80
    //     0x91e4d8: movz            x16, #0x50, lsl #16
    // 0x91e4dc: mul             x2, x1, x16
    // 0x91e4e0: add             x3, x2, #0x500, lsl #12
    // 0x91e4e4: LoadField: r6 = r4->field_b
    //     0x91e4e4: ldur            w6, [x4, #0xb]
    // 0x91e4e8: DecompressPointer r6
    //     0x91e4e8: add             x6, x6, HEAP, lsl #32
    // 0x91e4ec: stur            x6, [fp, #-0x20]
    // 0x91e4f0: LoadField: r7 = r6->field_23
    //     0x91e4f0: ldur            w7, [x6, #0x23]
    // 0x91e4f4: DecompressPointer r7
    //     0x91e4f4: add             x7, x7, HEAP, lsl #32
    // 0x91e4f8: r8 = LoadInt32Instr(r7)
    //     0x91e4f8: sbfx            x8, x7, #1, #0x1f
    //     0x91e4fc: tbz             w7, #0, #0x91e504
    //     0x91e500: ldur            x8, [x7, #7]
    // 0x91e504: cmp             x3, x8
    // 0x91e508: b.le            #0x91e514
    // 0x91e50c: mov             x0, x7
    // 0x91e510: b               #0x91e5f4
    // 0x91e514: cmp             x3, x8
    // 0x91e518: b.ge            #0x91e534
    // 0x91e51c: r0 = BoxInt64Instr(r3)
    //     0x91e51c: sbfiz           x0, x3, #1, #0x1f
    //     0x91e520: cmp             x3, x0, asr #1
    //     0x91e524: b.eq            #0x91e530
    //     0x91e528: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x91e52c: stur            x3, [x0, #7]
    // 0x91e530: b               #0x91e5f4
    // 0x91e534: r0 = 59
    //     0x91e534: movz            x0, #0x3b
    // 0x91e538: branchIfSmi(r7, 0x91e544)
    //     0x91e538: tbz             w7, #0, #0x91e544
    // 0x91e53c: r0 = LoadClassIdInstr(r7)
    //     0x91e53c: ldur            x0, [x7, #-1]
    //     0x91e540: ubfx            x0, x0, #0xc, #0x14
    // 0x91e544: cmp             x0, #0x3d
    // 0x91e548: b.ne            #0x91e5e0
    // 0x91e54c: r0 = BoxInt64Instr(r3)
    //     0x91e54c: sbfiz           x0, x3, #1, #0x1f
    //     0x91e550: cmp             x3, x0, asr #1
    //     0x91e554: b.eq            #0x91e560
    //     0x91e558: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x91e55c: stur            x3, [x0, #7]
    // 0x91e560: r1 = 59
    //     0x91e560: movz            x1, #0x3b
    // 0x91e564: branchIfSmi(r0, 0x91e570)
    //     0x91e564: tbz             w0, #0, #0x91e570
    // 0x91e568: r1 = LoadClassIdInstr(r0)
    //     0x91e568: ldur            x1, [x0, #-1]
    //     0x91e56c: ubfx            x1, x1, #0xc, #0x14
    // 0x91e570: cmp             x1, #0x3d
    // 0x91e574: b.ne            #0x91e5ac
    // 0x91e578: d0 = 0.000000
    //     0x91e578: eor             v0.16b, v0.16b, v0.16b
    // 0x91e57c: scvtf           d1, x3
    // 0x91e580: fcmp            d1, d0
    // 0x91e584: b.ne            #0x91e5ac
    // 0x91e588: add             x0, x3, x8
    // 0x91e58c: mul             x1, x0, x3
    // 0x91e590: mul             x3, x1, x8
    // 0x91e594: r0 = BoxInt64Instr(r3)
    //     0x91e594: sbfiz           x0, x3, #1, #0x1f
    //     0x91e598: cmp             x3, x0, asr #1
    //     0x91e59c: b.eq            #0x91e5a8
    //     0x91e5a0: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x91e5a4: stur            x3, [x0, #7]
    // 0x91e5a8: b               #0x91e5f4
    // 0x91e5ac: cbnz            x3, #0x91e5cc
    // 0x91e5b0: LoadField: d0 = r7->field_7
    //     0x91e5b0: ldur            d0, [x7, #7]
    // 0x91e5b4: fcmp            d0, #0.0
    // 0x91e5b8: b.vs            #0x91e5cc
    // 0x91e5bc: b.ne            #0x91e5c8
    // 0x91e5c0: r1 = 0.000000
    //     0x91e5c0: fmov            x1, d0
    // 0x91e5c4: cmp             x1, #0
    // 0x91e5c8: b.lt            #0x91e5d8
    // 0x91e5cc: LoadField: d0 = r7->field_7
    //     0x91e5cc: ldur            d0, [x7, #7]
    // 0x91e5d0: fcmp            d0, d0
    // 0x91e5d4: b.vc            #0x91e5f4
    // 0x91e5d8: mov             x0, x7
    // 0x91e5dc: b               #0x91e5f4
    // 0x91e5e0: r0 = BoxInt64Instr(r3)
    //     0x91e5e0: sbfiz           x0, x3, #1, #0x1f
    //     0x91e5e4: cmp             x3, x0, asr #1
    //     0x91e5e8: b.eq            #0x91e5f4
    //     0x91e5ec: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x91e5f0: stur            x3, [x0, #7]
    // 0x91e5f4: r3 = LoadInt32Instr(r0)
    //     0x91e5f4: sbfx            x3, x0, #1, #0x1f
    //     0x91e5f8: tbz             w0, #0, #0x91e600
    //     0x91e5fc: ldur            x3, [x0, #7]
    // 0x91e600: sub             x7, x3, x2
    // 0x91e604: r0 = BoxInt64Instr(r7)
    //     0x91e604: sbfiz           x0, x7, #1, #0x1f
    //     0x91e608: cmp             x7, x0, asr #1
    //     0x91e60c: b.eq            #0x91e618
    //     0x91e610: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x91e614: stur            x7, [x0, #7]
    // 0x91e618: stur            x0, [fp, #-0x18]
    // 0x91e61c: StoreField: r5->field_f = r0
    //     0x91e61c: stur            w0, [x5, #0xf]
    // 0x91e620: LoadField: r1 = r6->field_1b
    //     0x91e620: ldur            w1, [x6, #0x1b]
    // 0x91e624: DecompressPointer r1
    //     0x91e624: add             x1, x1, HEAP, lsl #32
    // 0x91e628: r0 = call 0x635b14
    //     0x91e628: bl              #0x635b14
    // 0x91e62c: mov             x3, x0
    // 0x91e630: ldur            x2, [fp, #-0x28]
    // 0x91e634: stur            x3, [fp, #-0x40]
    // 0x91e638: StoreField: r2->field_13 = r0
    //     0x91e638: stur            w0, [x2, #0x13]
    //     0x91e63c: ldurb           w16, [x2, #-1]
    //     0x91e640: ldurb           w17, [x0, #-1]
    //     0x91e644: and             x16, x17, x16, lsr #2
    //     0x91e648: tst             x16, HEAP, lsr #32
    //     0x91e64c: b.eq            #0x91e654
    //     0x91e650: bl              #0x94e168  ; WriteBarrierWrappersStub
    // 0x91e654: ldur            x4, [fp, #-0x20]
    // 0x91e658: LoadField: r5 = r4->field_3b
    //     0x91e658: ldur            w5, [x4, #0x3b]
    // 0x91e65c: DecompressPointer r5
    //     0x91e65c: add             x5, x5, HEAP, lsl #32
    // 0x91e660: stur            x5, [fp, #-0x38]
    // 0x91e664: LoadField: r6 = r4->field_37
    //     0x91e664: ldur            w6, [x4, #0x37]
    // 0x91e668: DecompressPointer r6
    //     0x91e668: add             x6, x6, HEAP, lsl #32
    // 0x91e66c: ldur            x7, [fp, #-0x10]
    // 0x91e670: stur            x6, [fp, #-0x30]
    // 0x91e674: LoadField: r0 = r7->field_f
    //     0x91e674: ldur            w0, [x7, #0xf]
    // 0x91e678: DecompressPointer r0
    //     0x91e678: add             x0, x0, HEAP, lsl #32
    // 0x91e67c: LoadField: r1 = r6->field_b
    //     0x91e67c: ldur            w1, [x6, #0xb]
    // 0x91e680: DecompressPointer r1
    //     0x91e680: add             x1, x1, HEAP, lsl #32
    // 0x91e684: r8 = LoadInt32Instr(r0)
    //     0x91e684: sbfx            x8, x0, #1, #0x1f
    //     0x91e688: tbz             w0, #0, #0x91e690
    //     0x91e68c: ldur            x8, [x0, #7]
    // 0x91e690: r0 = LoadInt32Instr(r1)
    //     0x91e690: sbfx            x0, x1, #1, #0x1f
    // 0x91e694: mov             x1, x8
    // 0x91e698: cmp             x1, x0
    // 0x91e69c: b.hs            #0x91e8b4
    // 0x91e6a0: LoadField: r0 = r6->field_f
    //     0x91e6a0: ldur            w0, [x6, #0xf]
    // 0x91e6a4: DecompressPointer r0
    //     0x91e6a4: add             x0, x0, HEAP, lsl #32
    // 0x91e6a8: ArrayLoad: r1 = r0[r8]  ; Unknown_4
    //     0x91e6a8: add             x16, x0, x8, lsl #2
    //     0x91e6ac: ldur            w1, [x16, #0xf]
    // 0x91e6b0: DecompressPointer r1
    //     0x91e6b0: add             x1, x1, HEAP, lsl #32
    // 0x91e6b4: r16 = "number"
    //     0x91e6b4: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eb98] "number"
    //     0x91e6b8: ldr             x16, [x16, #0xb98]
    // 0x91e6bc: stp             x16, x1, [SP]
    // 0x91e6c0: r4 = 0
    //     0x91e6c0: movz            x4, #0
    // 0x91e6c4: ldr             x0, [SP, #8]
    // 0x91e6c8: r16 = UnlinkedCall_0x2d3c80
    //     0x91e6c8: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2eba0] UnlinkedCall: 0x2d3c80 - SwitchableCallMissStub
    //     0x91e6cc: add             x16, x16, #0xba0
    // 0x91e6d0: ldp             x5, lr, [x16]
    // 0x91e6d4: blr             lr
    // 0x91e6d8: r1 = 59
    //     0x91e6d8: movz            x1, #0x3b
    // 0x91e6dc: branchIfSmi(r0, 0x91e6e8)
    //     0x91e6dc: tbz             w0, #0, #0x91e6e8
    // 0x91e6e0: r1 = LoadClassIdInstr(r0)
    //     0x91e6e0: ldur            x1, [x0, #-1]
    //     0x91e6e4: ubfx            x1, x1, #0xc, #0x14
    // 0x91e6e8: str             x0, [SP]
    // 0x91e6ec: mov             x0, x1
    // 0x91e6f0: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x91e6f0: ldr             x4, [PP, #0x490]  ; [pp+0x490] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x91e6f4: r0 = GDT[cid_x0 + 0x241f]()
    //     0x91e6f4: movz            x17, #0x241f
    //     0x91e6f8: add             lr, x0, x17
    //     0x91e6fc: ldr             lr, [x21, lr, lsl #3]
    //     0x91e700: blr             lr
    // 0x91e704: ldur            x1, [fp, #-0x38]
    // 0x91e708: mov             x3, x0
    // 0x91e70c: r2 = "{number}"
    //     0x91e70c: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ebb0] "{number}"
    //     0x91e710: ldr             x2, [x2, #0xbb0]
    // 0x91e714: r0 = call 0x2dd998
    //     0x91e714: bl              #0x2dd998
    // 0x91e718: mov             x2, x0
    // 0x91e71c: ldur            x0, [fp, #-0x10]
    // 0x91e720: stur            x2, [fp, #-0x38]
    // 0x91e724: LoadField: r1 = r0->field_f
    //     0x91e724: ldur            w1, [x0, #0xf]
    // 0x91e728: DecompressPointer r1
    //     0x91e728: add             x1, x1, HEAP, lsl #32
    // 0x91e72c: ldur            x3, [fp, #-0x30]
    // 0x91e730: LoadField: r0 = r3->field_b
    //     0x91e730: ldur            w0, [x3, #0xb]
    // 0x91e734: DecompressPointer r0
    //     0x91e734: add             x0, x0, HEAP, lsl #32
    // 0x91e738: r4 = LoadInt32Instr(r1)
    //     0x91e738: sbfx            x4, x1, #1, #0x1f
    //     0x91e73c: tbz             w1, #0, #0x91e744
    //     0x91e740: ldur            x4, [x1, #7]
    // 0x91e744: r1 = LoadInt32Instr(r0)
    //     0x91e744: sbfx            x1, x0, #1, #0x1f
    // 0x91e748: mov             x0, x1
    // 0x91e74c: mov             x1, x4
    // 0x91e750: cmp             x1, x0
    // 0x91e754: b.hs            #0x91e8b8
    // 0x91e758: LoadField: r0 = r3->field_f
    //     0x91e758: ldur            w0, [x3, #0xf]
    // 0x91e75c: DecompressPointer r0
    //     0x91e75c: add             x0, x0, HEAP, lsl #32
    // 0x91e760: ArrayLoad: r1 = r0[r4]  ; Unknown_4
    //     0x91e760: add             x16, x0, x4, lsl #2
    //     0x91e764: ldur            w1, [x16, #0xf]
    // 0x91e768: DecompressPointer r1
    //     0x91e768: add             x1, x1, HEAP, lsl #32
    // 0x91e76c: r16 = "signature"
    //     0x91e76c: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ebb8] "signature"
    //     0x91e770: ldr             x16, [x16, #0xbb8]
    // 0x91e774: stp             x16, x1, [SP]
    // 0x91e778: r4 = 0
    //     0x91e778: movz            x4, #0
    // 0x91e77c: ldr             x0, [SP, #8]
    // 0x91e780: r16 = UnlinkedCall_0x2d3c80
    //     0x91e780: add             x16, PP, #0x2e, lsl #12  ; [pp+0x2ebc0] UnlinkedCall: 0x2d3c80 - SwitchableCallMissStub
    //     0x91e784: add             x16, x16, #0xbc0
    // 0x91e788: ldp             x5, lr, [x16]
    // 0x91e78c: blr             lr
    // 0x91e790: r1 = 59
    //     0x91e790: movz            x1, #0x3b
    // 0x91e794: branchIfSmi(r0, 0x91e7a0)
    //     0x91e794: tbz             w0, #0, #0x91e7a0
    // 0x91e798: r1 = LoadClassIdInstr(r0)
    //     0x91e798: ldur            x1, [x0, #-1]
    //     0x91e79c: ubfx            x1, x1, #0xc, #0x14
    // 0x91e7a0: str             x0, [SP]
    // 0x91e7a4: mov             x0, x1
    // 0x91e7a8: r4 = const [0, 0x1, 0x1, 0x1, null]
    //     0x91e7a8: ldr             x4, [PP, #0x490]  ; [pp+0x490] List(5) [0, 0x1, 0x1, 0x1, Null]
    // 0x91e7ac: r0 = GDT[cid_x0 + 0x241f]()
    //     0x91e7ac: movz            x17, #0x241f
    //     0x91e7b0: add             lr, x0, x17
    //     0x91e7b4: ldr             lr, [x21, lr, lsl #3]
    //     0x91e7b8: blr             lr
    // 0x91e7bc: ldur            x1, [fp, #-0x38]
    // 0x91e7c0: mov             x3, x0
    // 0x91e7c4: r2 = "{signature}"
    //     0x91e7c4: add             x2, PP, #0x2e, lsl #12  ; [pp+0x2ebd0] "{signature}"
    //     0x91e7c8: ldr             x2, [x2, #0xbd0]
    // 0x91e7cc: r0 = call 0x2dd998
    //     0x91e7cc: bl              #0x2dd998
    // 0x91e7d0: r1 = Null
    //     0x91e7d0: mov             x1, NULL
    // 0x91e7d4: stur            x0, [fp, #-0x10]
    // 0x91e7d8: r0 = call 0x5720d0
    //     0x91e7d8: bl              #0x5720d0
    // 0x91e7dc: mov             x3, x0
    // 0x91e7e0: ldur            x0, [fp, #-0x20]
    // 0x91e7e4: stur            x3, [fp, #-0x38]
    // 0x91e7e8: LoadField: r4 = r0->field_f
    //     0x91e7e8: ldur            w4, [x0, #0xf]
    // 0x91e7ec: DecompressPointer r4
    //     0x91e7ec: add             x4, x4, HEAP, lsl #32
    // 0x91e7f0: stur            x4, [fp, #-0x30]
    // 0x91e7f4: r1 = Null
    //     0x91e7f4: mov             x1, NULL
    // 0x91e7f8: r2 = 4
    //     0x91e7f8: movz            x2, #0x4
    // 0x91e7fc: r0 = AllocateArray()
    //     0x91e7fc: bl              #0x94fa24  ; AllocateArrayStub
    // 0x91e800: r17 = "content-length"
    //     0x91e800: add             x17, PP, #8, lsl #12  ; [pp+0x8a70] "content-length"
    //     0x91e804: ldr             x17, [x17, #0xa70]
    // 0x91e808: StoreField: r0->field_f = r17
    //     0x91e808: stur            w17, [x0, #0xf]
    // 0x91e80c: ldur            x1, [fp, #-0x18]
    // 0x91e810: StoreField: r0->field_13 = r1
    //     0x91e810: stur            w1, [x0, #0x13]
    // 0x91e814: r16 = <String, dynamic>
    //     0x91e814: ldr             x16, [PP, #0xd38]  ; [pp+0xd38] TypeArguments: <String, dynamic>
    // 0x91e818: stp             x0, x16, [SP]
    // 0x91e81c: r0 = Map._fromLiteral()
    //     0x91e81c: bl              #0x2dc7b0  ; [dart:core] Map::Map._fromLiteral
    // 0x91e820: stur            x0, [fp, #-0x18]
    // 0x91e824: r0 = gE()
    //     0x91e824: bl              #0x8e7ce8  ; AllocategEStub -> gE (size=0x40)
    // 0x91e828: mov             x1, x0
    // 0x91e82c: ldur            x0, [fp, #-0x18]
    // 0x91e830: StoreField: r1->field_b = r0
    //     0x91e830: stur            w0, [x1, #0xb]
    // 0x91e834: r0 = "application/octet-stream"
    //     0x91e834: add             x0, PP, #0x2e, lsl #12  ; [pp+0x2ebd8] "application/octet-stream"
    //     0x91e838: ldr             x0, [x0, #0xbd8]
    // 0x91e83c: ArrayStore: r1[0] = r0  ; List_4
    //     0x91e83c: stur            w0, [x1, #0x17]
    // 0x91e840: ldur            x16, [fp, #-0x38]
    // 0x91e844: stp             x16, NULL, [SP, #0x20]
    // 0x91e848: ldur            x16, [fp, #-0x10]
    // 0x91e84c: ldur            lr, [fp, #-0x30]
    // 0x91e850: stp             lr, x16, [SP, #0x10]
    // 0x91e854: ldur            x16, [fp, #-0x40]
    // 0x91e858: stp             x1, x16, [SP]
    // 0x91e85c: r4 = const [0x1, 0x5, 0x5, 0x5, null]
    //     0x91e85c: ldr             x4, [PP, #0x1850]  ; [pp+0x1850] List(5) [0x1, 0x5, 0x5, 0x5, Null]
    // 0x91e860: r0 = call 0x635a7c
    //     0x91e860: bl              #0x635a7c
    // 0x91e864: ldur            x2, [fp, #-0x28]
    // 0x91e868: r1 = Function '<anonymous closure>': static.
    //     0x91e868: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ebe0] AnonymousClosure: static (0x635c70), in [oQi] Xz::<anonymous closure> (0x91e470)
    //     0x91e86c: ldr             x1, [x1, #0xbe0]
    // 0x91e870: stur            x0, [fp, #-0x10]
    // 0x91e874: r0 = AllocateClosure()
    //     0x91e874: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x91e878: ldur            x2, [fp, #-0x28]
    // 0x91e87c: r1 = Function '<anonymous closure>': static.
    //     0x91e87c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ebe8] AnonymousClosure: static (0x635be4), in [oQi] Xz::<anonymous closure> (0x91e470)
    //     0x91e880: ldr             x1, [x1, #0xbe8]
    // 0x91e884: stur            x0, [fp, #-0x18]
    // 0x91e888: r0 = AllocateClosure()
    //     0x91e888: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x91e88c: r16 = <Map<String, dynamic>>
    //     0x91e88c: ldr             x16, [PP, #0x4208]  ; [pp+0x4208] TypeArguments: <Map<String, dynamic>>
    // 0x91e890: ldur            lr, [fp, #-0x10]
    // 0x91e894: stp             lr, x16, [SP, #0x10]
    // 0x91e898: ldur            x16, [fp, #-0x18]
    // 0x91e89c: stp             x0, x16, [SP]
    // 0x91e8a0: r4 = const [0x1, 0x3, 0x3, 0x2, Pqb, 0x2, null]
    //     0x91e8a0: ldr             x4, [PP, #0x18b8]  ; [pp+0x18b8] List(7) [0x1, 0x3, 0x3, 0x2, "Pqb", 0x2, Null]
    // 0x91e8a4: r0 = call 0x7e60e0
    //     0x91e8a4: bl              #0x7e60e0
    // 0x91e8a8: r0 = ReturnAsync()
    //     0x91e8a8: b               #0x8c3a30  ; ReturnAsyncStub
    // 0x91e8ac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91e8ac: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91e8b0: b               #0x91e4a4
    // 0x91e8b4: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x91e8b4: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
    // 0x91e8b8: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x91e8b8: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<List<Y0>> khi<Y0>(dynamic, List<(dynamic) => Future<Y0>>, int) async {
    // ** addr: 0x91e8bc, size: 0x344
    // 0x91e8bc: EnterFrame
    //     0x91e8bc: stp             fp, lr, [SP, #-0x10]!
    //     0x91e8c0: mov             fp, SP
    // 0x91e8c4: AllocStack(0x50)
    //     0x91e8c4: sub             SP, SP, #0x50
    // 0x91e8c8: SetupParameters(dynamic _ /* r1 */, dynamic _ /* r2, fp-0x28 */, dynamic _ /* r3, fp-0x20 */)
    //     0x91e8c8: stur            NULL, [fp, #-8]
    //     0x91e8cc: movz            x0, #0
    //     0x91e8d0: add             x1, fp, w0, sxtw #2
    //     0x91e8d4: ldr             x1, [x1, #0x20]
    //     0x91e8d8: add             x2, fp, w0, sxtw #2
    //     0x91e8dc: ldr             x2, [x2, #0x18]
    //     0x91e8e0: stur            x2, [fp, #-0x28]
    //     0x91e8e4: add             x3, fp, w0, sxtw #2
    //     0x91e8e8: ldr             x3, [x3, #0x10]
    //     0x91e8ec: stur            x3, [fp, #-0x20]
    //     0x91e8f0: ldur            w0, [x1, #0x17]
    //     0x91e8f4: add             x0, x0, HEAP, lsl #32
    //     0x91e8f8: stur            x0, [fp, #-0x18]
    // 0x91e8fc: LoadField: r5 = r4->field_f
    //     0x91e8fc: ldur            w5, [x4, #0xf]
    // 0x91e900: DecompressPointer r5
    //     0x91e900: add             x5, x5, HEAP, lsl #32
    // 0x91e904: cbnz            w5, #0x91e910
    // 0x91e908: r4 = Null
    //     0x91e908: mov             x4, NULL
    // 0x91e90c: b               #0x91e920
    // 0x91e910: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x91e910: ldur            w5, [x4, #0x17]
    // 0x91e914: DecompressPointer r5
    //     0x91e914: add             x5, x5, HEAP, lsl #32
    // 0x91e918: add             x4, fp, w5, sxtw #2
    // 0x91e91c: ldr             x4, [x4, #0x10]
    // 0x91e920: LoadField: r5 = r1->field_f
    //     0x91e920: ldur            w5, [x1, #0xf]
    // 0x91e924: DecompressPointer r5
    //     0x91e924: add             x5, x5, HEAP, lsl #32
    // 0x91e928: ldr             x16, [THR, #0x90]  ; THR::empty_type_arguments
    // 0x91e92c: cmp             w5, w16
    // 0x91e930: b.ne            #0x91e93c
    // 0x91e934: mov             x1, x4
    // 0x91e938: b               #0x91e940
    // 0x91e93c: mov             x1, x5
    // 0x91e940: stur            x1, [fp, #-0x10]
    // 0x91e944: CheckStackOverflow
    //     0x91e944: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91e948: cmp             SP, x16
    //     0x91e94c: b.ls            #0x91ebec
    // 0x91e950: r1 = 5
    //     0x91e950: movz            x1, #0x5
    // 0x91e954: r0 = AllocateContext()
    //     0x91e954: bl              #0x94e988  ; AllocateContextStub
    // 0x91e958: mov             x4, x0
    // 0x91e95c: ldur            x0, [fp, #-0x18]
    // 0x91e960: stur            x4, [fp, #-0x30]
    // 0x91e964: StoreField: r4->field_b = r0
    //     0x91e964: stur            w0, [x4, #0xb]
    // 0x91e968: ldur            x0, [fp, #-0x28]
    // 0x91e96c: StoreField: r4->field_f = r0
    //     0x91e96c: stur            w0, [x4, #0xf]
    // 0x91e970: ldur            x1, [fp, #-0x10]
    // 0x91e974: r2 = Null
    //     0x91e974: mov             x2, NULL
    // 0x91e978: r3 = <List<Y0>>
    //     0x91e978: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ec20] TypeArguments: <List<Y0>>
    //     0x91e97c: ldr             x3, [x3, #0xc20]
    // 0x91e980: r30 = InstantiateTypeArgumentsStub
    //     0x91e980: ldr             lr, [PP, #0x430]  ; [pp+0x430] Stub: InstantiateTypeArguments (0x2c0ef0)
    // 0x91e984: LoadField: r30 = r30->field_7
    //     0x91e984: ldur            lr, [lr, #7]
    // 0x91e988: blr             lr
    // 0x91e98c: mov             x1, x0
    // 0x91e990: stur            x1, [fp, #-0x18]
    // 0x91e994: r0 = InitAsync()
    //     0x91e994: bl              #0x8c1de0  ; InitAsyncStub
    // 0x91e998: ldur            x1, [fp, #-0x10]
    // 0x91e99c: r2 = Null
    //     0x91e99c: mov             x2, NULL
    // 0x91e9a0: r3 = <Y0?>
    //     0x91e9a0: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ec28] TypeArguments: <Y0?>
    //     0x91e9a4: ldr             x3, [x3, #0xc28]
    // 0x91e9a8: r0 = Null
    //     0x91e9a8: mov             x0, NULL
    // 0x91e9ac: cmp             x2, x0
    // 0x91e9b0: b.ne            #0x91e9bc
    // 0x91e9b4: cmp             x1, x0
    // 0x91e9b8: b.eq            #0x91e9c8
    // 0x91e9bc: r30 = InstantiateTypeArgumentsMayShareFunctionTAStub
    //     0x91e9bc: ldr             lr, [PP, #0x2a08]  ; [pp+0x2a08] Stub: InstantiateTypeArgumentsMayShareFunctionTA (0x2c0c28)
    // 0x91e9c0: LoadField: r30 = r30->field_7
    //     0x91e9c0: ldur            lr, [lr, #7]
    // 0x91e9c4: blr             lr
    // 0x91e9c8: mov             x1, x0
    // 0x91e9cc: ldur            x2, [fp, #-0x30]
    // 0x91e9d0: stur            x1, [fp, #-0x18]
    // 0x91e9d4: LoadField: r0 = r2->field_f
    //     0x91e9d4: ldur            w0, [x2, #0xf]
    // 0x91e9d8: DecompressPointer r0
    //     0x91e9d8: add             x0, x0, HEAP, lsl #32
    // 0x91e9dc: r3 = LoadClassIdInstr(r0)
    //     0x91e9dc: ldur            x3, [x0, #-1]
    //     0x91e9e0: ubfx            x3, x3, #0xc, #0x14
    // 0x91e9e4: str             x0, [SP]
    // 0x91e9e8: mov             x0, x3
    // 0x91e9ec: r0 = GDT[cid_x0 + 0x9e28]()
    //     0x91e9ec: movz            x17, #0x9e28
    //     0x91e9f0: add             lr, x0, x17
    //     0x91e9f4: ldr             lr, [x21, lr, lsl #3]
    //     0x91e9f8: blr             lr
    // 0x91e9fc: ldur            x1, [fp, #-0x18]
    // 0x91ea00: mov             x2, x0
    // 0x91ea04: r0 = AllocateArray()
    //     0x91ea04: bl              #0x94fa24  ; AllocateArrayStub
    // 0x91ea08: mov             x1, x0
    // 0x91ea0c: ldur            x2, [fp, #-0x30]
    // 0x91ea10: stur            x1, [fp, #-0x18]
    // 0x91ea14: StoreField: r2->field_13 = r0
    //     0x91ea14: stur            w0, [x2, #0x13]
    //     0x91ea18: ldurb           w16, [x2, #-1]
    //     0x91ea1c: ldurb           w17, [x0, #-1]
    //     0x91ea20: and             x16, x17, x16, lsr #2
    //     0x91ea24: tst             x16, HEAP, lsr #32
    //     0x91ea28: b.eq            #0x91ea30
    //     0x91ea2c: bl              #0x94e168  ; WriteBarrierWrappersStub
    // 0x91ea30: ArrayStore: r2[0] = rZR  ; List_4
    //     0x91ea30: stur            wzr, [x2, #0x17]
    // 0x91ea34: r0 = Object()
    //     0x91ea34: bl              #0x8bea78  ; AllocateObjectStub -> Object (size=0x8)
    // 0x91ea38: ldur            x3, [fp, #-0x30]
    // 0x91ea3c: StoreField: r3->field_1b = r0
    //     0x91ea3c: stur            w0, [x3, #0x1b]
    //     0x91ea40: ldurb           w16, [x3, #-1]
    //     0x91ea44: ldurb           w17, [x0, #-1]
    //     0x91ea48: and             x16, x17, x16, lsr #2
    //     0x91ea4c: tst             x16, HEAP, lsr #32
    //     0x91ea50: b.eq            #0x91ea58
    //     0x91ea54: bl              #0x94e188  ; WriteBarrierWrappersStub
    // 0x91ea58: mov             x2, x3
    // 0x91ea5c: r1 = Function 'mFb': static.
    //     0x91ea5c: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ec30] AnonymousClosure: static (0x91ec00), in [oQi] Xz::khi (0x91e8bc)
    //     0x91ea60: ldr             x1, [x1, #0xc30]
    // 0x91ea64: r0 = AllocateClosure()
    //     0x91ea64: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x91ea68: mov             x4, x0
    // 0x91ea6c: ldur            x3, [fp, #-0x10]
    // 0x91ea70: stur            x4, [fp, #-0x28]
    // 0x91ea74: StoreField: r4->field_b = r3
    //     0x91ea74: stur            w3, [x4, #0xb]
    // 0x91ea78: mov             x0, x4
    // 0x91ea7c: ldur            x5, [fp, #-0x30]
    // 0x91ea80: StoreField: r5->field_1f = r0
    //     0x91ea80: stur            w0, [x5, #0x1f]
    //     0x91ea84: ldurb           w16, [x5, #-1]
    //     0x91ea88: ldurb           w17, [x0, #-1]
    //     0x91ea8c: and             x16, x17, x16, lsr #2
    //     0x91ea90: tst             x16, HEAP, lsr #32
    //     0x91ea94: b.eq            #0x91ea9c
    //     0x91ea98: bl              #0x94e1c8  ; WriteBarrierWrappersStub
    // 0x91ea9c: ldur            x0, [fp, #-0x20]
    // 0x91eaa0: r2 = LoadInt32Instr(r0)
    //     0x91eaa0: sbfx            x2, x0, #1, #0x1f
    //     0x91eaa4: tbz             w0, #0, #0x91eaac
    //     0x91eaa8: ldur            x2, [x0, #7]
    // 0x91eaac: r1 = <Future<void?>>
    //     0x91eaac: ldr             x1, [PP, #0x5dd0]  ; [pp+0x5dd0] TypeArguments: <Future<void?>>
    // 0x91eab0: r0 = call 0x2d76d0
    //     0x91eab0: bl              #0x2d76d0
    // 0x91eab4: mov             x1, x0
    // 0x91eab8: stur            x1, [fp, #-0x20]
    // 0x91eabc: r2 = 0
    //     0x91eabc: movz            x2, #0
    // 0x91eac0: stur            x2, [fp, #-0x38]
    // 0x91eac4: CheckStackOverflow
    //     0x91eac4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91eac8: cmp             SP, x16
    //     0x91eacc: b.ls            #0x91ebf4
    // 0x91ead0: LoadField: r0 = r1->field_b
    //     0x91ead0: ldur            w0, [x1, #0xb]
    // 0x91ead4: DecompressPointer r0
    //     0x91ead4: add             x0, x0, HEAP, lsl #32
    // 0x91ead8: r3 = LoadInt32Instr(r0)
    //     0x91ead8: sbfx            x3, x0, #1, #0x1f
    // 0x91eadc: cmp             x2, x3
    // 0x91eae0: b.ge            #0x91ebb4
    // 0x91eae4: ldur            x16, [fp, #-0x28]
    // 0x91eae8: str             x16, [SP]
    // 0x91eaec: ldur            x0, [fp, #-0x28]
    // 0x91eaf0: ClosureCall
    //     0x91eaf0: ldr             x4, [PP, #0x490]  ; [pp+0x490] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x91eaf4: ldur            x2, [x0, #0x1f]
    //     0x91eaf8: blr             x2
    // 0x91eafc: mov             x3, x0
    // 0x91eb00: r2 = Null
    //     0x91eb00: mov             x2, NULL
    // 0x91eb04: r1 = Null
    //     0x91eb04: mov             x1, NULL
    // 0x91eb08: stur            x3, [fp, #-0x40]
    // 0x91eb0c: r4 = 59
    //     0x91eb0c: movz            x4, #0x3b
    // 0x91eb10: branchIfSmi(r0, 0x91eb1c)
    //     0x91eb10: tbz             w0, #0, #0x91eb1c
    // 0x91eb14: r4 = LoadClassIdInstr(r0)
    //     0x91eb14: ldur            x4, [x0, #-1]
    //     0x91eb18: ubfx            x4, x4, #0xc, #0x14
    // 0x91eb1c: cmp             x4, #0x789
    // 0x91eb20: b.eq            #0x91eb4c
    // 0x91eb24: cmp             x4, #0xa23
    // 0x91eb28: b.eq            #0x91eb4c
    // 0x91eb2c: r17 = 5316
    //     0x91eb2c: movz            x17, #0x14c4
    // 0x91eb30: cmp             x4, x17
    // 0x91eb34: b.eq            #0x91eb4c
    // 0x91eb38: r8 = Future<void?>
    //     0x91eb38: add             x8, PP, #0x2e, lsl #12  ; [pp+0x2ec38] Type: Future<void?>
    //     0x91eb3c: ldr             x8, [x8, #0xc38]
    // 0x91eb40: r3 = Null
    //     0x91eb40: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ec40] Null
    //     0x91eb44: ldr             x3, [x3, #0xc40]
    // 0x91eb48: r0 = Future<void?>()
    //     0x91eb48: bl              #0x8c1e6c  ; IsType_Future<void?>_Stub
    // 0x91eb4c: ldur            x3, [fp, #-0x20]
    // 0x91eb50: LoadField: r0 = r3->field_b
    //     0x91eb50: ldur            w0, [x3, #0xb]
    // 0x91eb54: DecompressPointer r0
    //     0x91eb54: add             x0, x0, HEAP, lsl #32
    // 0x91eb58: r1 = LoadInt32Instr(r0)
    //     0x91eb58: sbfx            x1, x0, #1, #0x1f
    // 0x91eb5c: mov             x0, x1
    // 0x91eb60: ldur            x1, [fp, #-0x38]
    // 0x91eb64: cmp             x1, x0
    // 0x91eb68: b.hs            #0x91ebfc
    // 0x91eb6c: LoadField: r1 = r3->field_f
    //     0x91eb6c: ldur            w1, [x3, #0xf]
    // 0x91eb70: DecompressPointer r1
    //     0x91eb70: add             x1, x1, HEAP, lsl #32
    // 0x91eb74: ldur            x0, [fp, #-0x40]
    // 0x91eb78: ldur            x2, [fp, #-0x38]
    // 0x91eb7c: ArrayStore: r1[r2] = r0  ; List_4
    //     0x91eb7c: add             x25, x1, x2, lsl #2
    //     0x91eb80: add             x25, x25, #0xf
    //     0x91eb84: str             w0, [x25]
    //     0x91eb88: tbz             w0, #0, #0x91eba4
    //     0x91eb8c: ldurb           w16, [x1, #-1]
    //     0x91eb90: ldurb           w17, [x0, #-1]
    //     0x91eb94: and             x16, x17, x16, lsr #2
    //     0x91eb98: tst             x16, HEAP, lsr #32
    //     0x91eb9c: b.eq            #0x91eba4
    //     0x91eba0: bl              #0x94dd2c  ; ArrayWriteBarrierStub
    // 0x91eba4: add             x0, x2, #1
    // 0x91eba8: mov             x2, x0
    // 0x91ebac: mov             x1, x3
    // 0x91ebb0: b               #0x91eac0
    // 0x91ebb4: mov             x3, x1
    // 0x91ebb8: r16 = <void?>
    //     0x91ebb8: ldr             x16, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    // 0x91ebbc: stp             x3, x16, [SP]
    // 0x91ebc0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x91ebc0: ldr             x4, [PP, #0x9e0]  ; [pp+0x9e0] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x91ebc4: r0 = __unknown_function__()
    //     0x91ebc4: bl              #0x8c7b10  ; [] ::__unknown_function__
    // 0x91ebc8: mov             x1, x0
    // 0x91ebcc: stur            x1, [fp, #-0x20]
    // 0x91ebd0: r0 = Await()
    //     0x91ebd0: bl              #0x8c1bb8  ; AwaitStub
    // 0x91ebd4: ldur            x16, [fp, #-0x10]
    // 0x91ebd8: ldur            lr, [fp, #-0x18]
    // 0x91ebdc: stp             lr, x16, [SP]
    // 0x91ebe0: r4 = const [0x1, 0x1, 0x1, 0x1, null]
    //     0x91ebe0: ldr             x4, [PP, #0x9e0]  ; [pp+0x9e0] List(5) [0x1, 0x1, 0x1, 0x1, Null]
    // 0x91ebe4: r0 = call 0x50dc98
    //     0x91ebe4: bl              #0x50dc98
    // 0x91ebe8: r0 = ReturnAsyncNotFuture()
    //     0x91ebe8: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x91ebec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91ebec: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91ebf0: b               #0x91e950
    // 0x91ebf4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91ebf4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91ebf8: b               #0x91ead0
    // 0x91ebfc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x91ebfc: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static Future<void> mFb(dynamic) async {
    // ** addr: 0x91ec00, size: 0x1c0
    // 0x91ec00: EnterFrame
    //     0x91ec00: stp             fp, lr, [SP, #-0x10]!
    //     0x91ec04: mov             fp, SP
    // 0x91ec08: AllocStack(0x50)
    //     0x91ec08: sub             SP, SP, #0x50
    // 0x91ec0c: SetupParameters(dynamic _ /* r1 */)
    //     0x91ec0c: stur            NULL, [fp, #-8]
    //     0x91ec10: movz            x0, #0
    //     0x91ec14: add             x1, fp, w0, sxtw #2
    //     0x91ec18: ldr             x1, [x1, #0x10]
    //     0x91ec1c: ldur            w2, [x1, #0x17]
    //     0x91ec20: add             x2, x2, HEAP, lsl #32
    //     0x91ec24: stur            x2, [fp, #-0x18]
    // 0x91ec28: CheckStackOverflow
    //     0x91ec28: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91ec2c: cmp             SP, x16
    //     0x91ec30: b.ls            #0x91edac
    // 0x91ec34: LoadField: r3 = r1->field_b
    //     0x91ec34: ldur            w3, [x1, #0xb]
    // 0x91ec38: DecompressPointer r3
    //     0x91ec38: add             x3, x3, HEAP, lsl #32
    // 0x91ec3c: stur            x3, [fp, #-0x10]
    // 0x91ec40: InitAsync() -> Future<void?>
    //     0x91ec40: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x91ec44: bl              #0x8c1de0  ; InitAsyncStub
    // 0x91ec48: ldur            x0, [fp, #-0x18]
    // 0x91ec4c: LoadField: r1 = r0->field_13
    //     0x91ec4c: ldur            w1, [x0, #0x13]
    // 0x91ec50: DecompressPointer r1
    //     0x91ec50: add             x1, x1, HEAP, lsl #32
    // 0x91ec54: stur            x1, [fp, #-0x30]
    // 0x91ec58: LoadField: r2 = r1->field_7
    //     0x91ec58: ldur            w2, [x1, #7]
    // 0x91ec5c: DecompressPointer r2
    //     0x91ec5c: add             x2, x2, HEAP, lsl #32
    // 0x91ec60: stur            x2, [fp, #-0x28]
    // 0x91ec64: LoadField: r3 = r1->field_b
    //     0x91ec64: ldur            w3, [x1, #0xb]
    // 0x91ec68: DecompressPointer r3
    //     0x91ec68: add             x3, x3, HEAP, lsl #32
    // 0x91ec6c: r4 = LoadInt32Instr(r3)
    //     0x91ec6c: sbfx            x4, x3, #1, #0x1f
    // 0x91ec70: stur            x4, [fp, #-0x20]
    // 0x91ec74: ldur            x3, [fp, #-0x10]
    // 0x91ec78: CheckStackOverflow
    //     0x91ec78: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91ec7c: cmp             SP, x16
    //     0x91ec80: b.ls            #0x91edb4
    // 0x91ec84: r1 = 2
    //     0x91ec84: movz            x1, #0x2
    // 0x91ec88: r0 = AllocateContext()
    //     0x91ec88: bl              #0x94e988  ; AllocateContextStub
    // 0x91ec8c: mov             x3, x0
    // 0x91ec90: ldur            x0, [fp, #-0x18]
    // 0x91ec94: stur            x3, [fp, #-0x38]
    // 0x91ec98: StoreField: r3->field_b = r0
    //     0x91ec98: stur            w0, [x3, #0xb]
    // 0x91ec9c: StoreField: r3->field_f = rZR
    //     0x91ec9c: stur            wzr, [x3, #0xf]
    // 0x91eca0: mov             x2, x3
    // 0x91eca4: r1 = Function '<anonymous closure>': static.
    //     0x91eca4: add             x1, PP, #0x2e, lsl #12  ; [pp+0x2ec50] AnonymousClosure: static (0x635fb8), in [oQi] Xz::khi (0x91e8bc)
    //     0x91eca8: ldr             x1, [x1, #0xc50]
    // 0x91ecac: r0 = AllocateClosure()
    //     0x91ecac: bl              #0x94ed4c  ; AllocateClosureStub
    // 0x91ecb0: ldur            x1, [fp, #-0x10]
    // 0x91ecb4: StoreField: r0->field_b = r1
    //     0x91ecb4: stur            w1, [x0, #0xb]
    // 0x91ecb8: str             x0, [SP]
    // 0x91ecbc: ClosureCall
    //     0x91ecbc: ldr             x4, [PP, #0x490]  ; [pp+0x490] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x91ecc0: ldur            x2, [x0, #0x1f]
    //     0x91ecc4: blr             x2
    // 0x91ecc8: ldur            x1, [fp, #-0x38]
    // 0x91eccc: LoadField: r0 = r1->field_13
    //     0x91eccc: ldur            w0, [x1, #0x13]
    // 0x91ecd0: DecompressPointer r0
    //     0x91ecd0: add             x0, x0, HEAP, lsl #32
    // 0x91ecd4: cmp             w0, NULL
    // 0x91ecd8: b.eq            #0x91eda4
    // 0x91ecdc: str             x0, [SP]
    // 0x91ece0: ClosureCall
    //     0x91ece0: ldr             x4, [PP, #0x490]  ; [pp+0x490] List(5) [0, 0x1, 0x1, 0x1, Null]
    //     0x91ece4: ldur            x2, [x0, #0x1f]
    //     0x91ece8: blr             x2
    // 0x91ecec: mov             x1, x0
    // 0x91ecf0: stur            x1, [fp, #-0x40]
    // 0x91ecf4: r0 = Await()
    //     0x91ecf4: bl              #0x8c1bb8  ; AwaitStub
    // 0x91ecf8: mov             x3, x0
    // 0x91ecfc: ldur            x0, [fp, #-0x38]
    // 0x91ed00: stur            x3, [fp, #-0x48]
    // 0x91ed04: LoadField: r4 = r0->field_f
    //     0x91ed04: ldur            w4, [x0, #0xf]
    // 0x91ed08: DecompressPointer r4
    //     0x91ed08: add             x4, x4, HEAP, lsl #32
    // 0x91ed0c: mov             x0, x3
    // 0x91ed10: ldur            x2, [fp, #-0x28]
    // 0x91ed14: stur            x4, [fp, #-0x40]
    // 0x91ed18: r1 = Null
    //     0x91ed18: mov             x1, NULL
    // 0x91ed1c: cmp             w2, NULL
    // 0x91ed20: b.eq            #0x91ed40
    // 0x91ed24: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x91ed24: ldur            w4, [x2, #0x17]
    // 0x91ed28: DecompressPointer r4
    //     0x91ed28: add             x4, x4, HEAP, lsl #32
    // 0x91ed2c: r8 = X0
    //     0x91ed2c: ldr             x8, [PP, #0x190]  ; [pp+0x190] TypeParameter: X0
    // 0x91ed30: LoadField: r9 = r4->field_7
    //     0x91ed30: ldur            x9, [x4, #7]
    // 0x91ed34: r3 = Null
    //     0x91ed34: add             x3, PP, #0x2e, lsl #12  ; [pp+0x2ec58] Null
    //     0x91ed38: ldr             x3, [x3, #0xc58]
    // 0x91ed3c: blr             x9
    // 0x91ed40: ldur            x2, [fp, #-0x40]
    // 0x91ed44: r3 = LoadInt32Instr(r2)
    //     0x91ed44: sbfx            x3, x2, #1, #0x1f
    //     0x91ed48: tbz             w2, #0, #0x91ed50
    //     0x91ed4c: ldur            x3, [x2, #7]
    // 0x91ed50: ldur            x0, [fp, #-0x20]
    // 0x91ed54: mov             x1, x3
    // 0x91ed58: cmp             x1, x0
    // 0x91ed5c: b.hs            #0x91edbc
    // 0x91ed60: ldur            x1, [fp, #-0x30]
    // 0x91ed64: ldur            x0, [fp, #-0x48]
    // 0x91ed68: ArrayStore: r1[r3] = r0  ; List_4
    //     0x91ed68: add             x25, x1, x3, lsl #2
    //     0x91ed6c: add             x25, x25, #0xf
    //     0x91ed70: str             w0, [x25]
    //     0x91ed74: tbz             w0, #0, #0x91ed90
    //     0x91ed78: ldurb           w16, [x1, #-1]
    //     0x91ed7c: ldurb           w17, [x0, #-1]
    //     0x91ed80: and             x16, x17, x16, lsr #2
    //     0x91ed84: tst             x16, HEAP, lsr #32
    //     0x91ed88: b.eq            #0x91ed90
    //     0x91ed8c: bl              #0x94dd2c  ; ArrayWriteBarrierStub
    // 0x91ed90: ldur            x0, [fp, #-0x18]
    // 0x91ed94: ldur            x1, [fp, #-0x30]
    // 0x91ed98: ldur            x2, [fp, #-0x28]
    // 0x91ed9c: ldur            x4, [fp, #-0x20]
    // 0x91eda0: b               #0x91ec74
    // 0x91eda4: r0 = Null
    //     0x91eda4: mov             x0, NULL
    // 0x91eda8: r0 = ReturnAsyncNotFuture()
    //     0x91eda8: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x91edac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91edac: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91edb0: b               #0x91ec34
    // 0x91edb4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91edb4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91edb8: b               #0x91ec84
    // 0x91edbc: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x91edbc: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
  }
  [closure] static void jhi(dynamic, Object, (dynamic) => void) {
    // ** addr: 0x636144, size: -0x1
  }
  [closure] static FutureOr<Map<String, dynamic>> <anonymous closure>(dynamic, List<Map<String, dynamic>>) {
    // ** addr: 0x635598, size: -0x1
  }
  [closure] static bool <anonymous closure>(dynamic, Map<String, dynamic>) {
    // ** addr: 0x635a10, size: -0x1
  }
  [closure] static Map<String, dynamic> <anonymous closure>(dynamic, jE<dynamic>) {
    // ** addr: 0x635858, size: -0x1
  }
  [closure] static Map<String, Object> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x6357ec, size: -0x1
  }
  [closure] static Map<String, dynamic> <anonymous closure>(dynamic, jE<dynamic>) {
    // ** addr: 0x635c70, size: -0x1
  }
  [closure] static Map<String, Object> <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x635be4, size: -0x1
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0x635fb8, size: -0x1
  }
}
