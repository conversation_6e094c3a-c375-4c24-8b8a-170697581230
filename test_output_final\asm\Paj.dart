// lib: Gqj, url: paj

// class id: 1049662, size: 0x8
class :: {
}

// class id: 771, size: 0xc, field offset: 0xc
class MUa extends OTa {

  static late final vWa iog; // offset: 0x1248

  [closure] static MUa <anonymous closure>(dynamic) {
    // ** addr: 0x471304, size: -0x1
  }
  [closure] static MUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x47141c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x4712ac, size: -0x1
  }
}
