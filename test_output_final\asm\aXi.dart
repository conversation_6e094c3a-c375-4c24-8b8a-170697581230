// lib: , url: AXi

// class id: 1049071, size: 0x8
class :: {
}

// class id: 2069, size: 0x64, field offset: 0x58
//   transformed mixin,
abstract class _Xca extends iI
     with _Yca {
}

// class id: 2070, size: 0x68, field offset: 0x64
class Vca extends _Xca {
}

// class id: 2071, size: 0x80, field offset: 0x68
class Uca extends Vca {

  [closure] Future<void> <anonymous closure>(dynamic, Ea) async {
    // ** addr: 0x9358f4, size: 0x130
    // 0x9358f4: EnterFrame
    //     0x9358f4: stp             fp, lr, [SP, #-0x10]!
    //     0x9358f8: mov             fp, SP
    // 0x9358fc: AllocStack(0x18)
    //     0x9358fc: sub             SP, SP, #0x18
    // 0x935900: SetupParameters(Uca this /* r1 */)
    //     0x935900: stur            NULL, [fp, #-8]
    //     0x935904: movz            x0, #0
    //     0x935908: add             x1, fp, w0, sxtw #2
    //     0x93590c: ldr             x1, [x1, #0x18]
    //     0x935910: ldur            w2, [x1, #0x17]
    //     0x935914: add             x2, x2, HEAP, lsl #32
    //     0x935918: stur            x2, [fp, #-0x10]
    // 0x93591c: CheckStackOverflow
    //     0x93591c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x935920: cmp             SP, x16
    //     0x935924: b.ls            #0x935a1c
    // 0x935928: InitAsync() -> Future<void?>
    //     0x935928: ldr             x0, [PP, #0x1840]  ; [pp+0x1840] TypeArguments: <void?>
    //     0x93592c: bl              #0x8c1de0  ; InitAsyncStub
    // 0x935930: ldur            x0, [fp, #-0x10]
    // 0x935934: LoadField: r1 = r0->field_f
    //     0x935934: ldur            w1, [x0, #0xf]
    // 0x935938: DecompressPointer r1
    //     0x935938: add             x1, x1, HEAP, lsl #32
    // 0x93593c: LoadField: r2 = r1->field_6f
    //     0x93593c: ldur            w2, [x1, #0x6f]
    // 0x935940: DecompressPointer r2
    //     0x935940: add             x2, x2, HEAP, lsl #32
    // 0x935944: tbz             w2, #4, #0x9359e8
    // 0x935948: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x935948: ldur            w2, [x1, #0x17]
    // 0x93594c: DecompressPointer r2
    //     0x93594c: add             x2, x2, HEAP, lsl #32
    // 0x935950: cmp             w2, NULL
    // 0x935954: b.eq            #0x9359d8
    // 0x935958: LoadField: r3 = r1->field_73
    //     0x935958: ldur            w3, [x1, #0x73]
    // 0x93595c: DecompressPointer r3
    //     0x93595c: add             x3, x3, HEAP, lsl #32
    // 0x935960: stur            x3, [fp, #-0x18]
    // 0x935964: r2 = Instance_er
    //     0x935964: ldr             x2, [PP, #0x2b78]  ; [pp+0x2b78] Obj!er@69e921
    // 0x935968: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x935968: ldr             x4, [PP, #0x2a8]  ; [pp+0x2a8] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x93596c: r0 = call 0x315d4c
    //     0x93596c: bl              #0x315d4c
    // 0x935970: mov             x1, x0
    // 0x935974: ldur            x0, [fp, #-0x18]
    // 0x935978: r2 = LoadClassIdInstr(r0)
    //     0x935978: ldur            x2, [x0, #-1]
    //     0x93597c: ubfx            x2, x2, #0xc, #0x14
    // 0x935980: cmp             x2, #0x735
    // 0x935984: b.eq            #0x9359f0
    // 0x935988: LoadField: r2 = r0->field_27
    //     0x935988: ldur            w2, [x0, #0x27]
    // 0x93598c: DecompressPointer r2
    //     0x93598c: add             x2, x2, HEAP, lsl #32
    // 0x935990: LoadField: r3 = r0->field_7
    //     0x935990: ldur            x3, [x0, #7]
    // 0x935994: LoadField: r5 = r0->field_1b
    //     0x935994: ldur            w5, [x0, #0x1b]
    // 0x935998: DecompressPointer r5
    //     0x935998: add             x5, x5, HEAP, lsl #32
    // 0x93599c: r0 = LoadClassIdInstr(r2)
    //     0x93599c: ldur            x0, [x2, #-1]
    //     0x9359a0: ubfx            x0, x0, #0xc, #0x14
    // 0x9359a4: cmp             x0, #0x731
    // 0x9359a8: b.eq            #0x9359fc
    // 0x9359ac: r0 = LoadClassIdInstr(r2)
    //     0x9359ac: ldur            x0, [x2, #-1]
    //     0x9359b0: ubfx            x0, x0, #0xc, #0x14
    // 0x9359b4: mov             x16, x1
    // 0x9359b8: mov             x1, x2
    // 0x9359bc: mov             x2, x16
    // 0x9359c0: r0 = GDT[cid_x0 + -0xfd8]()
    //     0x9359c0: sub             lr, x0, #0xfd8
    //     0x9359c4: ldr             lr, [x21, lr, lsl #3]
    //     0x9359c8: blr             lr
    // 0x9359cc: mov             x1, x0
    // 0x9359d0: stur            x1, [fp, #-0x18]
    // 0x9359d4: r0 = Await()
    //     0x9359d4: bl              #0x8c1bb8  ; AwaitStub
    // 0x9359d8: ldur            x0, [fp, #-0x10]
    // 0x9359dc: LoadField: r1 = r0->field_f
    //     0x9359dc: ldur            w1, [x0, #0xf]
    // 0x9359e0: DecompressPointer r1
    //     0x9359e0: add             x1, x1, HEAP, lsl #32
    // 0x9359e4: r0 = call 0x73c2a8
    //     0x9359e4: bl              #0x73c2a8
    // 0x9359e8: r0 = Null
    //     0x9359e8: mov             x0, NULL
    // 0x9359ec: r0 = ReturnAsyncNotFuture()
    //     0x9359ec: b               #0x8c1af4  ; ReturnAsyncNotFutureStub
    // 0x9359f0: r0 = "Attempt to execute code removed by Dart AOT compiler (TFA)"
    //     0x9359f0: ldr             x0, [PP, #0x828]  ; [pp+0x828] "Attempt to execute code removed by Dart AOT compiler (TFA)"
    // 0x9359f4: r0 = Throw()
    //     0x9359f4: bl              #0x94dd08  ; ThrowStub
    // 0x9359f8: brk             #0
    // 0x9359fc: r0 = Ha()
    //     0x9359fc: bl              #0x8bc680  ; AllocateHaStub -> Ha (size=0x10)
    // 0x935a00: mov             x1, x0
    // 0x935a04: r0 = "Not supported for hybrid composition."
    //     0x935a04: add             x0, PP, #0x49, lsl #12  ; [pp+0x493b0] "Not supported for hybrid composition."
    //     0x935a08: ldr             x0, [x0, #0x3b0]
    // 0x935a0c: StoreField: r1->field_b = r0
    //     0x935a0c: stur            w0, [x1, #0xb]
    // 0x935a10: mov             x0, x1
    // 0x935a14: r0 = Throw()
    //     0x935a14: bl              #0x94dd08  ; ThrowStub
    // 0x935a18: brk             #0
    // 0x935a1c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x935a1c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x935a20: b               #0x935928
  }
  [closure] void _uae(dynamic, int) {
    // ** addr: 0x363c64, size: -0x1
  }
  [closure] void _aue(dynamic, sca, er) {
    // ** addr: 0x38c6a0, size: -0x1
  }
}

// class id: 2072, size: 0x58, field offset: 0x58
abstract class _Yca extends iI
    implements qaa {
}

// class id: 2575, size: 0x38, field offset: 0x24
class _Wca extends bM {

  late (dynamic, YJ) => Future<void> _Ste; // offset: 0x24
  late Set<bM> _Ade; // offset: 0x34

  [closure] bM <anonymous closure>(dynamic, ZI<bM>) {
    // ** addr: 0x66f07c, size: -0x1
  }
  [closure] void jCc(dynamic, YJ) {
    // ** addr: 0x78c248, size: -0x1
  }
}

// class id: 5520, size: 0x14, field offset: 0x14
enum _Tca extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5521, size: 0x14, field offset: 0x14
enum Sca extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
