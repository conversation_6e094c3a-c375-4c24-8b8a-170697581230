// lib: , url: Kkj

// class id: 1049747, size: 0x8
class :: {
}

// class id: 682, size: 0xc, field offset: 0x8
class ZXa extends Object {

  int dyn:get:length(ZXa) {
    // ** addr: 0x9152c0, size: 0x30
    // 0x9152c0: ldr             x1, [SP]
    // 0x9152c4: LoadField: r2 = r1->field_7
    //     0x9152c4: ldur            w2, [x1, #7]
    // 0x9152c8: DecompressPointer r2
    //     0x9152c8: add             x2, x2, HEAP, lsl #32
    // 0x9152cc: LoadField: r0 = r2->field_13
    //     0x9152cc: ldur            w0, [x2, #0x13]
    // 0x9152d0: DecompressPointer r0
    //     0x9152d0: add             x0, x0, HEAP, lsl #32
    // 0x9152d4: ret
    //     0x9152d4: ret             
  }
  int [](ZXa, int) {
    // ** addr: 0x9152f0, size: 0xa4
    // 0x9152f0: EnterFrame
    //     0x9152f0: stp             fp, lr, [SP, #-0x10]!
    //     0x9152f4: mov             fp, SP
    // 0x9152f8: ldr             x0, [fp, #0x10]
    // 0x9152fc: r2 = Null
    //     0x9152fc: mov             x2, NULL
    // 0x915300: r1 = Null
    //     0x915300: mov             x1, NULL
    // 0x915304: branchIfSmi(r0, 0x91532c)
    //     0x915304: tbz             w0, #0, #0x91532c
    // 0x915308: r4 = LoadClassIdInstr(r0)
    //     0x915308: ldur            x4, [x0, #-1]
    //     0x91530c: ubfx            x4, x4, #0xc, #0x14
    // 0x915310: sub             x4, x4, #0x3b
    // 0x915314: cmp             x4, #1
    // 0x915318: b.ls            #0x91532c
    // 0x91531c: r8 = int
    //     0x91531c: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x915320: r3 = Null
    //     0x915320: add             x3, PP, #0x32, lsl #12  ; [pp+0x32648] Null
    //     0x915324: ldr             x3, [x3, #0x648]
    // 0x915328: r0 = int()
    //     0x915328: bl              #0x9595b0  ; IsType_int_Stub
    // 0x91532c: ldr             x2, [fp, #0x18]
    // 0x915330: LoadField: r3 = r2->field_7
    //     0x915330: ldur            w3, [x2, #7]
    // 0x915334: DecompressPointer r3
    //     0x915334: add             x3, x3, HEAP, lsl #32
    // 0x915338: LoadField: r2 = r3->field_13
    //     0x915338: ldur            w2, [x3, #0x13]
    // 0x91533c: DecompressPointer r2
    //     0x91533c: add             x2, x2, HEAP, lsl #32
    // 0x915340: ldr             x4, [fp, #0x10]
    // 0x915344: r5 = LoadInt32Instr(r4)
    //     0x915344: sbfx            x5, x4, #1, #0x1f
    //     0x915348: tbz             w4, #0, #0x915350
    //     0x91534c: ldur            x5, [x4, #7]
    // 0x915350: r0 = LoadInt32Instr(r2)
    //     0x915350: sbfx            x0, x2, #1, #0x1f
    // 0x915354: mov             x1, x5
    // 0x915358: cmp             x1, x0
    // 0x91535c: b.hs            #0x915378
    // 0x915360: ArrayLoad: r1 = r3[r5]  ; List_1
    //     0x915360: add             x16, x3, x5
    //     0x915364: ldrb            w1, [x16, #0x17]
    // 0x915368: lsl             x0, x1, #1
    // 0x91536c: LeaveFrame
    //     0x91536c: mov             SP, fp
    //     0x915370: ldp             fp, lr, [SP], #0x10
    // 0x915374: ret
    //     0x915374: ret             
    // 0x915378: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x915378: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
  }
}
