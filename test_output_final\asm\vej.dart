// lib: , url: Vej

// class id: 1049454, size: 0x8
class :: {
}

// class id: 999, size: 0x14, field offset: 0x8
class uNa extends Object {

  late Uint8List data; // offset: 0x10

  Uint8List dyn:get:data(uNa) {
    // ** addr: 0x947f40, size: 0x50
    // 0x947f40: EnterFrame
    //     0x947f40: stp             fp, lr, [SP, #-0x10]!
    //     0x947f44: mov             fp, SP
    // 0x947f48: ldr             x1, [fp, #0x10]
    // 0x947f4c: LoadField: r0 = r1->field_f
    //     0x947f4c: ldur            w0, [x1, #0xf]
    // 0x947f50: DecompressPointer r0
    //     0x947f50: add             x0, x0, HEAP, lsl #32
    // 0x947f54: r16 = Sentinel
    //     0x947f54: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x947f58: cmp             w0, w16
    // 0x947f5c: b.eq            #0x947f6c
    // 0x947f60: LeaveFrame
    //     0x947f60: mov             SP, fp
    //     0x947f64: ldp             fp, lr, [SP], #0x10
    // 0x947f68: ret
    //     0x947f68: ret             
    // 0x947f6c: r9 = data
    //     0x947f6c: add             x9, PP, #0x24, lsl #12  ; [pp+0x241c8] Field <uNa.data>: late (offset: 0x10)
    //     0x947f70: ldr             x9, [x9, #0x1c8]
    // 0x947f74: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x947f74: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
}
