// lib: jpj, url: Eij

// class id: 1049611, size: 0x8
class :: {
}

// class id: 861, size: 0x20, field offset: 0x8
class HSa extends IRa {

  static late final vWa iog; // offset: 0x10f0
  late Uint8List _gqg; // offset: 0xc
  late Uint8List _hqg; // offset: 0x10
  late Uint8List _iqg; // offset: 0x14
  late Uint8List _jqg; // offset: 0x18
  late bool _kpg; // offset: 0x1c

  [closure] static (dynamic) => HSa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x483054, size: -0x1
  }
  [closure] static HSa <anonymous closure>(dynamic) {
    // ** addr: 0x4830a8, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x482ff0, size: -0x1
  }
}
