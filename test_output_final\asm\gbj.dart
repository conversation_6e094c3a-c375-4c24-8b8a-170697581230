// lib: , url: Gbj

// class id: 1049299, size: 0x8
class :: {
}

// class id: 1231, size: 0x1c, field offset: 0x8
//   const constructor, 
class sHa extends Object {
}

// class id: 3073, size: 0x24, field offset: 0x14
class _uHa extends Mt<dynamic>
    implements JGa {

  late double TXf; // offset: 0x18
  late double UXf; // offset: 0x1c

  [closure] void rXf(dynamic) {
    // ** addr: 0x648768, size: -0x1
  }
  [closure] void SXf(dynamic) {
    // ** addr: 0x676690, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x676858, size: -0x1
  }
}

// class id: 3074, size: 0x1c, field offset: 0x14
//   transformed mixin,
abstract class _nHa extends Mt<dynamic>
     with WH<X0 bound It> {

  [closure] void _BIc(dynamic) {
    // ** addr: 0x3ea1b8, size: -0x1
  }
}

// class id: 3075, size: 0x20, field offset: 0x1c
//   transformed mixin,
abstract class _oHa extends _nHa
     with Vz<X0 bound It> {
}

// class id: 3076, size: 0x28, field offset: 0x20
class _pHa extends _oHa {

  late lF<er> cYf; // offset: 0x24
  late final RGa controller; // offset: 0x20

  [closure] void fYf(dynamic) {
    // ** addr: 0x648174, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x648210, size: -0x1
  }
}

// class id: 3488, size: 0x14, field offset: 0x10
//   const constructor, 
class _vHa extends VG {
}

// class id: 3489, size: 0x14, field offset: 0x10
//   const constructor, 
class _qHa extends VG {
}

// class id: 3866, size: 0x28, field offset: 0xc
//   const constructor, 
class tHa extends It {
}

// class id: 3867, size: 0x34, field offset: 0xc
//   const constructor, 
class mHa extends It {
}

// class id: 4226, size: 0x18, field offset: 0x10
class _rHa extends wx<dynamic> {
}
