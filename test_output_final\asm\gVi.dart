// lib: Pqj, url: GVi

// class id: 1049671, size: 0x8
class :: {
}

// class id: 762, size: 0xc, field offset: 0xc
class eVa extends OTa {

  static late final vWa iog; // offset: 0x126c

  [closure] static eVa <anonymous closure>(dynamic) {
    // ** addr: 0x470524, size: -0x1
  }
  [closure] static eVa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x47063c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x4704cc, size: -0x1
  }
}
