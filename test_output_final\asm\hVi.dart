// lib: Qqj, url: HVi

// class id: 1049672, size: 0x8
class :: {
}

// class id: 761, size: 0xc, field offset: 0xc
class gVa extends OTa {

  static late final vWa iog; // offset: 0x1270

  [closure] static gVa <anonymous closure>(dynamic) {
    // ** addr: 0x4703ac, size: -0x1
  }
  [closure] static gVa _Psg(dynamic, String, LTa, KTa, za, za, List<int>?) {
    // ** addr: 0x4704ac, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x470354, size: -0x1
  }
}
