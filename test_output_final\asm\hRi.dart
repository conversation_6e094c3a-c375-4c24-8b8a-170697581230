// lib: , url: HRi

// class id: 1048785, size: 0x8
class :: {
}

// class id: 4261, size: 0xc, field offset: 0x8
class YD extends Object {

  void Ij(YD, (dynamic, String, List<String>) => void) {
    // ** addr: 0x8de6b0, size: 0x6c
    // 0x8de6b0: EnterFrame
    //     0x8de6b0: stp             fp, lr, [SP, #-0x10]!
    //     0x8de6b4: mov             fp, SP
    // 0x8de6b8: CheckStackOverflow
    //     0x8de6b8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8de6bc: cmp             SP, x16
    //     0x8de6c0: b.ls            #0x8de6fc
    // 0x8de6c4: ldr             x0, [fp, #0x10]
    // 0x8de6c8: r2 = Null
    //     0x8de6c8: mov             x2, NULL
    // 0x8de6cc: r1 = Null
    //     0x8de6cc: mov             x1, NULL
    // 0x8de6d0: r8 = (dynamic this, String, List<String>) => void?
    //     0x8de6d0: add             x8, PP, #0x20, lsl #12  ; [pp+0x20d60] FunctionType: (dynamic this, String, List<String>) => void?
    //     0x8de6d4: ldr             x8, [x8, #0xd60]
    // 0x8de6d8: r3 = Null
    //     0x8de6d8: add             x3, PP, #0x20, lsl #12  ; [pp+0x20d68] Null
    //     0x8de6dc: ldr             x3, [x3, #0xd68]
    // 0x8de6e0: r0 = DefaultTypeTest()
    //     0x8de6e0: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x8de6e4: ldr             x1, [fp, #0x18]
    // 0x8de6e8: ldr             x2, [fp, #0x10]
    // 0x8de6ec: r0 = call 0x40a108
    //     0x8de6ec: bl              #0x40a108
    // 0x8de6f0: LeaveFrame
    //     0x8de6f0: mov             SP, fp
    //     0x8de6f4: ldp             fp, lr, [SP], #0x10
    // 0x8de6f8: ret
    //     0x8de6f8: ret             
    // 0x8de6fc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8de6fc: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8de700: b               #0x8de6c4
  }
  bool dyn:get:isEmpty(YD) {
    // ** addr: 0x8de71c, size: 0x48
    // 0x8de71c: EnterFrame
    //     0x8de71c: stp             fp, lr, [SP, #-0x10]!
    //     0x8de720: mov             fp, SP
    // 0x8de724: CheckStackOverflow
    //     0x8de724: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8de728: cmp             SP, x16
    //     0x8de72c: b.ls            #0x8de744
    // 0x8de730: ldr             x1, [fp, #0x10]
    // 0x8de734: r0 = call 0x40a2a4
    //     0x8de734: bl              #0x40a2a4
    // 0x8de738: LeaveFrame
    //     0x8de738: mov             SP, fp
    //     0x8de73c: ldp             fp, lr, [SP], #0x10
    // 0x8de740: ret
    //     0x8de740: ret             
    // 0x8de744: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8de744: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8de748: b               #0x8de730
  }
  List<String>? [](YD, String) {
    // ** addr: 0x8de764, size: 0x84
    // 0x8de764: EnterFrame
    //     0x8de764: stp             fp, lr, [SP, #-0x10]!
    //     0x8de768: mov             fp, SP
    // 0x8de76c: CheckStackOverflow
    //     0x8de76c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8de770: cmp             SP, x16
    //     0x8de774: b.ls            #0x8de7c8
    // 0x8de778: ldr             x0, [fp, #0x10]
    // 0x8de77c: r2 = Null
    //     0x8de77c: mov             x2, NULL
    // 0x8de780: r1 = Null
    //     0x8de780: mov             x1, NULL
    // 0x8de784: r4 = 59
    //     0x8de784: movz            x4, #0x3b
    // 0x8de788: branchIfSmi(r0, 0x8de794)
    //     0x8de788: tbz             w0, #0, #0x8de794
    // 0x8de78c: r4 = LoadClassIdInstr(r0)
    //     0x8de78c: ldur            x4, [x0, #-1]
    //     0x8de790: ubfx            x4, x4, #0xc, #0x14
    // 0x8de794: sub             x4, x4, #0x5d
    // 0x8de798: cmp             x4, #1
    // 0x8de79c: b.ls            #0x8de7b0
    // 0x8de7a0: r8 = String
    //     0x8de7a0: ldr             x8, [PP, #0x328]  ; [pp+0x328] Type: String
    // 0x8de7a4: r3 = Null
    //     0x8de7a4: add             x3, PP, #0xe, lsl #12  ; [pp+0xe608] Null
    //     0x8de7a8: ldr             x3, [x3, #0x608]
    // 0x8de7ac: r0 = String()
    //     0x8de7ac: bl              #0x958918  ; IsType_String_Stub
    // 0x8de7b0: ldr             x1, [fp, #0x18]
    // 0x8de7b4: ldr             x2, [fp, #0x10]
    // 0x8de7b8: r0 = call 0x40a220
    //     0x8de7b8: bl              #0x40a220
    // 0x8de7bc: LeaveFrame
    //     0x8de7bc: mov             SP, fp
    //     0x8de7c0: ldp             fp, lr, [SP], #0x10
    // 0x8de7c4: ret
    //     0x8de7c4: ret             
    // 0x8de7c8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8de7c8: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8de7cc: b               #0x8de778
  }
  [closure] Ra<String, List<String>> <anonymous closure>(dynamic, String, List<String>) {
    // ** addr: 0x40de10, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, String, List<String>) {
    // ** addr: 0x70d6b0, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0x70d744, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0x40a19c, size: -0x1
  }
}
