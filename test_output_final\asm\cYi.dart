// lib: , url: CYi

// class id: 1049133, size: 0x8
class :: {
}

// class id: 2807, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class Aia extends _DF {
}

// class id: 2808, size: 0x8, field offset: 0x8
//   const constructor, 
class Eia extends Aia {
}

// class id: 2809, size: 0x8, field offset: 0x8
//   const constructor, 
class Dia extends Aia {
}

// class id: 2810, size: 0x8, field offset: 0x8
//   const constructor, 
class Cia extends Aia {
}

// class id: 2811, size: 0x8, field offset: 0x8
//   const constructor, 
class Bia extends Aia {
}
