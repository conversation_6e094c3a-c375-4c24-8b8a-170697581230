// lib: Iqj, url: fUi

// class id: 1049664, size: 0x8
class :: {
}

// class id: 769, size: 0xc, field offset: 0xc
class QUa extends OTa {

  static late final vWa iog; // offset: 0x1250

  [closure] static QUa <anonymous closure>(dynamic) {
    // ** addr: 0x470fe4, size: -0x1
  }
  [closure] static QUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x4710fc, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x470f8c, size: -0x1
  }
}
