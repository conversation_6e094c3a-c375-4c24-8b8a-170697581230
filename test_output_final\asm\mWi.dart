// lib: , url: MWi

// class id: 1049029, size: 0x8
class :: {
}

// class id: 2279, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class qZ extends Object {

  [closure] static qZ? Jdc(dynamic, qZ?, qZ?, double) {
    // ** addr: 0x71d748, size: -0x1
  }
}

// class id: 2280, size: 0x38, field offset: 0x8
//   const constructor, 
class _sZ extends qZ {

  _Double field_8;
  _Double field_10;
  _Double field_18;
  _Double field_20;
  _Double field_28;
  _Double field_30;
}

// class id: 2281, size: 0x28, field offset: 0x8
//   const constructor, 
class rZ extends qZ {

  _Double field_8;
  _Mint field_10;
  _Double field_18;
  _Mint field_20;

  rZ +(rZ, rZ) {
    // ** addr: 0x912010, size: 0x84
    // 0x912010: EnterFrame
    //     0x912010: stp             fp, lr, [SP, #-0x10]!
    //     0x912014: mov             fp, SP
    // 0x912018: CheckStackOverflow
    //     0x912018: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91201c: cmp             SP, x16
    //     0x912020: b.ls            #0x912074
    // 0x912024: ldr             x0, [fp, #0x10]
    // 0x912028: r2 = Null
    //     0x912028: mov             x2, NULL
    // 0x91202c: r1 = Null
    //     0x91202c: mov             x1, NULL
    // 0x912030: r4 = 59
    //     0x912030: movz            x4, #0x3b
    // 0x912034: branchIfSmi(r0, 0x912040)
    //     0x912034: tbz             w0, #0, #0x912040
    // 0x912038: r4 = LoadClassIdInstr(r0)
    //     0x912038: ldur            x4, [x0, #-1]
    //     0x91203c: ubfx            x4, x4, #0xc, #0x14
    // 0x912040: cmp             x4, #0x8e9
    // 0x912044: b.eq            #0x91205c
    // 0x912048: r8 = rZ
    //     0x912048: add             x8, PP, #0x13, lsl #12  ; [pp+0x13d68] Type: rZ
    //     0x91204c: ldr             x8, [x8, #0xd68]
    // 0x912050: r3 = Null
    //     0x912050: add             x3, PP, #0x13, lsl #12  ; [pp+0x13d70] Null
    //     0x912054: ldr             x3, [x3, #0xd70]
    // 0x912058: r0 = DefaultTypeTest()
    //     0x912058: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x91205c: ldr             x1, [fp, #0x18]
    // 0x912060: ldr             x2, [fp, #0x10]
    // 0x912064: r0 = call 0x5c5434
    //     0x912064: bl              #0x5c5434
    // 0x912068: LeaveFrame
    //     0x912068: mov             SP, fp
    //     0x91206c: ldp             fp, lr, [SP], #0x10
    // 0x912070: ret
    //     0x912070: ret             
    // 0x912074: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x912074: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x912078: b               #0x912024
  }
  rZ -(rZ, rZ) {
    // ** addr: 0x9120a0, size: 0x84
    // 0x9120a0: EnterFrame
    //     0x9120a0: stp             fp, lr, [SP, #-0x10]!
    //     0x9120a4: mov             fp, SP
    // 0x9120a8: CheckStackOverflow
    //     0x9120a8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x9120ac: cmp             SP, x16
    //     0x9120b0: b.ls            #0x912104
    // 0x9120b4: ldr             x0, [fp, #0x10]
    // 0x9120b8: r2 = Null
    //     0x9120b8: mov             x2, NULL
    // 0x9120bc: r1 = Null
    //     0x9120bc: mov             x1, NULL
    // 0x9120c0: r4 = 59
    //     0x9120c0: movz            x4, #0x3b
    // 0x9120c4: branchIfSmi(r0, 0x9120d0)
    //     0x9120c4: tbz             w0, #0, #0x9120d0
    // 0x9120c8: r4 = LoadClassIdInstr(r0)
    //     0x9120c8: ldur            x4, [x0, #-1]
    //     0x9120cc: ubfx            x4, x4, #0xc, #0x14
    // 0x9120d0: cmp             x4, #0x8e9
    // 0x9120d4: b.eq            #0x9120ec
    // 0x9120d8: r8 = rZ
    //     0x9120d8: add             x8, PP, #0x13, lsl #12  ; [pp+0x13d68] Type: rZ
    //     0x9120dc: ldr             x8, [x8, #0xd68]
    // 0x9120e0: r3 = Null
    //     0x9120e0: add             x3, PP, #0x13, lsl #12  ; [pp+0x13d80] Null
    //     0x9120e4: ldr             x3, [x3, #0xd80]
    // 0x9120e8: r0 = DefaultTypeTest()
    //     0x9120e8: bl              #0x94d998  ; DefaultTypeTestStub
    // 0x9120ec: ldr             x1, [fp, #0x18]
    // 0x9120f0: ldr             x2, [fp, #0x10]
    // 0x9120f4: r0 = call 0x5c54b0
    //     0x9120f4: bl              #0x5c54b0
    // 0x9120f8: LeaveFrame
    //     0x9120f8: mov             SP, fp
    //     0x9120fc: ldp             fp, lr, [SP], #0x10
    // 0x912100: ret
    //     0x912100: ret             
    // 0x912104: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x912104: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x912108: b               #0x9120b4
  }
  rZ *(rZ, double) {
    // ** addr: 0x912124, size: 0x50
    // 0x912124: EnterFrame
    //     0x912124: stp             fp, lr, [SP, #-0x10]!
    //     0x912128: mov             fp, SP
    // 0x91212c: CheckStackOverflow
    //     0x91212c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x912130: cmp             SP, x16
    //     0x912134: b.ls            #0x912154
    // 0x912138: ldr             x0, [fp, #0x10]
    // 0x91213c: LoadField: d0 = r0->field_7
    //     0x91213c: ldur            d0, [x0, #7]
    // 0x912140: ldr             x1, [fp, #0x18]
    // 0x912144: r0 = call 0x80f978
    //     0x912144: bl              #0x80f978
    // 0x912148: LeaveFrame
    //     0x912148: mov             SP, fp
    //     0x91214c: ldp             fp, lr, [SP], #0x10
    // 0x912150: ret
    //     0x912150: ret             
    // 0x912154: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x912154: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x912158: b               #0x912138
  }
}

// class id: 2282, size: 0x28, field offset: 0x8
//   const constructor, 
class iu extends qZ {

  _Double field_8;
  _Double field_10;
  _Double field_18;
  _Double field_20;

  iu +(iu, iu) {
    // ** addr: 0x8c7754, size: 0x88
    // 0x8c7754: EnterFrame
    //     0x8c7754: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7758: mov             fp, SP
    // 0x8c775c: CheckStackOverflow
    //     0x8c775c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7760: cmp             SP, x16
    //     0x8c7764: b.ls            #0x8c77bc
    // 0x8c7768: ldr             x0, [fp, #0x10]
    // 0x8c776c: r2 = Null
    //     0x8c776c: mov             x2, NULL
    // 0x8c7770: r1 = Null
    //     0x8c7770: mov             x1, NULL
    // 0x8c7774: r4 = 59
    //     0x8c7774: movz            x4, #0x3b
    // 0x8c7778: branchIfSmi(r0, 0x8c7784)
    //     0x8c7778: tbz             w0, #0, #0x8c7784
    // 0x8c777c: r4 = LoadClassIdInstr(r0)
    //     0x8c777c: ldur            x4, [x0, #-1]
    //     0x8c7780: ubfx            x4, x4, #0xc, #0x14
    // 0x8c7784: sub             x4, x4, #0x8ea
    // 0x8c7788: cmp             x4, #2
    // 0x8c778c: b.ls            #0x8c77a4
    // 0x8c7790: r8 = iu
    //     0x8c7790: add             x8, PP, #0x13, lsl #12  ; [pp+0x13d90] Type: iu
    //     0x8c7794: ldr             x8, [x8, #0xd90]
    // 0x8c7798: r3 = Null
    //     0x8c7798: add             x3, PP, #0x13, lsl #12  ; [pp+0x13d98] Null
    //     0x8c779c: ldr             x3, [x3, #0xd98]
    // 0x8c77a0: r0 = iu()
    //     0x8c77a0: bl              #0x8c789c  ; IsType_iu_Stub
    // 0x8c77a4: ldr             x1, [fp, #0x18]
    // 0x8c77a8: ldr             x2, [fp, #0x10]
    // 0x8c77ac: r0 = call 0x30e7ac
    //     0x8c77ac: bl              #0x30e7ac
    // 0x8c77b0: LeaveFrame
    //     0x8c77b0: mov             SP, fp
    //     0x8c77b4: ldp             fp, lr, [SP], #0x10
    // 0x8c77b8: ret
    //     0x8c77b8: ret             
    // 0x8c77bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c77bc: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c77c0: b               #0x8c7768
  }
  iu -(iu, iu) {
    // ** addr: 0x8c77dc, size: 0x88
    // 0x8c77dc: EnterFrame
    //     0x8c77dc: stp             fp, lr, [SP, #-0x10]!
    //     0x8c77e0: mov             fp, SP
    // 0x8c77e4: CheckStackOverflow
    //     0x8c77e4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c77e8: cmp             SP, x16
    //     0x8c77ec: b.ls            #0x8c7844
    // 0x8c77f0: ldr             x0, [fp, #0x10]
    // 0x8c77f4: r2 = Null
    //     0x8c77f4: mov             x2, NULL
    // 0x8c77f8: r1 = Null
    //     0x8c77f8: mov             x1, NULL
    // 0x8c77fc: r4 = 59
    //     0x8c77fc: movz            x4, #0x3b
    // 0x8c7800: branchIfSmi(r0, 0x8c780c)
    //     0x8c7800: tbz             w0, #0, #0x8c780c
    // 0x8c7804: r4 = LoadClassIdInstr(r0)
    //     0x8c7804: ldur            x4, [x0, #-1]
    //     0x8c7808: ubfx            x4, x4, #0xc, #0x14
    // 0x8c780c: sub             x4, x4, #0x8ea
    // 0x8c7810: cmp             x4, #2
    // 0x8c7814: b.ls            #0x8c782c
    // 0x8c7818: r8 = iu
    //     0x8c7818: add             x8, PP, #0x13, lsl #12  ; [pp+0x13d90] Type: iu
    //     0x8c781c: ldr             x8, [x8, #0xd90]
    // 0x8c7820: r3 = Null
    //     0x8c7820: add             x3, PP, #0x13, lsl #12  ; [pp+0x13da8] Null
    //     0x8c7824: ldr             x3, [x3, #0xda8]
    // 0x8c7828: r0 = iu()
    //     0x8c7828: bl              #0x8c789c  ; IsType_iu_Stub
    // 0x8c782c: ldr             x1, [fp, #0x18]
    // 0x8c7830: ldr             x2, [fp, #0x10]
    // 0x8c7834: r0 = call 0x30e828
    //     0x8c7834: bl              #0x30e828
    // 0x8c7838: LeaveFrame
    //     0x8c7838: mov             SP, fp
    //     0x8c783c: ldp             fp, lr, [SP], #0x10
    // 0x8c7840: ret
    //     0x8c7840: ret             
    // 0x8c7844: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c7844: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c7848: b               #0x8c77f0
  }
  iu *(iu, double) {
    // ** addr: 0x8c7864, size: 0x50
    // 0x8c7864: EnterFrame
    //     0x8c7864: stp             fp, lr, [SP, #-0x10]!
    //     0x8c7868: mov             fp, SP
    // 0x8c786c: CheckStackOverflow
    //     0x8c786c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8c7870: cmp             SP, x16
    //     0x8c7874: b.ls            #0x8c7894
    // 0x8c7878: ldr             x0, [fp, #0x10]
    // 0x8c787c: LoadField: d0 = r0->field_7
    //     0x8c787c: ldur            d0, [x0, #7]
    // 0x8c7880: ldr             x1, [fp, #0x18]
    // 0x8c7884: r0 = call 0x80f90c
    //     0x8c7884: bl              #0x80f90c
    // 0x8c7888: LeaveFrame
    //     0x8c7888: mov             SP, fp
    //     0x8c788c: ldp             fp, lr, [SP], #0x10
    // 0x8c7890: ret
    //     0x8c7890: ret             
    // 0x8c7894: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8c7894: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8c7898: b               #0x8c7878
  }
}
