// lib: , url: PSi

// class id: 1048841, size: 0x8
class :: {
}

// class id: 3264, size: 0x54, field offset: 0x48
class _JH extends KH<dynamic> {

  late yF _XUe; // offset: 0x48

  [closure] void Qgd(dynamic) {
    // ** addr: 0x5bb1fc, size: -0x1
  }
  [closure] Future<void> <anonymous closure>(dynamic, void) {
    // ** addr: 0x5bb360, size: -0x1
  }
}

// class id: 4005, size: 0x7c, field offset: 0x70
//   const constructor, 
class HH extends IH {
}
