// lib: , url: BUi

// class id: 1048926, size: 0x8
class :: {
}

// class id: 2926, size: 0x20, field offset: 0x1c
//   const constructor, 
class _UP extends TP {
}

// class id: 2927, size: 0x20, field offset: 0x1c
//   const constructor, 
class _SP extends TP {
}

// class id: 3780, size: 0x24, field offset: 0xc
//   const constructor, 
class RP extends Kt {
}

// class id: 3781, size: 0x20, field offset: 0xc
//   const constructor, 
class QP extends Kt {
}
