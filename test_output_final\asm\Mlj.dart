// lib: , url: Mlj

// class id: 1049803, size: 0x8
class :: {
}

// class id: 4541, size: 0x40, field offset: 0x40
abstract class uZa extends bX
    implements wZa {
}

// class id: 4544, size: 0x60, field offset: 0x54
//   transformed mixin,
abstract class _tZa extends Xra
     with uZa {

  late final yZa _cjf; // offset: 0x54

  [closure] bool <anonymous closure>(dynamic, pZa) {
    // ** addr: 0x51cb60, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, uZa) {
    // ** addr: 0x51d76c, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, uZa) {
    // ** addr: 0x51d724, size: -0x1
  }
}

// class id: 4545, size: 0x64, field offset: 0x60
class vZa extends _tZa {
}
