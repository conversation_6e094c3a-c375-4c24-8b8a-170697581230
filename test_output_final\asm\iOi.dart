// lib: , url: IOi

// class id: 1048644, size: 0x8
class :: {
}

// class id: 3397, size: 0x44, field offset: 0x30
class _nw extends Wu<dynamic> {

  [closure] Null <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x6ba048, size: -0x1
  }
}

// class id: 4129, size: 0x1c, field offset: 0x10
class mw extends Tu {

  late String? BNb; // offset: 0x14
  late String? MRf; // offset: 0x10
  late String? type; // offset: 0x18
}
