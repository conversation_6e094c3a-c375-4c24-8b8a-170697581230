// lib: Toj, url: QRi

// class id: 1048794, size: 0x8
class :: {
}

// class id: 4244, size: 0xc, field offset: 0x8
class tE extends Object {
}

// class id: 4245, size: 0xc, field offset: 0x8
class qE extends Object {

  [closure] String <anonymous closure>(dynamic, int) {
    // ** addr: 0x45d860, size: -0x1
  }
}

// class id: 4246, size: 0xc, field offset: 0xc
class sE extends qE {

  int dyn:get:length(sE) {
    // ** addr: 0x8ed9a4, size: 0x60
    // 0x8ed9a4: EnterFrame
    //     0x8ed9a4: stp             fp, lr, [SP, #-0x10]!
    //     0x8ed9a8: mov             fp, SP
    // 0x8ed9ac: CheckStackOverflow
    //     0x8ed9ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ed9b0: cmp             SP, x16
    //     0x8ed9b4: b.ls            #0x8ed9e4
    // 0x8ed9b8: ldr             x1, [fp, #0x10]
    // 0x8ed9bc: r0 = call 0x487bbc
    //     0x8ed9bc: bl              #0x487bbc
    // 0x8ed9c0: mov             x2, x0
    // 0x8ed9c4: r0 = BoxInt64Instr(r2)
    //     0x8ed9c4: sbfiz           x0, x2, #1, #0x1f
    //     0x8ed9c8: cmp             x2, x0, asr #1
    //     0x8ed9cc: b.eq            #0x8ed9d8
    //     0x8ed9d0: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8ed9d4: stur            x2, [x0, #7]
    // 0x8ed9d8: LeaveFrame
    //     0x8ed9d8: mov             SP, fp
    //     0x8ed9dc: ldp             fp, lr, [SP], #0x10
    // 0x8ed9e0: ret
    //     0x8ed9e0: ret             
    // 0x8ed9e4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ed9e4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ed9e8: b               #0x8ed9b8
  }
}

// class id: 4247, size: 0xc, field offset: 0xc
class rE extends qE {
}

// class id: 4248, size: 0x1c, field offset: 0x8
class oE extends Object
    implements nE {

  late final KRa _Ycg; // offset: 0x14
}

// class id: 4249, size: 0x8, field offset: 0x8
abstract class nE extends Object {
}

// class id: 5598, size: 0x14, field offset: 0x14
enum pE extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
