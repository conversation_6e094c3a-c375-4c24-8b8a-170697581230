// lib: fpj, url: Fij

// class id: 1049612, size: 0x8
class :: {
}

// class id: 860, size: 0x20, field offset: 0x8
class JSa extends IRa {

  static late final vWa iog; // offset: 0x10e0
  late Uint8List _hpg; // offset: 0x14

  [closure] static (dynamic) => JSa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x483ba4, size: -0x1
  }
  [closure] static JSa <anonymous closure>(dynamic) {
    // ** addr: 0x483bf8, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x483b0c, size: -0x1
  }
}
