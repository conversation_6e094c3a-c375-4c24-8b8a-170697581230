// lib: , url: Foj

// class id: 1049913, size: 0x8
class :: {
}

// class id: 3043, size: 0x20, field offset: 0x14
class _Yjb extends Mt<dynamic> {

  late final Hjb CZg; // offset: 0x18
  late final Neb BZg; // offset: 0x14
  late bool _DZg; // offset: 0x1c

  [closure] void _iXc(dynamic) {
    // ** addr: 0x64abdc, size: -0x1
  }
  [closure] void _KZg(dynamic) {
    // ** addr: 0x64aab8, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x64ab54, size: -0x1
  }
  [closure] FutureOr<hhb> _fuc(dynamic, ihb) {
    // ** addr: 0x6539e8, size: -0x1
  }
  [closure] void _euc(dynamic, ifb) {
    // ** addr: 0x6539ac, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, khb) {
    // ** addr: 0x6531e4, size: -0x1
  }
}

// class id: 3846, size: 0x54, field offset: 0xc
//   const constructor, 
class Xjb extends It
    implements Xjb {
}
