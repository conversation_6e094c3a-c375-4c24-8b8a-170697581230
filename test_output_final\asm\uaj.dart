// lib: , url: Uaj

// class id: 1049251, size: 0x8
class :: {
}

// class id: 3094, size: 0x18, field offset: 0x14
class _Fxa<C1X0> extends Mt<C1X0> {

  late C1X0 value; // offset: 0x14

  [closure] void _Rke(dynamic) {
    // ** addr: 0x53ee8c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x53ef28, size: -0x1
  }
}

// class id: 3882, size: 0x1c, field offset: 0xc
//   const constructor, 
class Exa<X0> extends It {
}
