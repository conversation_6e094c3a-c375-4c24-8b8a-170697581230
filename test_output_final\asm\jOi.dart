// lib: , url: JOi

// class id: 1048645, size: 0x8
class :: {
}

// class id: 3396, size: 0x40, field offset: 0x30
class _pw extends Wu<dynamic> {

  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x48b63c, size: -0x1
  }
  [closure] fla <anonymous closure>(dynamic, int) {
    // ** addr: 0x48a6c4, size: -0x1
  }
  [closure] Future<bool> <anonymous closure>(dynamic) {
    // ** addr: 0x48a254, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x48a330, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x48a650, size: -0x1
  }
}

// class id: 4128, size: 0x14, field offset: 0x10
class ow extends Tu {
}
