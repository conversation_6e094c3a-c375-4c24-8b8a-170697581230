// lib: , url: PRi

// class id: 1048793, size: 0x8
class :: {

  static late final RegExp _Fng; // offset: 0x1074

  [closure] static bool <anonymous closure>(dynamic, String, String) {
    // ** addr: 0x40a7cc, size: -0x1
  }
  [closure] static int <anonymous closure>(dynamic, String) {
    // ** addr: 0x40a760, size: -0x1
  }
  [closure] static void Ldh(dynamic, dynamic, String) {
    // ** addr: 0x424e00, size: -0x1
  }
  [closure] static void <anonymous closure>(dynamic, dynamic, dynamic) {
    // ** addr: 0x425480, size: -0x1
  }
  [closure] static void <anonymous closure>(dynamic) {
    // ** addr: 0x428688, size: -0x1
  }
}
