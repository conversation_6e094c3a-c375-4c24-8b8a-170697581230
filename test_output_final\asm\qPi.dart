// lib: , url: QPi

// class id: 1048702, size: 0x8
class :: {
}

// class id: 4500, size: 0x54, field offset: 0x8
class bz extends Object {

  Map<String, dynamic> WNb(bz) {
    // ** addr: 0x90ec7c, size: 0x48
    // 0x90ec7c: EnterFrame
    //     0x90ec7c: stp             fp, lr, [SP, #-0x10]!
    //     0x90ec80: mov             fp, SP
    // 0x90ec84: CheckStackOverflow
    //     0x90ec84: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90ec88: cmp             SP, x16
    //     0x90ec8c: b.ls            #0x90eca4
    // 0x90ec90: ldr             x1, [fp, #0x10]
    // 0x90ec94: r0 = call 0x5a6e80
    //     0x90ec94: bl              #0x5a6e80
    // 0x90ec98: LeaveFrame
    //     0x90ec98: mov             SP, fp
    //     0x90ec9c: ldp             fp, lr, [SP], #0x10
    // 0x90eca0: ret
    //     0x90eca0: ret             
    // 0x90eca4: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90eca4: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90eca8: b               #0x90ec90
  }
}

// class id: 4501, size: 0x20, field offset: 0x8
class az extends Object {

  Map<String, dynamic> WNb(az) {
    // ** addr: 0x90ebf8, size: 0x48
    // 0x90ebf8: EnterFrame
    //     0x90ebf8: stp             fp, lr, [SP, #-0x10]!
    //     0x90ebfc: mov             fp, SP
    // 0x90ec00: CheckStackOverflow
    //     0x90ec00: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x90ec04: cmp             SP, x16
    //     0x90ec08: b.ls            #0x90ec20
    // 0x90ec0c: ldr             x1, [fp, #0x10]
    // 0x90ec10: r0 = call 0x5a6d2c
    //     0x90ec10: bl              #0x5a6d2c
    // 0x90ec14: LeaveFrame
    //     0x90ec14: mov             SP, fp
    //     0x90ec18: ldp             fp, lr, [SP], #0x10
    // 0x90ec1c: ret
    //     0x90ec1c: ret             
    // 0x90ec20: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90ec20: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x90ec24: b               #0x90ec0c
  }
}
