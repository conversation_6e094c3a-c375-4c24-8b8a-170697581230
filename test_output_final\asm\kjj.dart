// lib: Upj, url: Kjj

// class id: 1049698, size: 0x8
class :: {
}

// class id: 725, size: 0x18, field offset: 0x8
class hWa extends iWa
    implements cSa {

  static late final vWa iog; // offset: 0x11ac
  late int _okb; // offset: 0x14
  late Uint8List _ksf; // offset: 0x10
  late Uint8List _Rxb; // offset: 0xc

  [closure] static (dynamic) => hWa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x460f00, size: -0x1
  }
  [closure] static hWa <anonymous closure>(dynamic) {
    // ** addr: 0x460f54, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x460e68, size: -0x1
  }
}
