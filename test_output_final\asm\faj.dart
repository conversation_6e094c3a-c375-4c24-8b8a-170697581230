// lib: , url: Faj

// class id: 1049233, size: 0x8
class :: {
}

// class id: 2012, size: 0x68, field offset: 0x58
class _Lva extends bfa {
}

// class id: 3564, size: 0x18, field offset: 0x10
//   const constructor, 
class _Jva extends Cva {
}

// class id: 3618, size: 0x10, field offset: 0x10
//   const constructor, 
class _Nva extends Fz {
}

// class id: 3619, size: 0x18, field offset: 0x10
//   const constructor, 
class _Kva extends Fz {
}

// class id: 3726, size: 0x14, field offset: 0xc
//   const constructor, 
class Mva extends Kt {
}

// class id: 3727, size: 0x1c, field offset: 0xc
//   const constructor, 
class Iva extends Kt {
}
