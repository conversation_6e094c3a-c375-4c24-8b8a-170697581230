// lib: , url: iYi

// class id: 1049115, size: 0x8
class :: {

  static late final vha cGe; // offset: 0xb48
}

// class id: 1840, size: 0x8, field offset: 0x8
abstract class _Hha extends Object {
}

// class id: 1841, size: 0x8, field offset: 0x8
class _Jha extends _Hha {
}

// class id: 1842, size: 0x10, field offset: 0x8
class _Iha extends _Hha {
}

// class id: 1843, size: 0x8, field offset: 0x8
abstract class Eha extends Object {
}

// class id: 1844, size: 0x28, field offset: 0x8
abstract class Dha extends Eha {

  [closure] Future<void> EFe(dynamic, YJ) {
    // ** addr: 0x66f1f0, size: -0x1
  }
}

// class id: 1846, size: 0x2c, field offset: 0x28
class Fha extends Dha {
}

// class id: 1847, size: 0x14, field offset: 0x8
//   const constructor, 
class _Cha extends Object {
}

// class id: 1848, size: 0x1c, field offset: 0x8
class _Bha extends Object {

  late (dynamic, er) => er bFe; // offset: 0x14

  [closure] xha <anonymous closure>(dynamic, int) {
    // ** addr: 0x509da8, size: -0x1
  }
  [closure] yha <anonymous closure>(dynamic, int) {
    // ** addr: 0x509d2c, size: -0x1
  }
}

// class id: 1849, size: 0x78, field offset: 0x8
class zha extends Object {

  [closure] List<int> <anonymous closure>(dynamic, xha) {
    // ** addr: 0x509510, size: -0x1
  }
  [closure] List<double> <anonymous closure>(dynamic, yha) {
    // ** addr: 0x5090d8, size: -0x1
  }
}

// class id: 1850, size: 0x50, field offset: 0x8
//   const constructor, 
class yha extends Object {
}

// class id: 1851, size: 0x18, field offset: 0x8
//   const constructor, 
class xha extends Object {
}

// class id: 1852, size: 0xc, field offset: 0x8
class wha extends Object {

  static late final wha _Alc; // offset: 0xb44

  [closure] Future<void> _aGe(dynamic, MethodCall) {
    // ** addr: 0x675340, size: -0x1
  }
}

// class id: 1853, size: 0x10, field offset: 0x8
class vha extends Object {
}

// class id: 5500, size: 0x14, field offset: 0x14
enum _Aha extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
