// lib: , url: RXi

// class id: 1049088, size: 0x8
class :: {
}

// class id: 1937, size: 0x14, field offset: 0x8
//   const constructor, 
class rfa extends Object {
}

// class id: 2008, size: 0x50, field offset: 0x50
abstract class qfa extends Waa {
}

// class id: 2057, size: 0x6c, field offset: 0x58
//   transformed mixin,
abstract class _sfa<X0 bound Xaa> extends iI
     with OX<X0 bound Waa, X1 bound Xaa> {

  [closure] tea? FWd(dynamic, Object?) {
    // ** addr: 0x3ab948, size: -0x1
  }
  [closure] tea? GWd(dynamic, Object?) {
    // ** addr: 0x3ab824, size: -0x1
  }
}

// class id: 2058, size: 0x90, field offset: 0x6c
abstract class ufa<X0 bound Xaa> extends _sfa<X0 bound Xaa>
    implements qfa {

  [closure] void zDc(dynamic, {Waa? ADc, fr? Wbc, Ea orb, pF BDc}) {
    // ** addr: 0x3c0288, size: -0x1
  }
  [closure] void _AWd(dynamic, sca, er) {
    // ** addr: 0x38d610, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, tea) {
    // ** addr: 0x394bac, size: -0x1
  }
  [closure] double ABc(dynamic, double) {
    // ** addr: 0x79f93c, size: -0x1
  }
}

// class id: 2059, size: 0x9c, field offset: 0x90
class wfa extends ufa<dynamic> {

  late double _umd; // offset: 0x90
  late double _Owe; // offset: 0x94
}

// class id: 2060, size: 0xa8, field offset: 0x90
class vfa extends ufa<dynamic> {

  late double _tmd; // offset: 0x9c
  late double _umd; // offset: 0xa0
}

// class id: 5509, size: 0x14, field offset: 0x14
enum pfa extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
