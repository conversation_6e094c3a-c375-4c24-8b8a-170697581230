// lib: Tpj, url: Jjj

// class id: 1049697, size: 0x8
class :: {
}

// class id: 726, size: 0x18, field offset: 0x8
class fWa extends Object
    implements cSa {

  static late final vWa iog; // offset: 0x11a8
  late hWa _HGb; // offset: 0x8
  late int _ssg; // offset: 0x14

  [closure] static (dynamic) => fWa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x4610b8, size: -0x1
  }
  [closure] static fWa <anonymous closure>(dynamic) {
    // ** addr: 0x46110c, size: -0x1
  }
  [closure] za <anonymous closure>(dynamic) {
    // ** addr: 0x8ada38, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x461020, size: -0x1
  }
}
