// lib: , url: WPi

// class id: 1048708, size: 0x8
class :: {
}

// class id: 3349, size: 0x3c, field offset: 0x30
class _nz extends Wu<dynamic> {

  [closure] String <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x50721c, size: -0x1
  }
  [closure] kz <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x5071dc, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, dynamic) {
    // ** addr: 0x6bf4ac, size: -0x1
  }
}

// class id: 4085, size: 0x10, field offset: 0x10
class mz extends Tu {
}
