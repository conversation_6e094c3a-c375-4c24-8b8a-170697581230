// lib: Ppj, url: Cjj

// class id: 1049690, size: 0x8
class :: {
}

// class id: 735, size: 0x10, field offset: 0x8
class PVa extends QVa {

  static late final vWa iog; // offset: 0x1198

  [closure] static (dynamic) => PVa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x4619d0, size: -0x1
  }
  [closure] static PVa <anonymous closure>(dynamic) {
    // ** addr: 0x461a24, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x461938, size: -0x1
  }
}
