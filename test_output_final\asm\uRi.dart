// lib: , url: uRi

// class id: 1048775, size: 0x8
class :: {
}

// class id: 4283, size: 0x8, field offset: 0x8
class lD extends Object {
}

// class id: 4284, size: 0xc, field offset: 0x8
abstract class LB extends Object {
}

// class id: 4285, size: 0x14, field offset: 0xc
abstract class sD extends LB {
}

// class id: 4286, size: 0x14, field offset: 0x14
class yD extends sD {
}

// class id: 4287, size: 0x14, field offset: 0x14
class xD extends sD {
}

// class id: 4288, size: 0x14, field offset: 0x14
abstract class uD extends sD {
}

// class id: 4289, size: 0x14, field offset: 0x14
class zD extends uD {
}

// class id: 4290, size: 0x14, field offset: 0x14
class wD extends uD {
}

// class id: 4291, size: 0x14, field offset: 0x14
class vD extends uD {
}

// class id: 4292, size: 0x18, field offset: 0x14
class tD extends sD {
}

// class id: 4293, size: 0x10, field offset: 0xc
class OC extends LB {
}

// class id: 4294, size: 0x10, field offset: 0x10
class PC extends OC {
}

// class id: 4295, size: 0x1c, field offset: 0xc
class KC extends LB {
}

// class id: 4296, size: 0x20, field offset: 0x1c
class NC extends KC {
}

// class id: 4297, size: 0x20, field offset: 0x1c
class MC extends KC {
}

// class id: 4298, size: 0x1c, field offset: 0x1c
class LC extends KC {
}

// class id: 4299, size: 0x10, field offset: 0xc
class uC extends LB {
}

// class id: 4300, size: 0x10, field offset: 0xc
class tC extends LB {
}

// class id: 4301, size: 0xc, field offset: 0xc
abstract class mC extends LB {
}

// class id: 4302, size: 0x10, field offset: 0xc
class qC extends mC {
}

// class id: 4303, size: 0x10, field offset: 0xc
class pC extends mC {
}

// class id: 4304, size: 0x10, field offset: 0xc
class oC extends mC {
}

// class id: 4305, size: 0x10, field offset: 0xc
class nC extends mC {
}

// class id: 4306, size: 0xc, field offset: 0xc
abstract class jC extends LB {
}

// class id: 4307, size: 0x10, field offset: 0xc
class JC extends jC {
}

// class id: 4308, size: 0xc, field offset: 0xc
abstract class GC extends jC {
}

// class id: 4309, size: 0x10, field offset: 0xc
class IC extends GC {
}

// class id: 4310, size: 0x10, field offset: 0xc
class HC extends GC {
}

// class id: 4311, size: 0x10, field offset: 0xc
class FC extends jC {
}

// class id: 4312, size: 0xc, field offset: 0xc
class EC extends jC {
}

// class id: 4314, size: 0x10, field offset: 0xc
class CC extends jC {
}

// class id: 4315, size: 0x14, field offset: 0xc
class zC extends jC {
}

// class id: 4316, size: 0xc, field offset: 0xc
class yC extends jC {
}

// class id: 4317, size: 0x10, field offset: 0xc
class xC extends jC {
}

// class id: 4318, size: 0x10, field offset: 0xc
class wC extends jC {
}

// class id: 4319, size: 0x14, field offset: 0xc
class vC extends jC {
}

// class id: 4320, size: 0x10, field offset: 0xc
class sC extends jC {
}

// class id: 4321, size: 0x10, field offset: 0xc
class rC extends jC {
}

// class id: 4322, size: 0x14, field offset: 0xc
class lC extends jC {
}

// class id: 4323, size: 0x14, field offset: 0xc
class kC extends jC {
}

// class id: 4324, size: 0xc, field offset: 0xc
abstract class hC extends LB {
}

// class id: 4325, size: 0x14, field offset: 0xc
class iC extends hC {
}

// class id: 4326, size: 0x10, field offset: 0xc
class gC extends LB {
}

// class id: 4327, size: 0x10, field offset: 0xc
class eC extends LB {
}

// class id: 4328, size: 0x10, field offset: 0xc
abstract class UB extends LB {

  String dyn:get:name(UB) {
    // ** addr: 0x902450, size: 0x6c
    // 0x902450: EnterFrame
    //     0x902450: stp             fp, lr, [SP, #-0x10]!
    //     0x902454: mov             fp, SP
    // 0x902458: AllocStack(0x8)
    //     0x902458: sub             SP, SP, #8
    // 0x90245c: CheckStackOverflow
    //     0x90245c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x902460: cmp             SP, x16
    //     0x902464: b.ls            #0x90249c
    // 0x902468: ldr             x0, [fp, #0x10]
    // 0x90246c: LoadField: r1 = r0->field_b
    //     0x90246c: ldur            w1, [x0, #0xb]
    // 0x902470: DecompressPointer r1
    //     0x902470: add             x1, x1, HEAP, lsl #32
    // 0x902474: str             x1, [SP]
    // 0x902478: r4 = 0
    //     0x902478: movz            x4, #0
    // 0x90247c: ldr             x0, [SP]
    // 0x902480: r16 = UnlinkedCall_0x2d3c80
    //     0x902480: add             x16, PP, #0x37, lsl #12  ; [pp+0x37ae0] UnlinkedCall: 0x2d3c80 - SwitchableCallMissStub
    //     0x902484: add             x16, x16, #0xae0
    // 0x902488: ldp             x5, lr, [x16]
    // 0x90248c: blr             lr
    // 0x902490: LeaveFrame
    //     0x902490: mov             SP, fp
    //     0x902494: ldp             fp, lr, [SP], #0x10
    // 0x902498: ret
    //     0x902498: ret             
    // 0x90249c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x90249c: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x9024a0: b               #0x902468
  }
}

// class id: 4329, size: 0x14, field offset: 0x10
class fC extends UB {
}

// class id: 4330, size: 0x14, field offset: 0x10
class bC extends UB {
}

// class id: 4331, size: 0x14, field offset: 0x14
class dC extends bC {
}

// class id: 4332, size: 0x10, field offset: 0x10
class aC extends UB {
}

// class id: 4333, size: 0x14, field offset: 0x10
class cC extends aC {
}

// class id: 4334, size: 0x10, field offset: 0x10
class ZB extends UB {
}

// class id: 4335, size: 0x10, field offset: 0x10
class YB extends UB {
}

// class id: 4336, size: 0x1c, field offset: 0x10
class XB extends UB {
}

// class id: 4337, size: 0x14, field offset: 0x10
class WB extends UB {
}

// class id: 4338, size: 0x10, field offset: 0x10
class VB extends UB {
}

// class id: 4339, size: 0x18, field offset: 0xc
class TB extends LB {
}

// class id: 4340, size: 0x10, field offset: 0xc
class SB extends LB {
}

// class id: 4341, size: 0x10, field offset: 0xc
class RB extends LB {
}

// class id: 4342, size: 0xc, field offset: 0xc
abstract class BC extends LB {
}

// class id: 4343, size: 0x10, field offset: 0xc
class rD extends BC {
}

// class id: 4344, size: 0x10, field offset: 0xc
class pD extends BC {
}

// class id: 4345, size: 0x10, field offset: 0xc
class VC extends BC {
}

// class id: 4346, size: 0xc, field offset: 0xc
class UC extends BC {
}

// class id: 4347, size: 0xc, field offset: 0xc
class TC extends BC {
}

// class id: 4348, size: 0xc, field offset: 0xc
class SC extends BC {
}

// class id: 4349, size: 0xc, field offset: 0xc
class RC extends BC {
}

// class id: 4350, size: 0x10, field offset: 0xc
class QC extends BC {
}

// class id: 4351, size: 0x14, field offset: 0xc
class AC extends BC {
}

// class id: 4352, size: 0x14, field offset: 0xc
class QB extends BC {
}

// class id: 4353, size: 0x14, field offset: 0x14
class oD extends QB {
}

// class id: 4354, size: 0x18, field offset: 0x14
class nD extends QB {
}

// class id: 4355, size: 0x14, field offset: 0x14
class mD extends QB {
}

// class id: 4356, size: 0x14, field offset: 0x14
class gD extends QB {
}

// class id: 4357, size: 0x14, field offset: 0x14
class fD extends QB {
}

// class id: 4358, size: 0x14, field offset: 0x14
class bD extends QB {
}

// class id: 4359, size: 0x14, field offset: 0x14
class aD extends QB {
}

// class id: 4360, size: 0x14, field offset: 0x14
class ZC extends QB {
}

// class id: 4361, size: 0x1c, field offset: 0x14
abstract class XC extends QB {
}

// class id: 4362, size: 0x1c, field offset: 0x1c
class kD extends XC {
}

// class id: 4363, size: 0x1c, field offset: 0x1c
class jD extends XC {
}

// class id: 4364, size: 0x1c, field offset: 0x1c
class iD extends XC {
}

// class id: 4365, size: 0x1c, field offset: 0x1c
class hD extends XC {
}

// class id: 4366, size: 0x1c, field offset: 0x1c
class eD extends XC {
}

// class id: 4367, size: 0x1c, field offset: 0x1c
class dD extends XC {
}

// class id: 4368, size: 0x1c, field offset: 0x1c
class cD extends XC {
}

// class id: 4369, size: 0x1c, field offset: 0x1c
class YC extends XC {
}

// class id: 4370, size: 0x14, field offset: 0x14
class WC extends QB {
}

// class id: 4371, size: 0x14, field offset: 0x14
class qD extends WC {
}

// class id: 4372, size: 0x18, field offset: 0x14
class PB extends QB {
}

// class id: 4373, size: 0xc, field offset: 0xc
class OB extends LB {

  String dyn:get:name(OB) {
    // ** addr: 0x902714, size: 0x24
    // 0x902714: r0 = "not"
    //     0x902714: add             x0, PP, #0x2c, lsl #12  ; [pp+0x2c648] "not"
    //     0x902718: ldr             x0, [x0, #0x648]
    // 0x90271c: ret
    //     0x90271c: ret             
  }
}

// class id: 4374, size: 0xc, field offset: 0xc
class NB extends LB {

  String dyn:get:name(NB) {
    // ** addr: 0x9027bc, size: 0x20
    // 0x9027bc: r0 = "&"
    //     0x9027bc: ldr             x0, [PP, #0x1178]  ; [pp+0x1178] "&"
    // 0x9027c0: ret
    //     0x9027c0: ret             
  }
}

// class id: 4375, size: 0xc, field offset: 0xc
class MB extends LB {

  String dyn:get:name(MB) {
    // ** addr: 0x902780, size: 0x24
    // 0x902780: r0 = "*"
    //     0x902780: add             x0, PP, #0x18, lsl #12  ; [pp+0x185a0] "*"
    //     0x902784: ldr             x0, [x0, #0x5a0]
    // 0x902788: ret
    //     0x902788: ret             
  }
}

// class id: 4376, size: 0x10, field offset: 0xc
class KB extends LB {
}

// class id: 4377, size: 0x8, field offset: 0x8
abstract class JB extends Object
    implements IB {
}

// class id: 4380, size: 0x8, field offset: 0x8
abstract class IB extends Object {
}
