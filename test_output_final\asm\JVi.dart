// lib: lrj, url: jVi

// class id: 1049679, size: 0x8
class :: {
}

// class id: 750, size: 0x18, field offset: 0x10
class LTa extends uVa {
}

// class id: 752, size: 0x18, field offset: 0x18
class KTa extends tVa {

  KTa? -(KTa, KTa) {
    // ** addr: 0x8ecb90, size: 0x4c
    // 0x8ecb90: EnterFrame
    //     0x8ecb90: stp             fp, lr, [SP, #-0x10]!
    //     0x8ecb94: mov             fp, SP
    // 0x8ecb98: CheckStackOverflow
    //     0x8ecb98: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ecb9c: cmp             SP, x16
    //     0x8ecba0: b.ls            #0x8ecbbc
    // 0x8ecba4: ldr             x1, [fp, #0x18]
    // 0x8ecba8: ldr             x2, [fp, #0x10]
    // 0x8ecbac: r0 = call 0x4642b0
    //     0x8ecbac: bl              #0x4642b0
    // 0x8ecbb0: LeaveFrame
    //     0x8ecbb0: mov             SP, fp
    //     0x8ecbb4: ldp             fp, lr, [SP], #0x10
    // 0x8ecbb8: ret
    //     0x8ecbb8: ret             
    // 0x8ecbbc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ecbbc: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ecbc0: b               #0x8ecba4
  }
  KTa? +(KTa, KTa?) {
    // ** addr: 0x8ed2a4, size: 0x4c
    // 0x8ed2a4: EnterFrame
    //     0x8ed2a4: stp             fp, lr, [SP, #-0x10]!
    //     0x8ed2a8: mov             fp, SP
    // 0x8ed2ac: CheckStackOverflow
    //     0x8ed2ac: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ed2b0: cmp             SP, x16
    //     0x8ed2b4: b.ls            #0x8ed2d0
    // 0x8ed2b8: ldr             x1, [fp, #0x18]
    // 0x8ed2bc: ldr             x2, [fp, #0x10]
    // 0x8ed2c0: r0 = call 0x464368
    //     0x8ed2c0: bl              #0x464368
    // 0x8ed2c4: LeaveFrame
    //     0x8ed2c4: mov             SP, fp
    //     0x8ed2c8: ldp             fp, lr, [SP], #0x10
    // 0x8ed2cc: ret
    //     0x8ed2cc: ret             
    // 0x8ed2d0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ed2d0: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ed2d4: b               #0x8ed2b8
  }
}

// class id: 754, size: 0x10, field offset: 0x8
class JTa extends sVa {

  JTa -(JTa, JTa) {
    // ** addr: 0x8ecbe8, size: 0x4c
    // 0x8ecbe8: EnterFrame
    //     0x8ecbe8: stp             fp, lr, [SP, #-0x10]!
    //     0x8ecbec: mov             fp, SP
    // 0x8ecbf0: CheckStackOverflow
    //     0x8ecbf0: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ecbf4: cmp             SP, x16
    //     0x8ecbf8: b.ls            #0x8ecc14
    // 0x8ecbfc: ldr             x1, [fp, #0x18]
    // 0x8ecc00: ldr             x2, [fp, #0x10]
    // 0x8ecc04: r0 = call 0x46d3fc
    //     0x8ecc04: bl              #0x46d3fc
    // 0x8ecc08: LeaveFrame
    //     0x8ecc08: mov             SP, fp
    //     0x8ecc0c: ldp             fp, lr, [SP], #0x10
    // 0x8ecc10: ret
    //     0x8ecc10: ret             
    // 0x8ecc14: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ecc14: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ecc18: b               #0x8ecbfc
  }
  JTa *(JTa, JTa) {
    // ** addr: 0x8ecc34, size: 0x4c
    // 0x8ecc34: EnterFrame
    //     0x8ecc34: stp             fp, lr, [SP, #-0x10]!
    //     0x8ecc38: mov             fp, SP
    // 0x8ecc3c: CheckStackOverflow
    //     0x8ecc3c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ecc40: cmp             SP, x16
    //     0x8ecc44: b.ls            #0x8ecc60
    // 0x8ecc48: ldr             x1, [fp, #0x18]
    // 0x8ecc4c: ldr             x2, [fp, #0x10]
    // 0x8ecc50: r0 = call 0x4645f8
    //     0x8ecc50: bl              #0x4645f8
    // 0x8ecc54: LeaveFrame
    //     0x8ecc54: mov             SP, fp
    //     0x8ecc58: ldp             fp, lr, [SP], #0x10
    // 0x8ecc5c: ret
    //     0x8ecc5c: ret             
    // 0x8ecc60: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8ecc60: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8ecc64: b               #0x8ecc48
  }
  JTa +(JTa, JTa) {
    // ** addr: 0x8ecc80, size: 0x4c
    // 0x8ecc80: EnterFrame
    //     0x8ecc80: stp             fp, lr, [SP, #-0x10]!
    //     0x8ecc84: mov             fp, SP
    // 0x8ecc88: CheckStackOverflow
    //     0x8ecc88: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8ecc8c: cmp             SP, x16
    //     0x8ecc90: b.ls            #0x8eccac
    // 0x8ecc94: ldr             x1, [fp, #0x18]
    // 0x8ecc98: ldr             x2, [fp, #0x10]
    // 0x8ecc9c: r0 = call 0x4647a8
    //     0x8ecc9c: bl              #0x4647a8
    // 0x8ecca0: LeaveFrame
    //     0x8ecca0: mov             SP, fp
    //     0x8ecca4: ldp             fp, lr, [SP], #0x10
    // 0x8ecca8: ret
    //     0x8ecca8: ret             
    // 0x8eccac: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8eccac: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8eccb0: b               #0x8ecc94
  }
}
