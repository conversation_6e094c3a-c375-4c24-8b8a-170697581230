// lib: , url: Aaj

// class id: 1049228, size: 0x8
class :: {
}

// class id: 3106, size: 0x18, field offset: 0x14
class _cva extends Mt<dynamic> {

  late Map<Object, Object?> data; // offset: 0x14

  Map<Object, Object?> dyn:get:data(_cva) {
    // ** addr: 0x91422c, size: 0x50
    // 0x91422c: EnterFrame
    //     0x91422c: stp             fp, lr, [SP, #-0x10]!
    //     0x914230: mov             fp, SP
    // 0x914234: ldr             x1, [fp, #0x10]
    // 0x914238: LoadField: r0 = r1->field_13
    //     0x914238: ldur            w0, [x1, #0x13]
    // 0x91423c: DecompressPointer r0
    //     0x91423c: add             x0, x0, HEAP, lsl #32
    // 0x914240: r16 = Sentinel
    //     0x914240: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x914244: cmp             w0, w16
    // 0x914248: b.ne            #0x914258
    // 0x91424c: r2 = data
    //     0x91424c: add             x2, PP, #0x3f, lsl #12  ; [pp+0x3f570] Field <<EMAIL>>: late (offset: 0x14)
    //     0x914250: ldr             x2, [x2, #0x570]
    // 0x914254: r0 = InitLateInstanceField()
    //     0x914254: bl              #0x94db08  ; InitLateInstanceFieldStub
    // 0x914258: LeaveFrame
    //     0x914258: mov             SP, fp
    //     0x91425c: ldp             fp, lr, [SP], #0x10
    // 0x914260: ret
    //     0x914260: ret             
  }
}

// class id: 3514, size: 0x18, field offset: 0x14
class _dva extends epa<dynamic> {
}

// class id: 3890, size: 0x10, field offset: 0xc
//   const constructor, 
class bva extends It {
}
