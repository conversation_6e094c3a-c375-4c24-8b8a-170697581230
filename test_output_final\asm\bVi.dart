// lib: , url: BVi

// class id: 1048972, size: 0x8
class :: {
}

// class id: 2343, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class HU extends Object {
}

// class id: 2344, size: 0x8, field offset: 0x8
//   const constructor, 
class JU extends HU {
}

// class id: 2345, size: 0x10, field offset: 0x8
//   const constructor, 
class IU extends HU {

  bool field_8;
  bool field_c;
}

// class id: 2899, size: 0xc, field offset: 0x8
//   const constructor, 
class KU extends _DF {

  _ConstMap<NJ, HU> field_8;

  [closure] HU? <anonymous closure>(dynamic, NJ) {
    // ** addr: 0x6e9cac, size: -0x1
  }
}

// class id: 3200, size: 0x18, field offset: 0x14
class _MU<C1X0> extends Mt<C1X0> {
}

// class id: 3201, size: 0x20, field offset: 0x14
//   transformed mixin,
abstract class _FU extends Mt<dynamic>
     with _CU<X0 bound It> {

  late lF<double> Nbd; // offset: 0x1c
  late lF<double> Mbd; // offset: 0x18

  [closure] void Obd(dynamic) {
    // ** addr: 0x5264cc, size: -0x1
  }
  [closure] void Pbd(dynamic, kF) {
    // ** addr: 0x5263a8, size: -0x1
  }
}

// class id: 3202, size: 0x24, field offset: 0x20
class _GU extends _FU {

  late _PU Fbd; // offset: 0x20
  static late final jF<double> _Tbd; // offset: 0x8ec
  static late final jF<double> _Hbd; // offset: 0x8f4
  static late final jF<double> _Ibd; // offset: 0x8f0
}

// class id: 3203, size: 0x20, field offset: 0x14
//   transformed mixin,
abstract class _BU extends Mt<dynamic>
     with _CU<X0 bound It> {

  late lF<double> Nbd; // offset: 0x1c
  late lF<double> Mbd; // offset: 0x18

  [closure] void Obd(dynamic) {
    // ** addr: 0x525a00, size: -0x1
  }
  [closure] void Pbd(dynamic, kF) {
    // ** addr: 0x52591c, size: -0x1
  }
}

// class id: 3204, size: 0x24, field offset: 0x20
class _DU extends _BU {

  late _NU Fbd; // offset: 0x20
  static late final jF<double> _Gbd; // offset: 0x8dc
  static late final jF<double> _Hbd; // offset: 0x8e0
  static late final jF<double> _Ibd; // offset: 0x8e4
  static late final jF<double?> _Jbd; // offset: 0x8e8
}

// class id: 3205, size: 0x14, field offset: 0x14
abstract class _CU<X0 bound It> extends Mt<X0 bound It> {
}

// class id: 3769, size: 0x20, field offset: 0xc
//   const constructor, 
class _zU extends Kt {

  static late final uG<double> _Vbd; // offset: 0x8fc
  static late final List<vG<double>> Ubd; // offset: 0x8f8

  [closure] _AU <anonymous closure>(dynamic, aoa, lF<double>, pI?) {
    // ** addr: 0x683e18, size: -0x1
  }
  [closure] _EU <anonymous closure>(dynamic, aoa, lF<double>, pI?) {
    // ** addr: 0x683de0, size: -0x1
  }
  [closure] _AU <anonymous closure>(dynamic, aoa, lF<double>, pI?) {
    // ** addr: 0x683da8, size: -0x1
  }
  [closure] _EU <anonymous closure>(dynamic, aoa, lF<double>, pI?) {
    // ** addr: 0x683d74, size: -0x1
  }
}

// class id: 3957, size: 0x24, field offset: 0xc
//   const constructor, 
class _LU<X0> extends It {
}

// class id: 3958, size: 0x1c, field offset: 0xc
//   const constructor, 
class _EU extends It {
}

// class id: 3959, size: 0x1c, field offset: 0xc
//   const constructor, 
class _AU extends It {
}

// class id: 4561, size: 0x40, field offset: 0x24
class _PU extends OU {

  [closure] void _Bbd(dynamic, dynamic) {
    // ** addr: 0x525078, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, sca, er) {
    // ** addr: 0x7eb860, size: -0x1
  }
}

// class id: 4562, size: 0x40, field offset: 0x24
class _NU extends OU {

  [closure] void <anonymous closure>(dynamic, sca, er) {
    // ** addr: 0x7eb4f4, size: -0x1
  }
}
