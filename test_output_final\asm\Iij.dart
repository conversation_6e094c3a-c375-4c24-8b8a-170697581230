// lib: kpj, url: Iij

// class id: 1049615, size: 0x8
class :: {

  static late final AWa _oqg; // offset: 0x10f8
}

// class id: 825, size: 0x24, field offset: 0x8
class OSa extends PSa
    implements dB {

  static late final vWa iog; // offset: 0x10f4

  [closure] static OSa <anonymous closure>(dynamic) {
    // ** addr: 0x481fb4, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x481f5c, size: -0x1
  }
}
