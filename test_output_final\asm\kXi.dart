// lib: , url: KXi

// class id: 1049081, size: 0x8
class :: {
}

// class id: 1940, size: 0x8, field offset: 0x8
abstract class Vea extends Object
    implements tea {
}

// class id: 1941, size: 0x8, field offset: 0x8
abstract class Uea extends Object
    implements Taa {
}

// class id: 1942, size: 0x8, field offset: 0x8
abstract class Tea extends Object {
}

// class id: 2014, size: 0x64, field offset: 0x54
//   transformed mixin,
abstract class _Yea extends tea
     with OX<X0 bound Waa, X1 bound Xaa> {
}

// class id: 2015, size: 0x64, field offset: 0x64
//   transformed mixin,
abstract class _Zea extends _Yea
     with Aea {

  [closure] bool <anonymous closure>(dynamic, Qaa) {
    // ** addr: 0x50bcdc, size: -0x1
  }
}

// class id: 2016, size: 0x64, field offset: 0x64
//   transformed mixin,
abstract class _afa extends _Zea
     with Vea {
}

// class id: 2017, size: 0x6c, field offset: 0x64
abstract class Jea extends _afa {

  [closure] void <anonymous closure>(dynamic, pea) {
    // ** addr: 0x3b4b08, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, pea) {
    // ** addr: 0x3b62d8, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, iI) {
    // ** addr: 0x3b68fc, size: -0x1
  }
}

// class id: 2205, size: 0x14, field offset: 0xc
//   transformed mixin,
abstract class _Wea extends uea
     with Xaa<X0 bound Waa> {
}

// class id: 2206, size: 0x18, field offset: 0x14
//   transformed mixin,
abstract class _Xea extends _Wea
     with Uea {
}

// class id: 2207, size: 0x20, field offset: 0x18
class Qea extends _Xea {
}
