// lib: Uqj, url: qMi

// class id: 1049676, size: 0x8
class :: {
}

// class id: 757, size: 0xc, field offset: 0xc
class oVa extends OTa {

  static late final vWa iog; // offset: 0x1280

  [closure] static oVa <anonymous closure>(dynamic) {
    // ** addr: 0x46fd84, size: -0x1
  }
  [closure] static oVa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x46fe9c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x46fd2c, size: -0x1
  }
}
