// lib: Hqj, url: ETi

// class id: 1049663, size: 0x8
class :: {
}

// class id: 770, size: 0xc, field offset: 0xc
class OUa extends OTa {

  static late final vWa iog; // offset: 0x124c

  [closure] static OUa <anonymous closure>(dynamic) {
    // ** addr: 0x471174, size: -0x1
  }
  [closure] static OUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x47128c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x47111c, size: -0x1
  }
}
