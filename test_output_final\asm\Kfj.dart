// lib: zqj, url: kfj

// class id: 1049655, size: 0x8
class :: {
}

// class id: 778, size: 0xc, field offset: 0xc
class yUa extends OTa {

  static late final vWa iog; // offset: 0x122c

  [closure] static yUa <anonymous closure>(dynamic) {
    // ** addr: 0x471df4, size: -0x1
  }
  [closure] static yUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>?) {
    // ** addr: 0x471ef8, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x471d9c, size: -0x1
  }
}
