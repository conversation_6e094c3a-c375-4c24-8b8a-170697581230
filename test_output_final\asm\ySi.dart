// lib: , url: YSi

// class id: 1048849, size: 0x8
class :: {
}

// class id: 2646, size: 0x2c, field offset: 0x2c
abstract class _MI extends NI<dynamic> {
}

// class id: 2647, size: 0x2c, field offset: 0x2c
class QI extends _MI {
}

// class id: 2648, size: 0x2c, field offset: 0x2c
class PI extends _MI {
}

// class id: 2649, size: 0x2c, field offset: 0x2c
class OI extends _MI {
}

// class id: 2667, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class LI extends Object {
}

// class id: 3023, size: 0x14, field offset: 0x8
//   const constructor, 
class SI extends _DF {
}

// class id: 5751, size: 0xc, field offset: 0xc
//   transformed mixin,
abstract class _TI extends Error
     with Ru {
}

// class id: 5752, size: 0x10, field offset: 0xc
class UI extends _TI
    implements ka {

  static late ((dynamic, SI) => void)? Pqb; // offset: 0x7c8
  static late (dynamic, ua) => ua fvc; // offset: 0x7cc
  static late final List<LI> _ivc; // offset: 0x7d8
  static late (dynamic, SI) => void gvc; // offset: 0x7d0

  [closure] static int <anonymous closure>(dynamic, int) {
    // ** addr: 0x304d10, size: -0x1
  }
  [closure] static void lvc(dynamic, SI, {bool nvc}) {
    // ** addr: 0x304fb8, size: -0x1
  }
  [closure] static OI <anonymous closure>(dynamic, String) {
    // ** addr: 0x37b2f4, size: -0x1
  }
}
