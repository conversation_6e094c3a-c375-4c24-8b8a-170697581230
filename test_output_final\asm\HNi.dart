// lib: , url: HNi

// class id: 1048597, size: 0x8
class :: {
}

// class id: 4641, size: 0x8, field offset: 0x8
abstract class st extends Object {
}

// class id: 4642, size: 0x28, field offset: 0x8
class tt extends st {

  late int _Myb; // offset: 0x24

  int dyn:get:length(tt) {
    // ** addr: 0x94b060, size: 0x80
    // 0x94b060: EnterFrame
    //     0x94b060: stp             fp, lr, [SP, #-0x10]!
    //     0x94b064: mov             fp, SP
    // 0x94b068: ldr             x2, [fp, #0x10]
    // 0x94b06c: LoadField: r3 = r2->field_23
    //     0x94b06c: ldur            w3, [x2, #0x23]
    // 0x94b070: DecompressPointer r3
    //     0x94b070: add             x3, x3, HEAP, lsl #32
    // 0x94b074: r16 = Sentinel
    //     0x94b074: ldr             x16, [PP, #0x40]  ; [pp+0x40] Sentinel
    // 0x94b078: cmp             w3, w16
    // 0x94b07c: b.eq            #0x94b0bc
    // 0x94b080: LoadField: r4 = r2->field_b
    //     0x94b080: ldur            x4, [x2, #0xb]
    // 0x94b084: LoadField: r5 = r2->field_13
    //     0x94b084: ldur            x5, [x2, #0x13]
    // 0x94b088: sub             x2, x4, x5
    // 0x94b08c: r4 = LoadInt32Instr(r3)
    //     0x94b08c: sbfx            x4, x3, #1, #0x1f
    //     0x94b090: tbz             w3, #0, #0x94b098
    //     0x94b094: ldur            x4, [x3, #7]
    // 0x94b098: sub             x3, x4, x2
    // 0x94b09c: r0 = BoxInt64Instr(r3)
    //     0x94b09c: sbfiz           x0, x3, #1, #0x1f
    //     0x94b0a0: cmp             x3, x0, asr #1
    //     0x94b0a4: b.eq            #0x94b0b0
    //     0x94b0a8: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94b0ac: stur            x3, [x0, #7]
    // 0x94b0b0: LeaveFrame
    //     0x94b0b0: mov             SP, fp
    //     0x94b0b4: ldp             fp, lr, [SP], #0x10
    // 0x94b0b8: ret
    //     0x94b0b8: ret             
    // 0x94b0bc: r9 = _Myb
    //     0x94b0bc: add             x9, PP, #0x23, lsl #12  ; [pp+0x23ea8] Field <tt._Myb@518080104>: late (offset: 0x24)
    //     0x94b0c0: ldr             x9, [x9, #0xea8]
    // 0x94b0c4: r0 = LateInitializationErrorSharedWithoutFPURegs()
    //     0x94b0c4: bl              #0x95035c  ; LateInitializationErrorSharedWithoutFPURegsStub
  }
  int [](tt, int) {
    // ** addr: 0x94b0e0, size: 0x98
    // 0x94b0e0: EnterFrame
    //     0x94b0e0: stp             fp, lr, [SP, #-0x10]!
    //     0x94b0e4: mov             fp, SP
    // 0x94b0e8: CheckStackOverflow
    //     0x94b0e8: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x94b0ec: cmp             SP, x16
    //     0x94b0f0: b.ls            #0x94b158
    // 0x94b0f4: ldr             x0, [fp, #0x10]
    // 0x94b0f8: r2 = Null
    //     0x94b0f8: mov             x2, NULL
    // 0x94b0fc: r1 = Null
    //     0x94b0fc: mov             x1, NULL
    // 0x94b100: branchIfSmi(r0, 0x94b128)
    //     0x94b100: tbz             w0, #0, #0x94b128
    // 0x94b104: r4 = LoadClassIdInstr(r0)
    //     0x94b104: ldur            x4, [x0, #-1]
    //     0x94b108: ubfx            x4, x4, #0xc, #0x14
    // 0x94b10c: sub             x4, x4, #0x3b
    // 0x94b110: cmp             x4, #1
    // 0x94b114: b.ls            #0x94b128
    // 0x94b118: r8 = int
    //     0x94b118: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x94b11c: r3 = Null
    //     0x94b11c: add             x3, PP, #0x2f, lsl #12  ; [pp+0x2f3a8] Null
    //     0x94b120: ldr             x3, [x3, #0x3a8]
    // 0x94b124: r0 = int()
    //     0x94b124: bl              #0x9595b0  ; IsType_int_Stub
    // 0x94b128: ldr             x1, [fp, #0x18]
    // 0x94b12c: ldr             x2, [fp, #0x10]
    // 0x94b130: r0 = call 0x8684c4
    //     0x94b130: bl              #0x8684c4
    // 0x94b134: mov             x2, x0
    // 0x94b138: r0 = BoxInt64Instr(r2)
    //     0x94b138: sbfiz           x0, x2, #1, #0x1f
    //     0x94b13c: cmp             x2, x0, asr #1
    //     0x94b140: b.eq            #0x94b14c
    //     0x94b144: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x94b148: stur            x2, [x0, #7]
    // 0x94b14c: LeaveFrame
    //     0x94b14c: mov             SP, fp
    //     0x94b150: ldp             fp, lr, [SP], #0x10
    // 0x94b154: ret
    //     0x94b154: ret             
    // 0x94b158: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x94b158: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x94b15c: b               #0x94b0f4
  }
}
