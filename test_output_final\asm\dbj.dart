// lib: , url: Dbj

// class id: 1049286, size: 0x8
class :: {
}

// class id: 1248, size: 0x20, field offset: 0x8
class kGa extends Object {

  static late kGa _Alc; // offset: 0xe68
  static late (dynamic) => bool _lgf; // offset: 0xe70
  late bool _ngf; // offset: 0xc
  static late (dynamic) => bool _kgf; // offset: 0xe6c
  late jg _mgf; // offset: 0x8
  late Lpa _data; // offset: 0x10
  late bool _ogf; // offset: 0x14
}
