// lib: , url: JPi

// class id: 1048695, size: 0x8
class :: {
}

// class id: 4509, size: 0x80, field offset: 0x8
class Sy extends Object {

  Map<String, dynamic> WNb(Sy) {
    // ** addr: 0x903358, size: 0x48
    // 0x903358: EnterFrame
    //     0x903358: stp             fp, lr, [SP, #-0x10]!
    //     0x90335c: mov             fp, SP
    // 0x903360: CheckStackOverflow
    //     0x903360: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x903364: cmp             SP, x16
    //     0x903368: b.ls            #0x903380
    // 0x90336c: ldr             x1, [fp, #0x10]
    // 0x903370: r0 = call 0x559b6c
    //     0x903370: bl              #0x559b6c
    // 0x903374: LeaveFrame
    //     0x903374: mov             SP, fp
    //     0x903378: ldp             fp, lr, [SP], #0x10
    // 0x90337c: ret
    //     0x90337c: ret             
    // 0x903380: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x903380: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x903384: b               #0x90336c
  }
}
