// lib: , url: Zaj

// class id: 1049256, size: 0x8
class :: {
}

// class id: 1636, size: 0x10, field offset: 0x8
//   const constructor, 
class hya<X0> extends Object
    implements AO<X0> {

  gZ field_c;
}

// class id: 1637, size: 0x10, field offset: 0x8
class _gya<X0> extends Object
    implements AO<X0> {
}

// class id: 1638, size: 0x20, field offset: 0x8
//   const constructor, 
class _fya<X0> extends Object
    implements AO<X0> {
}

// class id: 2453, size: 0xc, field offset: 0x8
abstract class AO<X0> extends Object {
}

// class id: 2843, size: 0x20, field offset: 0x20
//   const constructor, 
abstract class bya extends BO
    implements AO<X0> {
}

// class id: 2844, size: 0x24, field offset: 0x20
//   const constructor, 
class _cya extends bya {
}

// class id: 2945, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class HO extends zQ
    implements AO<X0> {
}

// class id: 2946, size: 0x14, field offset: 0x8
//   const constructor, 
class _aya extends HO {

  pha field_8;
  pha field_c;
  _OneByteString field_10;
}

// class id: 3038, size: 0x70, field offset: 0x70
//   const constructor, 
abstract class dya extends Rs
    implements AO<X0> {
}

// class id: 3039, size: 0x74, field offset: 0x70
//   const constructor, 
class _eya extends dya {
}

// class id: 4584, size: 0x2c, field offset: 0x2c
class iya extends iJ<dynamic> {
}

// class id: 4706, size: 0x10, field offset: 0x10
//   const constructor, 
abstract class Yxa extends mr
    implements AO<X0> {
}

// class id: 4707, size: 0x14, field offset: 0x10
class _Zxa extends Yxa {
}

// class id: 5447, size: 0x14, field offset: 0x14
enum Xxa extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
