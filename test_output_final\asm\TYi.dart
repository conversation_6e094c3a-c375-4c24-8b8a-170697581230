// lib: , url: tYi

// class id: 1049293, size: 0x8
class :: {
}

// class id: 1236, size: 0x48, field offset: 0x8
class RGa extends Object {

  [closure] void _LXf(dynamic) {
    // ** addr: 0x648098, size: -0x1
  }
}

// class id: 1237, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class NGa extends Object {
}

// class id: 1238, size: 0xc, field offset: 0x8
//   const constructor, 
class QGa extends NGa {
}

// class id: 1239, size: 0x8, field offset: 0x8
//   const constructor, 
class PGa extends NGa {
}

// class id: 1240, size: 0x8, field offset: 0x8
//   const constructor, 
class OGa extends NGa {
}

// class id: 1241, size: 0x8, field offset: 0x8
//   const constructor, 
class MGa extends Object {
}

// class id: 1242, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class LGa extends Object {
}

// class id: 1243, size: 0x8, field offset: 0x8
abstract class JGa extends Object {
}

// class id: 4582, size: 0x2c, field offset: 0x2c
class _SGa<X0> extends iJ<X0> {
}

// class id: 5438, size: 0x14, field offset: 0x14
enum KGa extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5439, size: 0x14, field offset: 0x14
enum IGa extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
