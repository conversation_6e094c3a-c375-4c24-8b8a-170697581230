// lib: , url: Rhj

// class id: 1049594, size: 0x8
class :: {
}

// class id: 870, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class nq extends Object {

  String WNb(nq) {
    // ** addr: 0x91c294, size: 0x48
    // 0x91c294: EnterFrame
    //     0x91c294: stp             fp, lr, [SP, #-0x10]!
    //     0x91c298: mov             fp, SP
    // 0x91c29c: CheckStackOverflow
    //     0x91c29c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x91c2a0: cmp             SP, x16
    //     0x91c2a4: b.ls            #0x91c2bc
    // 0x91c2a8: ldr             x1, [fp, #0x10]
    // 0x91c2ac: r0 = call 0x6323a8
    //     0x91c2ac: bl              #0x6323a8
    // 0x91c2b0: LeaveFrame
    //     0x91c2b0: mov             SP, fp
    //     0x91c2b4: ldp             fp, lr, [SP], #0x10
    // 0x91c2b8: ret
    //     0x91c2b8: ret             
    // 0x91c2bc: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x91c2bc: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x91c2c0: b               #0x91c2a8
  }
}
