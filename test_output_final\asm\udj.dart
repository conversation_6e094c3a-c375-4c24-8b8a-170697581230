// lib: , url: udj

// class id: 1049375, size: 0x8
class :: {
}

// class id: 1109, size: 0x8, field offset: 0x8
abstract class fKa extends Object
    implements Set<X0> {
}

// class id: 5161, size: 0xc, field offset: 0xc
abstract class _dLa extends Fd<dynamic>
    implements fKa {

  int length(_dLa) {
    // ** addr: 0x8fde2c, size: 0x68
    // 0x8fde2c: EnterFrame
    //     0x8fde2c: stp             fp, lr, [SP, #-0x10]!
    //     0x8fde30: mov             fp, SP
    // 0x8fde34: CheckStackOverflow
    //     0x8fde34: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fde38: cmp             SP, x16
    //     0x8fde3c: b.ls            #0x8fde74
    // 0x8fde40: ldr             x1, [fp, #0x10]
    // 0x8fde44: r0 = call 0x3db6ac
    //     0x8fde44: bl              #0x3db6ac
    // 0x8fde48: LoadField: r1 = r0->field_13
    //     0x8fde48: ldur            w1, [x0, #0x13]
    // 0x8fde4c: DecompressPointer r1
    //     0x8fde4c: add             x1, x1, HEAP, lsl #32
    // 0x8fde50: ArrayLoad: r2 = r0[0]  ; List_4
    //     0x8fde50: ldur            w2, [x0, #0x17]
    // 0x8fde54: DecompressPointer r2
    //     0x8fde54: add             x2, x2, HEAP, lsl #32
    // 0x8fde58: r3 = LoadInt32Instr(r1)
    //     0x8fde58: sbfx            x3, x1, #1, #0x1f
    // 0x8fde5c: r1 = LoadInt32Instr(r2)
    //     0x8fde5c: sbfx            x1, x2, #1, #0x1f
    // 0x8fde60: sub             x2, x3, x1
    // 0x8fde64: lsl             x0, x2, #1
    // 0x8fde68: LeaveFrame
    //     0x8fde68: mov             SP, fp
    //     0x8fde6c: ldp             fp, lr, [SP], #0x10
    // 0x8fde70: ret
    //     0x8fde70: ret             
    // 0x8fde74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fde74: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fde78: b               #0x8fde40
  }
  [closure] bool ek(dynamic, Object?) {
    // ** addr: 0x3db4d4, size: -0x1
  }
  [closure] bool <anonymous closure>(dynamic, Set<String>) {
    // ** addr: 0x7307b8, size: -0x1
  }
}

// class id: 5162, size: 0x10, field offset: 0xc
class cLa extends _dLa {
}
