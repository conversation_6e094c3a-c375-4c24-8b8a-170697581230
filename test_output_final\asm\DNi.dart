// lib: Lqj, url: DNi

// class id: 1049667, size: 0x8
class :: {
}

// class id: 766, size: 0xc, field offset: 0xc
class WUa extends OTa {

  static late final vWa iog; // offset: 0x125c

  [closure] static WUa <anonymous closure>(dynamic) {
    // ** addr: 0x470b4c, size: -0x1
  }
  [closure] static WUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>?) {
    // ** addr: 0x470c4c, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x470af4, size: -0x1
  }
}
