// lib: , url: Dfj

// class id: 1049479, size: 0x8
class :: {
}

// class id: 969, size: 0x8, field offset: 0x8
abstract class eOa extends Object {

  [closure] static void IGf(dynamic, int, int, int, int, int, Uint8List) {
    // ** addr: 0x87887c, size: -0x1
  }
  [closure] static void JGf(dynamic, int, int, int, int, int, Uint8List) {
    // ** addr: 0x8783c0, size: -0x1
  }
  [closure] static void KGf(dynamic, int, int, int, int, int, Uint8List) {
    // ** addr: 0x877990, size: -0x1
  }
}
