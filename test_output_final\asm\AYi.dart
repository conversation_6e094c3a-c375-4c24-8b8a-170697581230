// lib: , url: AYi

// class id: 1049131, size: 0x8
class :: {
}

// class id: 2249, size: 0x8, field offset: 0x8
//   const constructor, 
abstract class haa extends Object {
}

// class id: 2251, size: 0xc, field offset: 0x8
//   const constructor, 
class yia extends haa {
}

// class id: 2252, size: 0xc, field offset: 0x8
//   const constructor, 
class xia extends haa {
}

// class id: 2253, size: 0xc, field offset: 0x8
//   const constructor, 
class wia extends haa {
}

// class id: 2254, size: 0xc, field offset: 0x8
//   const constructor, 
class via extends haa {
}
