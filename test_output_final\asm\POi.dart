// lib: , url: POi

// class id: 1048651, size: 0x8
class :: {
}

// class id: 3391, size: 0x50, field offset: 0x30
class _Fw extends Wu<dynamic> {

  dynamic dyn:get:data(_Fw) {
    // ** addr: 0x8f0a08, size: 0x28
    // 0x8f0a08: ldr             x1, [SP]
    // 0x8f0a0c: LoadField: r0 = r1->field_47
    //     0x8f0a0c: ldur            w0, [x1, #0x47]
    // 0x8f0a10: DecompressPointer r0
    //     0x8f0a10: add             x0, x0, HEAP, lsl #32
    // 0x8f0a14: ret
    //     0x8f0a14: ret             
  }
  [closure] Null <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x6bad00, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic) {
    // ** addr: 0x6bae30, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0x4aa060, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0x4aa01c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0x4a9f94, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic, String) {
    // ** addr: 0x4a9f50, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x4a9a98, size: -0x1
  }
  [closure] Null <anonymous closure>(dynamic, Zy<dynamic>?) {
    // ** addr: 0x4a9df0, size: -0x1
  }
}

// class id: 4123, size: 0x10, field offset: 0x10
class Ew extends Tu {
}
