// lib: , url: DRi

// class id: 1048782, size: 0x8
class :: {
}

// class id: 4265, size: 0x8, field offset: 0x8
abstract class QD extends Object {
}

// class id: 4266, size: 0x8, field offset: 0x8
//   transformed mixin,
abstract class _SD extends QD
     with _RD {

  [closure] void Pqb(dynamic, HD, PD) {
    // ** addr: 0x40b3fc, size: -0x1
  }
  [closure] void KIf(dynamic, jE<dynamic>, OD) {
    // ** addr: 0x40b6fc, size: -0x1
  }
  [closure] void JIf(dynamic, hE, ND) {
    // ** addr: 0x428ec8, size: -0x1
  }
}

// class id: 4267, size: 0x14, field offset: 0x8
class TD extends _SD {
}

// class id: 4268, size: 0x8, field offset: 0x8
abstract class _RD extends QD {
}

// class id: 4269, size: 0x10, field offset: 0x8
abstract class _MD extends Object {
}

// class id: 4270, size: 0x10, field offset: 0x10
class PD extends _MD {
}

// class id: 4271, size: 0x10, field offset: 0x10
class OD extends _MD {
}

// class id: 4272, size: 0x10, field offset: 0x10
class ND extends _MD {
}

// class id: 4273, size: 0x14, field offset: 0x8
class LD<X0> extends Object {
}

// class id: 4274, size: 0xc, field offset: 0x8
class JD extends Object {
}

// class id: 4275, size: 0x8, field offset: 0x8
abstract class ID extends Object
    implements FD {

  [closure] static Never <anonymous closure>(dynamic, HD) {
    // ** addr: 0x40b128, size: -0x1
  }
}

// class id: 5601, size: 0x14, field offset: 0x14
enum KD extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5734, size: 0x24, field offset: 0xc
class UD extends B<dynamic> {

  int length(UD) {
    // ** addr: 0x8fd1e8, size: 0x48
    // 0x8fd1e8: EnterFrame
    //     0x8fd1e8: stp             fp, lr, [SP, #-0x10]!
    //     0x8fd1ec: mov             fp, SP
    // 0x8fd1f0: ldr             x2, [fp, #0x10]
    // 0x8fd1f4: LoadField: r3 = r2->field_1b
    //     0x8fd1f4: ldur            x3, [x2, #0x1b]
    // 0x8fd1f8: r0 = BoxInt64Instr(r3)
    //     0x8fd1f8: sbfiz           x0, x3, #1, #0x1f
    //     0x8fd1fc: cmp             x3, x0, asr #1
    //     0x8fd200: b.eq            #0x8fd20c
    //     0x8fd204: bl              #0x94fcac  ; AllocateMintSharedWithoutFPURegsStub
    //     0x8fd208: stur            x3, [x0, #7]
    // 0x8fd20c: LeaveFrame
    //     0x8fd20c: mov             SP, fp
    //     0x8fd210: ldp             fp, lr, [SP], #0x10
    // 0x8fd214: ret
    //     0x8fd214: ret             
  }
  void []=(UD, int, QD) {
    // ** addr: 0x8fd230, size: 0x200
    // 0x8fd230: EnterFrame
    //     0x8fd230: stp             fp, lr, [SP, #-0x10]!
    //     0x8fd234: mov             fp, SP
    // 0x8fd238: AllocStack(0x18)
    //     0x8fd238: sub             SP, SP, #0x18
    // 0x8fd23c: CheckStackOverflow
    //     0x8fd23c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x8fd240: cmp             SP, x16
    //     0x8fd244: b.ls            #0x8fd408
    // 0x8fd248: ldr             x0, [fp, #0x18]
    // 0x8fd24c: r2 = Null
    //     0x8fd24c: mov             x2, NULL
    // 0x8fd250: r1 = Null
    //     0x8fd250: mov             x1, NULL
    // 0x8fd254: branchIfSmi(r0, 0x8fd27c)
    //     0x8fd254: tbz             w0, #0, #0x8fd27c
    // 0x8fd258: r4 = LoadClassIdInstr(r0)
    //     0x8fd258: ldur            x4, [x0, #-1]
    //     0x8fd25c: ubfx            x4, x4, #0xc, #0x14
    // 0x8fd260: sub             x4, x4, #0x3b
    // 0x8fd264: cmp             x4, #1
    // 0x8fd268: b.ls            #0x8fd27c
    // 0x8fd26c: r8 = int
    //     0x8fd26c: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8fd270: r3 = Null
    //     0x8fd270: add             x3, PP, #0x23, lsl #12  ; [pp+0x23930] Null
    //     0x8fd274: ldr             x3, [x3, #0x930]
    // 0x8fd278: r0 = int()
    //     0x8fd278: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8fd27c: ldr             x0, [fp, #0x10]
    // 0x8fd280: r2 = Null
    //     0x8fd280: mov             x2, NULL
    // 0x8fd284: r1 = Null
    //     0x8fd284: mov             x1, NULL
    // 0x8fd288: r4 = 59
    //     0x8fd288: movz            x4, #0x3b
    // 0x8fd28c: branchIfSmi(r0, 0x8fd298)
    //     0x8fd28c: tbz             w0, #0, #0x8fd298
    // 0x8fd290: r4 = LoadClassIdInstr(r0)
    //     0x8fd290: ldur            x4, [x0, #-1]
    //     0x8fd294: ubfx            x4, x4, #0xc, #0x14
    // 0x8fd298: r17 = 4267
    //     0x8fd298: movz            x17, #0x10ab
    // 0x8fd29c: cmp             x4, x17
    // 0x8fd2a0: b.eq            #0x8fd2b8
    // 0x8fd2a4: r8 = QD
    //     0x8fd2a4: add             x8, PP, #0xe, lsl #12  ; [pp+0xe618] Type: QD
    //     0x8fd2a8: ldr             x8, [x8, #0x618]
    // 0x8fd2ac: r3 = Null
    //     0x8fd2ac: add             x3, PP, #0x23, lsl #12  ; [pp+0x23940] Null
    //     0x8fd2b0: ldr             x3, [x3, #0x940]
    // 0x8fd2b4: r0 = QD()
    //     0x8fd2b4: bl              #0x8c64d0  ; IsType_QD_Stub
    // 0x8fd2b8: ldr             x0, [fp, #0x20]
    // 0x8fd2bc: LoadField: r2 = r0->field_b
    //     0x8fd2bc: ldur            w2, [x0, #0xb]
    // 0x8fd2c0: DecompressPointer r2
    //     0x8fd2c0: add             x2, x2, HEAP, lsl #32
    // 0x8fd2c4: stur            x2, [fp, #-0x10]
    // 0x8fd2c8: LoadField: r0 = r2->field_b
    //     0x8fd2c8: ldur            w0, [x2, #0xb]
    // 0x8fd2cc: DecompressPointer r0
    //     0x8fd2cc: add             x0, x0, HEAP, lsl #32
    // 0x8fd2d0: ldr             x1, [fp, #0x18]
    // 0x8fd2d4: r3 = LoadInt32Instr(r1)
    //     0x8fd2d4: sbfx            x3, x1, #1, #0x1f
    //     0x8fd2d8: tbz             w1, #0, #0x8fd2e0
    //     0x8fd2dc: ldur            x3, [x1, #7]
    // 0x8fd2e0: stur            x3, [fp, #-0x18]
    // 0x8fd2e4: r4 = LoadInt32Instr(r0)
    //     0x8fd2e4: sbfx            x4, x0, #1, #0x1f
    // 0x8fd2e8: stur            x4, [fp, #-8]
    // 0x8fd2ec: cmp             x4, x3
    // 0x8fd2f0: b.ne            #0x8fd370
    // 0x8fd2f4: LoadField: r0 = r2->field_f
    //     0x8fd2f4: ldur            w0, [x2, #0xf]
    // 0x8fd2f8: DecompressPointer r0
    //     0x8fd2f8: add             x0, x0, HEAP, lsl #32
    // 0x8fd2fc: LoadField: r1 = r0->field_b
    //     0x8fd2fc: ldur            w1, [x0, #0xb]
    // 0x8fd300: DecompressPointer r1
    //     0x8fd300: add             x1, x1, HEAP, lsl #32
    // 0x8fd304: r0 = LoadInt32Instr(r1)
    //     0x8fd304: sbfx            x0, x1, #1, #0x1f
    // 0x8fd308: cmp             x4, x0
    // 0x8fd30c: b.ne            #0x8fd318
    // 0x8fd310: mov             x1, x2
    // 0x8fd314: r0 = call 0x2d7c98
    //     0x8fd314: bl              #0x2d7c98
    // 0x8fd318: ldur            x4, [fp, #-0x10]
    // 0x8fd31c: ldur            x5, [fp, #-8]
    // 0x8fd320: add             x0, x5, #1
    // 0x8fd324: lsl             x1, x0, #1
    // 0x8fd328: StoreField: r4->field_b = r1
    //     0x8fd328: stur            w1, [x4, #0xb]
    // 0x8fd32c: mov             x1, x5
    // 0x8fd330: cmp             x1, x0
    // 0x8fd334: b.hs            #0x8fd410
    // 0x8fd338: LoadField: r1 = r4->field_f
    //     0x8fd338: ldur            w1, [x4, #0xf]
    // 0x8fd33c: DecompressPointer r1
    //     0x8fd33c: add             x1, x1, HEAP, lsl #32
    // 0x8fd340: ldr             x0, [fp, #0x10]
    // 0x8fd344: ArrayStore: r1[r5] = r0  ; List_4
    //     0x8fd344: add             x25, x1, x5, lsl #2
    //     0x8fd348: add             x25, x25, #0xf
    //     0x8fd34c: str             w0, [x25]
    //     0x8fd350: tbz             w0, #0, #0x8fd36c
    //     0x8fd354: ldurb           w16, [x1, #-1]
    //     0x8fd358: ldurb           w17, [x0, #-1]
    //     0x8fd35c: and             x16, x17, x16, lsr #2
    //     0x8fd360: tst             x16, HEAP, lsr #32
    //     0x8fd364: b.eq            #0x8fd36c
    //     0x8fd368: bl              #0x94dd2c  ; ArrayWriteBarrierStub
    // 0x8fd36c: b               #0x8fd3f8
    // 0x8fd370: mov             x5, x4
    // 0x8fd374: mov             x4, x2
    // 0x8fd378: LoadField: r2 = r4->field_7
    //     0x8fd378: ldur            w2, [x4, #7]
    // 0x8fd37c: DecompressPointer r2
    //     0x8fd37c: add             x2, x2, HEAP, lsl #32
    // 0x8fd380: ldr             x0, [fp, #0x10]
    // 0x8fd384: r1 = Null
    //     0x8fd384: mov             x1, NULL
    // 0x8fd388: cmp             w2, NULL
    // 0x8fd38c: b.eq            #0x8fd3ac
    // 0x8fd390: ArrayLoad: r4 = r2[0]  ; List_4
    //     0x8fd390: ldur            w4, [x2, #0x17]
    // 0x8fd394: DecompressPointer r4
    //     0x8fd394: add             x4, x4, HEAP, lsl #32
    // 0x8fd398: r8 = X0
    //     0x8fd398: ldr             x8, [PP, #0x190]  ; [pp+0x190] TypeParameter: X0
    // 0x8fd39c: LoadField: r9 = r4->field_7
    //     0x8fd39c: ldur            x9, [x4, #7]
    // 0x8fd3a0: r3 = Null
    //     0x8fd3a0: add             x3, PP, #0x23, lsl #12  ; [pp+0x23950] Null
    //     0x8fd3a4: ldr             x3, [x3, #0x950]
    // 0x8fd3a8: blr             x9
    // 0x8fd3ac: ldur            x0, [fp, #-8]
    // 0x8fd3b0: ldur            x1, [fp, #-0x18]
    // 0x8fd3b4: cmp             x1, x0
    // 0x8fd3b8: b.hs            #0x8fd414
    // 0x8fd3bc: ldur            x2, [fp, #-0x10]
    // 0x8fd3c0: LoadField: r1 = r2->field_f
    //     0x8fd3c0: ldur            w1, [x2, #0xf]
    // 0x8fd3c4: DecompressPointer r1
    //     0x8fd3c4: add             x1, x1, HEAP, lsl #32
    // 0x8fd3c8: ldr             x0, [fp, #0x10]
    // 0x8fd3cc: ldur            x2, [fp, #-0x18]
    // 0x8fd3d0: ArrayStore: r1[r2] = r0  ; List_4
    //     0x8fd3d0: add             x25, x1, x2, lsl #2
    //     0x8fd3d4: add             x25, x25, #0xf
    //     0x8fd3d8: str             w0, [x25]
    //     0x8fd3dc: tbz             w0, #0, #0x8fd3f8
    //     0x8fd3e0: ldurb           w16, [x1, #-1]
    //     0x8fd3e4: ldurb           w17, [x0, #-1]
    //     0x8fd3e8: and             x16, x17, x16, lsr #2
    //     0x8fd3ec: tst             x16, HEAP, lsr #32
    //     0x8fd3f0: b.eq            #0x8fd3f8
    //     0x8fd3f4: bl              #0x94dd2c  ; ArrayWriteBarrierStub
    // 0x8fd3f8: r0 = Null
    //     0x8fd3f8: mov             x0, NULL
    // 0x8fd3fc: LeaveFrame
    //     0x8fd3fc: mov             SP, fp
    //     0x8fd400: ldp             fp, lr, [SP], #0x10
    // 0x8fd404: ret
    //     0x8fd404: ret             
    // 0x8fd408: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x8fd408: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x8fd40c: b               #0x8fd248
    // 0x8fd410: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8fd410: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
    // 0x8fd414: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8fd414: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
  }
  QD [](UD, int) {
    // ** addr: 0x8fd430, size: 0xac
    // 0x8fd430: EnterFrame
    //     0x8fd430: stp             fp, lr, [SP, #-0x10]!
    //     0x8fd434: mov             fp, SP
    // 0x8fd438: ldr             x0, [fp, #0x10]
    // 0x8fd43c: r2 = Null
    //     0x8fd43c: mov             x2, NULL
    // 0x8fd440: r1 = Null
    //     0x8fd440: mov             x1, NULL
    // 0x8fd444: branchIfSmi(r0, 0x8fd46c)
    //     0x8fd444: tbz             w0, #0, #0x8fd46c
    // 0x8fd448: r4 = LoadClassIdInstr(r0)
    //     0x8fd448: ldur            x4, [x0, #-1]
    //     0x8fd44c: ubfx            x4, x4, #0xc, #0x14
    // 0x8fd450: sub             x4, x4, #0x3b
    // 0x8fd454: cmp             x4, #1
    // 0x8fd458: b.ls            #0x8fd46c
    // 0x8fd45c: r8 = int
    //     0x8fd45c: ldr             x8, [PP, #0x3a8]  ; [pp+0x3a8] Type: int
    // 0x8fd460: r3 = Null
    //     0x8fd460: add             x3, PP, #0xe, lsl #12  ; [pp+0xe640] Null
    //     0x8fd464: ldr             x3, [x3, #0x640]
    // 0x8fd468: r0 = int()
    //     0x8fd468: bl              #0x9595b0  ; IsType_int_Stub
    // 0x8fd46c: ldr             x2, [fp, #0x18]
    // 0x8fd470: LoadField: r3 = r2->field_b
    //     0x8fd470: ldur            w3, [x2, #0xb]
    // 0x8fd474: DecompressPointer r3
    //     0x8fd474: add             x3, x3, HEAP, lsl #32
    // 0x8fd478: LoadField: r2 = r3->field_b
    //     0x8fd478: ldur            w2, [x3, #0xb]
    // 0x8fd47c: DecompressPointer r2
    //     0x8fd47c: add             x2, x2, HEAP, lsl #32
    // 0x8fd480: ldr             x4, [fp, #0x10]
    // 0x8fd484: r5 = LoadInt32Instr(r4)
    //     0x8fd484: sbfx            x5, x4, #1, #0x1f
    //     0x8fd488: tbz             w4, #0, #0x8fd490
    //     0x8fd48c: ldur            x5, [x4, #7]
    // 0x8fd490: r0 = LoadInt32Instr(r2)
    //     0x8fd490: sbfx            x0, x2, #1, #0x1f
    // 0x8fd494: mov             x1, x5
    // 0x8fd498: cmp             x1, x0
    // 0x8fd49c: b.hs            #0x8fd4c0
    // 0x8fd4a0: LoadField: r1 = r3->field_f
    //     0x8fd4a0: ldur            w1, [x3, #0xf]
    // 0x8fd4a4: DecompressPointer r1
    //     0x8fd4a4: add             x1, x1, HEAP, lsl #32
    // 0x8fd4a8: ArrayLoad: r0 = r1[r5]  ; Unknown_4
    //     0x8fd4a8: add             x16, x1, x5, lsl #2
    //     0x8fd4ac: ldur            w0, [x16, #0xf]
    // 0x8fd4b0: DecompressPointer r0
    //     0x8fd4b0: add             x0, x0, HEAP, lsl #32
    // 0x8fd4b4: LeaveFrame
    //     0x8fd4b4: mov             SP, fp
    //     0x8fd4b8: ldp             fp, lr, [SP], #0x10
    // 0x8fd4bc: ret
    //     0x8fd4bc: ret             
    // 0x8fd4c0: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x8fd4c0: bl              #0x94ff74  ; RangeErrorSharedWithoutFPURegsStub
  }
}
