// lib: , url: rWi

// class id: 1049101, size: 0x8
class :: {
}

// class id: 1905, size: 0x1c, field offset: 0x8
//   const constructor, 
class Aga extends Object {

  bool field_8;
  _OneByteString field_c;
  _ImmutableList<String> field_10;
  wga field_14;

  Map<String, dynamic>? WNb(Aga) {
    // ** addr: 0x943c40, size: 0x48
    // 0x943c40: EnterFrame
    //     0x943c40: stp             fp, lr, [SP, #-0x10]!
    //     0x943c44: mov             fp, SP
    // 0x943c48: CheckStackOverflow
    //     0x943c48: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x943c4c: cmp             SP, x16
    //     0x943c50: b.ls            #0x943c68
    // 0x943c54: ldr             x1, [fp, #0x10]
    // 0x943c58: r0 = call 0x383a24
    //     0x943c58: bl              #0x383a24
    // 0x943c5c: LeaveFrame
    //     0x943c5c: mov             SP, fp
    //     0x943c60: ldp             fp, lr, [SP], #0x10
    // 0x943c64: ret
    //     0x943c64: ret             
    // 0x943c68: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x943c68: bl              #0x94fb2c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x943c6c: b               #0x943c54
  }
}

// class id: 2322, size: 0x8, field offset: 0x8
abstract class tX extends Object {
}
