// lib: , url: STi

// class id: 1048896, size: 0x8
class :: {
}

// class id: 2468, size: 0x10, field offset: 0x8
//   const constructor, 
class _TN extends Object {

  _SN field_8;
  _SN field_c;
}

// class id: 2731, size: 0x28, field offset: 0x14
class RN extends mG<dynamic> {
}

// class id: 2735, size: 0x20, field offset: 0x14
class UN extends qG {

  late RN _QDc; // offset: 0x18
  late RN _RDc; // offset: 0x1c

  [closure] double <anonymous closure>(dynamic, _TN) {
    // ** addr: 0x717dd0, size: -0x1
  }
}

// class id: 5577, size: 0x14, field offset: 0x14
enum _SN extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
