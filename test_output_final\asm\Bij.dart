// lib: dpj, url: Bij

// class id: 1049608, size: 0x8
class :: {
}

// class id: 863, size: 0xc, field offset: 0x8
class BSa extends IRa {

  static late final vWa iog; // offset: 0x10d8

  [closure] static (dynamic) => BSa <anonymous closure>(dynamic, String, wa) {
    // ** addr: 0x4842f4, size: -0x1
  }
  [closure] static BSa <anonymous closure>(dynamic) {
    // ** addr: 0x484348, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x484290, size: -0x1
  }
}
