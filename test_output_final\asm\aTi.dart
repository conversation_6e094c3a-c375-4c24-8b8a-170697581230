// lib: , url: ATi

// class id: 1048879, size: 0x8
class :: {
}

// class id: 2582, size: 0x90, field offset: 0x24
abstract class wM extends bM {

  late NM _zze; // offset: 0x5c
  late NM _dSd; // offset: 0x58
  late double _Cze; // offset: 0x70
  late NM _Aze; // offset: 0x60

  [closure] static uM _Kze(dynamic, YJ) {
    // ** addr: 0x51db24, size: -0x1
  }
  [closure] static bool _xSd(dynamic, int) {
    // ** addr: 0x51db0c, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x7866cc, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x786be4, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x6cf978, size: -0x1
  }
  [closure] void jCc(dynamic, YJ) {
    // ** addr: 0x785248, size: -0x1
  }
  [closure] void <anonymous closure>(dynamic) {
    // ** addr: 0x6d2cc4, size: -0x1
  }
}

// class id: 2583, size: 0x90, field offset: 0x90
class zM extends wM {
}

// class id: 2584, size: 0x90, field offset: 0x90
class yM extends wM {
}

// class id: 2585, size: 0x90, field offset: 0x90
class xM extends wM {
}

// class id: 5584, size: 0x14, field offset: 0x14
enum _AM extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}

// class id: 5585, size: 0x14, field offset: 0x14
enum _vM extends _Enum {

  _Mint field_8;
  _OneByteString field_10;
}
