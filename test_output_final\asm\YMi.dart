// lib: Mqj, url: YMi

// class id: 1049668, size: 0x8
class :: {
}

// class id: 765, size: 0xc, field offset: 0xc
class YUa extends OTa {

  static late final vWa iog; // offset: 0x1260

  [closure] static YUa <anonymous closure>(dynamic) {
    // ** addr: 0x4709bc, size: -0x1
  }
  [closure] static YUa _Psg(dynamic, String, LTa, KTa, za, za, List<int>) {
    // ** addr: 0x470ad4, size: -0x1
  }
  static vWa iog() {
    // ** addr: 0x470964, size: -0x1
  }
}
